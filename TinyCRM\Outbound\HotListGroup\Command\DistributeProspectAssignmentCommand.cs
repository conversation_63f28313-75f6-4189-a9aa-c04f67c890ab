﻿using AutoMapper;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Threading.Tasks;
using Webaby;
using Webaby.Data;
using Webaby.Localization;

namespace TinyCRM.Outbound.HotListGroup.Command
{
    public class DistributeProspectAssignmentCommand : CommandBase
    {
        public List<Guid> SelectAgentIds { get; set; }

        public List<Guid> SelectProspectAssignmentIds { get; set; }

        public Guid HotListGroupId { get; set; }

        public Guid? LastAgentId { get; set; }

        public Boolean? OpenLead { get; set; }

        public DateTime DistributeTime { get; set; }

        public List<int> ToBeAssigned { get; set; }
    }
    internal class DistributeProspectAssignmentCommandHandler : CommandHandlerBase<DistributeProspectAssignmentCommand>
    {
        public DistributeProspectAssignmentCommandHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task ExecuteAsync(DistributeProspectAssignmentCommand command)
        {
            var cmd = EntitySet.CreateDbCommand();
            cmd.CommandType = CommandType.StoredProcedure;
            cmd.CommandText = "telesale.Prospect_ExecuteAgentReDistribution";

            DataTable dtProspect = new DataTable();
            dtProspect.Columns.Add("Id", typeof(Guid));
            if (command.SelectProspectAssignmentIds != null)
            {
                for (var i = 0; i < command.SelectProspectAssignmentIds.Count; i++)
                {
                    dtProspect.Rows.Add(command.SelectProspectAssignmentIds[i]);
                }
            }

            var prospectIds = new SqlParameter();
            prospectIds.ParameterName = "@selectProspectAssignmentIds";
            prospectIds.Value = dtProspect;
            prospectIds.SqlDbType = SqlDbType.Structured;

            DataTable dtAgent = new DataTable();
            dtAgent.Columns.Add("Id", typeof(Guid));
            dtAgent.Columns.Add("Number", typeof(int));
            dtAgent.Columns.Add("SortOrder", typeof(int));
            if (command.ToBeAssigned != null && command.SelectAgentIds != null)
            {
                for (var i = 0; i < command.SelectAgentIds.Count; i++)
                {
                    dtAgent.Rows.Add(command.SelectAgentIds[i], command.ToBeAssigned[i], 1);
                }
            }

            var agentMap = new SqlParameter();
            agentMap.ParameterName = "@selectAgentIds";
            agentMap.Value = dtAgent;
            agentMap.SqlDbType = SqlDbType.Structured;

            cmd.Parameters.Add(prospectIds);
            cmd.Parameters.Add(agentMap);
            cmd.Parameters.Add(DbParameterHelper.AddNullableGuid(cmd, "@hotListGroup", command.HotListGroupId));
            cmd.Parameters.Add(DbParameterHelper.AddNullableGuid(cmd, "@lastAgentId", command.LastAgentId));
            cmd.Parameters.Add(DbParameterHelper.NewNullableBooleanParameter(cmd, "@openLead", command.OpenLead));
            cmd.Parameters.Add(DbParameterHelper.NewNullableDateTimeParameter(cmd, "@distributedDate", command.DistributeTime));
            await EntitySet.ExecuteNonQueryAsync(cmd);
        }
    }
}
