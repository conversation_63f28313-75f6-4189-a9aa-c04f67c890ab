﻿using System.Data;
using Webaby;
using Webaby.Data;

namespace TinyCRM.RequestTicket.Queries
{
    public class GetRequestTicketByCustomerAndServiceTypeQuery : QueryBase<RequestTicketData>
    {
        public Guid CustomerId { get; set; }

        public Guid ServiceTypeId { get; set; }

        public bool IncludeDeleted { get; set; }
    }

    internal class GetRequestTicketByCustomerAndServiceTypeQueryHandler : QueryHandlerBase<GetRequestTicketByCustomerAndServiceTypeQuery, RequestTicketData>
    {
        public GetRequestTicketByCustomerAndServiceTypeQueryHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task<QueryResult<RequestTicketData>> ExecuteAsync(GetRequestTicketByCustomerAndServiceTypeQuery query)
        {
            // Store "GetRequestTicketByCustomerAndServiceType" -> LinQ

            var mainQuery = (from rt in EntitySet.Get<RequestTicketEntity>(true)
                             where rt.CustomerId == query.CustomerId
                             && rt.ServiceTypeId == query.ServiceTypeId
                             select rt);

            if (!query.IncludeDeleted)
            {
                mainQuery = mainQuery.Where(rt => rt.Deleted == false);
            }

            var mainQueryResult = Mapper.Map<List<RequestTicketData>>(mainQuery.ToList());
            return new QueryResult<RequestTicketData>(mainQueryResult);
        }
    }
}