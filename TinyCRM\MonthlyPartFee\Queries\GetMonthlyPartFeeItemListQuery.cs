﻿using System;
using System.Data;
using System.Data.Common;
using System.Linq;
using System.Threading.Tasks;
using Webaby;
using Webaby.Data;

namespace TinyCRM.MonthlyPartFee.Queries
{
    public class GetMonthlyPartFeeItemListQuery : QueryBase<MonthlyPartFeeExportItemInfo>
    {
        public Guid MonthlyPartFeeId { get; set; }
    }

    public class GetMonthlyPartFeeItemListQueryHandler : QueryHandlerBase<GetMonthlyPartFeeItemListQuery, MonthlyPartFeeExportItemInfo>
    {
        public GetMonthlyPartFeeItemListQueryHandler(IServiceProvider serviceProvider) : base(serviceProvider)
        {
        }

        public override async Task<QueryResult<MonthlyPartFeeExportItemInfo>> ExecuteAsync(GetMonthlyPartFeeItemListQuery query)
        {
            var cmd = EntitySet.CreateDbCommand(CommandType.StoredProcedure, "dbo.GetMonthlyPartFeeItemList");
            cmd.Parameters.Add(DbParameterHelper.AddNullableGuid(cmd, "@MonthlyPartFeeId", query.MonthlyPartFeeId));

            var result = (await EntitySet.ExecuteReadCommandAsync<MonthlyPartFeeExportItemInfo>(cmd)).ToList();
            await Task.CompletedTask;
            return new QueryResult<MonthlyPartFeeExportItemInfo>(result);
        }
    }
}