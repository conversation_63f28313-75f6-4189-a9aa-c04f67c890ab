﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using System.Threading.Tasks;
using Webaby;
using Webaby.Data;

namespace TinyCRM.Organization.Queries
{
    public class GetUserFuncBusinessPermissionConfigsInOrgQuery : QueryBase<UserFuncBusinessPermissionConfigsInOrgItem>
    {
        public Guid OrganizationId { get; set; }

        public List<Guid> BusinessPermissionIds { get; set; }
    }

    internal class GetUserFuncBusinessPermissionConfigsInOrgQueryHandler : QueryHandlerBase<GetUserFuncBusinessPermissionConfigsInOrgQuery, UserFuncBusinessPermissionConfigsInOrgItem>
    {
        public GetUserFuncBusinessPermissionConfigsInOrgQueryHandler(IServiceProvider serviceProvider) : base(serviceProvider)
        {
        }

        public override async Task<QueryResult<UserFuncBusinessPermissionConfigsInOrgItem>> ExecuteAsync(GetUserFuncBusinessPermissionConfigsInOrgQuery query)
        {
            var cmd = EntitySet.CreateDbCommand(CommandType.StoredProcedure, "dbo.GetUserFuncBusinessPermissionConfigsInOrg");
            cmd.Parameters.Add(DbParameterHelper.AddNullableGuid(cmd, "@OrganizationId", query.OrganizationId));
            cmd.Parameters.Add(DbParameterHelper.NewIdListParameter("@BusinessPermissionIds", query.BusinessPermissionIds));

            var mainQuery = await EntitySet.ExecuteReadCommandAsync<UserFuncBusinessPermissionConfigsInOrgItem>(cmd);
            await Task.CompletedTask;
            return QueryResult.Create(mainQuery);
        }
    }

    public class UserFuncBusinessPermissionConfigsInOrgItem
    {
        public Guid BusinessPermissionId { get; set; }

        public string BusinessPermission { get; set; }

        public Guid? OrganizationId { get; set; }

        public Guid? UserId { get; set; }

        public string FullName { get; set; }

        public Guid? RoleId { get; set; }

        public string RoleName { get; set; }
    }
}