﻿using System;
using System.Data;
using System.Data.SqlClient;
using System.Threading.Tasks;
using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Webaby;
using Webaby.Data;
using Webaby.Localization;

namespace TinyCRM.Outbound.ExcelImport.MassData
{
    public class UpdateImportCustomerCodeCommand : CommandBase
    {
        public Guid ImportSessionId { get; set; }

        public string CustomerFormatCode { get; set; }
    }
    internal class UpdateImportCustomerCodeCommandHandler : CommandHandlerBase<UpdateImportCustomerCodeCommand>
    {
        public UpdateImportCustomerCodeCommandHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task ExecuteAsync(UpdateImportCustomerCodeCommand command)
        {
            var cmd = EntitySet.CreateDbCommand();
            cmd.CommandText = "dbo.UpdateImportedVLCode";
            cmd.CommandType = CommandType.StoredProcedure;
            cmd.Parameters.Add(DbParameterHelper.AddNullableGuid(cmd, "@ImportSessionId", command.ImportSessionId));
            cmd.Parameters.Add(DbParameterHelper.AddNullableString(cmd, "@CustomerFormatCode", command.CustomerFormatCode));
            cmd.CommandTimeout = DatabaseCommandtimeout.Slow;
            await EntitySet.ExecuteNonQueryAsync(cmd, false);
        }
    }
}
