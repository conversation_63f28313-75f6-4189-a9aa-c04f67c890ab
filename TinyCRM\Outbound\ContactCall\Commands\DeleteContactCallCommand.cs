﻿using AutoMapper;
using Microsoft.EntityFrameworkCore;
using System;
using System.Data;
using System.Threading.Tasks;
using Webaby;
using Webaby.Data;
using Webaby.Localization;

namespace TinyCRM.Outbound.ContactCall.Commands
{
    public class DeleteContactCallCommand : CommandBase
    {
        public Guid Id
        {
            get;
            set;
        }
    }

    public class DeleteContactCallCommandHandler : CommandHandlerBase<DeleteContactCallCommand>
    {
        public DeleteContactCallCommandHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task ExecuteAsync(DeleteContactCallCommand command)
        {
            var cmd = EntitySet.CreateDbCommand();
            cmd.CommandTimeout = 3600;
            cmd.CommandType = CommandType.StoredProcedure;
            cmd.CommandText = "DeleteContactCall";
            cmd.Parameters.Add(DbParameterHelper.AddNullableGuid(cmd, "@ContactCallId", command.Id));

            await EntitySet.ExecuteNonQueryAsync(cmd);
        }
    }
}