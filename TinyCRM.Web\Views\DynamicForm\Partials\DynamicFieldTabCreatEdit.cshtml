﻿@using AutoMapper
@using Webaby.Core.DynamicForm
@using Webaby.Core.DynamicForm.Queries
@using TinyCRM.DynamicForm
@using TinyCRM.Web.Models.DynamicForm
@using Newtonsoft.Json
@Script.Require("ckeditorAutoComplete")
@Script.Require("ExcelFormulaBeauti")
@inject IMapper Mapper
@inject IServiceProvider ServiceProvider
@model DynamicFieldModel
@{
    Layout = "~/Views/Shared/_EmptyView-v5.cshtml";
    var staticFieldGroups = Newtonsoft.Json.JsonConvert.SerializeObject(Model.StaticFieldGroups);

    List<GlobalCalculatedScriptInfo> globalCalculatedScriptInfoList = new List<GlobalCalculatedScriptInfo>();
    string globalCalculatedScriptInfoListString = Configuration.GetValue<string>("globalcalculated.scriptinfo.list");
    if (globalCalculatedScriptInfoListString.IsNotNullOrEmpty())
    {
        globalCalculatedScriptInfoList = Newtonsoft.Json.JsonConvert.DeserializeObject<List<GlobalCalculatedScriptInfo>>(globalCalculatedScriptInfoListString);
    }
    var formulajson = JsonConvert.SerializeObject(Model.ExtensionExCelFormula);

    string formAction = Url.Action("CreateEditDynamicField", "DynamicForm");
    if (Model.VersioningCalcField.HasValue && Model.VersioningCalcField.Value)
    {
        formAction = Url.Action("CreateEditVersioningCalcDynamicField", "DynamicForm");
    }

    bool customerVersioningEnabled = Configuration.GetValue<bool>("customer.versioning.enabled");

    var listVersionName = new List<TinyCRM.CustomerVersionName.CustomerVersionNameEntity>();
    if (customerVersioningEnabled)
    {
        listVersionName = (await QueryExecutor.ExecuteManyAsync(new TinyCRM.CustomerVersionName.Queries.GetAllCustomerVersionNameQuery { })).ToList();
    }
    var appUrl = Configuration.GetValue<string>("application:url").Trim('/');
}
<div id="divCreateGuiKPI" class="modal-dialog modal-xlg" role="document">
    <div class="modal-content">
        <div class="modal-header">
            <h4 class="modal-title" id="createEditModalLabel">
                @if (Model.Id.HasValue)
                {
                    <i class="la la-edit"></i>
                    @T["Soạn Thảo Trường Thông Tin"]
                }
                else
                {
                    <i class="la la-plus"></i>
                    @T["Tạo mới"]
                }
            </h4>
            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                <span aria-hidden="true">
                    &times;
                </span>
            </button>
        </div>
        <div class="m-portlet m-portlet--unair m-portlet--tabs" style="margin-bottom: 10px">
            <div class="m-portlet__head">
                <div class="m-portlet__head-tools" id="tabdynamicfield">
                    <ul class="nav nav-tabs  m-tabs-line m-tabs-line--2x m-tabs-line--info" role="tablist">
                        <li class="nav-item m-tabs__item">
                            <a class="nav-link m-tabs__link active show" data-toggle="tab" href="#tabInfoBasic" role="tab" aria-selected="true">
                                <i class="la la-list"> </i>&nbsp;@T["Cơ bản"]
                            </a>
                        </li>
                        <li class="nav-item m-tabs__item">
                            <a class="nav-link m-tabs__link" data-toggle="tab" href="#tabCaculated" role="tab" aria-selected="false">
                                <i class="la la-filter"></i>@T["Công thức"]
                            </a>
                        </li>
                        <li class="nav-item m-tabs__item">
                            <a class="nav-link m-tabs__link" data-toggle="tab" href="#tabGlobalCaculated" role="tab" aria-selected="false">
                                <i class="la la-filter"></i>@T["Công thức Nhóm"]
                            </a>
                        </li>
                        <li class="nav-item m-tabs__item">
                            <a class="nav-link m-tabs__link" data-toggle="tab" href="#tabAdvancedInfo" role="tab" aria-selected="false">
                                <i class="la la-puzzle-piece"></i>@T["Nâng cao"]
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
            <div class="m-portlet__body" style="padding-bottom: 0px;">
                <form id="frmCreateEditDynamicField" action="@formAction" method="POST" class="form-horizontal ajax-form">
                    <div class="tab-content">
                        <div class="tab-pane" id="tabCaculated" role="tabpanel">
                            @if (Model.VersioningCalcField.HasValue && Model.VersioningCalcField.Value)
                            {
                                <div class="form-group m-form__group row">
                                    <div class="col-lg-4">
                                        <label>@T["Customer Version"]</label>
                                        @Html.EditorFor(model => model.CustomerVersions, new { urlArgument = new object[] { Model.DynamicFormId } })
                                    </div>
                                </div>
                            }
                            <div class="divInputCaculated replace-formula-1">
                                <div class="form-group m-form__group row">
                                    <div class="col-lg-12">
                                        <label>@T["Công thức"]</label>
                                        <div class="contain-icon" style="display: inline">
                                            <a href="javascript:void(0)" id="insertfield" onclick="changeFormula(this,'formular_2' , '', '', '')">
                                                <i class="fa flaticon-edit"></i>
                                            </a>
                                            <a href="javascript:void(0)" style="float: right" title="beautiful" onclick="beautiFormulaField('')">
                                                <i class="fa flaticon-medical"></i>
                                            </a>
                                            <a href="javascript:void(0)" style="float: right; margin-right: 10px;" title="refresh" onclick="ministringFormulaField()">
                                                <i class="fa flaticon-refresh"></i>
                                            </a>
                                        </div>
                                        <textarea id="UIFriendlyFormula" name="UIFriendlyFormula"></textarea>
                                        @Html.HiddenFor(model => model.UIFriendlyFormula, new { @id = "hidUIFriendlyFormula" })
                                        @Html.HiddenFor(model => model.VersioningCalcField)
                                        @*@Html.TextAreaFor(model => model.DefaultValue, new { @class = "form-control m-input ckeditor", @rows = "1" })*@
                                    </div>
                                </div>
                                <div class="form-group m-form__group row">
                                    <div class="col-lg-3">
                                        <label>@T["Chọn hàm"]</label>
                                        <select id="ExCelformula" class="form-control m-select2">
                                            <option value=""></option>
                                            @foreach (var item in Model.ExtensionExCelFormula.Where(x => x.value.Contains("(")))
                                            {
                                                <option value="@item.value.Replace("\\\"","\"")">@item.text.Replace("\\\"", "\"")</option>
                                            }
                                        </select>
                                    </div>
                                    <div class="col-lg-3">
                                        <label>@T["Trường hệ thống"]</label>
                                        <select id="cboSystemDynamicFields" class="form-control m-select2">
                                            <option value=""></option>
                                        </select>
                                    </div>
                                    @if (Model.DynamicFormId.Value != DynamicFormContants.SystemDynamicFormId)
                                    {
                                        <div class="change_color col-lg-3">
                                            <label>@T["Chọn Trường Gộp của Đại lý"]</label>
                                            <select id="KPIfield" class="form-control ">
                                                <option></option>
                                                @foreach (var item in Model.ColorDynamicFields)
                                                {
                                                    <option value="@item.Value">@item.Text</option>
                                                }
                                            </select>
                                        </div>
                                        if (Model.ColorDynamicTable != null)
                                        {
                                            <div class="change_color col-lg-3">
                                                <label>@T["Chọn Cột của Hợp đồng"]</label>
                                                <select id="TableFormular" class="form-control">
                                                    <option></option>
                                                    @foreach (var item in Model.ColorDynamicTable)
                                                    {
                                                        <option value="@item.Value">@item.Text</option>
                                                    }
                                                </select>
                                            </div>
                                        }
                                    }
                                </div>
                            </div>
                            <div class="replace-formula-2 hidden" style="padding: 0 0 0 0; margin: 0 0 0 0;"></div>
                        </div>
                        <div id="tabInfoBasic" class="tab-pane active show" role="tabpanel">
                            <div class="divInputDataBasic">
                                @Html.HiddenFor(model => model.Id)
                                @Html.HiddenFor(model => model.DynamicFormId)
                                @Html.HiddenFor(model => model.SourceFieldName)
                                @Html.HiddenFor(model => model.SourceTableName)
                                @Html.HiddenFor(model => model.IsNew)
                                @Html.HiddenFor(model => model.RepresentationDynamicFieldId)
                                <div class="form-group m-form__group row">
                                    <div class="col-lg-6">

                                        <label>@T["Tên hiển thị"]</label>
                                        @Html.TextBoxFor(model => model.DisplayName, new { @id = "txtDisplayName", @class = "form-control m-input", @placeholder = T["Nhập tên hiển thị"] })
                                    </div>
                                    <div class="col-lg-6">
                                        <label>@T["Tên Tham Khảo"]</label>
                                        @Html.TextBoxFor(model => model.Name, new { @id = "txtFieldName", @class = "form-control m-input", @placeholder = T["Nhập tên Trường thông tin"] })
                                    </div>

                                </div>
                                <div class="form-group m-form__group row">
                                    <div class="col-lg-6">
                                        <label>@T["Kiểu dữ liệu"]</label>
                                        @Html.EditorFor(x => x.DataType)
                                    </div>
                                    <div class="col-lg-6">
                                        <label>@T["Kiểu hiển thị"]</label>
                                        <select id="@Html.IdFor(x => x.ViewHint)" name="@Html.NameFor(x => x.ViewHint)">
                                            <option selected value="">@T["Chọn kiểu hiển thị"]</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="form-group m-form__group row">
                                    <div class="col-lg-6">
                                        <label>@T["Nhóm Trường"]</label>
                                        @Html.EditorFor(model => model.DynamicFieldSectionId, new { urlArgument = new object[] { Model.DynamicFormId } })
                                    </div>
                                    <div class="col-lg-6">
                                        <label>@T["Giá trị mặc định"]</label>
                                        @Html.TextBoxFor(model => model.DefaultValue, new { @class = "form-control m-input" })
                                    </div>
                                </div>
                                <div class="form-group m-form__group row source-field">

                                    <div class="col-lg-6">
                                        <label>
                                            @T["Nguồn đối tượng"]
                                            <i class="la la-link m--font-info"></i>
                                        </label>
                                        <select id="cboStaticFieldGroups" class="form-control m-input" style="margin: 0px">
                                            <option value="">@T["Chọn nguồn đối tượng"]</option>
                                            @foreach (var sfg in Model.StaticFieldGroups)
                                            {
                                                <option value="@Html.Raw(sfg.Name)">@Html.Raw(sfg.Name)</option>
                                            }
                                        </select>
                                    </div>
                                    <div class="col-lg-6">
                                        <label>
                                            @T["Nguồn thông tin"]
                                            <i class="la la-link  m--font-info"></i>
                                        </label>
                                        <select id="cboSourceNames" class="form-control m-input" style="margin: 0px">
                                            <option value="">@T["Chọn nguồn thông tin"]</option>
                                        </select>
                                    </div>
                                </div>

                                <div class="form-group m-form__group row">
                                    <div id="divDynamicDefinedTableSchemaId" class="col-lg-6" style="display: none;">
                                        <label>@T["Danh sách đã định nghĩa"]</label>
                                        @Html.EditorFor(model => model.DynamicDefinedTableSchemaId)
                                    </div>
                                    @if (!string.IsNullOrEmpty(Model.AdditionalFilter))
                                    {
                                        <div class="col-lg-6">
                                            <label>@T["Giới hạn dữ liệu"]</label>
                                            <select id="cboAdditionalFilter" class="form-control" name="AdditionalFilter">
                                                <option value="">@T["Chọn loại dữ liệu"]</option>
                                            </select>
                                        </div>
                                    }
                                </div>
                                <div class="form-group m-form__group row">
                                    <div class="col-lg-6">
                                        <label>@T["Loại thông tin"]</label>
                                        @Html.EditorFor(x => x.FieldType)
                                    </div>
                                    <div class="col-lg-@(customerVersioningEnabled ? 3 : 6)">
                                        <label>@T["Payment Type"]</label>
                                        @Html.EditorFor(model => model.PaymentType)
                                    </div>
                                    @if (customerVersioningEnabled)
                                    {
                                        <div class="col-lg-3">
                                            <label>@T["Version Code"]</label>
                                            <input name="VersionCode" value="@Model.VersionCode" class="form-control m-input" readonly />
                                        </div>
                                        @*<div class="col-lg-3">
                                                <label>@T["Customer Version Global"]</label>
                                                <div class="form-group">
                                                    <select id="selVersionCode" class="form-control m-select2" name="VersionCode">
                                                        <option value="">@T["Chọn versioning"]</option>
                                                        @foreach (var item in listVersionName)
                                                        {
                                                            <option @(Html.Raw(item.NameVersion == Model.VersionCode ? "selected" : "")) value="@item.NameVersion">@item.NameVersion</option>
                                                        }
                                                    </select>
                                                </div>
                                            </div>*@
                                    }
                                </div>

                                <div class="form-group m-form__group row">
                                    <div class="col-lg-6">
                                        <label>@T["Dùng làm điều kiện Workflow"]</label>
                                        @Html.EditorFor(model => model.FieldCondition)
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div id="tabAdvancedInfo" class="tab-pane" role="tabpanel">
                            <div class="divInputDataAdvanced">
                                <div class="form-group m-form__group row">
                                    <div class="col-lg-6">
                                        <label>@T["Thứ tự hiển thị"]</label>
                                        <input type="number" name="Order" value="@Model.Order" class="form-control m-input" readonly="readonly" />
                                    </div>
                                    <div class="col-lg-3">
                                        <label>@T["Chọn màu chữ: "]&nbsp;</label>
                                        <input id="Color" class="spectrum_color" name="Color" value="@(Model.Color.IsNotNullOrEmpty() ? Model.Color : "transparent")">
                                    </div>
                                    <div class="col-lg-3">
                                        <label>@T["Chọn màu border: "]&nbsp;</label>
                                        <input id="BackgroundColor" class="spectrum_color" name="BackgroundColor" value="@(Model.BackgroundColor.IsNotNullOrEmpty() ? Model.BackgroundColor : "transparent")">
                                    </div>
                                </div>
                                <div id="div360Mapping" class="form-group m-form__group row" @(Html.Raw(Model.FieldType == FieldType.Mapping ? "" : "style=\"display: none;\""))>
                                    <input type="hidden" id="hidMapping360FieldName" name="Mapping360FieldName" value="@Model.Mapping360FieldName" />
                                    <input type="hidden" id="hidMapping360FieldType" name="Mapping360FieldType" value="@Model.Mapping360FieldType" />
                                    <div class="col-lg-6">
                                        <label>@T["Trường ánh xạ 360 view"]</label>
                                        <select id="cboMapping360ViewDetail" class="form-control m-input" style="margin: 0px">
                                            <option>@T["Chọn trường ánh xạ"]</option>
                                        </select>
                                    </div>
                                    <div id="div360MappingRowOptions" class="col-lg-6" @(Html.Raw(Model.FieldType == FieldType.Mapping && Model.Mapping360FieldType == Mapping360Type.Table ? "" : "style=\"display: none;\""))>
                                        <label>@T["Dòng ánh xạ"]</label>
                                        <select id="cboMapping360RowOptions" name="Mapping360RowOptions" class="form-control m-input">
                                            <option value="SelectedRow">@T["Dòng được chọn"]</option>
                                        </select>
                                    </div>
                                </div>
                                <div id="m_repeater">
                                    <div class="form-group m-form__group row">
                                        <div class="col-lg-12">
                                            <label>@T["Giá trị chọn lựa"]</label>
                                            <div data-repeater-list="" data-nested="false">
                                                @if (Model.SelectOptionList != null && Model.SelectOptionList.Any())
                                                {
                                                    foreach (var option in Model.SelectOptionList)
                                                    {
                                                        <div data-repeater-item class="m--margin-bottom-10">
                                                            <div class="input-group">
                                                                <input type="text" class="form-control" name="@Html.NameFor(model => model.SelectOptionList)" value="@option">
                                                                <div class="input-group-append">
                                                                    <a href="javascript:void(0)" data-repeater-delete class="btn btn-danger m-btn m-btn--icon">
                                                                        <i class="la la-close"></i>
                                                                    </a>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    }
                                                }
                                                else
                                                {
                                                    <div data-repeater-item class="m--margin-bottom-10">
                                                        <div class="input-group">
                                                            <input type="text" class="form-control" name="@Html.NameFor(model => model.SelectOptionList)">
                                                            <div class="input-group-append">
                                                                <a href="javascript:void(0)" data-repeater-delete class="btn btn-danger m-btn m-btn--icon">
                                                                    <i class="la la-close"></i>
                                                                </a>
                                                            </div>
                                                        </div>
                                                    </div>
                                                }
                                            </div>
                                        </div>
                                        <div class="col-12">
                                            <div data-repeater-create="" class="btn btn btn-warning m-btn m-btn--icon">
                                                <span>
                                                    <i class="la la-plus"></i>
                                                    <span>
                                                        @T["Add"]
                                                    </span>
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div id="v_repeater">
                                    <div class="form-group m-form__group row">
                                        <div class="col-lg-12">
                                            <label>@T["Thẩm định dữ liệu"]</label>
                                            <div data-repeater-list="Validations">
                                                @if (Model.Validations != null && Model.Validations.Any())
                                                {
                                                    int count = 0;
                                                    foreach (var itm in Model.Validations)
                                                    {
                                                        <div data-repeater-item class="row m--margin-bottom-10">
                                                            <div class="col-6">
                                                                <div class="input-group">
                                                                    <input type="text" class="form-control" name="Validations[@(count)].Validation" value="@itm.Validation" />
                                                                </div>
                                                            </div>
                                                            <div class="col-6">
                                                                <div class="input-group">
                                                                    <input type="text" class="form-control" name="Validations[@(count)].ValidationMessage" value="@itm.ValidationMessage" />
                                                                    <div class="input-group-append">
                                                                        <a href="javascript:void(0)" data-repeater-delete class="btn btn-danger m-btn m-btn--icon">
                                                                            <i class="la la-close"></i>
                                                                        </a>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        count++;
                                                    }
                                                }
                                                else
                                                {
                                                    <div data-repeater-item class="row m--margin-bottom-10">
                                                        <div class="col-6">
                                                            <div class="input-group">
                                                                <input type="text" class="form-control" name="Validations[0].Validation" />
                                                            </div>
                                                        </div>
                                                        <div class="col-6">
                                                            <div class="input-group">
                                                                <input type="text" class="form-control" name="Validations[0].ValidationMessage" />
                                                                <div class="input-group-append">
                                                                    <a href="javascript:void(0)" data-repeater-delete class="btn btn-danger m-btn m-btn--icon">
                                                                        <i class="la la-close"></i>
                                                                    </a>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                }
                                            </div>
                                        </div>
                                        <div class="col-12">
                                            <div data-repeater-create="" class="btn btn btn-warning m-btn m-btn--icon">
                                                <span>
                                                    <i class="la la-plus"></i>
                                                    <span>
                                                        @T["Add"]
                                                    </span>
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div id="b_repeater">
                                    <div class="form-group m-form__group row">
                                        <div class="col-lg-12">
                                            <label>@T["Thẩm định dữ liệu nghiệp vụ"]</label>
                                            <div data-repeater-list="BusinessValidations">
                                                @if (Model.BusinessValidations != null && Model.BusinessValidations.Any())
                                                {
                                                    int count = 0;
                                                    foreach (var itm in Model.BusinessValidations)
                                                    {
                                                        <div data-repeater-item class="row m--margin-bottom-10">
                                                            <div class="col-6">
                                                                <div class="input-group">
                                                                    <input type="text" class="form-control" name="BusinessValidations[@(count)].Validation" value="@itm.Validation" />
                                                                </div>
                                                            </div>
                                                            <div class="col-6">
                                                                <div class="input-group">
                                                                    <input type="text" class="form-control" name="BusinessValidations[@(count)].ValidationMessage" value="@itm.ValidationMessage" />
                                                                    <div class="input-group-append">
                                                                        <a href="javascript:void(0)" data-repeater-delete class="btn btn-danger m-btn m-btn--icon">
                                                                            <i class="la la-close"></i>
                                                                        </a>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        count++;
                                                    }
                                                }
                                                else
                                                {
                                                    <div data-repeater-item class="row m--margin-bottom-10">
                                                        <div class="col-6">
                                                            <div class="input-group">
                                                                <input type="text" class="form-control" name="BusinessValidations[0].Validation" />
                                                            </div>
                                                        </div>
                                                        <div class="col-6">
                                                            <div class="input-group">
                                                                <input type="text" class="form-control" name="BusinessValidations[0].ValidationMessage" />
                                                                <div class="input-group-append">
                                                                    <a href="javascript:void(0)" data-repeater-delete class="btn btn-danger m-btn m-btn--icon">
                                                                        <i class="la la-close"></i>
                                                                    </a>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                }
                                            </div>
                                        </div>
                                        <div class="col-12">
                                            <div data-repeater-create="" class="btn btn btn-warning m-btn m-btn--icon">
                                                <span>
                                                    <i class="la la-plus"></i>
                                                    <span>
                                                        @T["Add"]
                                                    </span>
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div id="addmt_repeater">
                                    <div class="form-group m-form__group row">
                                        <div class="col-lg-12">
                                            <label>@T["Dữ liệu phụ trợ"]</label>
                                            <div data-repeater-list="AdditionalMetadatas">
                                                @if (Model.AdditionalMetadatas != null && Model.AdditionalMetadatas.Any())
                                                {
                                                    int count = 0;
                                                    foreach (var itm in Model.AdditionalMetadatas)
                                                    {
                                                        <div data-repeater-item class="row m--margin-bottom-10">
                                                            <div class="col-6">
                                                                <div class="input-group">
                                                                    <input type="text" class="form-control" name="AdditionalMetadatas[@(count)].Key" value="@itm.Key" />
                                                                </div>
                                                            </div>
                                                            <div class="col-6">
                                                                <div class="input-group">
                                                                    <input type="text" class="form-control" name="AdditionalMetadatas[@(count)].Value" value="@itm.Value" />
                                                                    <div class="input-group-append">
                                                                        <a href="javascript:void(0)" data-repeater-delete class="btn btn-danger m-btn m-btn--icon">
                                                                            <i class="la la-close"></i>
                                                                        </a>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        count++;
                                                    }
                                                }
                                                else
                                                {
                                                    <div data-repeater-item class="row m--margin-bottom-10">
                                                        <div class="col-6">
                                                            <div class="input-group">
                                                                <input type="text" class="form-control" name="AdditionalMetadatas[0].Key" />
                                                            </div>
                                                        </div>
                                                        <div class="col-6">
                                                            <div class="input-group">
                                                                <input type="text" class="form-control" name="AdditionalMetadatas[0].Value" />
                                                                <div class="input-group-append">
                                                                    <a href="javascript:void(0)" data-repeater-delete class="btn btn-danger m-btn m-btn--icon">
                                                                        <i class="la la-close"></i>
                                                                    </a>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                }
                                            </div>
                                        </div>
                                        <div class="col-12">
                                            <div data-repeater-create="" class="btn btn btn-warning m-btn m-btn--icon">
                                                <span>
                                                    <i class="la la-plus"></i>
                                                    <span>
                                                        @T["Add"]
                                                    </span>
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group m-form__group row loadurlview" style="display:none" id="loadUrlView">
                                    <div class="col-lg-12">
                                        <label>@T["Dữ liệu từ URL"]</label>
                                        @Html.EditorFor(model => model.SelectedConfigLoadUrl)
                                        <div data-repeater-list="LoadURLParameters" id="loadUrlParams" class=" m--margin-top-10">
                                            @if (Model.SelectedConfigLoadUrl.IsNotNullOrEmpty() && Model.LoadURLParameters != null)
                                            {
                                                int count = 0;
                                                foreach (var item in Model.LoadURLParameters)
                                                {
                                                    <div data-repeater-item class="row m--margin-bottom-10">
                                                        <div class="col-6">
                                                            <div class="input-group">
                                                                <input type="text" class="form-control" readonly name="LoadURLParameters[@(count)].Key" value="@item.Key" />
                                                            </div>
                                                        </div>
                                                        <div class="col-6">
                                                            <div class="input-group">
                                                                <input type="text" class="form-control" name="LoadURLParameters[@(count)].Value" value="@item.Value" />
                                                            </div>
                                                        </div>
                                                    </div>
                                                    count++;
                                                }
                                            }
                                        </div>
                                    </div>
                                </div>

                                <div class="form-group m-form__group row">
                                    <div class="col">
                                        <label class="m-checkbox">
                                            @Html.CheckBoxFor(model => model.IsRequired)
                                            @T["Trường bắt buộc"]
                                            <span></span>
                                        </label>
                                    </div>
                                    <div id="divIsReadOnly" class="col" style="">
                                        <label class="m-checkbox">
                                            @Html.CheckBoxFor(model => model.IsReadOnly)
                                            @T["Chỉ đọc"]
                                            <span></span>
                                        </label>
                                    </div>
                                    <div id="displaySelect" class="col">
                                        <label class="m-checkbox">
                                            @Html.CheckBoxFor(model => model.Display)
                                            @T["Hiển thị trên phiếu"]
                                            <span></span>
                                        </label>
                                    </div>
                                </div>
                                <div class="form-group m-form__group row">
                                    <div class="col" style="">
                                        <label class="m-checkbox">
                                            @Html.CheckBoxFor(model => model.IsExportByConditionBoolean)
                                            @T["Điều kiện xuất ra file kết quả"]
                                            <span></span>
                                        </label>
                                    </div>
                                    <div id="divIsReadOnly" class="col" style="">
                                        <label class="m-checkbox">
                                            @Html.CheckBoxFor(model => model.IsExportExcel)
                                            @T["Export Excel"]
                                            <span></span>
                                        </label>
                                    </div>
                                    <div class="col" style="">
                                        <label class="m-checkbox">
                                            @Html.CheckBoxFor(model => model.FreezeValue)
                                            @T["Đã khóa giá trị"]
                                            <span></span>
                                        </label>
                                    </div>
                                </div>
                                @if (Model.DataType != "TinyCRM.AppServices.DynamicDefinedTable.DynamicDefinedTableGridData")
                                {
                                    //Dynamic Field có type là table thì ko render
                                    <div class="form-group m-form__group row">
                                        <div class="col" style="">
                                            <label class="m-checkbox">
                                                @Html.CheckBoxFor(model => model.IsCheckDuplicate)
                                                @T["Check trùng trong hệ thống"]
                                                <span></span>
                                            </label>
                                        </div>
                                    </div>
                                }
                            </div>
                        </div>
                        <div class="tab-pane" id="tabGlobalCaculated" role="tabpanel">
                            <div class="tabGlobalCaculated">
                                <div class="form-group m-form__group row">
                                    @Html.HiddenFor(model => model.GlobalCalcFieldByVersion)
                                    <div class="col-lg-8">
                                        <label style="padding-top: 15px; font-size: 1.25rem;">@T["Công thức"]</label><br />
                                        <select id="cboGlobalCalculatedScripts" name="GlobalCalculatedScriptInfo.ExecutedScript" class="form-control m-select2">
                                            <option value="">@T["Chọn công thức vùng"]</option>
                                            @foreach (var globalCalculatedScriptInfo in globalCalculatedScriptInfoList)
                                            {
                                                <option value="@globalCalculatedScriptInfo.ExecutedScript" data-name="@globalCalculatedScriptInfo.Name" selected=@(globalCalculatedScriptInfo.ExecutedScript.IsEqualIgnoreCase(Model.GlobalCalculatedScriptInfo.ExecutedScript) ? "selected" : "")>@globalCalculatedScriptInfo.Name</option>
                                            }
                                        </select>
                                    </div>
                                    <div class="col-lg-4">
                                        <label style="padding-top: 20px;">@T["Trường thông tin tính toán"]</label><br />
                                        <select id="cboGlobalFormulaBasedOnDynamicFields" name="GlobalCalculatedScriptInfo.GlobalFormulaBasedOnDynamicFieldId" class="" data-initval="@(Model.GlobalCalculatedScriptInfo != null && Model.GlobalCalculatedScriptInfo.GlobalFormulaBasedOnDynamicFieldId.IsNotNullOrEmpty() ? Model.GlobalCalculatedScriptInfo.GlobalFormulaBasedOnDynamicFieldId.Value.ToString() : string.Empty)">
                                        </select>
                                    </div>
                                </div>
                                <div class="form-group m-form__group row">
                                    <div class="col-lg-12">
                                        <label style="padding-top: 15px; font-size: 1.25rem;">@T["Điều kiện trước khi tính"]</label><br />
                                        <table class="table m-table m-table--head-separator-metal table-hover">
                                            <thead>
                                                <tr>
                                                    <th>@T["Trường thông tin"]</th>
                                                    <th>@T["Toán tử"]</th>
                                                    <th>@T["Giá trị so sánh"]</th>
                                                    <th style="width: 64px; vertical-align: middle; text-align: center">
                                                        <a href="javascript:void(0)" onclick="addGlobalDynamicFieldConditionItem();">
                                                            <i class="fa fa-plus m--font-accent"></i>
                                                        </a>
                                                    </th>
                                                </tr>
                                            </thead>
                                            <tbody id="tblDynamicFieldConditions">
                                                @if (Model.GlobalCalculatedScriptInfo != null && Model.GlobalCalculatedScriptInfo.DynamicFieldConditions != null)
                                                {
                                                    int conditionIndex = 0;
                                                    foreach (var condition in Model.GlobalCalculatedScriptInfo.DynamicFieldConditions)
                                                    {
                                                        Guid rowId = Guid.NewGuid();
                                                        string newFieldName = string.Format("GlobalCalculatedScriptInfo.DynamicFieldConditions[{0}].ComparedValue", conditionIndex);

                                                        List<DynamicFieldValueInfo> dynamicFieldList = new List<DynamicFieldValueInfo>();

                                                        DynamicFieldDefinitionData dynamicFieldDefinitionData = await QueryExecutor.ExecuteOneAsync(new GetDynamicFieldInFormByNameQuery { FormId = Model.DynamicFormId.Value, Name = condition.DynamicFieldName });
                                                        if (dynamicFieldDefinitionData != null)
                                                        {
                                                            DynamicFieldValueInfo dynamicFieldValueInfo = DynamicFieldHelper.GetGlobalConditionComparedValueInput(dynamicFieldDefinitionData, newFieldName, Mapper);
                                                            dynamicFieldValueInfo.Value = condition.ComparedValue;

                                                            dynamicFieldList.Add(dynamicFieldValueInfo);
                                                        }

                                                        dynamicFieldList.MapTypeFromExtended();
                                                        object dynamicFormValue =await dynamicFieldList.ConvertToDynamicObjectAsync(ServiceProvider,"DynamicFormModel", "Dynamic");

                                                        <tr id="@rowId" class="glb-condition-row">
                                                            <td>
                                                                <select id="cboFormDynamicFieldListTemplate_@rowId" data-row-id="@rowId" name="@(string.Format("GlobalCalculatedScriptInfo.DynamicFieldConditions[{0}].DynamicFieldName", conditionIndex))" class="form-control m-input glb-condition-dynamicfield">
                                                                    <option value="">@T["Chọn trường thông tin"]</option>
                                                                    @foreach (var dynamicField in Model.FormDynamicFieldList)
                                                                    {
                                                                        var dataType = dynamicField.DataType.ToType();
                                                                        var baseType = dataType.BaseType();
                                                                        if (!baseType.IsEntityData() && dynamicField.Display)
                                                                        {
                                                                            <option value="@dynamicField.Name" data-id="@dynamicField.Id" data-sql-data-type="@DynamicFieldHelper.GetSqlDataType(dynamicField.DataType)" selected=@(condition.DynamicFieldName == dynamicField.Name ? "selected" : "")>@dynamicField.DisplayName</option>
                                                                        }
                                                                    }
                                                                </select>
                                                                <input type="hidden" data-row-id="@rowId" class="glb-condition-datatype" name="@(string.Format("GlobalCalculatedScriptInfo.DynamicFieldConditions[{0}].DataType", conditionIndex))" value="@condition.DataType" />
                                                            </td>
                                                            <td>
                                                                <select data-row-id="@rowId" name="@(string.Format("GlobalCalculatedScriptInfo.DynamicFieldConditions[{0}].CompareOperator", conditionIndex))" class="form-control m-input glb-condition-operator">
                                                                    <option value="">@T["Chọn toán tử"]</option>
                                                                    <option value=">" selected=@(condition.CompareOperator == ">" ? "selected" : "")>&gt;</option>
                                                                    <option value=">=" selected=@(condition.CompareOperator == ">=" ? "selected" : "")>&gt;=</option>
                                                                    <option value="<" selected=@(condition.CompareOperator == "<" ? "selected" : "")>&lt;</option>
                                                                    <option value="<=" selected=@(condition.CompareOperator == "<=" ? "selected" : "")>&lt;=</option>
                                                                    <option value="=" selected=@(condition.CompareOperator == "=" ? "selected" : "")>=</option>
                                                                </select>
                                                            </td>
                                                            <td class="glb-condition-comparedvalue-td" data-name="@newFieldName" style="vertical-align: middle">
                                                                @(await Html.PartialAsync("~/Views/DynamicForm/Partials/InputEditorForDynamicField.cshtml", dynamicFormValue))
                                                            </td>
                                                            <td style="text-align: center; vertical-align: middle">
                                                                <a href="javascript:void(0)" data-row-id="@rowId" onclick="removeGlobalDynamicFieldConditionItem(this)" class="btn-remove-glb-condition">
                                                                    <i class="fa fa-trash-alt m--font-danger"></i>
                                                                </a>
                                                            </td>
                                                        </tr>
                                                        conditionIndex++;
                                                    }
                                                }
                                                <tr id="trGlbConditionItemTemplate" style="display: none">
                                                    <td>
                                                        <select id="cboFormDynamicFieldListTemplate" class="form-control m-input glb-condition-dynamicfield">
                                                            <option value="">@T["Chọn trường thông tin"]</option>
                                                            @foreach (var dynamicField in Model.FormDynamicFieldList)
                                                            {
                                                                var dataType = dynamicField.DataType.ToType();
                                                                var baseType = dataType.BaseType();
                                                                if (!baseType.IsEntityData() && dynamicField.Display)
                                                                {
                                                                    <option value="@dynamicField.Name" data-id="@dynamicField.Id" data-sql-data-type="@DynamicFieldHelper.GetSqlDataType(dynamicField.DataType)">@dynamicField.DisplayName</option>
                                                                }
                                                            }
                                                        </select>
                                                        <input type="hidden" class="glb-condition-datatype" />
                                                    </td>
                                                    <td>
                                                        <select id="cboOperatorListTemplate" class="form-control m-input glb-condition-operator">
                                                            <option value="">@T["Chọn toán tử"]</option>
                                                            <option value=">">&gt;</option>
                                                            <option value=">=">&gt;=</option>
                                                            <option value="<">&lt;</option>
                                                            <option value="<=">&lt;=</option>
                                                            <option value="=">=</option>
                                                        </select>
                                                    </td>
                                                    <td class="glb-condition-comparedvalue-td" style="vertical-align: middle">
                                                        <input type="text" class="form-control m-input glb-condition-comparedvalue">
                                                    </td>
                                                    <td style="text-align: center; vertical-align: middle">
                                                        <a href="javascript:void(0)" data-row-id="" onclick="removeGlobalDynamicFieldConditionItem(this)" class="btn-remove-glb-condition">
                                                            <i class="fa fa-trash-alt m--font-danger"></i>
                                                        </a>
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </table>

                                    </div>
                                </div>
                                <div class="form-group m-form__group row">
                                    <div class="col-lg-12">
                                        <label style="padding-top: 15px; font-size: 1.25rem;">@T["Tham số cho công thức"]</label><br />

                                        <div style="padding: 0;" id="divGlobalCalculatedScriptParameters" class="m-portlet__body">
                                        </div>

                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
        <div class="modal-footer">
            @if (Model.Id.HasValue)
            {
                <button id="btndeletefield" type="button" onclick="removeDynamicField('@Model.Id', '@Model.DisplayName')" class="btn btn-outline-danger m-btn btn-sm  m-btn--icon mr-auto">
                    <i class="la la-trash"></i>@T["Xóa trường"]
                </button>
            }
            <button type="button" class="btn btn-secondary" data-dismiss="modal">
                @T["Thoát"]
            </button>
            <button type="button" id="btnSaveDynamicField" class="btn btn-info changeFieldSubmitId">
                <i class="la la-save"></i>@T["Lưu"]
            </button>
        </div>
    </div>
</div>
@using (Foot())
{
    <script type="text/javascript">
        var configAutoComplete = JSON.parse(`@Html.Raw(formulajson)`);
        var insertElementField;
        var editor;
        var isSupportAuto = true;
        var extensionExCelFormula = JSON.parse(@Newtonsoft.Json.JsonConvert.SerializeObject(Model.ExtensionExCelFormula.Where(x => x.value.Contains("(")).Select(x => new { text= x.text, id = x.value })).JsonRaw());
        var colorDynamicFields = JSON.parse(@Newtonsoft.Json.JsonConvert.SerializeObject(Model.ColorDynamicFields.Select(x => new { text = x.Text , id = x.Value.TrimEnd(',') })).JsonRaw());
        var colorDynamicTable = JSON.parse(@Newtonsoft.Json.JsonConvert.SerializeObject(Model.ColorDynamicTable.Select(x => new { text = x.Text, id = x.Value.Substring(0,x.Value.LastIndexOf(',')) })).JsonRaw());

        $(function () {
            var isSyncInput = '@Model.Name' == '' ? true : false;
            $('#tabdynamicfield a[data-toggle="tab"]').on('shown.bs.tab', function (e) {
                var tabId = $(e.target).attr('href');
                if (tabId == '#tabInfoBasic') {
                    $('.changeFieldSubmitId').attr('id','btnSaveDynamicField');
                } else if (tabId == '#tabCaculated') {
                    $('.changeFieldSubmitId').attr('id','submitKPI');
                } else if (tabId == '#tabGlobalCaculated') {
                    $('.changeFieldSubmitId').attr('id','btnSaveGlobalCalculatedScript');
                } else if (tabId == '#tabAdvancedInfo') {
                    $('.changeFieldSubmitId').attr('id','btnSaveDynamicField');
                }
                webaby.lastFocusSelector($('#frmCreateEditDynamicField :input:enabled:visible:not([readonly]):first'));
                editor.focus();
                setTimeout(ev => {
                    var range = editor.createRange();
                    range.moveToElementEditEnd(range.root);
                    editor.getSelection().selectRanges([range]);
                }, 200);
            });

            $("#txtFieldName").on('focus', function () {
                isSyncInput = false;
            })

            $("#txtFieldName").on('focusout', function () {
                if ($(this).val() == '') {
                    isSyncInput = true;
                }
            })

            $('#txtDisplayName').on('keyup', function () {
                if (isSyncInput) {
                    var data = webaby.removeVietnameseTones($(this).val());
                    $('#txtFieldName').val(data);
                }
            })

            $('#createGuiKPIModal').on('hidden.bs.modal', function () {
                if (CKEDITOR.instances['UIFriendlyFormula']) CKEDITOR.instances['UIFriendlyFormula'].destroy();
            })

            $('.changeFieldSubmitId').on('click', function (e) {
                e.preventDefault();
                var btnId = $(this).attr('id');
                if (btnId == 'btnSaveDynamicField') {
                    if ($('#FieldType').val() == 'Calculated' || $('#FieldType').val() == 'SuggestValueCalculated') {
                        if ($(this).closest(".modal-content").find("#frmCreateEditDynamicField .tab-content .replace-formula-2").length == 0) {
                            // chức năng tạo công thức mới convert UIFriendlyFormula
                            var htmlstring = CKEDITOR.instances['UIFriendlyFormula'].getData();
                            $('input[name=DefaultValue]').val(convertToPlain(htmlstring));
                        }
                        else {
                            var htmlstring = CKEDITOR.instances['UIFriendlyFormula'].getData();
                            $('input[name=DefaultValue]').val(convertToPlain(htmlstring));
                            if ($.isFunction(window.OrderingTable)) { OrderingTable("tbRule"); }
                        }
                    }
                    var dynamicFormValueCount = $('#DynamicFormValueCount').val();
                    var turnOffWarning = $('input[name="TurnOffWarning"]').val();
                    if (dynamicFormValueCount > 0 && turnOffWarning === false) {
                        webaby.confirm('@T["Xác nhận lưu thay đổi"]', '@T["Mẫu thông tin này đã có dữ liệu. Bạn có muốn tiếp tục lưu?"]', function () {
                            var htmlstring = CKEDITOR.instances['UIFriendlyFormula'].getData();
                            $('input[name=DefaultValue]').val(convertToPlain(htmlstring));
                            $('#frmCreateEditDynamicField').submit();
                        });
                    } else {
                        $('#frmCreateEditDynamicField').submit();
                    }
                } else if (btnId == 'submitKPI') {
                    var isRuleView = $(".replace-formula-2 :visible");
                    if (($('#FieldType').val() == 'Calculated' || $('#FieldType').val() == 'SuggestValueCalculated') && isRuleView.length == 0) {
                        var htmlstring = CKEDITOR.instances['UIFriendlyFormula'].getData();
                        $('input[name=DefaultValue]').val(convertToPlain(htmlstring));
                    }
                    if (isRuleView.length != 0) {
                        var htmlstring = CKEDITOR.instances['UIFriendlyFormula'].getData();
                        $('input[name=DefaultValue]').val(convertToPlain(htmlstring));
                        if ($.isFunction(window.OrderingTable)) { OrderingTable("tbRule"); }
                    }
                    $('#frmCreateEditDynamicField').submit();
                } else if (btnId == 'btnSaveGlobalCalculatedScript') {
                    $('#frmCreateEditDynamicField').submit();
                }
            });

            $('#frmCreateEditDynamicField').on('')

            $('#cboGlobalCalculatedScripts').select2({ "width": "100%" });
            $('#selVersionCode').select2({ "width": "100%" });
            $('#cboGlobalCalculatedScripts').change(function () {
                var executedScript = $('#cboGlobalCalculatedScripts').val();
                if (executedScript != '') {
                    webaby.ajax({
                        url: '@Url.Action("LoadGlobalCalculatedScriptParams", "DynamicForm")?DynamicFormId=@(Model.DynamicFormId)&DynamicFieldId=@(Model.Id)&ExecutedScript=' + executedScript + '&DynamicFieldOrder=@Model.Order',
                        success: function (htmlData) {
                            $('#divGlobalCalculatedScriptParameters').html(htmlData);
                        }
                    });
                }
                else {
                    $('#divGlobalCalculatedScriptParameters').html('');
                }
            });
            $('#cboGlobalCalculatedScripts').trigger('change');

            webaby.bindAjax($('#divCreateGuiKPI'));
            CKEDITOR.replace('UIFriendlyFormula', {
                plugins: 'autocomplete,textmatch,wysiwygarea,resize,onchange',
                toolbar: [],
                enterMode: CKEDITOR.ENTER_BR,
                on: {
                    instanceReady: function (evt) {
                        var itemTemplate = '<li data-id="{id}">' +
                            '<div class="item-title" style="color:{color};{customstyle}"><i class="{icon}"></i> {text}</div>' +
                            '<div>{description}</div>' +
                            '</li>',
                            outputTemplate = '{value}';

                        var autocomplete = new CKEDITOR.plugins.autocomplete(evt.editor, {
                            textTestCallback: textTestCallback,
                            dataCallback: dataCallback,
                            itemTemplate: itemTemplate,
                            outputTemplate: outputTemplate
                        });

                        // Override default getHtmlToInsert to enable rich content output.
                        autocomplete.getHtmlToInsert = function (item) {
                            insertElementField = item.value;
                            if (item.value.indexOf("\n") != -1) {
                                insertElementField = item.value.replace('\n', '<br>');
                            }
                            return this.outputTemplate.output(item).replace('\n', '<br>');
                        }

                        if (`@Html.Raw(Model.UIFriendlyFormula)`.indexOf('headerGrid') == -1) {
                            CKEDITOR.instances['UIFriendlyFormula'].setData(convertToPlain(`@Html.Raw(Model.UIFriendlyFormula)`).trim(), evv => {
                                var range = CKEDITOR.instances['UIFriendlyFormula'].createRange();
                                range.moveToElementEditEnd(range.root);
                                CKEDITOR.instances['UIFriendlyFormula'].getSelection().selectRanges([range]);
                            });
                        }
                    }
                },
                height: '170px',
            });

            editor = CKEDITOR.instances['UIFriendlyFormula'];

            $("#createGuiKPIModal").on('shown.bs.modal', function () {
                webaby.lastFocusSelector($('#frmCreateEditDynamicField :input:enabled:visible:not([readonly]):first'));
                editor.focus();
                setTimeout(ev => {
                    var range = editor.createRange();
                    range.moveToElementEditEnd(range.root);
                    editor.getSelection().selectRanges([range]);
                }, 200);
            });

            CKEDITOR.addCss('.function { color: #069; } .function_start,.function_stop { color: #ff0000; }  .quote_mark, .text { color: #0000ff; }');

            editor.on('contentDom', function () {
                $(editor.document.$).off('keyup');
                $(editor.document.$).on('keyup', function (e) {
                    if (e.which == 9 || e.which == 13) {
                        var sel = editor.getSelection();
                        var element = sel.getStartElement();
                        var ckdata = element.getHtml().replace(/&nbsp;/g, ' ');
                        var findString = insertElementField;
                        var ranges = editor.getSelection().getRanges();
                        var startIndex = ckdata.lastIndexOf(findString);
                        if (startIndex != -1 && isSupportAuto) {
                            if (findString.indexOf('(') >= 0 && findString.indexOf(')') >= 0) {
                                var setIndexcursor = startIndex + findString.lastIndexOf('(') + 1;
                                ranges[0].setEnd(element.getFirst(), setIndexcursor);
                                sel.selectRanges([ranges[0]]);
                            }
                        }
                    }
                });
            });

            $('select#ExCelformula, select#KPIfield, select#TableFormular, select#cboSystemDynamicFields').on('select2:select', function (e) {
                var selector = $(this).attr('id');
                if (selector != 'ExCelformula') {
                    var text = $('#' + selector).val().substr(0, $('#' + selector).val().indexOf(','));
                    //var color = $('#' + selector).val().substr($('#' + selector).val().indexOf(',') + 1, $('#' + selector).val().length - $('#' + selector).val().indexOf(','));
                    //var newElement = CKEDITOR.dom.element.createFromHtml('<span style="color:' + color + '; font-weight: 550">' + text + '</span>', editor.document);
                    editor.insertText(text);
                } else {
                    var text = $('#' + selector).val();
                    editor.insertText(text);
                    var sel = editor.getSelection();
                    var element = sel.getStartElement();
                    var ranges = editor.getSelection().getRanges();
                    var startIndex = element.getHtml().lastIndexOf(text);
                    if (startIndex != -1 && isSupportAuto) {
                        if (text.indexOf('(') >= 0 && text.indexOf(')') >= 0) {
                            var setIndexcursor = startIndex + text.lastIndexOf('(') + 1;
                            ranges[0].setEnd(element.getFirst(), setIndexcursor);
                            sel.selectRanges([ranges[0]]);
                        }
                    }
                }
            });

            $('#cboCustomerVersions').selectpicker();

            $('#ExCelformula').select2({
                width: '100%',
                placeholder: "Chọn giá trị",
                closeOnSelect: false
            }).on('select2:select select2:closing', function (evt) { $(this).val(null).trigger("change") });

            $('#KPIfield').select2({
                width: '100%',
                placeholder: "Chọn giá trị",
                closeOnSelect: false
            }).on('select2:select select2:closing', function (evt) { $(this).val(null).trigger("change") });

            $('#TableFormular').select2({
                width: '100%',
                placeholder: "Chọn giá trị",
                closeOnSelect: false
            }).on('select2:select select2:closing', function (evt) { $(this).val(null).trigger("change") });



            $('.change_color').click(function () {
                $('.select2-results__options').find('li').each(function () {
                    var color = $(this).attr('id').split(',')[1];
                    if (color != 'transparent') {
                        $(this).attr('style', 'color:' + color);
                    }
                });
            })
        });

        function beautiFormulaField(data) {
            isSupportAuto = false;
            var rootData;
            if (data != '') {
                rootData = convertToPlain(data).trim();
            } else {
                rootData = convertToPlain(editor.getData()).trim();
            }
            var beautiData = excelFormulaUtilities.formatFormulaHTML(rootData).substring(1);
            var div = editor.document.createElement('div');
            div.setHtml(beautiData);
            editor.setData("", function () {
                editor.insertElement(div);
            });
        }

        function ministringFormulaField() {
            isSupportAuto = true;
            var rootData = convertToPlain(editor.getData());
            editor.setData('', function () {
                editor.setData(rootData);
            });
        }

        function textTestCallback(range) {
            if (!range.collapsed) {
                return null;
            }
            return CKEDITOR.plugins.textMatch.match(range, matchCallback);
        }

        function matchCallback(text, offset) {
            var startindex = 0;
            var pattern = /(\s|[^0-9a-zA-Z]){0,1}([a-záàảãạăắằẳẵặâấầẩẫậéèẻẽẹêếềểễệóòỏõọôốồổỗộơớờởỡợíìỉĩịúùủũụưứừửữựýỳỷỹỵđ0-9]{1,}([\.|\_][a-záàảãạăắằẳẵặâấầẩẫậéèẻẽẹêếềểễệóòỏõọôốồổỗộơớờởỡợíìỉĩịúùủũụưứừửữựýỳỷỹỵđ0-9]{0,}){0,1})$/;
            var match = text.toLowerCase().slice(0, offset).match(pattern);
            if (!match) {
                return null;
            }
            if (text.slice(match.index) == '') {
                return null;
            } else {
                var inputstring = text.slice(match.index, offset).toLowerCase();
                if (/^[^0-9a-zA-Z]/.test(inputstring)) {
                    inputstring = text.slice(match.index, offset).toLowerCase().substring(1);
                    startindex = 1;
                }
                if (!filterByValue(configAutoComplete, inputstring)) {
                    return null;
                }
            }

            return {
                start: match.index + startindex,
                end: offset
            };
        }

        function filterByValue(array, string) {
            return array.some(arr => arr.text.toLowerCase().includes(string) || arr.value.toLowerCase().includes(string));
        }

        function dataCallback(matchInfo, callback) {
            var query = matchInfo.query.toLowerCase();
            var data = filterByAllProp(configAutoComplete, query);
            callback(data);
        }

        function filterByAllProp(array, string) {
            return array.filter(function (el) {
                return el.text.toLowerCase().includes(string) ||
                    el.description.toLowerCase().includes(string)
            });
        }

    </script>
}
@using (Foot())
{
    <script type="text/javascript">

        webaby.off('FieldType.Changed');
        webaby.off('DataType.Changed');
        webaby.off('ViewHint.Changed');
        webaby.off('SelectedConfigLoadUrl.Changed');
        webaby.off('cboMapping360ViewDetail.Changed');
        var staticFieldGroups = @Html.Raw(staticFieldGroups);
        var template = '@Html.Raw(Model.TemplateNameLinkField)';
        ////$('#@Html.IdFor(x => x.SelectedConfigLoadUrl)').select2({ width: '100%' });
        $('#@Html.IdFor(x => x.ViewHint)').select2({ width: '100%' });
        var loadViewHint = function () {
            $('#@Html.IdFor(x => x.ViewHint)').empty();
            var dataType = $('#@Html.IdFor(x => x.DataType)').val();
            var fieldType = $('#@Html.IdFor(x => x.FieldType)').val();
            if (fieldType == '' || fieldType == null) {
                return;
            }
            webaby.ajax({
                url: '@Url.Action("GetViewHintList")?type=' + dataType + '&fieldType=' + fieldType,
                success: function (data) {
                    $('#@Html.IdFor(x => x.ViewHint)').append('<option selected value="">@T["Chọn kiểu hiển thị"]</option>');
                    $(data).each(function () {
                        $('#@Html.IdFor(x => x.ViewHint)').append('<option value="' + this.Value + '">' + this.Text + '</option>');
                    });
                    $('#@Html.IdFor(x => x.ViewHint)').select2('destroy');
                    if ($('#@Html.IdFor(x => x.ViewHint) option[value="@Model.ViewHint"]').length > 0) {
                        if ('@Model.ViewHint.IsNullOrEmpty()' == 'True') {
                            $('#@Html.IdFor(x => x.ViewHint)').val(data[0].Value);
                        } else {
                            $('#@Html.IdFor(x => x.ViewHint)').val('@Model.ViewHint');
                        }
                    } else {
                        $('#@Html.IdFor(x => x.ViewHint)').val(data[0].Value);
                    }
                    $('#@Html.IdFor(x => x.ViewHint)').select2({ width: '100%' });
                    $('#@Html.IdFor(x => x.ViewHint)').change();
                }
            });
        }

        webaby.on('FieldType.Changed', function (selectedVal) {
            loadViewHint();
        });
        webaby.on('DataType.Changed', function (selectedVal) {
            loadViewHint();
        });
        $('#ViewHint').change(function () {
            if ($(this).val() == "Select_v5") {
                $('#loadUrlView').show();
            }
            else {
                $('#loadUrlView').hide();
            }

        });
        webaby.on('SelectedConfigLoadUrl.Changed', function (selectedVal) {
            const searchParams = new URLSearchParams(new URL(selectedVal, '@appUrl').search);
            $('#loadUrlParams').html("");
            let params = {};
            let count = 0;
            for (let [key, value] of searchParams) { 
                var realVal = value.includes('{') ? "" : value;
                var row = `<div data-repeater-item class="row m--margin-bottom-10">
                    <div class="col-6">
                        <div class="input-group">
                            <input type="text" class="form-control" readonly name="LoadURLParameters[${count}].Key" value="${key}"/>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="input-group">
                            <input type="text" class="form-control" name="LoadURLParameters[${count}].Value" value="${realVal}"/> 
                        </div>
                    </div>
                </div>`;
                $('#loadUrlParams').append(row); 
                count++;
            }
        })

        webaby.ajax({
            url: '@Url.Action("GetSystemDynamicFieldList", "DynamicForm")',
            success: function (data) {
                if ($('#cboSystemDynamicFields').data('select2')) {
                    $('#cboSystemDynamicFields').select2('destroy');
                }
                $(data).each(function () {
                    $('#cboSystemDynamicFields').append('<option value="__SysDF__' + this.Value + ',">' + this.Text + '</option>');
                });
                $('#cboSystemDynamicFields').select2({
                    width: '100%',
                    placeholder: "Chọn giá trị",
                    closeOnSelect: false
                }).on('select2:select select2:closing', function (evt) { $(this).val(null).trigger("change") });
            }
        });

        $(function () {
            @if(Model.FieldType == FieldType.Mapping
                && Model.Mapping360FieldType == Mapping360Type.Table
                && !string.IsNullOrEmpty(Model.Mapping360RowOptions))
            {
            <text>
            var tagValue = [{id: '@Model.Mapping360RowOptions', text: '@Model.Mapping360RowOptions'}];
            $('#cboMapping360RowOptions').select2({
                width: "100%",
                tags: true,
                data: tagValue,
            })
            $('#cboMapping360RowOptions').val('@Model.Mapping360RowOptions').trigger('change');
            </text>
            }

            $('#m_repeater').repeater({
                initEmpty: false,
                show: function () {
                    $(this).slideDown();
                },
                hide: function (deleteElement) {
                    $(this).slideUp(deleteElement);
                }
            });

            $('#v_repeater').repeater({
                initEmpty: false,
                show: function () {
                    $(this).slideDown();
                },
                hide: function (deleteElement) {
                    $(this).slideUp(deleteElement);
                },
            });

            $('#b_repeater').repeater({
                initEmpty: false,
                show: function () {
                    $(this).slideDown();
                },
                hide: function (deleteElement) {
                    $(this).slideUp(deleteElement);
                },
            });

            $('.spectrum_color').spectrum({
                type: "color",
                showPalette: false,
                showPaletteOnly: true,
                togglePaletteOnly: true,
                showInput: true,
                showAlpha: false,
                showButtons: false,
                allowEmpty: true
            });

            $('.sp-replacer.sp-light').click(function (event) {
                var selector = $(this).closest('.choosecolor').find('.spectrum_color').attr('id');
                if ($(this).hasClass('sp-active')) {
                    $(this).toggleClass('sp-active');
                    $('#' + selector).spectrum("show");
                }
                else {
                    $('#' + selector).spectrum("hide");
                }
            });

            $('#addmt_repeater').repeater({
                initEmpty: false,
                show: function () {
                    $(this).slideDown();
                },
                hide: function (deleteElement) {
                    $(this).slideUp(deleteElement);
                },
            });

            @if(Model.FieldType == FieldType.Mapping)
            {
            <text>
            showHideMapping360('@Model.FieldType');
            </text>
            }

            showHideLinkedSource('@Model.FieldType');
            webaby.on('FieldType.Changed', function (selectedVal) {
                showHideLinkedSource(selectedVal);
                showHideMapping360(selectedVal);
            });

            webaby.on('cboMapping360ViewDetail.Changed', function(selectType){
                if(selectType == '@((int)Mapping360Type.Table)'){
                    $('#div360MappingRowOptions').show();
                    if(!$('#cboMapping360RowOptions').hasClass('select2-hidden-accessible')){
                        $('#cboMapping360RowOptions').select2({
                            width: "100%",
                            tags: true
                        })
                    }
                } else {
                    $('#div360MappingRowOptions').hide();
                    $('#cboMapping360RowOptions').val("");
                }
            });

            $('#DataType').change(function(){
                var dataType = $(this).val();
                if (dataType === 'Webaby.Core.PredefinedList.Queries.PredefinedListItemData') {
                    if ($('#cboAdditionalFilter').length > 0) {
                        var addSelect = $('#cboAdditionalFilter');
                        var addSelectRow = addSelect.closest('div.row');
                        if (!addSelectRow.is(':visible')) {
                            addSelectRow.show();
                        }
                    }
                    else {
                        var currentRow = $(this).closest('div.row').next();
                        var col = $('<div class="col-lg-6"></div>');
                        col.append('<label>@T["Giới hạn dữ liệu"]</label>');
                        var addSelect = $('<select id="cboAdditionalFilter" class="form-control" name="AdditionalFilter"><option value="">@T["Chọn vật phẩm"]</option></select>');
                        col.append(addSelect);
                        currentRow.append(col);
                        webaby.ajax({
                            url: '@Url.Action("GetPredefinedListCategories", "PredefinedList").JsRaw()',
                            success: function (data) {
                                if (data) {
                                    $.each(data, function () {
                                        addSelect.append($('<option></option>').attr('value', 'categoryid=' + this.Value.toLowerCase()).text(this.Text));
                                    });
                                    addSelect.select2();
                                }
                            }
                        });
                    }
                }
                else if (dataType === 'TinyCRM.AppServices.DynamicDefinedTable.DynamicDefinedTableGridData') {
                    $('#divDynamicDefinedTableSchemaId').show();
                }
                else {
                    if ($('#cboAdditionalFilter')) {
                        $('#cboAdditionalFilter').closest('div').remove();
                    }
                    $('#divDynamicDefinedTableSchemaId').hide();
                }
            });

            $('#cboStaticFieldGroups').select2({ 'width': '100%' });

            $('#cboStaticFieldGroups').change(function () {
                $('#cboSourceNames').find('option').not(':first').remove();
                for(var i = 0; i < staticFieldGroups.length; i++) {
                    if($(this).val() == staticFieldGroups[i].Name) {
                        for(var j = 0; j < staticFieldGroups[i].StaticFieldList.length; j++) {
                            var newStaticField = $('<option>', {
                                value: staticFieldGroups[i].StaticFieldList[j].SourceTableName + '.' + staticFieldGroups[i].StaticFieldList[j].SourceFieldName,
                                text: staticFieldGroups[i].StaticFieldList[j].DisplayLabel
                            });
                            newStaticField.attr('sourcetablename', staticFieldGroups[i].StaticFieldList[j].SourceTableName);
                            newStaticField.attr('SourceFieldName', staticFieldGroups[i].StaticFieldList[j].SourceFieldName);
                            newStaticField.attr('DataType', staticFieldGroups[i].StaticFieldList[j].DataType);
                            newStaticField.attr('ViewHint', staticFieldGroups[i].StaticFieldList[j].ViewHint);
                            newStaticField.attr('DefaultValue', staticFieldGroups[i].StaticFieldList[j].DefaultValue);
                            newStaticField.attr('DisplayLabel', staticFieldGroups[i].StaticFieldList[j].DisplayLabel);
                            $('#cboSourceNames').append(newStaticField);
                        }
                    }
                }
                $('#cboSourceNames').select2({ 'width': '100%' });
            });

            $('#cboSourceNames').select2({ 'width': '100%' });
            $('#cboSourceNames').change(function () {
                if ($(this).val() != '') {
                    console.log('cboSourceNames ', template)

                    var option = $('option:selected', this);
                    $('#SourceFieldName').val(option.attr('sourcefieldname'));
                    $('#SourceTableName').val(option.attr('sourcetablename'));
                    $('#ViewHint').val(option.attr('ViewHint'));
                    $('#DefaultValue').val(option.attr('DefaultValue'));
                    //$('#txtFieldName').val(template.replace("{0}", option.attr('DisplayLabel')));
                    //$('#txtDisplayName').val(option.attr('DisplayLabel'));
                    //console.log('--- DataType: ' + option.attr('datatype'));
                    $('#DataType').select2({ 'width': '100%' }).enable(true);

                    $('#DataType').val(option.attr('datatype')).trigger('change');
                    $('#hidDataType').val(option.attr('datatype'));
                    $('#DataType').select2({ 'width': '100%' }).enable(false);
                }
                else {
                    $('#SourceFieldName').val('');
                    $('#SourceTableName').val('');
                }
            });

            initLinkedSources('@Html.Raw(Model.SourceTableName)', '@Html.Raw(Model.SourceFieldName)', '@Html.Raw(Model.Name)', '@Html.Raw(Model.DisplayName)');

            $('#frmCreateEditDynamicField').on('success', function () {
                mApp.block('#divBody', {
                    overlayColor: '#000000',
                    type: 'loader',
                    state: 'primary',
                    message: 'Processing...',
                });
                field.reload();
                $('#createGuiKPIModal').modal('hide');
            });

            @if (!string.IsNullOrEmpty(Model.AdditionalFilter))
            {
            <text>
            webaby.ajax({
                url: '@Url.Action("GetPredefinedListCategories", "PredefinedList").JsRaw()',
                success: function (data) {
                    if (data) {
                        $.each(data, function () {
                            $('#cboAdditionalFilter').append($('<option></option>').attr('value', 'categoryid=' + this.Value.toLowerCase()).text(this.Text));
                        });
                        if('@(Model.AdditionalFilter)' != ''){
                            $('#cboAdditionalFilter').val('@(Model.AdditionalFilter.ToLower())');
                        }
                        $('#cboAdditionalFilter').select2({ width : "100%"});
                    }
                }
            });
            </text>
            }

            $('#tblDynamicFieldConditions tr').not('[id="trGlbConditionItemTemplate"]').find('select.glb-condition-dynamicfield').each(function () {
                selectFieldChange($(this).attr('id'));
                $(this).select2({ "width": "100%" });
            });
        });

        function showHideLinkedSource(fieldType) {
            if (fieldType == '@FieldType.Linked') {
                $('.source-field').fadeIn();
                //$('#divIsReadOnly').show();
                $('#m_repeater').hide();

                $('#frmCreateEditDynamicField').append('<input type="hidden" id="hidDataType" name="DataType" />');
            }
            else {
                $('#m_repeater').show();
                $('.source-field').hide();
                //$('#divIsReadOnly').hide();
                $('#SourceFieldName').val('');
                $('#SourceTableName').val('');
                $('#DataType').select2({ 'width': '100%' }).enable(true);

                $('#hidDataType').remove();
            }
        }

        function showHideMapping360(fieldType){
            if(fieldType == '@FieldType.Mapping'){
                $('#div360Mapping').show();
                if(!$('#cboMapping360ViewDetail').hasClass('select2-hidden-accessible')){
                    webaby.ajax({
                        url: '@Url.Action("GetMapping360ViewOptions", "DynamicForm")',
                        type: 'post',
                        success: function(data){
                            if(data){
                                $('#cboMapping360ViewDetail optgroup[data-options="ajax-loading"]').remove();
                                $.each(data, function(){
                                    var optGroup = $('<optgroup data-options="ajax-loading" label="' + this.DisplayName + '"></optgroup>');
                                    var idGroup = this.Name;
                                    $.each(this.DefinitionList, function(){
                                        optGroup.append($('<option value="' + idGroup + '.' + this.Name + '" data-options="ajax-loading" data-type="' + this.Type + '">' + this.DisplayName + '</option>'));
                                    });
                                    $('#cboMapping360ViewDetail').append(optGroup);
                                });

                                @if (!string.IsNullOrEmpty(Model.Mapping360FieldName))
                                {
                                <text>
                                $('#cboMapping360ViewDetail').val('@Model.Mapping360FieldName').trigger('change');
                              Model </text>
                                }

                                $('#cboMapping360ViewDetail').select2({
                                    width : "100%"
                                });

                                $('#cboMapping360ViewDetail').change(function(){
                                    var selectedOption = $('#cboMapping360ViewDetail option:selected');
                                    var selectedValue = selectedOption.val();
                                    var type = selectedOption.attr('data-type');

                                    $('#hidMapping360FieldName').val('' + selectedValue + '');
                                    $('#hidMapping360FieldType').val('' + type + '');
                                    webaby.raise('cboMapping360ViewDetail.Changed', type);
                                });
                            }
                        }
                    })
                }
            } else {
                $('#div360Mapping').hide();
                $('#hidMapping360FieldName').val("");
                $('#hidMapping360FieldType').val("");
                $('#Mapping360RowOptions').val("");
            }
        }

        function initLinkedSources(sourceTable, sourceField, initFieldName, initFieldDisplayName) {
            if (sourceTable != '' && sourceField != '') {
                for(var i = 0; i < staticFieldGroups.length; i++) {
                    for(var j = 0; j < staticFieldGroups[i].StaticFieldList.length; j++) {
                        if (staticFieldGroups[i].StaticFieldList[j].SourceTableName == sourceTable && staticFieldGroups[i].StaticFieldList[j].SourceFieldName == sourceField) {
                            $('#cboStaticFieldGroups').val(staticFieldGroups[i].Name).trigger('change');
                            $('#cboSourceNames').val(sourceTable + '.' +sourceField).trigger('change');

                            $('#txtFieldName').val(initFieldName);
                            $('#txtDisplayName').val(initFieldDisplayName);
                        }
                    }
                }
            }
        }

        function addGlobalDynamicFieldConditionItem() {

            var newRowId = webaby.newGuid();
            var newselectId;
            var trGlbConditionItem = $('#trGlbConditionItemTemplate').clone();
            trGlbConditionItem.addClass('glb-condition-row');
            trGlbConditionItem.css('display', '');
            trGlbConditionItem.attr('id', newRowId);

            trGlbConditionItem.find('.glb-condition-dynamicfield').each(function () {
                $(this).data('row-id', newRowId);
            });
            trGlbConditionItem.find('.glb-condition-datatype').each(function () {
                $(this).data('row-id', newRowId);
            });
            trGlbConditionItem.find('.glb-condition-operator').each(function () {
                $(this).data('row-id', newRowId);
            });
            trGlbConditionItem.find('.glb-condition-comparedvalue').each(function () {
                $(this).data('row-id', newRowId);
            });
            trGlbConditionItem.find('.btn-remove-glb-condition').each(function () {
                $(this).data('row-id', newRowId);
            });

            trGlbConditionItem.find('select.glb-condition-dynamicfield').each(function () {
                newselectId = $(this).attr('id') + '_' + newRowId;
                $(this).attr('id', newselectId);
            });

            trGlbConditionItem.insertBefore('#trGlbConditionItemTemplate');
            reIndexGlobalDynamicFieldConditionItems();
            selectFieldChange(newselectId);
            $('#' + newselectId).select2({ "width": "100%" });
        }

        function removeGlobalDynamicFieldConditionItem(btnRemove) {
            var rowId = $(btnRemove).data('row-id');
            $('#' + rowId).remove();
            reIndexGlobalDynamicFieldConditionItems();
        }

        function reIndexGlobalDynamicFieldConditionItems() {
            var rowIndex = 0;
            $('#tblDynamicFieldConditions').find('.glb-condition-row').each(function () {
                $(this).find('.glb-condition-dynamicfield').each(function () {
                    $(this).attr('name', 'GlobalCalculatedScriptInfo.DynamicFieldConditions[' + rowIndex + '].DynamicFieldName');
                });
                $(this).find('.glb-condition-datatype').each(function () {
                    $(this).attr('name', 'GlobalCalculatedScriptInfo.DynamicFieldConditions[' + rowIndex + '].DataType');
                });
                $(this).find('.glb-condition-operator').each(function () {
                    $(this).attr('name', 'GlobalCalculatedScriptInfo.DynamicFieldConditions[' + rowIndex + '].CompareOperator');
                });
                $(this).find('.glb-condition-comparedvalue').each(function () {
                    $(this).attr('name', 'GlobalCalculatedScriptInfo.DynamicFieldConditions[' + rowIndex + '].ComparedValue');
                });
                $(this).find('.glb-condition-comparedvalue-td').each(function () {
                    $(this).attr('data-name', 'GlobalCalculatedScriptInfo.DynamicFieldConditions[' + rowIndex + '].ComparedValue');
                });
                rowIndex++;
            });
        }

        function selectFieldChange(selector) {
            $('#' + selector).unbind('change');
            $('#' + selector).change(function () {
                var rowId = $('#' + selector).data('row-id');
                var newFieldName = $('#' + rowId).find('.glb-condition-comparedvalue-td').first().data('name');
                console.log('newFieldName: ' + newFieldName)
                var selectedOption = $('#' + selector).find(":selected");
                if (selectedOption.val() != '') {
                    var fieldId = selectedOption.data('id');
                    var sqlDataType = selectedOption.data('sql-data-type');
                    webaby.ajax({
                        url: '@Url.Action("InputEditorForDynamicField", "DynamicForm")?DynamicFieldId=' + fieldId + '&NewFieldName=' + newFieldName,
                        success: function (data) {
                            $('#' + rowId).find('.glb-condition-comparedvalue-td').html(data);
                            $('#' + rowId).find('.glb-condition-datatype').val(sqlDataType);
                        }
                    });
                }
            });
        }



    @if (Model.FieldType == FieldType.GlobalCalculated)
    {
    <text>
        $(function () {
            initSelectGlobalFormulaBasedOnDynamicFields();
        });

        function initSelectGlobalFormulaBasedOnDynamicFields() {
            var selector = $('#cboGlobalFormulaBasedOnDynamicFields');
            selector.select2({ width: '100%' });
            var globalCalcFieldByVersion = $('#GlobalCalcFieldByVersion').val();
            webaby.ajax({
                url: '@Url.Action("LoadSelectGlobalFomular", "DynamicForm")?FormId=@(Model.DynamicFormId)&DynamicFieldOrder=@(Model.Order)&GlobalCalcFieldByVersion=' + globalCalcFieldByVersion + '&UseId=True',
                success: function (data) {
                    selector.select2('destroy');
                    selector.html('');
                    selector.append('<option selected value="">@T["Chọn Trường thông tin"]</option>');
                    $(data).each(function () {
                        selector.append('<option value="' + this.Value + '">' + this.Text + '</option>');
                    });
                    selector.val(selector.data('initval'));
                    selector.change();
                    selector.select2({ width: '100%' });
                }
            });
        }

    </text>
    }

    </script>
}