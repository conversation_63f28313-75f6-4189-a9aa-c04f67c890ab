﻿using Microsoft.Extensions.Logging;
using System.Reflection;
using Webaby.Data.Seeding;
using Webaby.Security;

namespace TinyCRM.DbMigration.SystemData.DataSeeding_AccountAdmin
{
    internal class SeedingData_00__AspNetUserRoles_01(MigrationDbContext dbContext) 
        : SeedingJsonData<ApplicationUserRole>(dbContext)
    {
        protected override Assembly ResourceAssembly => Assembly.GetExecutingAssembly();
        protected override SeedingJsonDataAction SeedingJsonDataAction => SeedingJsonDataAction.AddOrUpdate;
    }
}
