﻿using AutoMapper;
using System;
using System.Data;
using System.Data.Common;
using System.Threading.Tasks;
using Webaby;
using Webaby.Data;

namespace TinyCRM.MonthlyPartFeeBatch.Commands
{
    public class CreateMonthlyPartFeeBatchCommand : CommandBase
    {
        public Guid MonthlyPartFeeBatchId { get; set; }

        public Guid PartCategoryId { get; set; }

        public Guid? BlockId { get; set; }

        public Guid? FloorId { get; set; }

        public string PartCode { get; set; }

        public int Year { get; set; }

        public int Month { get; set; }

        public DebitNoteGeneratedStatus DebitNoteGeneratedStatus { get; set; }

        public MonthlyPartFeeNotifiedStatus MonthlyPartFeeNotifiedStatus { get; set; }

        public Guid CreatedBy { get; set; }
    }

    internal class CreateMonthlyPartFeeBatchCommandHandler : CommandHandlerBase<CreateMonthlyPartFeeBatchCommand>
    {
        public CreateMonthlyPartFeeBatchCommandHandler(IServiceProvider serviceProvider) : base(serviceProvider)
        {
        }

        public override async Task ExecuteAsync(CreateMonthlyPartFeeBatchCommand command)
        {
            var sqlCommand = EntitySet.CreateDbCommand(CommandType.StoredProcedure, "dbo.CreateMonthlyPartFeeBatch");
            sqlCommand.Parameters.Add(DbParameterHelper.AddNullableGuid(sqlCommand, "@MonthlyPartFeeBatchId", command.MonthlyPartFeeBatchId));
            sqlCommand.Parameters.Add(DbParameterHelper.AddNullableGuid(sqlCommand, "@PartCategoryId", command.PartCategoryId));
            sqlCommand.Parameters.Add(DbParameterHelper.AddNullableGuid(sqlCommand, "@BlockId", command.BlockId));
            sqlCommand.Parameters.Add(DbParameterHelper.AddNullableGuid(sqlCommand, "@FloorId", command.FloorId));
            sqlCommand.Parameters.Add(DbParameterHelper.AddNullableString(sqlCommand, "@PartCode", command.PartCode));
            sqlCommand.Parameters.Add(DbParameterHelper.AddNullableInt(sqlCommand, "@Year", command.Year));
            sqlCommand.Parameters.Add(DbParameterHelper.AddNullableInt(sqlCommand, "@Month", command.Month));
            sqlCommand.Parameters.Add(DbParameterHelper.AddNullableEnum(sqlCommand, "@DebitNoteGeneratedStatus", command.DebitNoteGeneratedStatus));
            sqlCommand.Parameters.Add(DbParameterHelper.AddNullableEnum(sqlCommand, "@MonthlyPartFeeNotifiedStatus", command.MonthlyPartFeeNotifiedStatus));
            sqlCommand.Parameters.Add(DbParameterHelper.AddNullableGuid(sqlCommand, "@CreatedBy", command.CreatedBy));

            await EntitySet.ExecuteNonQueryAsync(sqlCommand);
        }
    }
}