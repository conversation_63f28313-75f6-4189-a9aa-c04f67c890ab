﻿using System.Data;
using System.Data.Common;
using TinyCRM.ServiceType;
using Webaby;
using Webaby.Data;

namespace TinyCRM.RequestTicketDynamicModel.Queries
{
    public class GetRequestTicketDynamicModelQuery : QueryBase<RequestTicketDynamicModelData>
    {
        public Guid ServiceTypeId { get; set; }

        public bool CreatedMode { get; set; }

        public bool IsNoneCustomerTicket { get; set; }
    }

    internal class GetRequestTicketDynamicModelQueryHandler : QueryHandlerBase<GetRequestTicketDynamicModelQuery, RequestTicketDynamicModelData>
    {
        public GetRequestTicketDynamicModelQueryHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task<QueryResult<RequestTicketDynamicModelData>> ExecuteAsync(GetRequestTicketDynamicModelQuery query)
        {
            // Store "GetRequestTicketDynamicModels" -> LinQ

            var serviceType = EntitySet.Get<ServiceTypeEntity>()
                                    .Where(st => st.Id == query.ServiceTypeId)
                                    .Select(st => new
                                    {
                                        st.Level1Id,
                                        st.Level2Id,
                                        st.Level3Id,
                                        st.Level4Id
                                    })
                                    .FirstOrDefault();

            if (serviceType == null)
                return null;

            var requestTicketDynamicModelEntity = EntitySet.Get<RequestTicketDynamicModelEntity>()
                                                            .Where(rtdm =>
                                                                rtdm.CreatedMode == query.CreatedMode &&
                                                                rtdm.IsNoneCustomerTicket == query.IsNoneCustomerTicket &&
                                                                rtdm.Level1Id == serviceType.Level1Id
                                                            )
                                                            .Select(rtdm => new
                                                            {
                                                                Model = rtdm,
                                                                PriorityOrder =
                                                                    (rtdm.ServiceTypeId == query.ServiceTypeId ? 10000 : 0) +
                                                                    (rtdm.Level4Id == serviceType.Level4Id ? 1000 : 0) +
                                                                    (rtdm.Level3Id == serviceType.Level3Id ? 100 : 0) +
                                                                    (rtdm.Level2Id == serviceType.Level2Id ? 10 : 0) +
                                                                    (rtdm.Level1Id == serviceType.Level1Id ? 1 : 0)
                                                            })
                                                            .OrderByDescending(x => x.PriorityOrder)
                                                            .Select(x => x.Model)
                                                            .FirstOrDefault();

            var RequestTicketDynamicModelData = Mapper.Map<RequestTicketDynamicModelData>(requestTicketDynamicModelEntity);
            return new QueryResult<RequestTicketDynamicModelData>(RequestTicketDynamicModelData);
        }
    }
}