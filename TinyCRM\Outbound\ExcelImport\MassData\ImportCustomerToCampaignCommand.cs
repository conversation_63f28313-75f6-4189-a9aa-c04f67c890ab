﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Threading.Tasks;
using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Webaby;
using Webaby.Data;
using Webaby.Localization;

namespace TinyCRM.Outbound.ExcelImport.MassData
{
    public class ImportCustomerToCampaignCommand : CommandBase
    {
        public Guid ImportSessionId { get; set; }

        public Guid UserId { get; set; }

        public Guid CampaignId { get; set; }

        public IEnumerable<string> ErrorCode { get; set; }
    }
    internal class ImportCustomerToCampaignCommandHandler : CommandHandlerBase<ImportCustomerToCampaignCommand>
    {
        public ImportCustomerToCampaignCommandHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task ExecuteAsync(ImportCustomerToCampaignCommand command)
        {
            var cmd = EntitySet.CreateDbCommand();
            cmd.CommandText = "dbo.ImportCustomerToCampaign";
            cmd.CommandType = CommandType.StoredProcedure;

            cmd.Parameters.Add(DbParameterHelper.AddNullableGuid(cmd, "@ImportSessionId", command.ImportSessionId));
            cmd.Parameters.Add(DbParameterHelper.AddNullableGuid(cmd, "@UserId", command.UserId));
            cmd.Parameters.Add(DbParameterHelper.AddNullableGuid(cmd, "@CampaignId", command.CampaignId));

            var dt = new DataTable();
            dt.Columns.Add("Value", typeof(string));
            if (command.ErrorCode != null)
            {
                foreach (var option in command.ErrorCode)
                {
                    dt.Rows.Add(option);
                }
            }
            var updateOptions = cmd.CreateParameter();
            updateOptions.ParameterName = "@ErrorCode";
            updateOptions.Value = dt;
            cmd.Parameters.Add(updateOptions);

            await EntitySet.ExecuteNonQueryAsync(cmd);
        }
    }
}
