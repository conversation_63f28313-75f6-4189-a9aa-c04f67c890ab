﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Webaby.Data;

namespace TinyCRM.DynamicDefinedTable
{
    [Table("DynamicDefinedTableRowJsonValue", Schema = "dbo")]
    public class DynamicDefinedTableRowJsonValueEntity : IEntity, ICreatedDateEnabledEntity, ICreatedByEnabledEntity, IModifiedDateEnabledEntity, IModifiedByEnabledEntity
    {
        [Key]
        public Guid Id { get; set; }

        [Column]
        public Guid DynamicFieldValueId { get; set; }

        [Column]
        public Guid DynamicDefinedTableSchemaId { get; set; }

        [Column]
        public string RowJsonValue { get; set; }

        [Column]
        public int RowNumber { get; set; }

        [Column]
        public Guid CreatedBy { get; set; }

        [Column]
        public DateTime CreatedDate { get; set; }

        [Column]
        public DateTime? ModifiedDate { get; set; }

        [Column]
        public Guid? ModifiedBy { get; set; }
    }
}