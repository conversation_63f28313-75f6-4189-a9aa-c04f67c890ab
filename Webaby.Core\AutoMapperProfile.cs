﻿using AutoMapper;
using Webaby.Core.Access.Queries;
using Webaby.Core.DueTime;
using Webaby.Core.DueTime.Commands;
using Webaby.Core.DueTime.Queries;
using Webaby.Core.DynamicForm;
using Webaby.Core.DynamicForm.Commands;
using Webaby.Core.DynamicForm.Queries;
using Webaby.Core.File;
using Webaby.Core.File.Commands;
using Webaby.Core.File.Queries;
using Webaby.Core.Organization;
using Webaby.Core.Organization.Commands;
using Webaby.Core.Organization.Queries;
using Webaby.Core.PredefinedList;
using Webaby.Core.PredefinedList.Queries;
using Webaby.Language;
using Webaby.Language.Queries;
using Webaby.Security;

namespace Webaby.Core
{
    public class AutoMapperProfile : Profile
    {
        public AutoMapperProfile()
        {
            // File
            CreateMap<CreateEditFileCommand, FileEntity>().ForMember(x => x.Data, o => o.Ignore());
            CreateMap<FileEntity, FileData>().ForMember(x => x.Data, o => o.Ignore());
            CreateMap<FileData, FileEntity>().ForMember(x => x.Data, o => o.Ignore());

            // Organization
            CreateMap<OrganizationEntity, OrganizationData>();            

            // DynamicForm
            CreateMap<DynamicFormEntity, DynamicFormData>();
            CreateMap<DynamicFieldSectionEntity, DynamicFieldSectionData>();

            
            CreateMap<CreateEditOrganizationCommand, OrganizationEntity>();

            //CreateMap<UserProfileEntity, UserProfileData>();

            //CreateMap<UserProfileData, UserProfileEntity>();

            //CreateMap<UpdateUserProfileCommand, UserProfileEntity>();

            //CreateMap<UpdateUserProfileCommand, UserProfileData>();
            //CreateMap<UserProfileData, UpdateUserProfileCommand>();

            // Access
            CreateMap<AccessEntity, AccessData>();
            CreateMap<BusinessPermissionEntity, BusinessPermissionData>();
            CreateMap<RoleBusinessPermissionEntity, RoleBusinessPermissionData>();

            // DueTime
            CreateMap<DueTimeEntity, DueTimeData>();
            CreateMap<DueTimeData, DueTimeEntity>();
            CreateMap<DueTimeInfo, DueTimeData>();
            CreateMap<DueTimeData, DueTimeInfo>();
            CreateMap<DueTimeInfo, DueTimeEntity>();
            CreateMap<DueTimeEntity, DueTimeInfo>();
            CreateMap<DueTimeReferenceEntity, DueTimeReferenceData>();
            CreateMap<CreateEditDueTimeCommand, DueTimeEntity>();
            CreateMap<DueTimeData, CreateEditDueTimeCommand>();
            
            CreateMap<DynamicFieldDefinitionEntity, DynamicFieldDefinitionData>();


            // DynamicForm            
            CreateMap<DynamicFormValueEntity, DynamicFormValueData>();
            CreateMap<DynamicFieldDefinitionEntity, DynamicFieldDefinitionData>();
            CreateMap<DynamicFieldValueEntity, DynamicFieldValueData>();
            CreateMap<CreateEditDynamicFieldDefinitionCommand, DynamicFieldDefinitionEntity>();
            CreateMap<CreateEditDynamicFormCommand, DynamicFormEntity>();            
            CreateMap<CreateEditDynamicFieldSectionCommand, DynamicFieldSectionEntity>();
            CreateMap<DynamicFieldDefinitionEntity, DynamicFieldValueInfo>();
            CreateMap<DynamicFieldDefinitionData, DynamicFieldValueInfo>();
            CreateMap<DynamicFieldDefinitionData, DynamicFieldDefinitionEntity>();

            //PredefinedList
            CreateMap<PredefinedListValuesEntity, PredefinedListValuesData>();
            CreateMap<PredefinedListConcreteRecordEntity, PredefinedListConcreteRecordData>();
            //CreateMap<CreateEditPredefinedListValuesCommand, PredefinedListValuesEntity>();
            CreateMap<PredefinedListCategoryEntity, PredefinedListCategoryData>();

            // Language
            CreateMap<LanguageEntity, LanguageData>();
        }
    }
}