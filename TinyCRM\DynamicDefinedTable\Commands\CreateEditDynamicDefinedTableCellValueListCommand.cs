﻿using AutoMapper;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Newtonsoft.Json;
using System.Data;
using System.Data.Common;
using System.Globalization;
using System.Text;
using TinyCRM.DynamicDefinedTable.Queries;
using Webaby;
using Webaby.Core.DynamicForm;
using Webaby.Core.File;
using Webaby.Data;
using Webaby.Localization;
using Webaby.Security;

namespace TinyCRM.DynamicDefinedTable.Commands
{
    public class CreateEditDynamicDefinedTableCellValueListCommand : CommandBase
    {
        public Guid Id { get; set; }
        public int PageIndex { get; set; }
        public int PageSize { get; set; }
        public List<DynamicDefinedTableCellValueData> ListItems { get; set; }
        public Dictionary<int, Guid> TableRowIds { get; set; }
        public string SystemTableStatus { get; set; }
    }

    internal class CreateEditDynamicDefinedTableCellValueListCommandHandler : CommandHandlerBase<CreateEditDynamicDefinedTableCellValueListCommand>
    {
        private readonly IUserService _userService;

        public CreateEditDynamicDefinedTableCellValueListCommandHandler(IServiceProvider serviceProvider) : base(serviceProvider)
        {
            _userService = ServiceProvider.GetRequiredService<IUserService>();
        }

        public override async Task ExecuteAsync(CreateEditDynamicDefinedTableCellValueListCommand command)
        {
            if(command.PageIndex == 0 && command.PageSize == 0)
            {
                command.PageIndex = 0;
                command.PageSize = int.MaxValue;
            }
            //Because sort on dynamicTable on database start 0;
            int startRow = command.PageIndex * command.PageSize;
            int endRow = command.PageIndex * command.PageSize + command.PageSize;

            #region Save File Entities first

            var newFiles = new List<FileEntity>();
            foreach (var update in command.ListItems)
            {
                var commandItem = command.ListItems.First(x => x.Id == update.Id);
                update.DynamicDefinedTableColumnId = commandItem.DynamicDefinedTableColumnId;
                update.DynamicFieldValueId = commandItem.DynamicFieldValueId;
                update.RowNumber = commandItem.RowNumber;
                update.Value = commandItem.Value;

                if (commandItem.SingleFileData != null && commandItem.SingleFileData.FileData.Length > 0)
                {
                    Guid fileId = Guid.NewGuid();
                    update.Value = fileId.ToString();

                    FileEntity fileEntity = new FileEntity
                    {
                        Id = fileId,
                        FileName = commandItem.SingleFileData.FileData.FileName,
                        Extension = string.IsNullOrEmpty(Path.GetExtension(commandItem.SingleFileData.FileData.FileName)) ? string.Empty : Path.GetExtension(commandItem.SingleFileData.FileData.FileName)
                    };

                    var mem = new MemoryStream();
                    await commandItem.SingleFileData.FileData.CopyToAsync(mem);
                    fileEntity.Data = mem.ToArray();

                    newFiles.Add(fileEntity);
                }
                else if (commandItem.MultiFileDataList != null && commandItem.MultiFileDataList.Count > 0)
                {
                    foreach (var fileItem in commandItem.MultiFileDataList)
                    {
                        FileEntity fileEntity = new FileEntity
                        {
                            Id = fileItem.Id,
                            FileName = fileItem.FileData.FileName,
                            Extension = string.IsNullOrEmpty(Path.GetExtension(fileItem.FileData.FileName)) ? string.Empty : Path.GetExtension(fileItem.FileData.FileName)
                        };

                        var mem = new MemoryStream();
                        await  commandItem.SingleFileData.FileData.CopyToAsync(mem);
                        fileEntity.Data = mem.ToArray();

                        newFiles.Add(fileEntity);
                    }
                }
            }

            if (newFiles.Count > 0)
            {
                await Repository.SaveAsync(newFiles);
            }

            #endregion

            DynamicDefinedTableColumnData dynamicDefinedTableColumnData = null;
            DynamicDefinedTableSchemaData dynamicDefinedTableSchemaData = null;

            DynamicDefinedTableCellValueData dynamicDefinedTableCellValueData = command.ListItems.FirstOrDefault();
            if (dynamicDefinedTableCellValueData != null)
            {
                dynamicDefinedTableColumnData = await QueryExecutor.ExecuteOneAsync(new GetDynamicDefinedTableColumnByIdQuery { DynamicDefinedTableColumnId = dynamicDefinedTableCellValueData.DynamicDefinedTableColumnId });
                dynamicDefinedTableSchemaData = await QueryExecutor.ExecuteOneAsync(new GetDynamicDefinedTableSchemaQuery { DynamicDefinedTableSchemaId = dynamicDefinedTableColumnData.DynamicDefinedTableSchemaId });
            }

            if(command.SystemTableStatus == "filtering")
            {
                //Tức là đang ở System DynamicTable, và đang filter trên UI và muốn lưu các row trên table
                if (dynamicDefinedTableSchemaData != null && dynamicDefinedTableSchemaData.RowStoredMethod != DynamicDefinedTableStoreMethod.OwnDbTable)
                {
                    throw new Exception(string.Format(T["Cập nhật giá trị cho System Table khi đang filter chỉ dùng được khi cấu hình là bảng riêng"]));
                }
            }

            if (dynamicDefinedTableSchemaData != null && dynamicDefinedTableSchemaData.RowStoredMethod == DynamicDefinedTableStoreMethod.OwnDbTable)
            {
                var columnList = await QueryExecutor.ExecuteManyAsync(new GetDynamicDefinedTableColumnListByTableSchemaQuery { DynamicDefinedTableSchemaId = dynamicDefinedTableColumnData.DynamicDefinedTableSchemaId });
                var colCheckDuplicate = new Dictionary<string, string>();
                foreach (var item in columnList.Where(x => x.IsCheckDuplicateData == true))
                {
                    colCheckDuplicate.Add(item.Name, item.DisplayName);
                }

                #region Own DataTable

                var mappingLists = new List<string[]>
                {
                    new[] {"Id", "Id"},
                    new[] { "DynamicDefinedTableSchemaId", "DynamicDefinedTableSchemaId"},
                    new[] { "DynamicFieldValueId", "DynamicFieldValueId"},
                    new[] { "RowNumber", "RowNumber"},
                };

                DataTable tbOwnTable = new DataTable();
                tbOwnTable.Columns.Add("Id", typeof(Guid));
                tbOwnTable.Columns.Add("DynamicDefinedTableSchemaId", typeof(Guid));
                tbOwnTable.Columns.Add("DynamicFieldValueId", typeof(Guid));
                tbOwnTable.Columns.Add("RowNumber", typeof(int));
                foreach (var column in columnList)
                {
                    mappingLists.Add(new[] { column.Name, column.Name.ToUpper() });

                    var columnDataType = typeof(string);
                    if (column.DataType.Contains("Int32", StringComparison.OrdinalIgnoreCase))
                    {
                        columnDataType = typeof(int);
                    }
                    else if (column.DataType.Contains("Int64", StringComparison.OrdinalIgnoreCase))
                    {
                        columnDataType = typeof(long);
                    }
                    else if (column.DataType.Contains("Double", StringComparison.OrdinalIgnoreCase))
                    {
                        columnDataType = typeof(double);
                    }
                    else if (column.DataType.Contains("Boolean", StringComparison.OrdinalIgnoreCase))
                    {
                        columnDataType = typeof(bool);
                    }
                    else if (column.DataType.Contains("DateTime", StringComparison.OrdinalIgnoreCase))
                    {
                        columnDataType = typeof(DateTime);
                    }

                    tbOwnTable.Columns.Add(column.Name, columnDataType);
                }

                var rowList = command.ListItems.GroupBy(x => x.RowNumber).Select(x => new { RowNumber = x.Key, CellValues = x.ToList() });

                StringBuilder sqlCreateEditRow = new StringBuilder();
                string columnNames = string.Join(", ", tbOwnTable.Columns.Cast<DataColumn>().Select(column => $"[{column.ColumnName}]"));

                foreach (var rowValues in rowList)
                {
                    var sqlValues = new List<string>();

                    DataRow newRow = tbOwnTable.NewRow();
                    try
                    {
                        newRow["Id"] = command.TableRowIds[rowValues.RowNumber];
                    } catch
                    {
                        newRow["Id"] = Guid.NewGuid();
                    }
                    newRow["DynamicDefinedTableSchemaId"] = dynamicDefinedTableColumnData.DynamicDefinedTableSchemaId;
                    newRow["DynamicFieldValueId"] = command.Id;
                    newRow["RowNumber"] = startRow + rowValues.RowNumber;

                    //                    
                    sqlValues.Add($"'{newRow["Id"].ToString().Replace("'", "''")}'");
                    sqlValues.Add($"'{newRow["DynamicDefinedTableSchemaId"].ToString().Replace("'", "''")}'");
                    sqlValues.Add($"'{newRow["DynamicFieldValueId"].ToString().Replace("'", "''")}'");
                    sqlValues.Add(newRow["RowNumber"].ToString());

                    foreach (var column in columnList)
                    {
                        string cellValue = null;
                        var cellItem = rowValues.CellValues.Where(cell => cell.DynamicDefinedTableColumnId == column.Id).FirstOrDefault();
                        if (cellItem != null)
                        {
                            cellValue = cellItem.Value;
                        }

                        var columnDataType = typeof(string);
                        if (column.DataType.Contains("Int32", StringComparison.OrdinalIgnoreCase))
                        {
                            columnDataType = typeof(int);
                        }
                        else if (column.DataType.Contains("Int64", StringComparison.OrdinalIgnoreCase))
                        {
                            columnDataType = typeof(long);
                        }
                        else if (column.DataType.Contains("Double", StringComparison.OrdinalIgnoreCase))
                        {
                            columnDataType = typeof(double);
                        }
                        else if (column.DataType.Contains("Boolean", StringComparison.OrdinalIgnoreCase))
                        {
                            columnDataType = typeof(bool);
                        }
                        else if (column.DataType.Contains("DateTime", StringComparison.OrdinalIgnoreCase))
                        {
                            columnDataType = typeof(DateTime);
                        }

                        var typeConverter = columnDataType.GetTypeConverter(string.IsNullOrEmpty(cellValue));
                        if (cellValue.IsNullOrEmpty())
                        {
                            sqlValues.Add("NULL");
                            newRow[column.Name] = DBNull.Value;
                        }
                        else
                        {
                            newRow[column.Name] = typeConverter.ConvertFromString(null, CultureInfo.CurrentCulture, cellValue);
                            if(columnDataType == typeof(string) || columnDataType == typeof(Guid)) sqlValues.Add($"N'{newRow[column.Name].ToString().Replace("'", "''")}'");
                            else if(columnDataType == typeof(DateTime)) sqlValues.Add($"'{((DateTime)newRow[column.Name]).ToString("yyyy-MM-dd HH:mm:ss")}'");
                            else if (columnDataType == typeof(bool)) sqlValues.Add(((bool)newRow[column.Name]) ? "1" : "0");
                            else sqlValues.Add(newRow[column.Name].ToString());
                        }
                    }                    
                    tbOwnTable.Rows.Add(newRow);
                    sqlCreateEditRow.AppendLine($" DELETE dbo.DynamicDefinedTable_{dynamicDefinedTableSchemaData.Name} where Id = '{newRow["Id"].ToString()}'" +
                        $" INSERT INTO dbo.DynamicDefinedTable_{dynamicDefinedTableSchemaData.Name} ({columnNames}) VALUES ({string.Join(", ", sqlValues)});");
                }

                //Check Duplicate In 1 page
                foreach (KeyValuePair<string, string> item in colCheckDuplicate)
                {
                    var listDuplicateDataInPage = tbOwnTable.AsEnumerable()
                       .GroupBy(row => row[item.Key])
                       .Where(group => group.Count() > 1)
                       .SelectMany(group => group)
                       .Select(row => row[item.Key])
                       .Distinct().ToList();
                    if (listDuplicateDataInPage != null && listDuplicateDataInPage.Count > 0)
                    {
                        var dupData = string.Join("; ", listDuplicateDataInPage);
                        throw new Exception(string.Format("Bị trùng dữ liệu ở cột {0}, Các dữ liệu trùng là: {1}", item.Value ,dupData));
                    }
                }

                // Get temp Data được delete
                DbCommand dbGetDeleteOwnCommand = EntitySet.CreateDbCommand();
                dbGetDeleteOwnCommand.CommandType = CommandType.Text;
                dbGetDeleteOwnCommand.CommandText = string.Format("SELECT * FROM dbo.DynamicDefinedTable_{0} WHERE DynamicFieldValueId = @DynamicFieldValueId AND (RowNumber BETWEEN @StartRow AND @EndRow ) ORDER BY RowNumber ", dynamicDefinedTableSchemaData.Name);

                dbGetDeleteOwnCommand.Parameters.Add(DbParameterHelper.AddNullableGuid(dbGetDeleteOwnCommand, "@DynamicFieldValueId", command.Id));
                dbGetDeleteOwnCommand.Parameters.Add(DbParameterHelper.AddNullableInt(dbGetDeleteOwnCommand, "@StartRow", startRow));
                dbGetDeleteOwnCommand.Parameters.Add(DbParameterHelper.AddNullableInt(dbGetDeleteOwnCommand, "@EndRow", endRow));

                var tempDataSet = await EntitySet.ExecuteReadCommandAsync(dbGetDeleteOwnCommand);
                var tempDataTable = tempDataSet.Tables[0];

                var isDeleteData = true;
                //Check Duplicate on all table
                foreach (KeyValuePair<string, string> item in colCheckDuplicate)
                {
                    var newData = tbOwnTable.Rows.Select(row => $"N'{row[item.Key]}'");
                    var newDataParam = string.Join(",", newData);
                    var oldDataParam = "";

                    if(command.SystemTableStatus == "filtering")
                    {
                        //Tức là chỉ check trùng khi edit
                        var getIds = tbOwnTable.Rows.Select(row => $"N'{row["Id"]}'");
                        oldDataParam = string.Join(",", getIds);
                    } else
                    {
                        var oldData = tempDataTable.Rows.Select(row => $"N'{row[item.Key]}'");
                        oldDataParam = string.Join(",", oldData);
                    }
                    var dynamicString = $"SELECT * FROM dbo.DynamicDefinedTable_{dynamicDefinedTableSchemaData.Name} WHERE {item.Key} IN ({newDataParam}) ";
                    if (oldDataParam.IsNotNullOrEmpty())
                    {
                        dynamicString = command.SystemTableStatus == "filtering" ? dynamicString + $" AND Id NOT IN ({oldDataParam})" : dynamicString + $" AND {item.Key} NOT IN ({oldDataParam})";
                    }

                    SqlCommand sqlcheckDupCommand = new SqlCommand();
                    sqlcheckDupCommand.CommandType = CommandType.Text;
                    sqlcheckDupCommand.CommandText = dynamicString;
                    var checkDataSet = await EntitySet.ExecuteReadCommandAsync(sqlcheckDupCommand);
                    var checkData = checkDataSet.Tables[0];
                    if (checkData.Rows.Count > 0)
                    {
                        isDeleteData = false;
                        var listDataDup = new List<object>();
                        foreach (DataRow row in checkData.Rows)
                        {
                            if (row.Table.Columns.Contains(item.Key))
                            {
                                listDataDup.Add(row[item.Key]);
                            }
                        }
                        var errMess = string.Join("; ", listDataDup.Distinct());
                        throw new Exception(string.Format("Bị trùng dữ liệu ở cột {0}, Các dữ liệu trùng là: {1}", item.Value, errMess));
                    }
                }

                #region Xử lý khi là dynamicTable trên Ticket/Task link với SystemTable

                var tableField = await EntitySet.Get<DynamicFieldDefinitionEntity>().FirstOrDefaultAsync(x => x.DynamicFormId == new Guid("00000000-1111-2222-3333-************")
                && x.DynamicDefinedTableSchemaId == dynamicDefinedTableColumnData.DynamicDefinedTableSchemaId);
                if (tableField != null)
                {
                    var getDynamicFieldValueIdSystem = tableField.DefaultValue;
                    if (getDynamicFieldValueIdSystem.IsNotNullOrEmpty())
                    {
                        var tableSystemId = new Guid(getDynamicFieldValueIdSystem);
                        if (tableSystemId != command.Id)
                        {
                            //Tức là table dạng system này đang được lưu ở từ Ticket hoặc Task và muốn đồng bộ data về table Chung
                            //Để làm được việc đồng bộ này cần ít nhất 1 column làm unique. Khi đó xử lý tạo mới và delete phức tạp
                            if (colCheckDuplicate.Count == 0) throw new Exception("Để đồng bộ dữ liệu về system table cần cấu hình ít nhất 1 cột có check trùng");
                            //Check có trùng với data của system Table
                            foreach (KeyValuePair<string, string> item in colCheckDuplicate)
                            {
                                var newData = tbOwnTable.Rows.Select(row => $"N'{row[item.Key]}'");
                                var newDataParam = string.Join(",", newData);

                                var dynamicString = $"SELECT COUNT({item.Key}) FROM dbo.DynamicDefinedTable_{dynamicDefinedTableSchemaData.Name}" +
                                    $" WHERE {item.Key} IN ({newDataParam}) AND ( DynamicFieldValueId = '{tableSystemId}' OR DynamicFieldValueId = '{command.Id}' )" +
                                    $" GROUP BY {item.Key} HAVING COUNT({item.Key}) = 1 ";
                                SqlCommand sqlcheckDupCommand = new SqlCommand();
                                sqlcheckDupCommand.CommandType = CommandType.Text;
                                sqlcheckDupCommand.CommandText = dynamicString;
                                var checkDataSet = await EntitySet.ExecuteReadCommandAsync(sqlcheckDupCommand);
                                var checkData = checkDataSet.Tables[0];
                                if (checkData.Rows.Count > 0)
                                {
                                    isDeleteData = false;
                                    var listDataDup = new List<object>();
                                    foreach (DataRow row in checkData.Rows)
                                    {
                                        if (row.Table.Columns.Contains(item.Key))
                                        {
                                            listDataDup.Add(row[item.Key]);
                                        }
                                    }
                                    var errMess = string.Join("; ", listDataDup.Distinct());
                                    throw new Exception(string.Format("Bị trùng dữ liệu ở cột {0}, Các dữ liệu trùng là: {1}", item.Value, errMess));
                                }
                            }
                            if (isDeleteData)
                            {
                                DataTable newDataSystemTable = tbOwnTable.Clone();
                                foreach (DataRow row in tbOwnTable.Rows)
                                {
                                    DataRow newRow = newDataSystemTable.NewRow();
                                    foreach (DataColumn col in tbOwnTable.Columns)
                                    {
                                        if (col.ColumnName == "Id") newRow["Id"] = Guid.NewGuid();
                                        else if (col.ColumnName == "DynamicFieldValueId") newRow["DynamicFieldValueId"] = tableSystemId;
                                        else newRow[col.ColumnName] = row[col];
                                    }
                                    newDataSystemTable.Rows.Add(newRow);
                                }
                                //Get OldValue to delete
                                SqlCommand sqlGetOwnCommand = new SqlCommand();
                                sqlGetOwnCommand.CommandType = CommandType.Text;
                                sqlGetOwnCommand.CommandText = string.Format("SELECT * FROM dbo.DynamicDefinedTable_{0} WHERE DynamicFieldValueId = @DynamicFieldValueId AND (RowNumber BETWEEN @StartRow AND @EndRow )", dynamicDefinedTableSchemaData.Name);
                                sqlGetOwnCommand.Parameters.AddWithValue("@DynamicFieldValueId", command.Id);
                                sqlGetOwnCommand.Parameters.AddWithValue("@StartRow", startRow);
                                sqlGetOwnCommand.Parameters.AddWithValue("@EndRow", endRow);                                
                                var deleteDataSet = await EntitySet.ExecuteReadCommandAsync(sqlGetOwnCommand);
                                var deleteDataTable = deleteDataSet.Tables[0];

                                //Xóa data cho table trên SystemTable và chính ticket/task
                                var columnsToDelete = deleteDataTable.Columns
                                    .Cast<DataColumn>()
                                    .Where(col => col.ColumnName != "Id" && col.ColumnName != "DynamicDefinedTableSchemaId" && col.ColumnName != "DynamicFieldValueId" && col.ColumnName != "RowNumber")
                                    .Select(col => col.ColumnName)
                                    .ToList();

                                var deleteQueries = new List<string>();

                                foreach (DataRow row in deleteDataTable.Rows)
                                {
                                    var conditions = new List<string>();

                                    // Tạo điều kiện so sánh cho mỗi cột
                                    foreach (var column in columnsToDelete)
                                    {
                                        if (row[column] != DBNull.Value)
                                        {
                                            var value = row[column];
                                            string formattedValue;
                                            if (value is string) formattedValue = $"N'{value.ToString().Replace("'", "''")}'";
                                            else if (value is DateTime) formattedValue = $"'{((DateTime)value):yyyy-MM-dd HH:mm:ss}'";
                                            else if (value is bool) formattedValue = (bool)value ? "1" : "0";
                                            else formattedValue = value.ToString();
                                            conditions.Add($"{column} = {formattedValue}");
                                        }
                                    }
                                    string deleteQuery = $"DELETE FROM [dbo].[DynamicDefinedTable_{dynamicDefinedTableSchemaData.Name}] WHERE {string.Join(" AND ", conditions)}" +
                                        $" AND ( DynamicFieldValueId = '{tableSystemId}' OR DynamicFieldValueId = '{command.Id}' ) ";
                                    deleteQueries.Add(deleteQuery);
                                }

                                //Delete Value của table trên system và chính nó
                                if(deleteQueries != null && deleteQueries.Count > 0)
                                {
                                    SqlCommand sqlDeleteSystemCommand = new SqlCommand();
                                    sqlDeleteSystemCommand.CommandType = CommandType.Text;
                                    sqlDeleteSystemCommand.CommandText = string.Join(Environment.NewLine, deleteQueries);
                                    await EntitySet.ExecuteNonQueryAsync(sqlDeleteSystemCommand);
                                }

                                //Tạo data cho table trên ticket và task
                                Repository.BulkInsertAll(tbOwnTable, string.Format("[dbo].[DynamicDefinedTable_{0}]", dynamicDefinedTableSchemaData.Name), mappingLists);
                                Repository.BulkInsertAll(newDataSystemTable, string.Format("[dbo].[DynamicDefinedTable_{0}]", dynamicDefinedTableSchemaData.Name), mappingLists);

                                //Update lại rowNumber cho SystemTable
                                SqlCommand sqlExcuteCommand = new SqlCommand();
                                sqlExcuteCommand.CommandType = CommandType.Text;
                                var updateRowNumber = string.Format(" UPDATE dbo.DynamicDefinedTable_{0} SET RowNumber = NewRowNumber" +
                                    " FROM ( SELECT Id, ROW_NUMBER() OVER (ORDER BY Id ASC) - 1 AS NewRowNumber FROM dbo.DynamicDefinedTable_{0} WHERE DynamicFieldValueId = '{1}')" +
                                    " AS temp WHERE dbo.DynamicDefinedTable_{0}.Id = temp.Id AND DynamicFieldValueId = '{1}' ", dynamicDefinedTableSchemaData.Name, tableSystemId);
                                sqlExcuteCommand.CommandText = updateRowNumber;
                                await EntitySet.ExecuteNonQueryAsync(sqlExcuteCommand);
                                return;
                            }
                        }
                    }
                }

                #endregion

                if (isDeleteData)
                {
                    //Nếu ko bị dup, tiến hành delete và Insert
                    if (command.SystemTableStatus == "filtering")
                    {
                        //Trường hợp có filter trên systemTable, mà muốn Edit thì dùng sqlCommand delete insert. Ko dùng rowNumber
                        SqlCommand sqlExcuteCommand = new SqlCommand();
                        sqlExcuteCommand.CommandType = CommandType.Text;
                        var updateRowNumber = string.Format(" UPDATE dbo.DynamicDefinedTable_{0} SET RowNumber = NewRowNumber" +
                            " FROM ( SELECT Id, ROW_NUMBER() OVER (ORDER BY Id ASC) - 1 AS NewRowNumber FROM dbo.DynamicDefinedTable_{0} WHERE DynamicFieldValueId = '{1}')" +
                            " AS temp WHERE dbo.DynamicDefinedTable_{0}.Id = temp.Id AND DynamicFieldValueId = '{1}' ", dynamicDefinedTableSchemaData.Name, command.Id);
                        sqlExcuteCommand.CommandText = sqlCreateEditRow.ToString() + updateRowNumber;
                        await EntitySet.ExecuteNonQueryAsync(sqlExcuteCommand);
                    } else
                    {                       
                        SqlCommand sqlDeleteOwnCommand = new SqlCommand();
                        sqlDeleteOwnCommand.CommandType = CommandType.Text;
                        sqlDeleteOwnCommand.CommandText = string.Format("DELETE dbo.DynamicDefinedTable_{0} WHERE DynamicFieldValueId = @DynamicFieldValueId AND (RowNumber BETWEEN @StartRow AND @EndRow )", dynamicDefinedTableSchemaData.Name);
                        sqlDeleteOwnCommand.Parameters.AddWithValue("@DynamicFieldValueId", command.Id);
                        sqlDeleteOwnCommand.Parameters.AddWithValue("@StartRow", startRow);
                        sqlDeleteOwnCommand.Parameters.AddWithValue("@EndRow", endRow);
                        await EntitySet.ExecuteNonQueryAsync(sqlDeleteOwnCommand);


                        try
                        {
                            Repository.BulkInsertAll(tbOwnTable, string.Format("[dbo].[DynamicDefinedTable_{0}]", dynamicDefinedTableSchemaData.Name), mappingLists);
                        } catch
                        {
                            //Xử lý khi tạo mới service có dùng giá trị mặc định từ dynamicForm
                            foreach (DataRow row in tbOwnTable.Rows)
                            {
                                foreach (DataColumn col in tbOwnTable.Columns)
                                {
                                    if (col.ColumnName == "Id") row["Id"] = Guid.NewGuid();
                                }
                            }                            
                            Repository.BulkInsertAll(tbOwnTable, string.Format("[dbo].[DynamicDefinedTable_{0}]", dynamicDefinedTableSchemaData.Name), mappingLists);
                        }                        
                    }
                }                              
                #endregion
            }
            else if (dynamicDefinedTableSchemaData != null && dynamicDefinedTableSchemaData.RowStoredMethod == DynamicDefinedTableStoreMethod.RowJsonValue)
            {
                List<DynamicDefinedTableRowJsonValueEntity> savedDynamicDefinedTableRowJsonValueEntities = new List<DynamicDefinedTableRowJsonValueEntity>();

                var listItemByRows = command.ListItems.GroupBy(c => c.RowNumber).Select(x => new { RowNumber = startRow + x.Key, CellValueList = x.ToList() }).ToList();
                foreach (var listItemByRow in listItemByRows)
                {
                    Dictionary<Guid, string> rowDic = listItemByRow.CellValueList.ToDictionary(keySelector: x => x.DynamicDefinedTableColumnId, elementSelector: x => x.Value);
                    List<Dictionary<Guid, string>> rowDicList = new List<Dictionary<Guid, string>>();
                    rowDicList.Add(rowDic);

                    DynamicDefinedTableRowJsonValueEntity rowJsonValueEntity = new DynamicDefinedTableRowJsonValueEntity
                    {
                        Id = Guid.NewGuid(),
                        DynamicDefinedTableSchemaId = dynamicDefinedTableSchemaData.Id,
                        DynamicFieldValueId = command.Id,
                        RowNumber = listItemByRow.RowNumber,
                        RowJsonValue = JsonConvert.SerializeObject(rowDicList)
                    };
                    savedDynamicDefinedTableRowJsonValueEntities.Add(rowJsonValueEntity);
                }

                // Store "CreateEditDynamicDefinedTableRowJsonValueList" -> LinQ
                IQueryable<DynamicDefinedTableRowJsonValueEntity> deletedQuery = (from jsonValues in EntitySet.Get<DynamicDefinedTableRowJsonValueEntity>()
                                                                                  where jsonValues.DynamicFieldValueId == command.Id
                                                                                  && jsonValues.RowNumber >= startRow
                                                                                  && jsonValues.RowNumber <= endRow
                                                                                  select jsonValues);

                await Repository.DeleteFromAsync(deletedQuery);
                await Repository.InsertAsync(savedDynamicDefinedTableRowJsonValueEntities);
            }
            else
            {
                // Store "CreateEditDynamicDefinedTableCellValueList" -> LinQ

                List<DynamicDefinedTableCellValueEntity> saveDynamicDefinedTableCellValueEntities = new List<DynamicDefinedTableCellValueEntity>();

                foreach (var listItem in command.ListItems)
                {
                    Guid listItemId = listItem.Id.IsNullOrEmpty() ? Guid.NewGuid() : listItem.Id.Value;
                    DynamicDefinedTableCellValueEntity dynamicDefinedTableCellValueEntity = new DynamicDefinedTableCellValueEntity
                    {
                        Id = listItemId,
                        DynamicDefinedTableColumnId = listItem.DynamicDefinedTableColumnId,
                        DynamicFieldValueId = command.Id,
                        RowNumber = startRow + listItem.RowNumber,
                        Value = listItem.Value
                    };
                }

                IQueryable<DynamicDefinedTableCellValueEntity> deletedQuery = (from cell in EntitySet.Get<DynamicDefinedTableCellValueEntity>()
                                                                                  where cell.DynamicFieldValueId == command.Id
                                                                                  && cell.RowNumber >= startRow
                                                                                  && cell.RowNumber <= endRow
                                                                                  select cell);

                await Repository.DeleteFromAsync(deletedQuery);
                await Repository.InsertAsync(saveDynamicDefinedTableCellValueEntities);
            }
        }
    }
}