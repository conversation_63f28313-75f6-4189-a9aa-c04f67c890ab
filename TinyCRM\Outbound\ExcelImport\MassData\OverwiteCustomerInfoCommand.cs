﻿using AutoMapper;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Threading.Tasks;
using Webaby;
using Webaby.Data;
using Webaby.Localization;

namespace TinyCRM.Outbound.ExcelImport.MassData
{
    public class OverwiteCustomerInfoCommand : CommandBase
    {
        public Guid ImportSessionId { get; set; }

        public Guid UserId { get; set; }

        public IEnumerable<string> Update { get; set; }
    }
    internal class OverwriteCustomerInfoCommandHandler : CommandHandlerBase<OverwiteCustomerInfoCommand>
    {
        public OverwriteCustomerInfoCommandHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task ExecuteAsync(OverwiteCustomerInfoCommand cmd)
        {
            var command = EntitySet.CreateDbCommand();
            command.CommandText = "dbo.ImportCustomer_OverwriteInfo";
            command.CommandType = CommandType.StoredProcedure;
            command.Parameters.Add(DbParameterHelper.AddNullableGuid(command, "@importSessionId", cmd.ImportSessionId));
            command.Parameters.Add(DbParameterHelper.AddNullableGuid(command, "@userId", cmd.UserId));
            var dt = new DataTable();
            dt.Columns.Add("Value", typeof(string));
            if (cmd.Update != null)
            {
                foreach (var option in cmd.Update)
                {
                    dt.Rows.Add(option);
                }
            }
            var updateOptions = new SqlParameter();
            updateOptions.ParameterName = "@update";
            updateOptions.Value = dt;
            updateOptions.SqlDbType = SqlDbType.Structured;
            command.Parameters.Add(updateOptions);
            await EntitySet.ExecuteNonQueryAsync(command);
        }
    }
}
