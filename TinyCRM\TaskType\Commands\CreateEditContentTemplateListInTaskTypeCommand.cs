﻿using AutoMapper;
using LinqToDB;
using LinqToDB.Data;
using Microsoft.Data.SqlClient;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Threading.Tasks;
using TinyCRM.ContentTemplate;
using Webaby;
using Webaby.Data;
using Webaby.Localization;
using Webaby.Security;

namespace TinyCRM.TaskType.Commands
{
    public class CreateEditContentTemplateListInTaskTypeCommand : CommandBase
    {
        public Guid TaskTypeId { get; set; }

        public List<ContentTemplateListInTaskTypeItem> ContentTemplateListInTaskTypeItems { get; set; }
    }

    internal class CreateEditContentTemplateListInTaskTypeCommandHandler : CommandHandlerBase<CreateEditContentTemplateListInTaskTypeCommand>
    {        
        public IUserService _userService { get; set; }

        public CreateEditContentTemplateListInTaskTypeCommandHandler(IServiceProvider serviceProvider) : base(serviceProvider) { _userService = ServiceProvider.GetRequiredService<IUserService>(); }

        public override async Task ExecuteAsync(CreateEditContentTemplateListInTaskTypeCommand command)
        {
            var linq2dbCtx = (DataConnection)EntitySet.GetLinqToDBContext();
            var curentUser = _userService.GetCurrentUser().Id;
            var curentDate = DateTime.Now;

            var contentTemplateTaskType = EntitySet.Get<ContentTemplateTaskTypeEntity>();

            //Mark deleted
            var deletedRows = from cttt in contentTemplateTaskType
                            where cttt.TaskTypeId == command.TaskTypeId
                                  && !command.ContentTemplateListInTaskTypeItems.Any(ct => ct.ContentTemplateId == cttt.ContentTemplateId)
                            select cttt;

            await deletedRows.UpdateAsync(cttt => new ContentTemplateTaskTypeEntity
            {
                Deleted = true,
                DeletedBy = curentUser,
                DeletedDate = curentDate
            });

            //Update entity

            var updatedRows =
                from cttt in contentTemplateTaskType
                join ct in command.ContentTemplateListInTaskTypeItems on cttt.ContentTemplateId equals ct.ContentTemplateId
                where cttt.TaskTypeId == command.TaskTypeId
                select new { cttt, ct.WorkingOrder };

            await updatedRows.Set(x => x.cttt.WorkingOrder, x => x.WorkingOrder)
                .Set(x => x.cttt.ModifiedBy, x => curentUser)
                .Set(x => x.cttt.ModifiedDate, x => curentDate)
                .UpdateAsync();

            //Insert new entity

            var existingIds = await contentTemplateTaskType
                .Where(x => x.TaskTypeId == command.TaskTypeId)
                .Select(x => x.ContentTemplateId)
                .ToListAsync();

            var newEntities = command.ContentTemplateListInTaskTypeItems
                .Where(x => !existingIds.Contains(x.ContentTemplateId))
                .Select(x => new ContentTemplateTaskTypeEntity
                {
                    Id = Guid.NewGuid(),
                    ContentTemplateId = x.ContentTemplateId,
                    TaskTypeId = command.TaskTypeId,
                    WorkingOrder = x.WorkingOrder,
                    CreatedBy = curentUser,
                    CreatedDate = curentDate,
                    Deleted = false
                });

            linq2dbCtx.BulkCopy(new BulkCopyOptions
            {
                TableName = "ContentTemplateTaskType",
                BulkCopyType = BulkCopyType.Default
            }, newEntities);

            //var cmd = EntitySet.CreateDbCommand();
            //cmd.Parameters.Add(DbParameterHelper.AddNullableGuid(cmd, "@TaskTypeId", command.TaskTypeId));
            //cmd.Parameters.Add(DbParameterHelper.AddNullableGuid(cmd, "@UserId", _userService.GetCurrentUser().Id));
            //DataTable taskTypeList = new DataTable();
            //taskTypeList.Columns.Add("Id", typeof(Guid));
            //taskTypeList.Columns.Add("SortOrder", typeof(int));

            //foreach(var item in command.ContentTemplateListInTaskTypeItems)
            //{
            //    taskTypeList.Rows.Add(item.ContentTemplateId, item.WorkingOrder);
            //}           

            //var taskTypeListParameter = new SqlParameter();
            //taskTypeListParameter.ParameterName = "@ContentTemplateList";
            //taskTypeListParameter.Value = taskTypeList;
            //taskTypeListParameter.SqlDbType = SqlDbType.Structured;
            //taskTypeListParameter.TypeName = "dbo.SortIdList";
                
            //cmd.Parameters.Add(taskTypeListParameter);

            //cmd.CommandText = "dbo.CreateEditContentTemplateListInTaskType";
            //cmd.CommandType = CommandType.StoredProcedure;

            //await EntitySet.ExecuteNonQueryAsync(cmd);
        }
    }

    public class ContentTemplateListInTaskTypeItem
    {
        public Guid ContentTemplateId { get; set; }

        public int WorkingOrder { get; set; }
    }
}