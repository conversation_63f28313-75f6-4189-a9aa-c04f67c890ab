﻿using AutoMapper;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Data;
using System.Threading.Tasks;
using TinyCRM.Outbound.CallResult;
using TinyCRM.Outbound.Prospect;
using Webaby;
using Webaby.Data;
using Webaby.Localization;

namespace TinyCRM.Outbound.ProspectAssignment.Commands
{
    public class RollbackDistributeCommand : CommandBase
    {
        public List<Guid> ProspectAssignmentIds { get; set; }
    }

    internal class RollbackDistributeCommandHandler : CommandHandlerBase<RollbackDistributeCommand>
    {
        public RollbackDistributeCommandHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }
        public override async Task ExecuteAsync(RollbackDistributeCommand command)
        {
            var cmd = EntitySet.CreateDbCommand();
            cmd.CommandText = "dbo.RollbackDistribute";
            cmd.CommandType = CommandType.StoredProcedure;

            var idListParam = cmd.Parameters.Add(DbParameterHelper.NewIdListParameter("@ProspectAssignmentIds", command.ProspectAssignmentIds));
            cmd.Parameters.Add(idListParam);

            await EntitySet.ExecuteNonQueryAsync(cmd);
        }
    }
}