﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using System.Threading.Tasks;
using Webaby;
using Webaby.Data;

namespace Webaby.Core.Organization.Queries
{
    public class GetTaskServiceTypeDefaultOrganizationQuery : QueryBase<TaskServiceTypeDefaultOrganization>
    {
        public Guid OrganizationId { get; set; }
    }

    internal class GetTaskServiceTypeDefaultOrganizationQueryHandler : QueryHandlerBase<GetTaskServiceTypeDefaultOrganizationQuery, TaskServiceTypeDefaultOrganization>
    {
        public GetTaskServiceTypeDefaultOrganizationQueryHandler(IServiceProvider serviceProvider) : base(serviceProvider)
        {
        }

        public override async Task<QueryResult<TaskServiceTypeDefaultOrganization>> ExecuteAsync(GetTaskServiceTypeDefaultOrganizationQuery query)
        {
            var cmd = EntitySet.CreateDbCommand(CommandType.StoredProcedure, "dbo.GetTaskServiceTypeDefaultOrganization");
            cmd.Parameters.Add(DbParameterHelper.AddNullableGuid(cmd, "@OrganizationId", query.OrganizationId));

            var mainQuery = await EntitySet.ExecuteReadCommandAsync<TaskServiceTypeDefaultOrganization>(cmd);
            await Task.CompletedTask;
            return QueryResult.Create(mainQuery);
        }
    }

    public class TaskServiceTypeDefaultOrganization
    {
        public string Level1 { get; set; }

        public string Level2 { get; set; }

        public string Level3 { get; set; }

        public string Level4 { get; set; }
    }
}