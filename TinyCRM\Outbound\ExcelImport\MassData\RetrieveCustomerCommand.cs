﻿using AutoMapper;
using Microsoft.EntityFrameworkCore;
using System;
using System.Data;
using System.Threading.Tasks;
using Webaby;
using Webaby.Data;
using Webaby.Localization;

namespace TinyCRM.Outbound.ExcelImport.MassData
{
    public class RetrieveCustomerCommand : CommandBase
    {
        public Guid CampaignId { get; set; }
        public Guid ImportSessionId { get; set; }
        public Guid UserId { get; set; }
        public int? Status { get; set; }
        public Guid? TMR { get; set; }
    }

    internal class RetrieveCustomerCommandHandler : CommandHandlerBase<RetrieveCustomerCommand>
    {
        public RetrieveCustomerCommandHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task ExecuteAsync(RetrieveCustomerCommand cmd)
        {
            var command = EntitySet.CreateDbCommand();
            command.CommandText = "dbo.ImportCustomer_Retrieve";
            command.CommandType = CommandType.StoredProcedure;
            command.Parameters.Add(DbParameterHelper.AddNullableGuid(command,"@ImportSessionId", cmd.ImportSessionId));
            command.Parameters.Add(DbParameterHelper.AddNullableGuid(command,"@Manager", cmd.UserId));
            command.Parameters.Add(DbParameterHelper.AddNullableGuid(command, "@CampaignId", cmd.CampaignId));
            command.Parameters.Add(DbParameterHelper.AddNullableInt(command, "@Status", cmd.Status));
            command.Parameters.Add(DbParameterHelper.AddNullableGuid(command, "@TMR", cmd.TMR));
            await EntitySet.ExecuteNonQueryAsync(command);
        }
    }
}
