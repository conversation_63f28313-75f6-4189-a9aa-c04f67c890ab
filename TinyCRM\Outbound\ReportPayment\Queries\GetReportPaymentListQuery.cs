﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Webaby;
using Webaby.Data;
using Webaby.Security;

namespace TinyCRM.Outbound.ReportPayment.Queries
{
    public class GetReportPaymentListQuery : QueryBase<ReportPaymentData>
    {
        public int PageIndex { get; set; }
        public int PageSize { get; set; }
    }

    public class ReportPaymentData
    {
        public string ContestName { get; set; }
        public string ContestCode { get; set; }
        public string ContestStatus { get; set; }
        public long? ExpenseAmount { get; set; }
        public int? ExpenseQualifier { get; set; }
        public long? AdjustmentAmount { get; set; }
        public int? AdjustmentQualifier { get; set; }
        public long? EndoreAmount { get; set; }
        public int? EndoreQualifier { get; set; }
        public double? EndoreCompleted { get; set; }
        public long? PaymentAmount { get; set; }
        public int? PaymentQualifier { get; set; }
        public double? PaymentCompleted { get; set; }
        public int TotalCount { get; set; }
    }
    internal class GetReportPaymentListQueryHandler : QueryHandlerBase<GetReportPaymentListQuery, ReportPaymentData>
    {
        public IUserService _userService { get; set; }

        public GetReportPaymentListQueryHandler(IServiceProvider serviceProvider, IUserService userService) : base(serviceProvider)
        {
            _userService = userService;
        }

        public override async Task<QueryResult<ReportPaymentData>> ExecuteAsync(GetReportPaymentListQuery query)
        {
            int startRow = query.PageIndex * query.PageSize + 1;
            int endRow = query.PageIndex * query.PageSize + query.PageSize; var cmd = EntitySet.CreateDbCommand();
            cmd.AddDataAuthorizedParameters(_userService);
            cmd.Parameters.Add(DbParameterHelper.AddNullableInt(cmd, "@StartRow", startRow));
            cmd.Parameters.Add(DbParameterHelper.AddNullableInt(cmd, "@EndRow", endRow));

            cmd.CommandText = "fwd.GetReportPayment";
            cmd.CommandType = CommandType.StoredProcedure;
            var mainQuery = await EntitySet.ExecuteReadCommandAsync<ReportPaymentData>(cmd);
            return QueryResult.Create(mainQuery);
        }
    }
}