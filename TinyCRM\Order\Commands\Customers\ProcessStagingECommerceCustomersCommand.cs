﻿using System;
using System.Data;
using System.Data.Common;
using System.Threading.Tasks;
using AutoMapper;
using TinyCRM.Enums;
using Webaby;
using Webaby.Data;

namespace TinyCRM.Order.Commands
{
    public class ProcessStagingECommerceCustomersCommand : CommandBase
    {
        public Guid CustomerFileId { get; set; }
    }

    internal class ProcessStagingECommerceCustomersCommandHandler : CommandHandlerBase<ProcessStagingECommerceCustomersCommand>
    {
        public ProcessStagingECommerceCustomersCommandHandler(IServiceProvider serviceProvider) : base(serviceProvider)
        {
        }

        public override async Task ExecuteAsync(ProcessStagingECommerceCustomersCommand command)
        {
            var cmd = EntitySet.CreateDbCommand(CommandType.StoredProcedure, "ec.ProcessStagingECommerceCustomers");
            cmd.Parameters.Add(DbParameterHelper.AddNullableGuid(cmd, "@CustomerFileId", command.CustomerFileId));

            await EntitySet.ExecuteNonQueryAsync(cmd);
        }
    }
}