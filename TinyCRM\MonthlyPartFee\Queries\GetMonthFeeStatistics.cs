﻿using System;
using System.Data;
using System.Data.Common;
using System.Linq;
using System.Threading.Tasks;
using Webaby;
using Webaby.Data;

namespace TinyCRM.MonthlyPartFee.Queries
{
    public class GetMonthFeeStatisticsQuery : QueryBase<GetMonthFeeStatisticsQuery.Result>
    {
        public class Result
        {
            public int Month { get; internal set; }
            public int Year { get; internal set; }
            public int HostMissingCount { get; internal set; }
            public int ServiceUsedHistoryMissingCount { get; internal set; }
            public int FirstTimeReminderCount { get; internal set; }
            public int SecondTimeReminderCount { get; internal set; }
            public int PaidCount { get; internal set; }
            public int NoBillCreatedCount { get; internal set; }
            public int BillCreatedCount { get; internal set; }
            public int Total { get; internal set; }
        }
        public int Month { get; set; }
        public int Year { get; set; }
    }

    public class GetMonthFeeStatisticsQueryHandler : QueryHandlerBase<GetMonthFeeStatisticsQuery, GetMonthFeeStatisticsQuery.Result>
    {
        public GetMonthFeeStatisticsQueryHandler(IServiceProvider serviceProvider) : base(serviceProvider)
        {
        }

        public override async Task<QueryResult<GetMonthFeeStatisticsQuery.Result>> ExecuteAsync(GetMonthFeeStatisticsQuery query)
        {
            var cmd = EntitySet.CreateDbCommand(CommandType.StoredProcedure, "dbo.GetMonthFeeStatistics");
            cmd.Parameters.Add(DbParameterHelper.AddNullableInt(cmd, "@Year", query.Year));
            cmd.Parameters.Add(DbParameterHelper.AddNullableInt(cmd, "@Month", query.Month));

            var result = (await EntitySet.ExecuteReadCommandAsync<GetMonthFeeStatisticsQuery.Result>(cmd)).ToList();
            await Task.CompletedTask;
            return new QueryResult<GetMonthFeeStatisticsQuery.Result>(result);
        }
    }
}