﻿using System;
using System.ComponentModel;
using Webaby.Data;

using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace TinyCRM.ServiceType
{
    [Table("ServiceType", Schema = "dbo")]
    public class ServiceTypeEntity : IEntity, IDataAuthorizedEnabledEntity, ICreatedDateEnabledEntity, ICreatedByEnabledEntity, IModifiedDateEnabledEntity, IModifiedByEnabledEntity, ISoftDeleteEnabledEntity, IDeletedByEnabledEntity
    {
        [Key]
        public Guid Id { get; set; }

        [Column]
        public Guid? Level1Id { get; set; }

        [Column]
        public Guid? Level2Id { get; set; }

        [Column]
        public Guid? Level3Id { get; set; }

        [Column]
        public Guid? Level4Id { get; set; }

        [Column]
        public string Code { get; set; }

        [Column]
        public Guid? AcceptDueTimeId { get; set; }

        [Column]
        public Guid? ProcessDueTimeId { get; set; }

        [Column]
        public Guid? WorkflowId { get; set; }

        [Column]
        public Guid? DynamicFormId { get; set; }

        [Column]
        public Guid? DefaultOrganizationId { get; set; }

        [Column]
        public Guid? TicketOwnerDefaultOrganizationId { get; set; }

        [Column]
        public bool WarningTaskCreatingWhenCreateTicket { get; set; }

        [Column]
        public bool? AllowManualSLA { get; set; }

        [Column]
        public RegularType? RegularType { get; set; }

        [Column]
        public TaskListViewMode TaskListViewMode { get; set; }

        [Column]
        public int? PricePerUnit { get; set; }

        [Column]
        public string Unit { get; set; }

        [Column]
        public DateTime CreatedDate { get; set; }

        [Column]
        public Guid CreatedBy { get; set; }

        [Column]
        public DateTime? ModifiedDate { get; set; }

        [Column]
        public Guid? ModifiedBy { get; set; }

        [Column]
        public Boolean Deleted { get; set; }

        [Column]
        public DateTime? DeletedDate { get; set; }

        [Column]
        public Guid? DeletedBy { get; set; }

        [Column]
        public TicketCodeMode? TicketMode { get; set; }

        [Column]
        public MultiRequestTicketMode MultiRequestTicketMode { get; set; }

        [Column]
        public string TemplateTicketCode { get; set; }

        [Column]
        public Guid? DataAuthorizedId { get; set; }
    }

    public enum RegularType
    {
        MA = 1,
        Daily = 2
    }

    public enum TaskListViewMode
    {
        [Description("Mặc định")]
        Default = 1,
        [Description("Tất cả tác vụ")]
        AllTasks = 2,
        [Description("Duyệt từng tác vụ")]
        LoopEachTask = 3,
        [Description("Tác vụ theo time")]
        TaskByTime = 4
    }

    public enum TicketCodeMode
    {
        [Description("Mặc định")]
        Default = 1,
        [Description("Nhập tay")]
        InputCode = 2,
        [Description("Theo template")]
        TemplateCode = 3,
        [Description("Auto")] //Sử dụng Default nếu Code(input) null ~ (Default | InputCode)
        Auto = 4
    }

    public enum MultiRequestTicketMode
    {
        [Description("Được tạo nhiều Phiếu")]
        Default = 1,
        [Description("Không trùng với Phiếu chưa xóa")]
        NotDupUndeleted = 2,
        [Description("Không trùng với Phiếu đã tạo ( kể cả xóa)")]
        NotDupAll = 3
    }
}
