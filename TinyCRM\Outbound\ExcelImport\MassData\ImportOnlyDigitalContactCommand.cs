﻿using System;
using System.Data;
using System.Threading.Tasks;
using Webaby;
using Webaby.Data;
using Microsoft.EntityFrameworkCore;

namespace TinyCRM.Outbound.ExcelImport.MassData
{
    public class ImportOnlyDigitalContactCommand : CommandBase
    {
        public Guid ImportSessionId { get; set; }

        public Guid? CampaignId { get; set; }
    }
    internal class ImportOnlyDigitalContactCommandHandler : CommandHandlerBase<ImportOnlyDigitalContactCommand>
    {
        public ImportOnlyDigitalContactCommandHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task ExecuteAsync(ImportOnlyDigitalContactCommand command)
        {
            var cmd = EntitySet.CreateDbCommand();
            cmd.CommandText = "dbo.ImportOnlyDigitalContactCommand";
            cmd.CommandType = CommandType.StoredProcedure;
            cmd.Parameters.Add(DbParameterHelper.AddNullableGuid(cmd, "@ImportSessionId", command.ImportSessionId));
            cmd.Parameters.Add(DbParameterHelper.AddNullableGuid(cmd, "@CampaignId", command.CampaignId));
            await EntitySet.ExecuteNonQueryAsync(cmd);
        }
    }
}
