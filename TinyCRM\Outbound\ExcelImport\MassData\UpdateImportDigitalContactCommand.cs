﻿using System;
using System.Data;
using System.Data.SqlClient;
using System.Threading.Tasks;
using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Webaby;
using Webaby.Data;
using Webaby.Localization;

namespace TinyCRM.Outbound.ExcelImport.MassData
{
    public class UpdateImportDigitalContactCommand : CommandBase
    {
        public Guid ImportSessionId { get; set; }
    }
    internal class UpdateImportDigitalContactCommandHandler : CommandHandlerBase<UpdateImportDigitalContactCommand>
    {
        public UpdateImportDigitalContactCommandHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task ExecuteAsync(UpdateImportDigitalContactCommand command)
        {
            var cmd = EntitySet.CreateDbCommand();
            cmd.CommandText = "dbo.UpdateImportDigitalContact";
            cmd.CommandType = CommandType.StoredProcedure;
            cmd.Parameters.Add(DbParameterHelper.AddNullableGuid(cmd, "@ImportSessionId", command.ImportSessionId));
            cmd.CommandTimeout = DatabaseCommandtimeout.Slow;
            await EntitySet.ExecuteNonQueryAsync(cmd, false);
        }
    }
}