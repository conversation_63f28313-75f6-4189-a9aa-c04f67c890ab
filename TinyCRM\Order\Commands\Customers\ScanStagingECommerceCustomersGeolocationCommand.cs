﻿using System;
using System.Data;
using System.Data.Common;
using System.Threading.Tasks;
using AutoMapper;
using TinyCRM.Enums;
using Webaby;
using Webaby.Data;

namespace TinyCRM.Order.Commands
{
    public class ScanStagingECommerceCustomersGeolocationCommand : CommandBase
    {
        public Guid CustomerFileId { get; set; }
    }

    internal class ScanStagingECommerceCustomersGeolocationCommandHandler : CommandHandlerBase<ScanStagingECommerceCustomersGeolocationCommand>
    {
        public ScanStagingECommerceCustomersGeolocationCommandHandler(IServiceProvider serviceProvider) : base(serviceProvider)
        {
        }

        public override async Task ExecuteAsync(ScanStagingECommerceCustomersGeolocationCommand command)
        {
            var cmd = EntitySet.CreateDbCommand(CommandType.StoredProcedure, "ec.ScanStagingECommerceCustomersGeolocation");
            cmd.Parameters.Add(DbParameterHelper.AddNullableGuid(cmd, "@CustomerFileId", command.CustomerFileId));

            await EntitySet.ExecuteNonQueryAsync(cmd);
        }
    }
}