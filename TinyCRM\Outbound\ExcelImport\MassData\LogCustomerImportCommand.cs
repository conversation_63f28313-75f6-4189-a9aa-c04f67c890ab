﻿using AutoMapper;
using Microsoft.EntityFrameworkCore;
using System;
using System.Data;
using System.Data.SqlClient;
using System.Threading.Tasks;
using Webaby;
using Webaby.Data;
using Webaby.Localization;

namespace TinyCRM.Outbound.ExcelImport.MassData
{
    public class LogCustomerImportCommand : CommandBase
    {
        public Guid ImportSessionId { get; set; }
    }
    internal class LogCustomerImportCommandHandler : CommandHandlerBase<LogCustomerImportCommand>
    {
        public LogCustomerImportCommandHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task ExecuteAsync(LogCustomerImportCommand command)
        {
            var cmd = EntitySet.CreateDbCommand();
            cmd.CommandType = CommandType.StoredProcedure;
            cmd.CommandText = "dbo.ImportCustomer_Log";
            cmd.Parameters.Add(DbParameterHelper.AddNullableGuid(cmd, "@ImportSessionId", command.ImportSessionId));
            await EntitySet.ExecuteNonQueryAsync(cmd);
        }
    }
}
