﻿@using Webaby.Core.DynamicForm
@using TinyCRM.Web.Models.DynamicForm

@model DynamicFieldModel

@{ 
    Layout = "~/Views/Shared/_EmptyView-v5.cshtml";
    var staticFieldGroups = Newtonsoft.Json.JsonConvert.SerializeObject(Model.StaticFieldGroups);
    bool customerVersioningEnabled = Configuration.GetValue<bool>("customer.versioning.enabled");

    var listVersionName = new List<TinyCRM.CustomerVersionName.CustomerVersionNameEntity>();
    if (customerVersioningEnabled)
    {
        listVersionName = (await QueryExecutor.ExecuteManyAsync(new TinyCRM.CustomerVersionName.Queries.GetAllCustomerVersionNameQuery { })).ToList();
    }
}

<div id="divCreateEditFieldModel" class="modal-dialog modal-lg" role="document">
    <div class="modal-content">
        <div class="modal-header">
            <h4 class="modal-title" id="createEditModalLabel">
                @if (Model.Id.HasValue)
                {
                    <i class="la la-edit"></i>
                    @T["Chỉnh sửa"]
                }
                else
                {
                    <i class="la la-plus"></i>
                    @T["Tạo mới"]
                }
            </h4>
            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                <span aria-hidden="true">
                    &times;
                </span>
            </button>
        </div>
        <form id="frmCreateEditDynamicField_useless" action="@Url.Action("CreateEditDynamicField", "DynamicForm")" method="POST" class="form-horizontal ajax-form">
            <div class="modal-body">
                @Html.HiddenFor(model => model.Id)
                @Html.HiddenFor(model => model.DynamicFormId)
                @Html.HiddenFor(model => model.SourceFieldName)
                @Html.HiddenFor(model => model.SourceTableName)
                @Html.HiddenFor(model => model.IsNew)
                <input type="hidden" name="UIFriendlyFormula" value="@Model.UIFriendlyFormula" />

                <div class="form-group m-form__group row">
                    <div class="col-lg-6">
                        <label>@T["Tên hiển thị"]</label>
                        @Html.TextBoxFor(model => model.DisplayName, new { @id = "txtDisplayName", @class = "form-control m-input", @placeholder = T["Nhập tên hiển thị"] })
                    </div>

                    <div class="col-lg-6">
                        <label>@T["Tên tham khảo"]</label>
                        @Html.TextBoxFor(model => model.Name, new { @id = "txtFieldName", @class = "form-control m-input", @placeholder = T["Nhập tên trường thông tin"] })
                    </div>

                </div>

                <div class="form-group m-form__group row">
                    <div class="col-lg-6">
                        <label>@T["Loại thông tin"]</label>
                        @Html.EditorFor(x => x.FieldType)
                    </div>
                    <div class="col-lg-6">
                        <label>@T["Giá trị mặc định"]</label>
                        @Html.TextBoxFor(model => model.DefaultValue, new { @class = "form-control m-input" })
                    </div>

                    <div id="divDynamicDefinedTableSchemaId" class="col-lg-6" style="display: none;">
                        <label>@T["Danh sách đã định nghĩa"]</label>
                        @Html.EditorFor(model => model.DynamicDefinedTableSchemaId)
                    </div>
                    @if (!string.IsNullOrEmpty(Model.AdditionalFilter))
                    {
                        <div class="col-lg-6">
                            <label>@T["Giới hạn dữ liệu"]</label>
                            <select id="cboAdditionalFilter" class="form-control" name="AdditionalFilter">
                                <option value="">@T["Chọn loại dữ liệu"]</option>
                            </select>
                        </div>
                    }
                </div>
                <div class="form-group m-form__group row source-field">
                    <div class="col-lg-6">
                        <label>
                            @T["Nguồn đối tượng 1"]
                            <i class="la la-link m--font-info"></i>
                        </label>
                        <select id="cboStaticFieldGroups" class="form-control m-input" style="margin: 0px">
                            <option value="">@T["Chọn nguồn đối tượng"]</option>
                            @foreach (var sfg in Model.StaticFieldGroups)
                            {
                                <option value="@Html.Raw(sfg.Name)">@Html.Raw(sfg.Name)</option>
                            }
                        </select>
                    </div>
                    <div class="col-lg-6">
                        <label>
                            @T["Nguồn thông tin"]
                            <i class="la la-link  m--font-info"></i>
                        </label>
                        <select id="cboSourceNames" class="form-control m-input" style="margin: 0px">
                            <option value="">@T["Chọn nguồn thông tin"]</option>
                        </select>
                    </div>
                </div>
                <div class="form-group m-form__group row">

                    <div class="col-lg-6">
                        <label>@T["Kiểu dữ liệu"]</label>
                        @Html.EditorFor(x => x.DataType)
                    </div>
                    <div class="col-lg-6">
                        <label>@T["Kiểu hiển thị"]</label>
                        <select id="@Html.IdFor(x => x.ViewHint)" name="@Html.NameFor(x => x.ViewHint)">
                            <option selected value="">@T["Chọn kiểu hiển thị"]</option>
                        </select>
                    </div>
                </div>


                <div class="form-group m-form__group row">

                    <div class="col-lg-6">
                        <label>@T["Thứ tự hiển thị"]</label>
                        <input type="number" name="Order" value="@Model.Order" class="form-control m-input" />
                    </div>
                    <div class="col-lg-3 choosecolor">
                        <label>@T["Chọn màu chữ: "]&nbsp;</label>
                        <input id="Color" class="spectrum_color" name="Color" value="@(Model.Color.IsNotNullOrEmpty() ? Model.Color : "transparent")">
                    </div>
                    <div class="col-lg-3 choosecolor">
                        <label>@T["Chọn màu border: "]&nbsp;</label>
                        <input id="BackgroundColor" class="spectrum_color" name="BackgroundColor" value="@(Model.BackgroundColor.IsNotNullOrEmpty() ? Model.BackgroundColor : "transparent")">
                    </div>
                </div>
                <div id="div360Mapping" class="form-group m-form__group row" @(Html.Raw(Model.FieldType == FieldType.Mapping ? "" : "style=\"display: none;\""))>
                    <input type="hidden" id="hidMapping360FieldName" name="Mapping360FieldName" value="@Model.Mapping360FieldName" />
                    <input type="hidden" id="hidMapping360FieldType" name="Mapping360FieldType" value="@Model.Mapping360FieldType" />
                    <div class="col-lg-6">
                        <label>@T["Trường ánh xạ 360 view"]</label>
                        <select id="cboMapping360ViewDetail" class="form-control m-input" style="margin: 0px">
                            <option>@T["Chọn trường ánh xạ"]</option>
                        </select>
                    </div>
                    <div id="div360MappingRowOptions" class="col-lg-6" @(Html.Raw(Model.FieldType == FieldType.Mapping && Model.Mapping360FieldType == Mapping360Type.Table ? "" : "style=\"display: none;\""))>
                        <label>@T["Dòng ánh xạ"]</label>
                        <select id="cboMapping360RowOptions" name="Mapping360RowOptions" class="form-control m-input">
                            <option value="SelectedRow">@T["Dòng được chọn"]</option>
                        </select>
                    </div>
                </div>
                <div class="row form-group m-form__group div_choose_color">
                 
                </div>
                <div class="area-hidden-data">
                    <div id="m_repeater">
                        <div class="form-group m-form__group row">
                            <div class="col-lg-12">
                                <label>@T["Giá trị chọn lựa"]</label>
                                <div data-repeater-list="" data-nested="false">
                                    @if (Model.SelectOptionList != null && Model.SelectOptionList.Any())
                                    {
                                        foreach (var option in Model.SelectOptionList)
                                        {
                                            <div data-repeater-item class="m--margin-bottom-10">
                                                <div class="input-group">
                                                    <input type="text" class="form-control" name="@Html.NameFor(model => model.SelectOptionList)" value="@option">
                                                    <div class="input-group-append">
                                                        <a href="javascript:void(0)" data-repeater-delete class="btn btn-danger m-btn m-btn--icon">
                                                            <i class="la la-close"></i>
                                                        </a>
                                                    </div>
                                                </div>
                                            </div>
                                        }
                                    }
                                    else
                                    {
                                        <div data-repeater-item class="m--margin-bottom-10">
                                            <div class="input-group">
                                                <input type="text" class="form-control" name="@Html.NameFor(model => model.SelectOptionList)">
                                                <div class="input-group-append">
                                                    <a href="javascript:void(0)" data-repeater-delete class="btn btn-danger m-btn m-btn--icon">
                                                        <i class="la la-close"></i>
                                                    </a>
                                                </div>
                                            </div>
                                        </div>
                                    }
                                </div>
                            </div>
                            <div class="col-12">
                                <div data-repeater-create="" class="btn btn btn-warning m-btn m-btn--icon">
                                    <span>
                                        <i class="la la-plus"></i>
                                        <span>
                                            @T["Add"]
                                        </span>
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div id="v_repeater">
                        <div class="form-group m-form__group row">
                            <div class="col-lg-12">
                                <label>@T["Thẩm định dữ liệu"]</label>
                                <div data-repeater-list="Validations">
                                    @if (Model.Validations != null && Model.Validations.Any())
                                    {
                                        int count = 0;
                                        foreach (var itm in Model.Validations)
                                        {
                                            <div data-repeater-item class="row m--margin-bottom-10">
                                                <div class="col-6">
                                                    <div class="input-group">
                                                        <input type="text" class="form-control" name="Validations[@(count)].Validation" value="@itm.Validation" />
                                                    </div>
                                                </div>
                                                <div class="col-6">
                                                    <div class="input-group">
                                                        <input type="text" class="form-control" name="Validations[@(count)].ValidationMessage" value="@itm.ValidationMessage" />
                                                        <div class="input-group-append">
                                                            <a href="javascript:void(0)" data-repeater-delete class="btn btn-danger m-btn m-btn--icon">
                                                                <i class="la la-close"></i>
                                                            </a>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            count++;
                                        }
                                    }
                                    else
                                    {
                                        <div data-repeater-item class="row m--margin-bottom-10">
                                            <div class="col-6">
                                                <div class="input-group">
                                                    <input type="text" class="form-control" name="Validations[0].Validation" />
                                                </div>
                                            </div>
                                            <div class="col-6">
                                                <div class="input-group">
                                                    <input type="text" class="form-control" name="Validations[0].ValidationMessage" />
                                                    <div class="input-group-append">
                                                        <a href="javascript:void(0)" data-repeater-delete class="btn btn-danger m-btn m-btn--icon">
                                                            <i class="la la-close"></i>
                                                        </a>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    }
                                </div>
                            </div>
                            <div class="col-12">
                                <div data-repeater-create="" class="btn btn btn-warning m-btn m-btn--icon">
                                    <span>
                                        <i class="la la-plus"></i>
                                        <span>
                                            @T["Add"]
                                        </span>
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div id="b_repeater">
                        <div class="form-group m-form__group row">
                            <div class="col-lg-12">
                                <label>@T["Thẩm định dữ liệu nghiệp vụ"]</label>
                                <div data-repeater-list="BusinessValidations">
                                    @if (Model.BusinessValidations != null && Model.BusinessValidations.Any())
                                    {
                                        int count = 0;
                                        foreach (var itm in Model.BusinessValidations)
                                        {
                                            <div data-repeater-item class="row m--margin-bottom-10">
                                                <div class="col-6">
                                                    <div class="input-group">
                                                        <input type="text" class="form-control" name="BusinessValidations[@(count)].Validation" value="@itm.Validation" />
                                                    </div>
                                                </div>
                                                <div class="col-6">
                                                    <div class="input-group">
                                                        <input type="text" class="form-control" name="BusinessValidations[@(count)].ValidationMessage" value="@itm.ValidationMessage" />
                                                        <div class="input-group-append">
                                                            <a href="javascript:void(0)" data-repeater-delete class="btn btn-danger m-btn m-btn--icon">
                                                                <i class="la la-close"></i>
                                                            </a>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            count++;
                                        }
                                    }
                                    else
                                    {
                                        <div data-repeater-item class="row m--margin-bottom-10">
                                            <div class="col-6">
                                                <div class="input-group">
                                                    <input type="text" class="form-control" name="BusinessValidations[0].Validation" />
                                                </div>
                                            </div>
                                            <div class="col-6">
                                                <div class="input-group">
                                                    <input type="text" class="form-control" name="BusinessValidations[0].ValidationMessage" />
                                                    <div class="input-group-append">
                                                        <a href="javascript:void(0)" data-repeater-delete class="btn btn-danger m-btn m-btn--icon">
                                                            <i class="la la-close"></i>
                                                        </a>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    }
                                </div>
                            </div>
                            <div class="col-12">
                                <div data-repeater-create="" class="btn btn btn-warning m-btn m-btn--icon">
                                    <span>
                                        <i class="la la-plus"></i>
                                        <span>
                                            @T["Add"]
                                        </span>
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div id="addmt_repeater">
                        <div class="form-group m-form__group row">
                            <div class="col-lg-12">
                                <label>@T["Dữ liệu phụ trợ"]</label>
                                <div data-repeater-list="AdditionalMetadatas">
                                    @if (Model.AdditionalMetadatas != null && Model.AdditionalMetadatas.Any())
                                    {
                                        int count = 0;
                                        foreach (var itm in Model.AdditionalMetadatas)
                                        {
                                            <div data-repeater-item class="row m--margin-bottom-10">
                                                <div class="col-6">
                                                    <div class="input-group">
                                                        <input type="text" class="form-control" name="AdditionalMetadatas[@(count)].Key" value="@itm.Key" />
                                                    </div>
                                                </div>
                                                <div class="col-6">
                                                    <div class="input-group">
                                                        <input type="text" class="form-control" name="AdditionalMetadatas[@(count)].Value" value="@itm.Value" />
                                                        <div class="input-group-append">
                                                            <a href="javascript:void(0)" data-repeater-delete class="btn btn-danger m-btn m-btn--icon">
                                                                <i class="la la-close"></i>
                                                            </a>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            count++;
                                        }
                                    }
                                    else
                                    {
                                        <div data-repeater-item class="row m--margin-bottom-10">
                                            <div class="col-6">
                                                <div class="input-group">
                                                    <input type="text" class="form-control" name="AdditionalMetadatas[0].Key" />
                                                </div>
                                            </div>
                                            <div class="col-6">
                                                <div class="input-group">
                                                    <input type="text" class="form-control" name="AdditionalMetadatas[0].Value" />
                                                    <div class="input-group-append">
                                                        <a href="javascript:void(0)" data-repeater-delete class="btn btn-danger m-btn m-btn--icon">
                                                            <i class="la la-close"></i>
                                                        </a>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    }
                                </div>
                            </div>
                            <div class="col-12">
                                <div data-repeater-create="" class="btn btn btn-warning m-btn m-btn--icon">
                                    <span>
                                        <i class="la la-plus"></i>
                                        <span>
                                            @T["Add"]
                                        </span>
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="form-group m-form__group row">
                    <div class="col-lg-6">
                        <label>@T["Nhóm thông tin"]</label>
                        @Html.EditorFor(model => model.DynamicFieldSectionId, new { urlArgument = new object[] { Model.DynamicFormId } })
                    </div>
                    @if (Model.VersioningCalcField.HasValue && Model.VersioningCalcField.Value)
                    {
                        <div class="col-lg-6">
                            <label>@T["Customer Version"]</label>
                            @Html.EditorFor(model => model.CustomerVersions, new { urlArgument = new object[] { Model.DynamicFormId } })
                        </div>
                    }
                </div>
                <div class="form-group m-form__group row area-hidden-data">
                    <div class="col-lg-3">
                        <label class="m-checkbox">
                            @Html.CheckBoxFor(model => model.IsRequired)
                            @T["Trường bắt buộc"]
                            <span></span>
                        </label>
                    </div>
                    <div id="divIsReadOnly" class="col-lg-3" style="@Html.Raw(Model.FieldType == FieldType.Linked ? "" : "display : none;")">
                        <label class="m-checkbox">
                            @Html.CheckBoxFor(model => model.IsReadOnly)
                            @T["Chỉ đọc"]
                            <span></span>
                        </label>
                    </div>
                    <div id="displaySelect" class="col-lg-3">
                        <label class="m-checkbox">
                            @Html.CheckBoxFor(model => model.Display)
                            @T["Hiển thị trên phiếu"]
                            <span></span>
                        </label>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <div style="float: right;">
                    <button type="button" id="btnCancel" class="btn btn-secondary" data-dismiss="modal">
                        @T["Thoát"]
                    </button>
                    <button type="button" id="btnSaveDynamicField" class="btn btn-outline-primary">
                        <i class="la la-save"></i>@T["Lưu"]
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>
@using (Foot())
{
<script type="text/javascript">
    webaby.off('FieldType.Changed');
    webaby.off('DataType.Changed');
    webaby.off('cboMapping360ViewDetail.Changed');
    var staticFieldGroups = @Html.Raw(staticFieldGroups);
    var template = '@Html.Raw(Model.TemplateNameLinkField)';

    $('#@Html.IdFor(x => x.ViewHint)').select2({ width: '100%' });
    var loadViewHint = function () {
        $('#@Html.IdFor(x => x.ViewHint)').empty();
        var dataType = $('#@Html.IdFor(x => x.DataType)').val();
        var fieldType = $('#@Html.IdFor(x => x.FieldType)').val();
        if (fieldType == '' || fieldType == null) {
            return;
        }
        webaby.ajax({
            url: '@Url.Action("GetViewHintList")?type=' + dataType + '&fieldType=' + fieldType,
            success: function (data) {
                $('#@Html.IdFor(x => x.ViewHint)').append('<option selected value="">@T["Chọn kiểu hiển thị"]</option>');
                $(data).each(function () {
                    $('#@Html.IdFor(x => x.ViewHint)').append('<option value="' + this.Value + '">' + this.Text + '</option>');
                });
                $('#@Html.IdFor(x => x.ViewHint)').select2('destroy');
                if ($('#@Html.IdFor(x => x.ViewHint) option[value="@Model.ViewHint"]').length > 0) {
                    $('#@Html.IdFor(x => x.ViewHint)').val('@Model.ViewHint');
                } else {
                    $('#@Html.IdFor(x => x.ViewHint)').val(data[0].Value);
                }
                $('#@Html.IdFor(x => x.ViewHint)').select2({ width: '100%' });
                $('#@Html.IdFor(x => x.ViewHint)').change();
            }
        });
    }

    webaby.on('FieldType.Changed', function (selectedVal) {
        loadViewHint();
    });
    webaby.on('DataType.Changed', function (selectedVal) {
        loadViewHint();
    });

        $(function () {

            webaby.bindAjax($('#divCreateEditFieldModel'));

            @if(Model.FieldType == FieldType.Mapping
                && Model.Mapping360FieldType == Mapping360Type.Table
                && !string.IsNullOrEmpty(Model.Mapping360RowOptions))
            {
            <text>
            var tagValue = [{id: '@Model.Mapping360RowOptions', text: '@Model.Mapping360RowOptions'}];
            $('#cboMapping360RowOptions').select2({
                width: "100%",
                tags: true,
                data: tagValue,
            })
            $('#cboMapping360RowOptions').val('@Model.Mapping360RowOptions').trigger('change');
            </text>
            }

            $('#m_repeater').repeater({
                initEmpty: false,
                show: function () {
                    $(this).slideDown();
                },
                hide: function (deleteElement) {
                    $(this).slideUp(deleteElement);
                }
            });

            $('#cboCustomerVersions').selectpicker();

            $('#v_repeater').repeater({
                initEmpty: false,
                show: function () {
                    $(this).slideDown();
                },
                hide: function (deleteElement) {
                    $(this).slideUp(deleteElement);
                },
            });

            $('#b_repeater').repeater({
                initEmpty: false,
                show: function () {
                    $(this).slideDown();
                },
                hide: function (deleteElement) {
                    $(this).slideUp(deleteElement);
                },
            });

            $('.spectrum_color').spectrum({
                type: "color",
                showPalette: false,
                showPaletteOnly: true,
                togglePaletteOnly: true,
                showInput: true,
                showAlpha: false,
                showButtons: false,
                allowEmpty: true
            });

            $('.sp-replacer.sp-light').click(function (event) {
                var selector = $(this).closest('.choosecolor').find('.spectrum_color').attr('id');
                if ($(this).hasClass('sp-active')) {
                    $(this).toggleClass('sp-active');
                    $('#' + selector).spectrum("show");
                }
                else {
                    $('#' + selector).spectrum("hide");
                }
            });

            $('#addmt_repeater').repeater({
                initEmpty: false,
                show: function () {
                    $(this).slideDown();
                },
                hide: function (deleteElement) {
                    $(this).slideUp(deleteElement);
                },
            });

            @if(Model.FieldType == FieldType.Mapping)
            {
            <text>
            showHideMapping360('@Model.FieldType');
            </text>
            }

            showHideLinkedSource('@Model.FieldType');
            webaby.on('FieldType.Changed', function (selectedVal) {
                showHideLinkedSource(selectedVal);
                showHideMapping360(selectedVal);
            });

            webaby.on('cboMapping360ViewDetail.Changed', function(selectType){
                if(selectType == '@((int)Mapping360Type.Table)'){
                    $('#div360MappingRowOptions').show();
                    if(!$('#cboMapping360RowOptions').hasClass('select2-hidden-accessible')){
                        $('#cboMapping360RowOptions').select2({
                            width: "100%",
                            tags: true
                        })
                    }
                } else {
                    $('#div360MappingRowOptions').hide();
                    $('#cboMapping360RowOptions').val("");
                }
            });

            $('#DataType').change(function(){
                var dataType = $(this).val();
                if (dataType === 'Webaby.Core.PredefinedList.Queries.PredefinedListItemData') {
                    if ($('#cboAdditionalFilter').length > 0) {
                        var addSelect = $('#cboAdditionalFilter');
                        var addSelectRow = addSelect.closest('div.row');
                        if (!addSelectRow.is(':visible')) {
                            addSelectRow.show();
                        }
                    }
                    else {
                        var currentRow = $(this).closest('div.row').next();
                        var col = $('<div class="col-lg-6"></div>');
                        col.append('<label>@T["Giới hạn dữ liệu"]</label>');
                        var addSelect = $('<select id="cboAdditionalFilter" class="form-control" name="AdditionalFilter"><option value="">@T["Chọn vật phẩm"]</option></select>');
                        col.append(addSelect);
                        currentRow.append(col);
                        webaby.ajax({
                            url: '@Url.Action("GetPredefinedListCategories", "PredefinedList").JsRaw()',
                            success: function (data) {
                                if (data) {
                                    $.each(data, function () {
                                        addSelect.append($('<option></option>').attr('value', 'categoryid=' + this.Value.toLowerCase()).text(this.Text));
                                    });
                                    addSelect.select2();
                                }
                            }
                        });
                    }
                }
                    else if (dataType === 'TinyCRM.AppServices.DynamicDefinedTable.DynamicDefinedTableGridData') {
                    $('#divDynamicDefinedTableSchemaId').show();
                }
                else {
                    if ($('#cboAdditionalFilter')) {
                        $('#cboAdditionalFilter').closest('div').remove();
                    }
                    $('#divDynamicDefinedTableSchemaId').hide();
                }
            });

            $('#cboStaticFieldGroups').select2({ 'width': '100%' });

            $('#cboStaticFieldGroups').change(function () {
                $('#cboSourceNames').find('option').not(':first').remove();
                for(var i = 0; i < staticFieldGroups.length; i++) {
                    if($(this).val() == staticFieldGroups[i].Name) {
                        for(var j = 0; j < staticFieldGroups[i].StaticFieldList.length; j++) {
                            var newStaticField = $('<option>', {
                                value: staticFieldGroups[i].StaticFieldList[j].SourceTableName + '.' + staticFieldGroups[i].StaticFieldList[j].SourceFieldName,
                                text: staticFieldGroups[i].StaticFieldList[j].DisplayLabel
                            });
                            newStaticField.attr('sourcetablename', staticFieldGroups[i].StaticFieldList[j].SourceTableName);
                            newStaticField.attr('SourceFieldName', staticFieldGroups[i].StaticFieldList[j].SourceFieldName);
                            newStaticField.attr('DataType', staticFieldGroups[i].StaticFieldList[j].DisplayDataType);
                            newStaticField.attr('ViewHint', staticFieldGroups[i].StaticFieldList[j].ViewHint);
                            newStaticField.attr('DefaultValue', staticFieldGroups[i].StaticFieldList[j].DefaultValue);
                            newStaticField.attr('DisplayLabel', staticFieldGroups[i].StaticFieldList[j].DisplayLabel);
                            $('#cboSourceNames').append(newStaticField);
                        }
                    }
                }
                $('#cboSourceNames').select2({ 'width': '100%' });
            });

            $('#cboSourceNames').select2({ 'width': '100%' });
            $('#cboSourceNames').change(function () {
                if ($(this).val() != '') {
                    var option = $('option:selected', this);
                    $('#SourceFieldName').val(option.attr('sourcefieldname'));
                    $('#SourceTableName').val(option.attr('sourcetablename'));
                    $('#ViewHint').val(option.attr('ViewHint'));
                    $('#DefaultValue').val(option.attr('DefaultValue'));
                    $('#txtFieldName').val(template.replace("{0}", option.attr('DisplayLabel')));
                    $('#txtDisplayName').val(option.attr('DisplayLabel'));
                    $('#cboDataType').select2({ 'width': '100%' }).enable(true);
                    $('#cboDataType').val(option.attr('datatype')).trigger('change');
                    $('#hidDataType').val(option.attr('datatype'));
                    $('#cboDataType').select2({ 'width': '100%' }).enable(false);
                }
                else {
                    $('#SourceFieldName').val('');
                    $('#SourceTableName').val('');
                }
            });

            initLinkedSources('@Html.Raw(Model.SourceTableName)', '@Html.Raw(Model.SourceFieldName)', '@Html.Raw(Model.Name)', '@Html.Raw(Model.DisplayName)');

            $('#btnSaveDynamicField').click(function (e) {
                e.preventDefault();
                var turnOffWarning = $('input[name="TurnOffWarning"]').val();
                var dynamicFormValueCount = $('#DynamicFormValueCount').val();
                if (dynamicFormValueCount > 0 && turnOffWarning === false) {
                    webaby.confirm('@T["Xác nhận lưu thay đổi"]', '@T["Mẫu thông tin này đã có dữ liệu. Bạn có muốn tiếp tục lưu?"]', function () {
                        $('#frmCreateEditDynamicField_useless').submit();
                    });
                } else {
                    $('#frmCreateEditDynamicField_useless').submit();
                }
            });

            $('#frmCreateEditDynamicField_useless').on('success', function() {
                field.reload();
                $('#createEditDynamicFieldModal').modal('hide');
                loadLayoutOverview('@Model.DynamicFormId')
            });

            @if (!string.IsNullOrEmpty(Model.AdditionalFilter))
            {
            <text>
            webaby.ajax({
                url: '@Url.Action("GetPredefinedListCategories", "PredefinedList").JsRaw()',
                success: function (data) {
                    if (data) {
                        $.each(data, function () {
                            $('#cboAdditionalFilter').append($('<option></option>').attr('value', 'categoryid=' + this.Value.toLowerCase()).text(this.Text));
                        });
                        if('@(Model.AdditionalFilter)' != ''){
                            $('#cboAdditionalFilter').val('@(Model.AdditionalFilter.ToLower())');
                        }
                        $('#cboAdditionalFilter').select2({ width : "100%"});
                    }
                }
            });
            </text>
            }
        });



        function showHideLinkedSource(fieldType) {
            if (fieldType == '@FieldType.Linked') {
                $('.source-field').fadeIn();
                $('#divIsReadOnly').show();
                $('#m_repeater').hide();
                $('#cboDataType').select2({ 'width': '100%' }).enable(false);

                $('#frmCreateEditDynamicField_useless').append('<input type="hidden" id="hidDataType" name="DataTypeDisplay" />');
            }
            else {
                $('#m_repeater').show();
                $('.source-field').hide();
                $('#divIsReadOnly').hide();
                $('#SourceFieldName').val('');
                $('#SourceTableName').val('');
                $('#cboDataType').select2({ 'width': '100%' }).enable(true);

                $('#hidDataType').remove();
            }
        }

        function showHideMapping360(fieldType){
            if(fieldType == '@FieldType.Mapping'){
                $('#div360Mapping').show();
                if(!$('#cboMapping360ViewDetail').hasClass('select2-hidden-accessible')){
                    webaby.ajax({
                        url: '@Url.Action("GetMapping360ViewOptions", "DynamicForm")',
                        type: 'post',
                        success: function(data){
                            if(data){
                                $('#cboMapping360ViewDetail optgroup[data-options="ajax-loading"]').remove();
                                $.each(data, function(){
                                    var optGroup = $('<optgroup data-options="ajax-loading" label="' + this.DisplayName + '"></optgroup>');
                                    var idGroup = this.Name;
                                    $.each(this.DefinitionList, function(){
                                        optGroup.append($('<option value="' + idGroup + '.' + this.Name + '" data-options="ajax-loading" data-type="' + this.Type + '">' + this.DisplayName + '</option>'));
                                    });
                                    $('#cboMapping360ViewDetail').append(optGroup);
                                });

                                @if (!string.IsNullOrEmpty(Model.Mapping360FieldName))
                                {
                                <text>
                                $('#cboMapping360ViewDetail').val('@Model.Mapping360FieldName').trigger('change');
                                </text>
                                }

                                $('#cboMapping360ViewDetail').select2({
                                    width : "100%"
                                });

                                $('#cboMapping360ViewDetail').change(function(){
                                    var selectedOption = $('#cboMapping360ViewDetail option:selected');
                                    var selectedValue = selectedOption.val();
                                    var type = selectedOption.attr('data-type');

                                    $('#hidMapping360FieldName').val('' + selectedValue + '');
                                    $('#hidMapping360FieldType').val('' + type + '');
                                    webaby.raise('cboMapping360ViewDetail.Changed', type);
                                });
                            }
                        }
                    })
                }
            } else {
                $('#div360Mapping').hide();
                $('#hidMapping360FieldName').val("");
                $('#hidMapping360FieldType').val("");
                $('#Mapping360RowOptions').val("");
            }
        }

        function initLinkedSources(sourceTable, sourceField, initFieldName, initFieldDisplayName) {
            if (sourceTable != '' && sourceField != '') {
                for(var i = 0; i < staticFieldGroups.length; i++) {
                    for(var j = 0; j < staticFieldGroups[i].StaticFieldList.length; j++) {
                        if (staticFieldGroups[i].StaticFieldList[j].SourceTableName == sourceTable && staticFieldGroups[i].StaticFieldList[j].SourceFieldName == sourceField) {
                            $('#cboStaticFieldGroups').val(staticFieldGroups[i].Name).trigger('change');
                            $('#cboSourceNames').val(sourceTable + '.' + sourceField).trigger('change');

                            $('#txtFieldName').val(initFieldName);
                            $('#txtDisplayName').val(initFieldDisplayName);
                        }
                    }
                }
            }
        }
</script>
}
