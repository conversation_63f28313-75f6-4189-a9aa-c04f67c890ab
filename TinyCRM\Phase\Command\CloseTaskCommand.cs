﻿using Webaby;
using System.Data;
using System.Data.Common;
using Webaby.Data;

namespace TinyCRM.Phase.Command
{
    public class CloseTaskCommand : CommandBase
    {
        public Guid TaskId { get; set; }

        public Guid? BusinessResultId { get; set; }

        public Guid ClosedBy { get; set; }
    }
    internal class CloseTaskCommandHandler : CommandHandlerBase<CloseTaskCommand>
    {
        public CloseTaskCommandHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task ExecuteAsync(CloseTaskCommand command)
        {
            DbCommand cmd = EntitySet.CreateDbCommand();
            cmd.CommandText = "dbo.CloseTask";
            cmd.CommandType = CommandType.StoredProcedure;
            cmd.Parameters.AddRange(new[]
            {
                DbParameterHelper.AddNullableGuid(cmd,"@TaskId", command.TaskId),
                DbParameterHelper.AddNullableGuid(cmd,"@BusinessResultId", command.BusinessResultId),
                DbParameterHelper.AddNullableGuid(cmd,"@ClosedBy", command.ClosedBy),
            });

            await EntitySet.ExecuteNonQueryAsync(cmd);
        }
    }
}