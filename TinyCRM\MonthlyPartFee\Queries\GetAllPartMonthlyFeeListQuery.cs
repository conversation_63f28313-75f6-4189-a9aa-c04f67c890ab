﻿using System;
using System.Data;
using System.Data.Common;
using System.Linq;
using System.Threading.Tasks;
using TinyCRM.PartCustomer;
using Webaby;
using Webaby.Data;

namespace TinyCRM.MonthlyPartFee.Queries
{
    public class GetAllPartMonthlyFeeListQuery : QueryBase<MonthlyPartFeeInfo>
    {
        public Guid? BlockId { get; set; }

        public Guid? FloorId { get; set; }

        public int? Year { get; set; }

        public int? Month { get; set; }

        public MonthlyPartFeeStatus? Status { get; set; }

    }

    public class GetAllPartMonthlyFeeListQueryHandler : QueryHandlerBase<GetAllPartMonthlyFeeListQuery, MonthlyPartFeeInfo>
    {
        public GetAllPartMonthlyFeeListQueryHandler(IServiceProvider serviceProvider) : base(serviceProvider)
        {
        }

        public override async Task<QueryResult<MonthlyPartFeeInfo>> ExecuteAsync(GetAllPartMonthlyFeeListQuery query)
        {
            int startRow = query.Pagination.Index * query.Pagination.Size + 1;
            int endRow = query.Pagination.Index * query.Pagination.Size + query.Pagination.Size;

            var cmd = EntitySet.CreateDbCommand(CommandType.StoredProcedure, "dbo.GetAllPartMonthlyFeeList");
            cmd.Parameters.Add(DbParameterHelper.AddNullableGuid(cmd, "@BlockId", query.BlockId));
            cmd.Parameters.Add(DbParameterHelper.AddNullableGuid(cmd, "@FloorId", query.FloorId));
            cmd.Parameters.Add(DbParameterHelper.AddNullableInt(cmd, "@Year", query.Year));
            cmd.Parameters.Add(DbParameterHelper.AddNullableInt(cmd, "@Month", query.Month));
            cmd.Parameters.Add(DbParameterHelper.AddNullableEnum(cmd, "@Status", query.Status));
            cmd.Parameters.Add(DbParameterHelper.AddNullableInt(cmd, "@StartRow", startRow));
            cmd.Parameters.Add(DbParameterHelper.AddNullableInt(cmd, "@EndRow", endRow));

            var result = (await EntitySet.ExecuteReadCommandAsync<MonthlyPartFeeInfo>(cmd)).ToList();
            await Task.CompletedTask;
            return QueryResult.Create(result);
        }
    }
}