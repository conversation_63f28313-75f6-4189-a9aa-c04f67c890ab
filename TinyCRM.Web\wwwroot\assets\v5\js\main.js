﻿(function (win, $) {
    var id = 0;

    var signalRConnection = null;
    var signalRCurrentConnectedTab = false;

    const SignalR_CurrentConnected_Key = 'SignalR_CurrentConnected';
    const SignalR_HeartBeat_Key = 'SignalR_HeartBeat';
    const SignalR_HeartBeat_Interval = 2000;
    const SignalR_Connected_Timeout = 5000;

    var debugMode = true;
    var guidEmpty = "00000000-0000-0000-0000-000000000000";
    var globalEventPrefix = "globalevent.";
    var hubEventPrefix = "hub.";
    var modals = [];
    var doc = win.document;

    var loadingImageId = newGuid(false);
    var windowId = newGuid(false);

    function getWindowId() {
        return windowId;
    }

    function trigger($element, event, args) {
        var eventHandler = $element.attr("on" + event);
        if (eventHandler && typeof eventHandler == "string") {
            eventHandler = new Function("event", eventHandler);
            $element.removeAttr("on" + event);
            $element.bind(event, eventHandler);
        }
        $element.trigger(event, args);
    }

    function makeGlobalEventName(e) {
        return globalEventPrefix + e.toLowerCase();
    }

    function makeHubEventName(e) {
        return hubEventPrefix + e.toLowerCase();
    }

    function debug(data) {
        if (debugMode && win.console) {
            //console.log(data);
        }
    }

    function newGuid(numbersOnly) {
        var result = "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(
            /[xy]/g,
            function (c) {
                var r = (Math.random() * 16) | 0,
                    v = c == "x" ? r : (r & 0x3) | 0x8;
                return v.toString(16);
            }
        );
        if (numbersOnly) result = result.replace(/-/g, "");
        return result;
    }

    function uniqueId(prefix) {
        id++;
        return (prefix || "element_") + id;
    }

    function addSeparatorsNF(nStr, inD, outD, sep) {
        if (nStr) {
            var reg = /^(\d+(?:[\,]\d+)?)$/
            if (reg.test(nStr.replaceAll(sep, ''))) {
                while (nStr.indexOf(sep) >= 0) {
                    nStr = nStr.replace(sep, "");
                }
                nStr += "";
                var dpos = nStr.indexOf(inD);
                var nStrEnd = "";
                if (dpos != -1) {
                    nStrEnd = outD + nStr.substring(dpos + 1, nStr.length);
                    nStr = nStr.substring(0, dpos);
                }
                var rgx = /(\d+)(\d{3})/;
                while (rgx.test(nStr)) {
                    nStr = nStr.replace(rgx, "$1" + sep + "$2");
                }
                return nStr + nStrEnd;
            }
            return nStr
        }
    }

    var commandTypeEnum = {
        redirect: 0
    };

    var alertTypeEnum = {
        0: "info",
        1: "warn",
        2: "error"
    };
    //A thiếu \uff21
    var accentedMap = [
        {
            base: "A",
            letters:
                "\u0041\u24B6\u00C0\u00C1\u00C2\u1EA6\u1EA4\u1EAA\u1EA8\u00C3\u0100\u0102\u1EB0\u1EAE\u1EB4\u1EB2\u0226\u01E0\u00C4\u01DE\u1EA2\u00C5\u01FA\u01CD\u0200\u0202\u1EA0\u1EAC\u1EB6\u1E00\u0104\u023A\u2C6F"
        },
        { base: "AA", letters: "\uA732" },
        { base: "AE", letters: "\u00C6\u01FC\u01E2" },
        { base: "AO", letters: "\uA734" },
        { base: "AU", letters: "\uA736" },
        { base: "AV", letters: "\uA738\uA73A" },
        { base: "AY", letters: "\uA73C" },
        {
            base: "B",
            letters: "\u0042\u24B7\uFF22\u1E02\u1E04\u1E06\u0243\u0182\u0181"
        },
        {
            base: "C",
            letters:
                "\u0043\u24B8\uFF23\u0106\u0108\u010A\u010C\u00C7\u1E08\u0187\u023B\uA73E"
        },
        {
            base: "D",
            letters:
                "\u0044\u24B9\uFF24\u1E0A\u010E\u1E0C\u1E10\u1E12\u1E0E\u0110\u018B\u018A\u0189\uA779"
        },
        { base: "DZ", letters: "\u01F1\u01C4" },
        { base: "Dz", letters: "\u01F2\u01C5" },
        {
            base: "E",
            letters:
                "\u0045\u24BA\uFF25\u00C8\u00C9\u00CA\u1EC0\u1EBE\u1EC4\u1EC2\u1EBC\u0112\u1E14\u1E16\u0114\u0116\u00CB\u1EBA\u011A\u0204\u0206\u1EB8\u1EC6\u0228\u1E1C\u0118\u1E18\u1E1A\u0190\u018E"
        },
        { base: "F", letters: "\u0046\u24BB\uFF26\u1E1E\u0191\uA77B" },
        {
            base: "G",
            letters:
                "\u0047\u24BC\uFF27\u01F4\u011C\u1E20\u011E\u0120\u01E6\u0122\u01E4\u0193\uA7A0\uA77D\uA77E"
        },
        {
            base: "H",
            letters:
                "\u0048\u24BD\uFF28\u0124\u1E22\u1E26\u021E\u1E24\u1E28\u1E2A\u0126\u2C67\u2C75\uA78D"
        },
        {
            base: "I",
            letters:
                "\u0049\u24BE\uFF29\u00CC\u00CD\u00CE\u0128\u012A\u012C\u0130\u00CF\u1E2E\u1EC8\u01CF\u0208\u020A\u1ECA\u012E\u1E2C\u0197"
        },
        { base: "J", letters: "\u004A\u24BF\uFF2A\u0134\u0248" },
        {
            base: "K",
            letters:
                "\u004B\u24C0\uFF2B\u1E30\u01E8\u1E32\u0136\u1E34\u0198\u2C69\uA740\uA742\uA744\uA7A2"
        },
        {
            base: "L",
            letters:
                "\u004C\u24C1\uFF2C\u013F\u0139\u013D\u1E36\u1E38\u013B\u1E3C\u1E3A\u0141\u023D\u2C62\u2C60\uA748\uA746\uA780"
        },
        { base: "LJ", letters: "\u01C7" },
        { base: "Lj", letters: "\u01C8" },
        { base: "M", letters: "\u004D\u24C2\uFF2D\u1E3E\u1E40\u1E42\u2C6E\u019C" },
        {
            base: "N",
            letters:
                "\u004E\u24C3\uFF2E\u01F8\u0143\u00D1\u1E44\u0147\u1E46\u0145\u1E4A\u1E48\u0220\u019D\uA790\uA7A4"
        },
        { base: "NJ", letters: "\u01CA" },
        { base: "Nj", letters: "\u01CB" },
        {
            base: "O",
            letters:
                "\u004F\u24C4\uFF2F\u00D2\u00D3\u00D4\u1ED2\u1ED0\u1ED6\u1ED4\u00D5\u1E4C\u022C\u1E4E\u014C\u1E50\u1E52\u014E\u022E\u0230\u00D6\u022A\u1ECE\u0150\u01D1\u020C\u020E\u01A0\u1EDC\u1EDA\u1EE0\u1EDE\u1EE2\u1ECC\u1ED8\u01EA\u01EC\u00D8\u01FE\u0186\u019F\uA74A\uA74C"
        },
        { base: "OI", letters: "\u01A2" },
        { base: "OO", letters: "\uA74E" },
        { base: "OU", letters: "\u0222" },
        {
            base: "P",
            letters: "\u0050\u24C5\uFF30\u1E54\u1E56\u01A4\u2C63\uA750\uA752\uA754"
        },
        { base: "Q", letters: "\u0051\u24C6\uFF31\uA756\uA758\u024A" },
        {
            base: "R",
            letters:
                "\u0052\u24C7\uFF32\u0154\u1E58\u0158\u0210\u0212\u1E5A\u1E5C\u0156\u1E5E\u024C\u2C64\uA75A\uA7A6\uA782"
        },
        {
            base: "S",
            letters:
                "\u0053\u24C8\uFF33\u1E9E\u015A\u1E64\u015C\u1E60\u0160\u1E66\u1E62\u1E68\u0218\u015E\u2C7E\uA7A8\uA784"
        },
        {
            base: "T",
            letters:
                "\u0054\u24C9\uFF34\u1E6A\u0164\u1E6C\u021A\u0162\u1E70\u1E6E\u0166\u01AC\u01AE\u023E\uA786"
        },
        { base: "TZ", letters: "\uA728" },
        {
            base: "U",
            letters:
                "\u0055\u24CA\uFF35\u00D9\u00DA\u00DB\u0168\u1E78\u016A\u1E7A\u016C\u00DC\u01DB\u01D7\u01D5\u01D9\u1EE6\u016E\u0170\u01D3\u0214\u0216\u01AF\u1EEA\u1EE8\u1EEE\u1EEC\u1EF0\u1EE4\u1E72\u0172\u1E76\u1E74\u0244"
        },
        { base: "V", letters: "\u0056\u24CB\uFF36\u1E7C\u1E7E\u01B2\uA75E\u0245" },
        { base: "VY", letters: "\uA760" },
        {
            base: "W",
            letters: "\u0057\u24CC\uFF37\u1E80\u1E82\u0174\u1E86\u1E84\u1E88\u2C72"
        },
        { base: "X", letters: "\u0058\u24CD\uFF38\u1E8A\u1E8C" },
        {
            base: "Y",
            letters:
                "\u0059\u24CE\uFF39\u1EF2\u00DD\u0176\u1EF8\u0232\u1E8E\u0178\u1EF6\u1EF4\u01B3\u024E\u1EFE"
        },
        {
            base: "Z",
            letters:
                "\u005A\u24CF\uFF3A\u0179\u1E90\u017B\u017D\u1E92\u1E94\u01B5\u0224\u2C7F\u2C6B\uA762"
        },
        {
            base: "a",
            letters:
                "\u0061\u24D0\uFF41\u1E9A\u00E0\u00E1\u00E2\u1EA7\u1EA5\u1EAB\u1EA9\u00E3\u0101\u0103\u1EB1\u1EAF\u1EB5\u1EB3\u0227\u01E1\u00E4\u01DF\u1EA3\u00E5\u01FB\u01CE\u0201\u0203\u1EA1\u1EAD\u1EB7\u1E01\u0105\u2C65\u0250"
        },
        { base: "aa", letters: "\uA733" },
        { base: "ae", letters: "\u00E6\u01FD\u01E3" },
        { base: "ao", letters: "\uA735" },
        { base: "au", letters: "\uA737" },
        { base: "av", letters: "\uA739\uA73B" },
        { base: "ay", letters: "\uA73D" },
        {
            base: "b",
            letters: "\u0062\u24D1\uFF42\u1E03\u1E05\u1E07\u0180\u0183\u0253"
        },
        {
            base: "c",
            letters:
                "\u0063\u24D2\uFF43\u0107\u0109\u010B\u010D\u00E7\u1E09\u0188\u023C\uA73F\u2184"
        },
        {
            base: "d",
            letters:
                "\u0064\u24D3\uFF44\u1E0B\u010F\u1E0D\u1E11\u1E13\u1E0F\u0111\u018C\u0256\u0257\uA77A"
        },
        { base: "dz", letters: "\u01F3\u01C6" },
        {
            base: "e",
            letters:
                "\u0065\u24D4\uFF45\u00E8\u00E9\u00EA\u1EC1\u1EBF\u1EC5\u1EC3\u1EBD\u0113\u1E15\u1E17\u0115\u0117\u00EB\u1EBB\u011B\u0205\u0207\u1EB9\u1EC7\u0229\u1E1D\u0119\u1E19\u1E1B\u0247\u025B\u01DD"
        },
        { base: "f", letters: "\u0066\u24D5\uFF46\u1E1F\u0192\uA77C" },
        {
            base: "g",
            letters:
                "\u0067\u24D6\uFF47\u01F5\u011D\u1E21\u011F\u0121\u01E7\u0123\u01E5\u0260\uA7A1\u1D79\uA77F"
        },
        {
            base: "h",
            letters:
                "\u0068\u24D7\uFF48\u0125\u1E23\u1E27\u021F\u1E25\u1E29\u1E2B\u1E96\u0127\u2C68\u2C76\u0265"
        },
        { base: "hv", letters: "\u0195" },
        {
            base: "i",
            letters:
                "\u0069\u24D8\uFF49\u00EC\u00ED\u00EE\u0129\u012B\u012D\u00EF\u1E2F\u1EC9\u01D0\u0209\u020B\u1ECB\u012F\u1E2D\u0268\u0131"
        },
        { base: "j", letters: "\u006A\u24D9\uFF4A\u0135\u01F0\u0249" },
        {
            base: "k",
            letters:
                "\u006B\u24DA\uFF4B\u1E31\u01E9\u1E33\u0137\u1E35\u0199\u2C6A\uA741\uA743\uA745\uA7A3"
        },
        {
            base: "l",
            letters:
                "\u006C\u24DB\uFF4C\u0140\u013A\u013E\u1E37\u1E39\u013C\u1E3D\u1E3B\u017F\u0142\u019A\u026B\u2C61\uA749\uA781\uA747"
        },
        { base: "lj", letters: "\u01C9" },
        { base: "m", letters: "\u006D\u24DC\uFF4D\u1E3F\u1E41\u1E43\u0271\u026F" },
        {
            base: "n",
            letters:
                "\u006E\u24DD\uFF4E\u01F9\u0144\u00F1\u1E45\u0148\u1E47\u0146\u1E4B\u1E49\u019E\u0272\u0149\uA791\uA7A5"
        },
        { base: "nj", letters: "\u01CC" },
        {
            base: "o",
            letters:
                "\u006F\u24DE\uFF4F\u00F2\u00F3\u00F4\u1ED3\u1ED1\u1ED7\u1ED5\u00F5\u1E4D\u022D\u1E4F\u014D\u1E51\u1E53\u014F\u022F\u0231\u00F6\u022B\u1ECF\u0151\u01D2\u020D\u020F\u01A1\u1EDD\u1EDB\u1EE1\u1EDF\u1EE3\u1ECD\u1ED9\u01EB\u01ED\u00F8\u01FF\u0254\uA74B\uA74D\u0275"
        },
        { base: "oi", letters: "\u01A3" },
        { base: "ou", letters: "\u0223" },
        { base: "oo", letters: "\uA74F" },
        {
            base: "p",
            letters: "\u0070\u24DF\uFF50\u1E55\u1E57\u01A5\u1D7D\uA751\uA753\uA755"
        },
        { base: "q", letters: "\u0071\u24E0\uFF51\u024B\uA757\uA759" },
        {
            base: "r",
            letters:
                "\u0072\u24E1\uFF52\u0155\u1E59\u0159\u0211\u0213\u1E5B\u1E5D\u0157\u1E5F\u024D\u027D\uA75B\uA7A7\uA783"
        },
        {
            base: "s",
            letters:
                "\u0073\u24E2\uFF53\u00DF\u015B\u1E65\u015D\u1E61\u0161\u1E67\u1E63\u1E69\u0219\u015F\u023F\uA7A9\uA785\u1E9B"
        },
        {
            base: "t",
            letters:
                "\u0074\u24E3\uFF54\u1E6B\u1E97\u0165\u1E6D\u021B\u0163\u1E71\u1E6F\u0167\u01AD\u0288\u2C66\uA787"
        },
        { base: "tz", letters: "\uA729" },
        {
            base: "u",
            letters:
                "\u0075\u24E4\uFF55\u00F9\u00FA\u00FB\u0169\u1E79\u016B\u1E7B\u016D\u00FC\u01DC\u01D8\u01D6\u01DA\u1EE7\u016F\u0171\u01D4\u0215\u0217\u01B0\u1EEB\u1EE9\u1EEF\u1EED\u1EF1\u1EE5\u1E73\u0173\u1E77\u1E75\u0289"
        },
        { base: "v", letters: "\u0076\u24E5\uFF56\u1E7D\u1E7F\u028B\uA75F\u028C" },
        { base: "vy", letters: "\uA761" },
        {
            base: "w",
            letters:
                "\u0077\u24E6\uFF57\u1E81\u1E83\u0175\u1E87\u1E85\u1E98\u1E89\u2C73"
        },
        { base: "x", letters: "\u0078\u24E7\uFF58\u1E8B\u1E8D" },
        {
            base: "y",
            letters:
                "\u0079\u24E8\uFF59\u1EF3\u00FD\u0177\u1EF9\u0233\u1E8F\u00FF\u1EF7\u1E99\u1EF5\u01B4\u024F\u1EFF"
        },
        {
            base: "z",
            letters:
                "\u007A\u24E9\uFF5A\u017A\u1E91\u017C\u017E\u1E93\u1E95\u01B6\u0225\u0240\u2C6C\uA763"
        }
    ];

    function getAccentedMap() {
        return accentedMap;
    }

    function validateInput(input) {
        $("label[for=" + $(input).attr("id") + "]").removeClass("label-danger");
        $("label[for=" + $(input).attr("id") + "]")
            .closest("div")
            .find("div.label-danger")
            .remove();

        if (
            $(input).data("val-required") != null &&
            $(input).data("val-required") != undefined &&
            $(input).data("val-required") != ""
        ) {
            var hasValue = $(input).val() == "";
            if (
                $(input).data("object-required") != null &&
                $(input).data("object-required") != undefined &&
                $(input).data("object-required") != ""
            ) {
                var name = $(input).attr("name");
                name = name + "." + $(input).data("object-required");
                $("input[name='" + name + "'").each(function () {
                    if ($(this).val() != "") {
                        hasValue = true;
                        return false;
                    }
                });
            }
            if (hasValue) {
                $("label[for=" + $(input).attr("id") + "]").addClass("label-danger");
                $("label[for=" + $(input).attr("id") + "]")
                    .closest("div")
                    .append(
                        $(
                            '<div class="form-control-feedback label-danger">' +
                            $(input).data("val-required") +
                            "</div>"
                        )
                    );
                return false;
            }
        }

        if (
            $(input).data("val-regex-pattern") != null &&
            $(input).data("val-regex-pattern") != undefined &&
            $(input).data("val-regex-pattern") != ""
        ) {
            if ($(input).val() != "") {
                var inputVal = $(input).val();
                var regExp = new RegExp($(input).data("val-regex-pattern"));

                if (!regExp.test(inputVal)) {
                    $("label[for=" + $(input).attr("id") + "]").addClass("label-danger");
                    $("label[for=" + $(input).attr("id") + "]")
                        .closest("div")
                        .append(
                            $(
                                '<div class="form-control-feedback label-danger">' +
                                $(input).data("val-regex") +
                                "</div>"
                            )
                        );
                    return false;
                }
            }
        }

        return true;
    }

    function processResult(json, viewRenderer, dataHandler, formId) {
        if (json) {
            if (typeof json == "string") {
                if (json.indexOf('"ajax-result"') == -1) {
                    dataHandler(json);
                    return;
                }
                var tag = $("<div></div>").html(json);
                var jsonText = tag.find("#ajax-result").val();
                if (!jsonText) return;
                json = $.parseJSON(jsonText);
                if (!json) return;
            }
            debug(json);
            if (json.HasView && viewRenderer) viewRenderer(json.ViewData);
            if (json.Alerts) {
                $.each(json.Alerts, function () {
                    webaby.alert({ text: this.Message, type: alertTypeEnum[this.Type] });
                });
            }
            var errors = [];
            if (json.Errors) {
                var targetFormId = formId;
                if (formId == null || formId == undefined) {
                    if (json.Data != undefined && json.Data != null) {
                        if (json.Data.FormId != null && json.Data.FormId != undefined) {
                            targetFormId = json.Data.FormId;
                        }
                    }
                }

                if (
                    targetFormId != "" &&
                    targetFormId != undefined &&
                    targetFormId != null
                ) {
                    $("#" + targetFormId + " .label-danger").removeClass("label-danger");
                    $("#" + targetFormId + " .form-control-danger").removeClass(
                        "form-control-danger"
                    );
                    $("#" + targetFormId + " .form-control-feedback").remove();
                }

                $.each(json.Errors, function () {
                    if (!this.Message) return;

                    errors.push(this.Message);

                    if (
                        targetFormId != "" &&
                        targetFormId != undefined &&
                        targetFormId != null
                    ) {
                        if (
                            this.Target != "" &&
                            this.Target != undefined &&
                            this.Target != null
                        ) {
                            var controlItem = $(
                                "#" + targetFormId + ' input[name="' + this.Target + '"]'
                            );
                            if (
                                controlItem == null ||
                                controlItem == undefined ||
                                controlItem.length == 0
                            ) {
                                controlItem = $(
                                    "#" + targetFormId + ' textarea[name="' + this.Target + '"]'
                                );
                            }
                            if (
                                controlItem == null ||
                                controlItem == undefined ||
                                controlItem.length == 0
                            ) {
                                controlItem = $(
                                    "#" + targetFormId + ' select[name="' + this.Target + '"]'
                                );
                            }

                            if (
                                controlItem != null &&
                                controlItem != undefined &&
                                controlItem.length > 0
                            ) {
                                $("label[for=" + controlItem.attr("id") + "]").addClass(
                                    "label-danger"
                                );
                                $("label[for=" + controlItem.attr("id") + "]")
                                    .closest("div")
                                    .append(
                                        $(
                                            '<div class="form-control-feedback label-danger">' +
                                            this.Message +
                                            "</div>"
                                        )
                                    );
                            }
                        }
                    }
                });
            }
            if (errors.length) {
                webaby.alert({ text: errors.join("<br/>"), type: "error" });
            }
            if (json.HasData && dataHandler) {
                dataHandler(json.Data);
            } else if (json && dataHandler) {
                dataHandler(json);
            }
            if (json.Command) {
                switch (json.Command.Type) {
                    case commandTypeEnum.redirect:
                        win.location.href = json.Command.Params;
                        break;
                }
            }
            return !json.Errors || !json.Errors.length;
        }
        return false;
    }

    function createInputs(data, $form) {
        $.each(data, function (name, value) {
            $form.find('[name="' + name + '"]').remove();
            if ($.isArray(value)) {
                $.each(value, function (index, item) {
                    $('<input type="hidden"/>')
                        .attr("name", name)
                        .val(item)
                        .appendTo($form);
                });
            } else {
                $('<input type="hidden"/>')
                    .attr("name", name)
                    .val(value)
                    .appendTo($form);
            }
        });
    }

    function evalHash(expression, context) {
        return new Function("return {" + expression + "};").call(context);
    }

    function exportForm(formId) {
        $("#" + formId).attr("export-data", "true");
    }

    function bindAjax(context) {
        $(context)
            .find(".ajax-form, [data-ajax]")
            .each(function () {
                var $this = $(this);
                if ($this.is("form")) {
                    $this.find("input, select, textarea").each(function () {
                        $(this).blur(function () {
                            validateInput(this);
                        });
                    });

                    var frameCreated = false;
                    $this.submit(function () {
                        $this.find(".label-danger").removeClass("label-danger");
                        $this
                            .find(".form-control-danger")
                            .removeClass("form-control-danger");
                        $this.find(".form-control-feedback").remove();

                        var formId = $this.attr("ajax-block-containerid");
                        if (formId == "" || formId == undefined || formId == null) {
                            formId = $this.attr("id");
                        }
                        if (formId != "" && formId != undefined && formId != null) {
                            if ($this.attr("export-data") != "true") {
                                mApp.block("#" + formId, {
                                    overlayColor: "#000000",
                                    type: "loader",
                                    state: "primary",
                                    message: "Processing..."
                                });
                            } else {
                                $this.removeAttr("export-data");
                            }
                        }

                        var disabledSelector = $this.data("disabled-selector");
                        if (disabledSelector) {
                            $(disabledSelector).css("pointer-events", "none");
                            $(disabledSelector).css("opacity", "0.5");
                        }

                        if ($this.hasClass("search-remember")) {
                            FormInputCookies.SaveCookies($this);
                        }

                        $this.find("input[type=hidden][name=X-Requested-By]").remove();
                        if ($this.attr("response-type") == "json") {
                            $this.append(
                                '<input type="hidden" name="X-Requested-By" value="webaby-client-ajax"/>'
                            );
                        } else {
                            $this.append(
                                $(
                                    '<input type="hidden" name="X-Requested-By" value="webaby-client"/>'
                                )
                            );
                        }
                        if (frameCreated) {
                            if (!$this.data("submit-by-script")) {
                                $this.find("[data-inputs]").each(function () {
                                    var data = evalHash($(this).data("inputs"), this);
                                    createInputs(data, $this);
                                });
                            }
                            return;
                        }

                        var frameName = uniqueId();
                        var $iframe = $("<iframe></iframe>")
                            .attr("name", frameName)
                            .attr("src", "about:blank")
                            .css({
                                position: "absolute",
                                width: 0,
                                height: 0,
                                marginLeft: -10000,
                                marginTop: -10000
                            })
                            .appendTo(doc.body)
                            .bind("load", function () {
                                if (formId != "" && formId != undefined && formId != null) {
                                    mApp.unblock("#" + formId);
                                }

                                if (disabledSelector) {
                                    $(disabledSelector).css("pointer-events", "unset");
                                    $(disabledSelector).css("opacity", "1");
                                }

                                var w = $iframe.get(0).contentWindow;
                                if (w.location.href == "about:blank") return;
                                var $result = $(w.document).find("#ajax-result");
                                var jsonText;
                                // not json result
                                if (!$result.length) {
                                    // Permission denied
                                    if (
                                        w.location.href
                                            .toLowerCase()
                                            .indexOf("user/useraccessdenied") >= 0
                                    ) {
                                        webaby.alert({
                                            text:
                                                "Bạn bị giới hạn phân quyền chức năng này.<br/>Vui lòng lên hệ quản trị để biết thêm thông tin.",
                                            type: "error"
                                        });
                                    } else {
                                        var preText = $(w.document.body).children("pre");
                                        if (preText.length) {
                                            jsonText = preText.text();
                                        } else {
                                            if (typeof responseText != "undefined")
                                                $this.empty().append(responseText);
                                            bindAjax($this);
                                            bindControls($this);
                                        }
                                    }
                                } else {
                                    jsonText = $result.val();
                                }

                                if (jsonText) {
                                    var $ajaxDebugConsole = $("#ajax-debug-console");

                                    if ($ajaxDebugConsole.length) {
                                        var pre = $("<pre></pre>").text(jsonText);
                                        $ajaxDebugConsole.append(pre);
                                    }

                                    var json = $.parseJSON(jsonText);
                                    var responseData;
                                    var success = processResult(
                                        json,
                                        function (viewData) {
                                            var $placeHolder = $this.data("ajax-placeholder");
                                            if (!$placeHolder) {
                                                $placeHolder = $this;
                                            } else {
                                                $placeHolder = $($placeHolder);
                                            }

                                            $placeHolder.html(viewData);
                                            bindAjax($placeHolder);
                                            bindControls($placeHolder);
                                        },
                                        function (data) {
                                            responseData = data;
                                        },
                                        formId
                                    );

                                    if (success) {
                                        trigger($this, "success", responseData);
                                    } else {
                                        trigger($this, "fail", responseData);
                                    }
                                }
                            });
                        $this.attr("target", frameName);
                        frameCreated = true;
                    });
                } else {
                    $this.click(function (e) {
                        var ajaxData = evalHash($this.data("ajax"), $this.get(0));
                        if (ajaxData.option_confirm) {
                            if (!confirm(ajaxData.option_confirm)) {
                                e.preventDefault();
                                return false;
                            }
                        }

                        var $form = $this.data("ajax-form");
                        if ($form) {
                            $form = $($form);
                        }
                        if (!$form || !$form.length) {
                            var href = $this.attr("href");
                            if (href && href.charAt(0) == "#") $form = $(href);
                        }

                        if (!$form || !$form.length) $form = $this.closest("form");

                        createInputs(ajaxData, $form);

                        $form.data("submit-by-script", true);

                        $form.submit();

                        $form.removeData("submit-by-script");
                    });
                }
            });
    }

    function clearLocalStorage() {
        window.localStorage.clear();
        location.reload();
    }
    function monkeyPatchAutocomplete() {
        var oldFn = $.ui.autocomplete.prototype._renderItem;
        $.ui.autocomplete.prototype._renderItem = function (ul, item) {
            // Escape any regex syntax inside this.term
            var cleanTerm = this.term.replace(/[-\/\\^$*+?.()|[\]{}]/g, "\\$&");
            var cleanText = cleanTerm.replace("::", " ");
            // Build pipe separated string of terms to highlight
            var keywords = $.trim(cleanText)
                .replace("  ", " ")
                .split(" ")
                .join("|");
            // Get the new label text to use with matched terms wrapped
            // in a span tag with a class to do the highlighting
            var re = new RegExp("(" + keywords + ")", "gi");
            var output = item.label.replace(
                re,
                "<span class ='highlighted'>$1</span>"
            );

            return $("<li>")
                .append($("<a>").html(output))
                .appendTo(ul);
        };
    }
    function shortContent(selector, dictionary) {

    }
    function delimiterNum(Num) {
        Num += "";
        Num = Num.replaceAll(".", "");
        x = Num.split(",");
        x1 = x[0];
        x2 = x.length > 1 ? "," + x[1] : "";
        var rgx = /(\d+)(\d{3})/;
        while (rgx.test(x1)) x1 = x1.replace(rgx, "$1" + "." + "$2");
        return x1 + x2;
    }
    function autoComplete(
        selector,
        getTemplateUrl,
        getParamUrl,
        areaForFillUrlParamValue
    ) {
        var resolveObject = function (path, obj) {
            var value = path.split(".").reduce(function (prev, curr) {
                return prev ? prev[curr] : null;
            }, obj || self);
            return value == null ? 0 : value;
        };

        var showModal = function (body, callback, cancel) {
            var modal = $(
                '\
              <div class="modal fade" role="dialog">\
                <div class="modal-dialog">\
                  <div class="modal-content">\
                    <div class="modal-header">\
                      <h4 class="modal-title">Input param</h4>\
                    </div>\
                    <div class="modal-body pb-1">\
                      ' +
                body +
                '\
                    </div>\
                    <div class="modal-footer">\
                      <button type="button" class="btn btn-info modal-submit">OK</button>\
                      <button type="button" class="btn btn-warning modal-cancel">Close</button>\
                    </div>\
                  </div>\
                </div>\
              </div>'
            );
            modal.find(".modal-submit").click(x => {
                modal.data("submit", true);
                modal.modal("hide");
            });
            modal.find(".modal-cancel").click(x => {
                modal.modal("hide");
            });
            modal
                .on("hidden.bs.modal", () => {
                    if (modal.data("submit")) {
                        callback(modal);
                    } else {
                        cancel(modal);
                    }
                    modal.remove();
                })
                .modal("show");
        };
        if (
            getTemplateUrl &&
            getTemplateUrl != "" &&
            getTemplateUrl != "undefined"
        ) {
            webaby.monkeyPatchAutocomplete();
            selector.autocomplete({
                position: { collision: "flip" },
                source: function (req, responseFn) {
                    if (req.term.indexOf("::") >= 0) {
                        var tempTerm = req.term.substring(
                            req.term.indexOf("::"),
                            req.term.length
                        );
                        tempTerm = tempTerm.replace("::", "").trim();
                        webaby.ajax({
                            url: getTemplateUrl,
                            type: "post",
                            data: "queryText=" + tempTerm,
                            success: function (data) {
                                //console.log(data);
                                var a = $.grep(data, function (item, index) {
                                    return true;
                                }).map(v => {
                                    var keywordText = v.Keyword ? "(" + v.Keyword + ")|" : "";
                                    var urlTemplate = v.UrlGetTemplateParameter;
                                    return {
                                        label: urlTemplate
                                            ? keywordText + "Quiz content"
                                            : v.Content.length > 200
                                                ? keywordText + v.Content.substring(0, 200) + "..."
                                                : keywordText + v.Content,
                                        value: urlTemplate
                                            ? keywordText + "Quiz content"
                                            : v.Content,
                                        url: v.UrlGetTemplateParameter,
                                        fullContent: v.Content
                                    };
                                });
                                responseFn(a);
                            }
                        });
                    }
                },
                select: function (value, data) {
                    var selectedValue = data.item.fullContent;
                    var urlApi = data.item.url ? data.item.url : "";
                    var ureg = new RegExp(
                        "^(?<l>[^?\\s]+)(\\??(?<ps>(?<p>[a-z_][a-z_0-9]*)(&(?<pn>[a-z_][a-z_0-9]*))*))?$",
                        "i"
                    );
                    var match = ureg.exec(urlApi);
                    if (match) {
                        var exec = (_params, async) => {
                            var urlApiParam = "{}";
                            if (_params) {
                                urlApiParam =
                                    "{" +
                                    _params
                                        .map(x => '"' + x.Name + '"' + ":" + '"' + x.Value + '"')
                                        .join(",") +
                                    "}";
                                urlApiParam = '{"originalParams": ' + urlApiParam + "}";
                            }
                            webaby.ajax({
                                url: getParamUrl,
                                async: async,
                                type: "post",
                                data: {
                                    urlApi: urlApi,
                                    urlApiParam: urlApiParam
                                },
                                success: function (rs) {
                                    var rs = JSON.parse(rs);
                                    if (rs.status == "FAIL") {
                                        webaby.confirm(
                                            "Building content failed!",
                                            rs.message,
                                            () => { },
                                            () => { }
                                        );
                                        return;
                                    }
                                    rs = $.extend(rs, {
                                        request: JSON.parse(urlApiParam).originalParams,
                                        currentDate: moment(new Date()).format("YYYYMMDD"),
                                        currentDateTime: moment(new Date()).format(
                                            "DD/MM/YYYY HH:mm"
                                        )
                                    });
                                    var matched;
                                    var listReg = () =>
                                        new RegExp(
                                            "\\[(?<lp>[a-z0-9_\\.]+)][{][{](?<lc>((?!{{).)+)[}][}]",
                                            "ig"
                                        );
                                    var reg = () =>
                                        new RegExp(
                                            "(\\[(?<p>[a-z0-9_\\.]+)])|(%%(?<pr>[a-z0-9_\\.]+)%%)",
                                            "ig"
                                        );
                                    var regFo = () =>
                                        new RegExp(
                                            "\\[(?<p>[a-z0-9_\\.]+)\\s*(?<op>[\\+-])\\s*(?<ex>\\d+)(?<un>d|m|y)?]",
                                            "ig"
                                        );
                                    var regMaxMin = () =>
                                        new RegExp(
                                            "\\[((?<t>max|min) (?<ar>[a-z0-9_\\.]+) (?<vl>[a-z0-9_\\.]+))]",
                                            "ig"
                                        );
                                    var regMaxMinFo = () =>
                                        new RegExp(
                                            "\\[((?<t>max|min) (?<ar>[a-z0-9_\\.]+) (?<vl>[a-z0-9_\\.]+)\\s*(?<op>[\\+-])\\s*(?<ex>\\d+)(?<un>d|m|y)?)]",
                                            "ig"
                                        );
                                    var regIfElse = () =>
                                        new RegExp(
                                            '\\[(?<cond>(?<cp>[a-z0-9_\\.]+)(?<op>(>|<|=|!)=?)(?<vp>(\\d+)|null|undefined|("((?!").)+")))][{][{][{](?<ic>((?!{{{).)+)[}][}][}]',
                                            "ig"
                                        );
                                    var regExp = () =>
                                        new RegExp("\\[exp !!(?<e>((?!!!).)+)!!]", "ig");
                                    var regReplace = (str, data) => {
                                        var resolveFo = function (value, operation, ext, extUnit) {
                                            if (operation == "+") ext = ext;
                                            if (operation == "-") ext = ext * -1;
                                            if (!extUnit) {
                                                value = Number(value) + ext;
                                            } else {
                                                var dtFormat = "DD/MM/YYYY HH:mm:ss";
                                                var _value = value;
                                                value = moment(_value, dtFormat);
                                                if (!value._isValid) {
                                                    var dtFormat = "DD/MM/YYYY";
                                                    value = moment(_value, "YYYYMMDD");
                                                }
                                                if (extUnit.toLowerCase() == "m")
                                                    value = value.add(ext, "M").format(dtFormat);
                                                else
                                                    value = value
                                                        .add(ext, extUnit.toLowerCase())
                                                        .format(dtFormat);
                                            }
                                            return value;
                                        };
                                        str = str.replace(regExp(), matched => {
                                            matched = regExp().exec(matched);
                                            return eval(matched.groups["e"]);
                                        });
                                        str = str.replace(regIfElse(), matched => {
                                            matched = regIfElse().exec(matched);
                                            if (
                                                eval(
                                                    "data." +
                                                    matched.groups["cp"] +
                                                    matched.groups["op"] +
                                                    matched.groups["vp"]
                                                )
                                            ) {
                                                return matched.groups["ic"];
                                            }
                                            return "";
                                        });
                                        str = str.replace(listReg(), matched => {
                                            matched = listReg().exec(matched);
                                            var listPath = matched.groups["lp"];
                                            var objectPath = resolveObject(listPath, data);
                                            if (objectPath && $.isArray(objectPath)) {
                                                var listContentFull = "";
                                                $(objectPath).each(function () {
                                                    var objectPathItem = this;
                                                    var listContent = matched.groups["lc"];
                                                    listContent = regReplace(listContent, objectPathItem);
                                                    listContentFull +=
                                                        (listContentFull != "" ? "\n" : "") + listContent;
                                                });
                                                return listContentFull;
                                            }
                                        });
                                        str = str.replace(regMaxMin(), matched => {
                                            matched = regMaxMin().exec(matched);
                                            var type = matched.groups["t"];
                                            var array = matched.groups["ar"];
                                            var valuePart = matched.groups["vl"];
                                            return resolveObject(
                                                valuePart,
                                                resolveObject(array, data).sort((a, b) => {
                                                    a = resolveObject(valuePart, a);
                                                    b = resolveObject(valuePart, b);
                                                    return type == "min"
                                                        ? typeof a == typeof "str"
                                                            ? a.localeCompare(b)
                                                            : a - b
                                                        : typeof a == typeof "str"
                                                            ? b.localeCompare(a)
                                                            : b - a;
                                                })[0]
                                            );
                                        });
                                        str = str.replace(regMaxMinFo(), matched => {
                                            matched = regMaxMinFo().exec(matched);
                                            var type = matched.groups["t"];
                                            var array = matched.groups["ar"];
                                            var valuePart = matched.groups["vl"];
                                            var value = resolveObject(
                                                valuePart,
                                                resolveObject(array, data).sort((a, b) => {
                                                    a = resolveObject(valuePart, a);
                                                    b = resolveObject(valuePart, b);
                                                    return type == "min"
                                                        ? typeof a == typeof "str"
                                                            ? a.localeCompare(b)
                                                            : a - b
                                                        : typeof a == typeof "str"
                                                            ? b.localeCompare(a)
                                                            : b - a;
                                                })[0]
                                            );
                                            var operation = matched.groups["op"];
                                            var ext = Number(matched.groups["ex"]);
                                            var extUnit = matched.groups["un"];
                                            return resolveFo(value, operation, ext, extUnit);
                                        });
                                        str = str.replace(reg(), matched => {
                                            matched = reg().exec(matched);
                                            if (matched.groups["p"]) {
                                                return resolveObject(matched.groups["p"], data);
                                            } else if (matched.groups["pr"]) {
                                                return resolveObject(matched.groups["pr"], rs);
                                            }
                                        });
                                        str = str.replace(regFo(), matched => {
                                            matched = regFo().exec(matched);
                                            var path = matched.groups["p"];
                                            var pathValue = resolveObject(path, data);
                                            var operation = matched.groups["op"];
                                            var ext = Number(matched.groups["ex"]);
                                            var extUnit = matched.groups["un"];
                                            return resolveFo(pathValue, operation, ext, extUnit);
                                        });
                                        str = str.replace(
                                            new RegExp("(\\d{2}/\\d{2}/\\d{4}) 00:00:00", "ig"),
                                            "$1"
                                        );
                                        str = str.replace(new RegExp("##nl##", "ig"), "\n");
                                        return str;
                                    };
                                    selectedValue = regReplace(selectedValue, rs);
                                    if (async) {
                                        selector.val(selectedValue);
                                        selector.text(selectedValue);
                                        setTimeout(() => autosize.update(selector), 500);
                                    } else {
                                        data.item.value = selectedValue;
                                        setTimeout(() => autosize.update(selector), 500);
                                    }
                                }
                            });
                        };
                        urlApi = match.groups["l"];
                        var params = match.groups["ps"];
                        if (params) {
                            params = params.split("&").map(x => {
                                if ($(areaForFillUrlParamValue).length > 0) {
                                    var vals = $(areaForFillUrlParamValue)
                                        .find('[name="' + x + '"]')
                                        .map(function () {
                                            return $(this).val();
                                        });
                                    vals = [...new Set(vals)];
                                    if (vals.length == 1) {
                                        return {
                                            Name: x,
                                            Value: vals[0]
                                        };
                                    }
                                    if (vals.length > 1) {
                                        return {
                                            Name: x,
                                            Value: null,
                                            ValueSelect: vals
                                        };
                                    }
                                }
                                return {
                                    Name: x,
                                    Value: selector.data(x.toLowerCase())
                                };
                            });
                            if (
                                params.filter(x => x.Value == null || x.Value == "").length > 0
                            ) {
                                params = params.sort(
                                    (a, b) => (b.Value ? 1 : 0) - (a.Value ? 1 : 0)
                                );
                                var body = "";
                                $(params).each(function () {
                                    var ip =
                                        '<input data-name="' +
                                        this.Name +
                                        '" class="form-control m-input qc-param" type="text" value="' +
                                        (this.Value ? this.Value : "") +
                                        '">';
                                    if (this.ValueSelect) {
                                        var op = "";
                                        $(this.ValueSelect).each(function () {
                                            op =
                                                op +
                                                '<option value="' +
                                                this +
                                                '">' +
                                                this +
                                                "</option>";
                                        });
                                        ip =
                                            '<select data-name="' +
                                            this.Name +
                                            '" class="form-control m-input qc-param">' +
                                            op +
                                            "</select>";
                                    }
                                    body +=
                                        '\
                                <div class="form-group m-form__group row">\
                                    <label class="col-3 col-form-label" style="text-align:right">' +
                                        this.Name +
                                        '</label>\
                                    <div class="col-9">\
                                        ' +
                                        ip +
                                        "\
                                    </div>\
                                </div>";
                                });
                                showModal(
                                    body,
                                    m => {
                                        var pValue = [];
                                        $(m)
                                            .find(".qc-param")
                                            .each(function () {
                                                pValue.push({
                                                    Name: $(this).data("name"),
                                                    Value: $(this).val()
                                                });
                                            });
                                        if (
                                            pValue.filter(x => x.Value == null || x.Value == "")
                                                .length > 0
                                        ) {
                                            selector.val("");
                                            selector.text("");
                                        } else {
                                            exec(pValue, true);
                                        }
                                    },
                                    m => {
                                        selector.val("");
                                        selector.text("");
                                    }
                                );
                                data.item.value = "building content..";
                            } else {
                                exec(params, false);
                            }
                        } else {
                            exec(null, false);
                        }
                    }
                }
            });
        }
    }

    function setUserCulture(culture) {
        Cookies.set("UserCulture", culture);
        location.reload();
    }

    function isHubGroupJoined(hubGroup) {
        var stateObj = JSON.parse(localStorage.getItem("signalr_state"));
        if (stateObj && stateObj.hubGroups) {
            var hubGroups = JSON.parse(stateObj.hubGroups);
            if (hubGroups) {
                for (var i = 0; i < hubGroups.length; i++) {
                    if (hubGroups[i] == hubGroup) {
                        return true;
                    }
                }
            }
        }
        return false;
    }

    function parseObjectFromQueryString(queryParam) {
        var params = new Array();
        var queries, temp, i, l;

        // Split into key/value pairs
        queries = queryParam.split("&");

        // Convert the array of strings into an object
        for (i = 0, l = queries.length; i < l; i++) {
            temp = queries[i].split("=");

            var param = new Object();
            param.Name = decodeURIComponent(temp[0] || "");
            param.Value = decodeURIComponent(temp[1] || "");

            params.push(param);
        }

        return params;
    }

    function bindFormData(frm, dataArray, ignoreContainList) {
        $.each(dataArray, function () {
            var name = this.Name;
            var isIgnore = false;
            if (ignoreContainList != null && ignoreContainList != undefined) {
                $.each(ignoreContainList, function () {
                    if (name.includes(this)) {
                        isIgnore = true;
                    }
                });
            }
            if (!isIgnore) {
                $("#" + frm + ' input[name="' + this.Name + '"]').val(this.Value);
                $("#" + frm + ' select[name="' + this.Name + '"]')
                    .val(this.Value)
                    .trigger("change");
            }
        });
    }

    var webaby = {
        getWindowId: getWindowId,
        debug: debug,
        parseObjectFromQueryString: parseObjectFromQueryString,
        bindFormData: bindFormData,
        lastFocusSelector: function (sel) {
            var data = sel.val();
            sel.focus().val('').val(data);
        },
        removeVietnameseTones: function (str) {
            str = str.replace(/à|á|ạ|ả|ã|â|ầ|ấ|ậ|ẩ|ẫ|ă|ằ|ắ|ặ|ẳ|ẵ/g, "a");
            str = str.replace(/è|é|ẹ|ẻ|ẽ|ê|ề|ế|ệ|ể|ễ/g, "e");
            str = str.replace(/ì|í|ị|ỉ|ĩ/g, "i");
            str = str.replace(/ò|ó|ọ|ỏ|õ|ô|ồ|ố|ộ|ổ|ỗ|ơ|ờ|ớ|ợ|ở|ỡ/g, "o");
            str = str.replace(/ù|ú|ụ|ủ|ũ|ư|ừ|ứ|ự|ử|ữ/g, "u");
            str = str.replace(/ỳ|ý|ỵ|ỷ|ỹ/g, "y");
            str = str.replace(/đ/g, "d");
            str = str.replace(/À|Á|Ạ|Ả|Ã|Â|Ầ|Ấ|Ậ|Ẩ|Ẫ|Ă|Ằ|Ắ|Ặ|Ẳ|Ẵ/g, "A");
            str = str.replace(/È|É|Ẹ|Ẻ|Ẽ|Ê|Ề|Ế|Ệ|Ể|Ễ/g, "E");
            str = str.replace(/Ì|Í|Ị|Ỉ|Ĩ/g, "I");
            str = str.replace(/Ò|Ó|Ọ|Ỏ|Õ|Ô|Ồ|Ố|Ộ|Ổ|Ỗ|Ơ|Ờ|Ớ|Ợ|Ở|Ỡ/g, "O");
            str = str.replace(/Ù|Ú|Ụ|Ủ|Ũ|Ư|Ừ|Ứ|Ự|Ử|Ữ/g, "U");
            str = str.replace(/Ỳ|Ý|Ỵ|Ỷ|Ỹ/g, "Y");
            str = str.replace(/Đ/g, "D");
            // Some system encode vietnamese combining accent as individual utf-8 characters
            // Một vài bộ encode coi các dấu mũ, dấu chữ như một kí tự riêng biệt nên thêm hai dòng này
            str = str.replace(/\u0300|\u0301|\u0303|\u0309|\u0323/g, ""); // ̀ ́ ̃ ̉ ̣  huyền, sắc, ngã, hỏi, nặng
            str = str.replace(/\u02C6|\u0306|\u031B/g, ""); // ˆ ̆ ̛  Â, Ê, Ă, Ơ, Ư
            // Bỏ dấu câu, kí tự đặc biệt
            str = str.replace(/!|@@|%|\^|\*|\(|\)|\+|\=|\<|\>|\?|\/|,|\.|\:|\;|\'|\"|\&|\#|\[|\]|~|\$|`|-|{|}|\||\\/g, '');
            str = str.replace(/\s/g, '_');
            return str;
        },
        format: function (text) {
            if (text == null) return "";
            if (typeof text == "string") {
                return this.format(Array.prototype.slice.call(arguments, 1), text);
            }
            var args = text;
            text = arguments[1];
            if (text == null) return "";
            text = text.toString();
            return text.replace(/{([^}]+)}/g, function (match, group1) {
                return args[group1];
            });
        },
        highlight: function (keyword, container, wordsOnly) {
            if (keyword) {
                var reList = keyword.split(" ");
                var eleContainer = document.getElementById(container);
                var innerHtml = eleContainer.innerHTML;
                for (var i = 0; i < reList.length; i++) {
                    var word = reList[i];
                    for (var j = 0; j < accentedMap.length; j++) {
                        word = word.replace(
                            accentedMap[j].base,
                            "[" + accentedMap[j].letters + "]"
                        );
                    }
                    var pattern = "(" + word + ")";
                    var regReList = new RegExp(pattern, "gi");
                    innerHtml = innerHtml.replace(
                        regReList,
                        "<span style='background-color:yellow'>$1</span>"
                    );
                }
                eleContainer.innerHTML = innerHtml;
            }
        },
        once: function (callback) {
            var callbackId = uniqueId("callback_");
            var called = false;
            win[callbackId] = function () {
                if (called) return null;
                called = true;
                delete win[callbackId];
                return callback.apply(this, arguments);
            };
            return callbackId;
        },
        graphic: {
            imageSize: function (source, onComplete) {
                var image = new Image();
                image.src = source;
                image.onload = onComplete;
                return this;
            }
        },
        base64toFile: function (data, contentType, fileName) {
            var base64ToArrayBuffer = function (data) {
                var bString = window.atob(data);
                var bLength = bString.length;
                var bytes = new Uint8Array(bLength);
                for (var i = 0; i < bLength; i++) {
                    var ascii = bString.charCodeAt(i);
                    bytes[i] = ascii;
                }
                return bytes;
            };
            var bufferArray = base64ToArrayBuffer(data);
            var blobStore = new Blob([bufferArray], { type: contentType });
            if (window.navigator && window.navigator.msSaveOrOpenBlob) {
                window.navigator.msSaveOrOpenBlob(blobStore);
                return;
            }
            var data = window.URL.createObjectURL(blobStore);
            var link = document.createElement('a');
            document.body.appendChild(link);
            link.href = data;
            link.download = fileName;
            link.click();
            window.URL.revokeObjectURL(data);
            link.remove();
        },
        base64toFilePreview: function (data, contentType, fileName) {
            var newWindow = window.open('');
            var height = contentType.startsWith('image/') ? 'auto' : '100%';
            newWindow.document.write('<embed style="border:0" width="100%" height="' + height + '" src="data:' + contentType + ';base64, ' + data + '" type="' + contentType + '"></embed>');
            newWindow.document.title = 'Preview: ' + fileName;
        },
        startConnectHub: function () {

            console.log('SignalR_CurrentConnected_Key: ' + windowId + '. ' + Date.now());

            const now = Date.now();

            localStorage.setItem(SignalR_CurrentConnected_Key, windowId);
            localStorage.setItem(SignalR_HeartBeat_Key, now.toString());
            signalRCurrentConnectedTab = true;

            if (signalRConnection == null) {

                signalRConnection = new signalR.HubConnectionBuilder()
                    .withUrl(ApplicationSettings.Url + "/eventHub")
                    .withAutomaticReconnect()
                    .build();

                signalRConnection.on("MessageReceived", (eventName, eventData) => {
                    //console.log('SignalR.MessageReceived: ' + eventName + '. ' + Date.now())
                    //console.log(eventData)
                    eventName = makeHubEventName(eventName);
                    webaby.raise(eventName, eventData);
                });
            }

            if (signalRConnection.state === signalR.HubConnectionState.Disconnected) {
                signalRConnection.start().then(function () {
                    webaby.raise("HubConnected");
                    console.log("SignalR Hub Connected: " + Date.now());
                }).catch(function (err) {
                    return console.error(err.toString());
                });
            }

            return this;
        },
        stopConnectHub: function () {
            signalRCurrentConnectedTab = false;
            localStorage.removeItem(SignalR_CurrentConnected_Key);
            if (signalRConnection != null) {
                signalRConnection.stop();
            }
            return this;
        },
        startHub: function (params) {

            if (signalRConnection && signalRConnection.state == signalR.HubConnectionState.Connected) {

                if (params != null && params != undefined) {
                    if (params.HubGroups != null && params.HubGroups != undefined) {
                        for (var i = 0; i < params.HubGroups.length; i++) {
                            signalRConnection.invoke("JoinGroup", params.HubGroups[i]);
                            console.log("HubGroupJoined: " + params.HubGroups[i]);
                            webaby.raise("HubGroupJoined-" + params.HubGroups[i]);
                        }
                    }
                }
            }

            return this;
        },
        modal: function (data, modalId, containerId, backdrop) {
            if (
                containerId === undefined ||
                containerId == null ||
                containerId == ""
            ) {
                containerId = "divBody";
            }
            if (backdrop === undefined) {
                backdrop = "static";
            }
            if ($("#" + modalId).length !== 0) {
                $("#" + modalId).remove();
            }

            if (typeof data !== "object") {
                //var modalContent = $(
                //    '<div id="' +
                //    modalId +
                //    '" class="modal fade" role="dialog" aria-hidden="true"></div>'
                //).html(data);

                const modalElement = $(
                    '<div id="' + modalId + '" class="modal fade" role="dialog" aria-hidden="true">' + data + '</div>'
                );

                $("#" + containerId).append(modalElement);
                $("#" + modalId).modal({
                    backdrop: backdrop,
                    keyboard: false,
                    show: true
                });

                // đã Show the modal = true không cần show nữa
                /*$("#" + modalId).modal("show");*/

                // Use .one() instead of .on() for auto cleanup
                $("#" + modalId).one("hidden.bs.modal", function () {
                    $("#" + modalId).remove();
                });
            }
        },
        bindAjax: bindAjax,
        exportForm: exportForm,
        validateInput: validateInput,
        validateByApiAsync: async function (inputControl) {
            var name = $(inputControl).attr('name');
            var value = $(inputControl).val();
            if (value == null || value == '') {
                $(inputControl).removeClass('validate-by-api-faild');
                $(inputControl).next('span.range-number2-container').find('input').removeClass('validate-by-api-faild');
                return Promise.resolve();
            }
            //console.log('xxx', value)
            var api = $(inputControl).attr('validate-by-api');
            var apiType = $(inputControl).attr('validate-by-api-type');
            if (apiType == 'cep') {
                api = appConfigs.AppURL.replace(/\/+$/, '').trim() + '/' + api.replace(/^\/+/, '').trim()
            } else if (apiType == 'core') {
                api = appConfigs.AppURL.replace(/\/+$/, '').trim() + '/AdditionalDisplay/ValidateInput?apiUrl=' + api.replace(/^\/+/, '').trim();
            }
            var data = {
                value: value,
                customerCode: $('[data-customer-code]').data('customer-code')
            };
            return new Promise((rs, rj) => {
                webaby.ajax({
                    type: 'post',
                    url: api,
                    data: data,
                    success: function (data) {
                        if (!data.Success) {
                            webaby.alert({
                                type: 'error',
                                text: 'Validate "' + name + '": ' + data.Message
                            });
                            $(inputControl).addClass('validate-by-api-faild');
                            $(inputControl).next('span.range-number2-container').find('input').addClass('validate-by-api-faild');
                        } else {
                            $(inputControl).removeClass('validate-by-api-faild');
                            $(inputControl).next('span.range-number2-container').find('input').removeClass('validate-by-api-faild');
                        }
                    },
                    error: function () {
                        webaby.alert({
                            type: 'error',
                            text: 'Validate "' + name + '": Call API error'
                        });
                        $(inputControl).addClass('validate-by-api-faild');
                        $(inputControl).next('span.range-number2-container').find('input').addClass('validate-by-api-faild');
                    },
                    complete: function () {
                        $(inputControl).addClass('has-validate-by-api-first')
                        rs();
                    }
                });
                setTimeout(function () {
                    rj();
                }, 30000);
            });
        },
        setUserCulture: setUserCulture,
        ajax: function (params) {
            params = $.extend(params, {});
            params.headers = $.extend(params.headers, {
                "X-Requested-By": "webaby-client"
            });
            var success = params.success;
            var view = params.view;
            delete params.view;
            params.success = function (data) {
                processResult(data, view, success);
            };

            if (params.containterId) {
                params.beforeSend = function () {
                    mApp.block("#" + params.containterId, {
                        overlayColor: "#000000",
                        type: "loader",
                        state: "primary",
                        message: "Processing..."
                    });
                };
                params.complete = function () {
                    mApp.unblock("#" + params.containterId);
                };
            }
            if (params.disabledSelector) {
                params.beforeSend = function () {
                    params.disabledSelector.css("pointer-events", "none");
                    params.disabledSelector.css("opacity", "0.5");
                };
                params.complete = function () {
                    params.disabledSelector.css("pointer-events", "unset");
                    params.disabledSelector.css("opacity", "1");
                };
            }

            if (params.loadingContainerId) {
                params.beforeSend = function () {
                    mApp.block("#" + params.loadingContainerId, {
                        overlayColor: "#000000",
                        type: "loader",
                        state: "primary",
                        message: "Loading...",
                        centerY: false,
                        centerX: false
                    });
                };

                params.complete = function () {
                    mApp.unblock("#" + params.loadingContainerId);
                };
            }

            $.ajax(params);
            return this;
        },
        confirm: function (
            confirmHeader,
            confirmMessage,
            confirmedFunc,
            cancelFunc,
            label
        ) {
            var manualCancel = true;
            var modal = $(
                '<div class="modal fade" id="" role="dialog" aria-hidden="true">\
                                <div class="modal-dialog modal-lg" role="document">\
                                    <div class="modal-content">\
                                        <div class="modal-header">\
                                            <h4 class="modal-title">' +
                confirmHeader +
                '</h4>\
                                        </div>\
                                        <div class="modal-body" style="max-height:500px;overflow-y:auto">\
                                            ' +
                confirmMessage +
                '\
                                        </div>\
                                        <div class="modal-footer">\
                                            <button id="btnSystemConfirmCancel" type="button" class="btn btn-warning" data-dismiss="modal">'+ (label?.cancel ?? 'Hủy') + '</button>\
                                            <button id="btnSystemConfirmConfirm" type="button" class="btn btn-success">'+ (label?.confirm ?? 'Xác nhận') + '</button>\
                                        </div>\
                                    </div>\
                                </div>\
                            </div>'
            );
            $("#divBody").append(modal);
            modal.modal("show");
            modal.on("hidden.bs.modal", function () {
                if (manualCancel) {
                    if (typeof cancelFunc == "function") {
                        cancelFunc();
                    }
                }
                modal.remove();
            });
            $("#btnSystemConfirmCancel").click(function (e) {
                if (typeof cancelFunc == "function") {
                    cancelFunc();
                    manualCancel = false;
                }
                modal.modal("hide");
            });
            $("#btnSystemConfirmConfirm").click(async function (e) {
                if (typeof confirmedFunc == "function") {
                    if (modal.find(".modal-body .confirm-return")) {
                        await confirmedFunc(modal.find(".modal-body .confirm-return").val(), modal);
                    } else {
                        await confirmedFunc(modal);
                    }
                    manualCancel = false;
                    modal.modal("hide");
                }
            });
        },
        route: function (route, tokens) {
            if (!tokens) return route;
            var pattern = [];
            $.each(tokens, function (name) {
                pattern.push(name);
            });
            return route.replace(new RegExp(pattern.join("|"), "g"), function (m) {
                return tokens[m];
            });
        },
        isHubGroupJoined: isHubGroupJoined,
        guidEmpty: guidEmpty,
        newGuid: newGuid,
        uniqueId: uniqueId,
        addSeparatorsNF: addSeparatorsNF,
        monkeyPatchAutocomplete: monkeyPatchAutocomplete,
        delimiterNum: delimiterNum,
        autoComplete: autoComplete,
        clearLocalStorage: clearLocalStorage,
        pDownload: function () {
            for (var i = 0; i < arguments.length; i++) {
                var iframe = $('<iframe style="visibility: collapse;"></iframe>');
                $("body").append(iframe);
                var content = iframe[0].contentDocument;
                var form = '<form action="' + arguments[i] + '" method="POST"></form>';
                //console.log(form);
                content.write(form);
                $("form", content).submit();
                setTimeout(
                    (function (iframe) {
                        return function () {
                            iframe.remove();
                        };
                    })(iframe),
                    2000
                );
            }
        },
        on: function (e, handler) {
            $(doc).bind(
                makeGlobalEventName(e),
                (handler.__wrapper = function () {
                    var args = [];
                    for (var i = 1; i < arguments.length; i++) {
                        args.push(arguments[i]);
                    }
                    handler.apply(this, args);
                })
            );
            return this;
        },
        off: function (e) {
            $(doc).unbind(makeGlobalEventName(e));
            return this;
        },
        raise: function (e, a) {
            var args = [];
            for (var i = 1; i < arguments.length; i++) {
                args.push(arguments[i]);
            }
            $(doc).trigger(makeGlobalEventName(e), args);
            return this;
        },
        resize: function (width, height) {
            // resize window to normal size (etc. 400x400)
            win.resizeTo(400, 400);
            // must delay before resizing
            setTimeout(function () {
                $(win).unbind("resize", arguments.callee);
                var windowWidth = $(win).width();
                var windowHeight = $(win).height();
                var offsetWidth = 400 - windowWidth;
                var offsetHeight = 400 - windowHeight;
                //alert(width + offsetWidth);
                win.resizeTo(width + offsetWidth, height + offsetHeight);
                return false;
            }, 100);
            return this;
        },
        reopenModal: function (overwriteOptions) {
            var modal = modals.pop();
            if (modal) {
                overwriteOptions = $.extend(modal.data("options"), overwriteOptions);
                modal.modal("hide");
                this.alert(overwriteOptions);
            }
            return this;
        },
        closeModal: function (closeAll) {
            if (closeAll) {
                $.each(modals.slice(), function () {
                    this.modal("close");
                });
            } else {
                var modal = modals.pop();
                if (modal) modal.modal("hide");
            }
            return this;
        },
        initSelect: function (el, data, width, hasSearch) {
            el.find("option:not(:first)").remove();
            $.each(data, function () {
                el.append(
                    $("<option></option>")
                        .attr("value", this.Value)
                        .text(this.Text)
                );
            });
            if (el.attr("data-value") != "") {
                var id = el.attr("data-value");
                if (el.find('option[value="' + id + '"]').length > 0) {
                    el.val(id);
                }
            }
            var selectWidth = "100%";
            if (width != null && width != undefined && width != "") {
                selectWidth = width;
            }
            el.select2({ width: selectWidth, minimumResultsForSearch: hasSearch });
        },
        initSelectTree: function (el, data, options) {
            $.each(data, function () {
                var optionItem = $(
                    '<option data-options="ajax-loading" ' +
                    (this.Value === "" && "@(allowSelectNonLeaf)" == "False"
                        ? 'data-selectable="false"'
                        : 'data-selectable="true"') +
                    "></option>"
                )
                    .attr("value", this.Node)
                    .attr("data-pup", this.Parent)
                    .attr("class", "l" + this.Level + (this.IsLeaf ? "" : " non-leaf"))
                    .attr("data-node-value", this.Value)
                    .attr("data-node-data", this.Data)
                    .text(this.Text);
                el.append(optionItem);
            });
            if (el.attr("data-value") != "") {
                var id = el.attr("data-value");
                el.val(id);
            }
            el.select2ToTree(options);
        },
        alert: function (params, completedFunc) {
            toastr.options = {
                closeButton: true,
                debug: false,
                newestOnTop: false,
                progressBar: false,
                positionClass: "toast-top-right",
                preventDuplicates: false,
                onclick: null,
                showDuration: "300",
                hideDuration: "1000",
                timeOut: "5000",
                extendedTimeOut: "1000",
                showEasing: "swing",
                hideEasing: "linear",
                showMethod: "fadeIn",
                hideMethod: "fadeOut",
                onclick: params.onclick
            };
            if (params.type == "success") {
                toastr.success(params.text);
            } else if (params.type == "info") {
                toastr.info(params.text);
            } else if (params.type == "warn") {
                toastr.warning(params.text);
            } else {
                toastr.error(params.text);
            }
            return this;
        }
    };

    webaby.postify = function (value) {
        var result = {};

        var buildResult = function (object, prefix) {
            for (var key in object) {
                var postKey = isFinite(key)
                    ? (prefix != "" ? prefix : "") + "[" + key + "]"
                    : (prefix != "" ? prefix + "." : "") + key;

                switch (typeof object[key]) {
                    case "number":
                    case "string":
                    case "boolean":
                        result[postKey] = object[key];
                        break;
                    case "object":
                        if (object[key] && object[key].toUTCString)
                            result[postKey] = object[key].toUTCString().replace("UTC", "GMT");
                        else {
                            buildResult(object[key], postKey != "" ? postKey : key);
                        }
                }
            }
        };

        buildResult(value, "");

        return result;
    };

    function bindControls(context) {
        $(context)
            .find("[data-ui]")
            .each(function () {
                var $this = $(this);
                var methodName = $this.data("ui");
                var method = $this[methodName];
                if (!method) return;
                var params = new Function(
                    "return [" + ($(this).data("ui-params") || "") + "];"
                ).call(this);
                method.apply($this, params);
            });
    }

    function checkSignalRConnectTab() {

        const currentConnectedWindowId = localStorage.getItem(SignalR_CurrentConnected_Key);
        const heartbeatRaw = parseInt(localStorage.getItem(SignalR_HeartBeat_Key) || '0', 10);
        const lastHeartbeat = heartbeatRaw ? parseInt(heartbeatRaw, 10) : 0;
        const now = Date.now();

        // console.log('Current WindowId: ' + webaby.getWindowId() + '. CurrentConnectedWindowId: ' + currentConnectedWindowId + '. (now - lastHeartbeat): ' + ((now - lastHeartbeat)));
        const currentConnectedExpired = !lastHeartbeat || (now - lastHeartbeat) > SignalR_Connected_Timeout;

        if (!currentConnectedWindowId || currentConnectedExpired) {
            webaby.startConnectHub();
        }
        else if (currentConnectedWindowId === webaby.getWindowId()) {
            localStorage.setItem(SignalR_HeartBeat_Key, now.toString());
        }
        else {
            if (signalRCurrentConnectedTab) {
                webaby.stopConnectHub();
            }
        }
    }

    $(function () {
        $(doc).on("change", "[data-checker]", function () {
            var groupName = $(this).data("checker");
            var checkedCount = 0;
            var totalCheckbox = $('[data-checker="' + groupName + '"]').each(
                function () {
                    if ($(this).prop("checked")) checkedCount++;
                }
            ).length;
            webaby.raise("checker-change", {
                group: groupName,
                checked: checkedCount,
                total: totalCheckbox
            });
        });

        bindAjax(doc);

        // bind jquery ui controls
        bindControls(doc);

        webaby.on('Layout-Loaded', function () {
            checkSignalRConnectTab();
            setInterval(checkSignalRConnectTab, SignalR_HeartBeat_Interval);
        });

        window.addEventListener('beforeunload', () => {
            const currentConnectedWindowId = localStorage.getItem(SignalR_CurrentConnected_Key);
            if (signalRCurrentConnectedTab && currentConnectedWindowId == webaby.getWindowId()) {
                webaby.stopConnectHub();
            }
        });

        var windowsList = [];
        if (localStorage.getItem("window_list")) {
            windowsList = JSON.parse(localStorage.getItem("window_list"));
        }
        if (windowsList.indexOf(windowId) === -1) {
            windowsList.push(windowId);
            localStorage.setItem("window_list", JSON.stringify(windowsList));
        }

        window.addEventListener("beforeunload", function () {
            var oldWindowsList = [];
            if (localStorage.getItem("window_list")) {
                oldWindowsList = JSON.parse(localStorage.getItem("window_list"));
            }
            if (oldWindowsList.indexOf(windowId) !== -1) {
                oldWindowsList.splice(oldWindowsList.indexOf(windowId), 1);
                localStorage.setItem("window_list", JSON.stringify(oldWindowsList));
            }
        });
    });

    // resize page content
    $(win)
        .resize(function () {
            //$('.page-content').css('min-height', Math.max($(window).height() - 75, $('.page-sidebar').outerHeight()));
        })
        .resize();

    win.webaby = webaby;
})(window, window.jQuery);

/**
 * jQuery JSON plugin 2.4.0
 *
 * <AUTHOR> Harris, 2009-2011
 * <AUTHOR> Tijhof, 2011-2012
 * @source This plugin is heavily influenced by MochiKit's serializeJSON, which is
 *         copyrighted 2005 by Bob Ippolito.
 * @source Brantley Harris wrote this plugin. It is based somewhat on the JSON.org
 *         website's http://www.json.org/json2.js, which proclaims:
 *         "NO WARRANTY EXPRESSED OR IMPLIED. USE AT YOUR OWN RISK.", a sentiment that
 *         I uphold.
 * @license MIT License <http://www.opensource.org/licenses/mit-license.php>
 */
(function ($) {
    "use strict";

    var escape = /["\\\x00-\x1f\x7f-\x9f]/g,
        meta = {
            "\b": "\\b",
            "\t": "\\t",
            "\n": "\\n",
            "\f": "\\f",
            "\r": "\\r",
            '"': '\\"',
            "\\": "\\\\"
        },
        hasOwn = Object.prototype.hasOwnProperty;

    /**
     * jQuery.toJSON
     * Converts the given argument into a JSON representation.
     *
     * @param o {Mixed} The json-serializable *thing* to be converted
     *
     * If an object has a toJSON prototype, that will be used to get the representation.
     * Non-integer/string keys are skipped in the object, as are keys that point to a
     * function.
     *
     */
    $.toJSON =
        typeof JSON === "object" && JSON.stringify
            ? JSON.stringify
            : function (o) {
                if (o === null) {
                    return "null";
                }

                var pairs,
                    k,
                    name,
                    val,
                    type = $.type(o);

                if (type === "undefined") {
                    return undefined;
                }

                // Also covers instantiated Number and Boolean objects,
                // which are typeof 'object' but thanks to $.type, we
                // catch them here. I don't know whether it is right
                // or wrong that instantiated primitives are not
                // exported to JSON as an {"object":..}.
                // We choose this path because that's what the browsers did.
                if (type === "number" || type === "boolean") {
                    return String(o);
                }
                if (type === "string") {
                    return $.quoteString(o);
                }
                if (typeof o.toJSON === "function") {
                    return $.toJSON(o.toJSON());
                }
                if (type === "date") {
                    var month = o.getUTCMonth() + 1,
                        day = o.getUTCDate(),
                        year = o.getUTCFullYear(),
                        hours = o.getUTCHours(),
                        minutes = o.getUTCMinutes(),
                        seconds = o.getUTCSeconds(),
                        milli = o.getUTCMilliseconds();

                    if (month < 10) {
                        month = "0" + month;
                    }
                    if (day < 10) {
                        day = "0" + day;
                    }
                    if (hours < 10) {
                        hours = "0" + hours;
                    }
                    if (minutes < 10) {
                        minutes = "0" + minutes;
                    }
                    if (seconds < 10) {
                        seconds = "0" + seconds;
                    }
                    if (milli < 100) {
                        milli = "0" + milli;
                    }
                    if (milli < 10) {
                        milli = "0" + milli;
                    }
                    return (
                        '"' +
                        year +
                        "-" +
                        month +
                        "-" +
                        day +
                        "T" +
                        hours +
                        ":" +
                        minutes +
                        ":" +
                        seconds +
                        "." +
                        milli +
                        'Z"'
                    );
                }

                pairs = [];

                if ($.isArray(o)) {
                    for (k = 0; k < o.length; k++) {
                        pairs.push($.toJSON(o[k]) || "null");
                    }
                    return "[" + pairs.join(",") + "]";
                }

                // Any other object (plain object, RegExp, ..)
                // Need to do typeof instead of $.type, because we also
                // want to catch non-plain objects.
                if (typeof o === "object") {
                    for (k in o) {
                        // Only include own properties,
                        // Filter out inherited prototypes
                        if (hasOwn.call(o, k)) {
                            // Keys must be numerical or string. Skip others
                            type = typeof k;
                            if (type === "number") {
                                name = '"' + k + '"';
                            } else if (type === "string") {
                                name = $.quoteString(k);
                            } else {
                                continue;
                            }
                            type = typeof o[k];

                            // Invalid values like these return undefined
                            // from toJSON, however those object members
                            // shouldn't be included in the JSON string at all.
                            if (type !== "function" && type !== "undefined") {
                                val = $.toJSON(o[k]);
                                pairs.push(name + ":" + val);
                            }
                        }
                    }
                    return "{" + pairs.join(",") + "}";
                }
            };

    /**
     * jQuery.evalJSON
     * Evaluates a given json string.
     *
     * @param str {String}
     */
    $.evalJSON =
        typeof JSON === "object" && JSON.parse
            ? JSON.parse
            : function (str) {
                /*jshint evil: true */
                return eval("(" + str + ")");
            };

    /**
     * jQuery.secureEvalJSON
     * Evals JSON in a way that is *more* secure.
     *
     * @param str {String}
     */
    $.secureEvalJSON =
        typeof JSON === "object" && JSON.parse
            ? JSON.parse
            : function (str) {
                var filtered = str
                    .replace(/\\["\\\/bfnrtu]/g, "@")
                    .replace(
                        /"[^"\\\n\r]*"|true|false|null|-?\d+(?:\.\d*)?(?:[eE][+\-]?\d+)?/g,
                        "]"
                    )
                    .replace(/(?:^|:|,)(?:\s*\[)+/g, "");

                if (/^[\],:{}\s]*$/.test(filtered)) {
                    /*jshint evil: true */
                    return eval("(" + str + ")");
                }
                throw new SyntaxError("Error parsing JSON, source is not valid.");
            };

    /**
     * jQuery.quoteString
     * Returns a string-repr of a string, escaping quotes intelligently.
     * Mostly a support function for toJSON.
     * Examples:
     * >>> jQuery.quoteString('apple')
     * "apple"
     *
     * >>> jQuery.quoteString('"Where are we going?", she asked.')
     * "\"Where are we going?\", she asked."
     */
    $.quoteString = function (str) {
        if (str.match(escape)) {
            return (
                '"' +
                str.replace(escape, function (a) {
                    var c = meta[a];
                    if (typeof c === "string") {
                        return c;
                    }
                    c = a.charCodeAt();
                    return (
                        "\\u00" + Math.floor(c / 16).toString(16) + (c % 16).toString(16)
                    );
                }) +
                '"'
            );
        }
        return '"' + str + '"';
    };
})(jQuery);

$(document).on("ifChecked", ".bcheck", function () {
    $(this).val(true);
    $(this)
        .closest(".checkBoolean")
        .find('input[type="hidden"]')
        .val("true");
});

$(document).on("ifUnchecked", ".bcheck", function () {
    $(this).val(false);
    $(this)
        .closest(".checkBoolean")
        .find('input[type="hidden"]')
        .val("true");
});

$(document).on("ifChecked", ".rdcheck", function () {
    $(this).attr("checked", true);
});

$(document).on("ifUnchecked", ".rdcheck", function () {
    $(this).attr("checked", false);
});

$(document.body).on(
    "mouseover",
    "[data-tooltip-id],[data-tooltip-for]",
    function () {
        var id = $(this).data("tooltip-id");
        if ($(this).is("[data-tooltip-for]")) {
            $(this).addClass("show");
            return;
        }
        var tooltip = $(this).find('[data-tooltip-for="' + id + '"]');
        if (tooltip.length != 0) {
            $(document.body).append(tooltip);
        } else {
            tooltip = $(document.body).find('[data-tooltip-for="' + id + '"]');
        }
        var pos = $(this).offset();
        pos.left += $(this).width() / 2;
        pos.top += $(this).height() / 2;
        tooltip.offset(pos);
        var popupSizeW = $(this).data("popup-size-w");
        if (popupSizeW) {
            tooltip.css("max-width", popupSizeW);
        }
        var popupSizeH = $(this).data("popup-size-h");
        if (popupSizeH) {
            tooltip.css("max-height", popupSizeH);
        }
        var popupBgcolor = $(this).data("popup-bgcolor");
        if (popupBgcolor) {
            tooltip.css("background-color", popupBgcolor);
        }
        tooltip.addClass("show");
    }
);

$(document.body).on(
    "mouseleave",
    "[data-tooltip-id],[data-tooltip-for]",
    function () {
        var id = $(this).data("tooltip-id");
        if ($(this).is("[data-tooltip-for]")) {
            $(this).removeClass("show");
            return;
        }
        $('[data-tooltip-for="' + id + '"]').removeClass("show");
    }
);

$(document).on("click", ".check-box-disabled", function (e) {
    e.preventDefault();
});

$(document.body).on('change', '[validate-by-api]', function () {
    webaby.validateByApiAsync(this);
});

$(document.body).on('focus', '[input-mask=range-number]:not(.input-mask-has-init)', function () {
    Inputmask({ regex: "\\d+ - \\d+" }).mask($(this));
    $(this).addClass('input-mask-has-init');
});

function clearFormInput(formSelector) {
    $(formSelector + ' :input:not(".ignore-clear")').each(function () {
        $(this).val("");
        $(this).trigger("change");
        if ($(this).is(".bs-select") || $(this).is(".bs-ajax-select")) {
            $(this).selectpicker("refresh");
        }
    });
}

var numberWithSeperatorHandler = function () {
    $(".number-seperator").each(function () {
        $(this).val(webaby.addSeparatorsNF($(this).val(), ",", ",", "."));
    });

    $(".number-seperator").unbind("change");
    $(".number-seperator").unbind("keyup");
    //$('.number-seperator').unbind('blur');

    $(".number-seperator").change(function () {
        $(this).val(webaby.addSeparatorsNF($(this).val(), ",", ",", "."));
    });
    $(".number-seperator").keyup(function () {
        $(this).val(webaby.addSeparatorsNF($(this).val(), ",", ",", "."));
    });
    $(".number-seperator").blur(function () {
        $(this).val(webaby.addSeparatorsNF($(this).val(), ",", ",", "."));
    });
};

var custom_mDataTableLayout = function () {
    var onchange = function () {
        var field = $(this)
            .parents("th")
            .first()
            .data("field");
        var checked = $(this).prop("checked");
        var table = $(this)
            .parents("table")
            .first();
        table
            .find(
                'tbody td[data-field="' +
                field +
                '"] .m-checkbox.m-checkbox--single>input[type=checkbox]'
            )
            .each(function () {
                $(this).prop("checked", checked);
            });
        table
            .find(
                'tbody td[data-field="' +
                field +
                '"] .m-checkbox.m-checkbox--single > input[type=checkbox]'
            )
            .first()
            .trigger("change");
    };

    $(document.body).off(
        "change",
        ".m-checkbox.m-checkbox--single.m-checkbox--all > input[type=checkbox]",
        onchange
    );
    $(document.body).on(
        "change",
        ".m-checkbox.m-checkbox--single.m-checkbox--all > input[type=checkbox]",
        onchange
    );
};
custom_mDataTableLayout();

function updateQueryStringParameter(uri, key, value) {
    var re = new RegExp("([?&])" + key + "=.*?(&|$)", "i");
    var separator = uri.indexOf("?") !== -1 ? "&" : "?";
    if (uri.match(re)) {
        return uri.replace(re, "$1" + key + "=" + value + "$2");
    } else {
        return uri + separator + key + "=" + value;
    }
}

function removeParamFromURL(sourceURL, key) {
    var rtn = sourceURL.split("?")[0],
        param,
        params_arr = [],
        queryString = sourceURL.indexOf("?") !== -1 ? sourceURL.split("?")[1] : "";
    if (queryString !== "") {
        params_arr = queryString.split("&");
        for (var i = params_arr.length - 1; i >= 0; i -= 1) {
            param = params_arr[i].split("=")[0];
            if (param === key) {
                params_arr.splice(i, 1);
            }
        }
        rtn = rtn + "?" + params_arr.join("&");
    }
    return rtn;
}

var Dropdown = {
    InitDropdown: function (
        url,
        selector,
        val_name,
        text_name,
        null_text,
        callback,
        group,
        no_empty,
        deleted
    ) {
        var del_text = "";
        if (no_empty == undefined || no_empty == null || no_empty == false) {
            selector.empty();
        }
        webaby.ajax({
            url: url,
            type: "GET",
            success: function (_data) {
                var data = Object;
                if (_data.Many != undefined) {
                    data = _data.Many;
                } else {
                    data = _data;
                }
                if (null_text != null && null_text != undefined) {
                    selector.append(
                        $("<option></option>")
                            .attr("value", "")
                            .text(null_text)
                    );
                }
                if (group == undefined || group == null) {
                    if (data) {
                        $.each(data, function (i, v1) {
                            var text = "";
                            if ($.isArray(text_name)) {
                                $.each(text_name, function (j, v2) {
                                    if (v1[v2] != null) {
                                        text += v1[v2];
                                        if (j < text_name.length - 1) {
                                            text += " - ";
                                        }
                                    }
                                });
                            } else {
                                text = v1[text_name];
                            }
                            if (deleted != undefined && deleted != null && deleted != "") {
                                if (this[deleted]) del_text = " (đã xóa)";
                                else del_text = "";
                            }
                            selector.append(
                                $("<option></option>")
                                    .attr("value", this[val_name])
                                    .text(text + del_text)
                            );
                        });
                    }
                } else {
                    if (data) {
                        var group_text = null;
                        var jgroup = null;
                        var jgroups = new Array();
                        if (Array.prototype.find == undefined) {
                            //use ie or edge
                            Array.prototype.find = function (func) {
                                for (var i = 0; i < this.length; i++) {
                                    if (func(this[i])) {
                                        return this[i];
                                    }
                                }
                                return undefined;
                            };
                        }
                        $.each(data, function (i, v1) {
                            if (this[group] != group_text) {
                                group_text = this[group];
                                var gr = jgroups.find(function (elm) {
                                    return elm.value == group_text;
                                });
                                if (gr == undefined) {
                                    jgroup = $(
                                        '<optgroup label="' + group_text + '"></optgroup>'
                                    );
                                    jgroups.push({ value: group_text, selector: jgroup });
                                    selector.append(jgroup);
                                } else {
                                    jgroup = gr["selector"];
                                }
                            }
                            var text = "";
                            if ($.isArray(text_name)) {
                                $.each(text_name, function (j, v2) {
                                    if (v1[v2] != null) {
                                        text += v1[v2];
                                        if (j < text_name.length - 1) {
                                            text += " - ";
                                        }
                                    }
                                });
                            } else {
                                text = v1[text_name];
                            }
                            if (deleted != undefined && deleted != null && deleted != "") {
                                if (this[deleted]) del_text = "(đã xóa)";
                                else del_text = "";
                            }
                            jgroup.append(
                                $("<option></option>")
                                    .attr("value", this[val_name])
                                    .text(text + del_text)
                            );
                        });
                    }
                }
                if (
                    callback != null &&
                    callback != undefined &&
                    typeof callback == "function"
                ) {
                    callback(selector);
                }
            }
        });
    }
};