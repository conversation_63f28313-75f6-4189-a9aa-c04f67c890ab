﻿using System;
using System.Data;
using System.Data.SqlClient;
using System.Threading.Tasks;
using TinyCRM.Customer.Queries;
using Webaby;
using Webaby.Data;

namespace TinyCRM.Outbound.ExcelImport.MassData
{
    public class ExecuteImportFWDContractCommand : CommandBase
    {
        public Guid ImportSessionId { get; set; }
    }
    internal class ExecuteImportFWDContractCommandHandler : CommandHandlerBase<ExecuteImportFWDContractCommand>
    {
        public ExecuteImportFWDContractCommandHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task ExecuteAsync(ExecuteImportFWDContractCommand command)
        {
            var cmd = EntitySet.CreateDbCommand();
            cmd.CommandText = "fwd.ImportContractRaw_Excute";
            cmd.CommandType = CommandType.StoredProcedure;
            cmd.Parameters.Add(DbParameterHelper.AddNullableGuid(cmd,"@ImportSessionId", command.ImportSessionId));
            await EntitySet.ExecuteNonQueryAsync(cmd);
        }
    }
}
