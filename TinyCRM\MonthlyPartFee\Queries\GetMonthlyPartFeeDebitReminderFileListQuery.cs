﻿using System;
using System.Data;
using System.Data.Common;
using System.Linq;
using System.Threading.Tasks;
using Webaby;
using Webaby.Data;

namespace TinyCRM.MonthlyPartFee.Queries
{
    public class GetMonthlyPartFeeDebitReminderFileListQuery : QueryBase<MonthlyPartFeeFileData>
    {
        public Guid? BlockId { get; set; }

        public Guid? FloorId { get; set; }

        public int Year { get; set; }

        public int Month { get; set; }
    }

    public class GetMonthlyPartFeeDebitReminderFileListQueryHandler : QueryHandlerBase<GetMonthlyPartFeeDebitReminderFileListQuery, MonthlyPartFeeFileData>
    {
        public GetMonthlyPartFeeDebitReminderFileListQueryHandler(IServiceProvider serviceProvider) : base(serviceProvider)
        {
        }

        public override async Task<QueryResult<MonthlyPartFeeFileData>> ExecuteAsync(GetMonthlyPartFeeDebitReminderFileListQuery query)
        {
            var cmd = EntitySet.CreateDbCommand(CommandType.StoredProcedure, "dbo.GetMonthlyPartFeeDebitReminderFileList");
            cmd.Parameters.Add(DbParameterHelper.AddNullableGuid(cmd, "@BlockId", query.BlockId));
            cmd.Parameters.Add(DbParameterHelper.AddNullableGuid(cmd, "@FloorId", query.FloorId));
            cmd.Parameters.Add(DbParameterHelper.AddNullableInt(cmd, "@Year", query.Year));
            cmd.Parameters.Add(DbParameterHelper.AddNullableInt(cmd, "@Month", query.Month));

            var result = (await EntitySet.ExecuteReadCommandAsync<MonthlyPartFeeFileData>(cmd)).ToList();
            await Task.CompletedTask;
            return new QueryResult<MonthlyPartFeeFileData>(result);
        }
    }
}