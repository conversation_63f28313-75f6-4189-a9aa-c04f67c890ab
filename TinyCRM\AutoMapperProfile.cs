﻿using AutoMapper;
using TinyCRM.AutomaticTask;
using TinyCRM.AutomaticTask.Command;
using TinyCRM.AutomaticTask.Queries;
using TinyCRM.Behavior;
using TinyCRM.Behavior.Commands;
using TinyCRM.Behavior.Queries;
using TinyCRM.Building;
using TinyCRM.Building.Commands;
using TinyCRM.BusinessResult;
using TinyCRM.BusinessResult.Commands;
using TinyCRM.BusinessResult.Queries;
using TinyCRM.Channel;
using TinyCRM.Channel.Commands;
using TinyCRM.Channel.Queries;
using TinyCRM.ContentTemplate;
using TinyCRM.ContentTemplate.Queries;
using TinyCRM.Customer;
using TinyCRM.Customer.Commands;
using TinyCRM.Customer.Queries;
using TinyCRM.CustomerContext;
using TinyCRM.CustomerContext.Commands;
using TinyCRM.DigitalChannel;
using TinyCRM.DigitalChannel.Queries;
using TinyCRM.DigitalChannelMessageTemplate;
using TinyCRM.DigitalChannelMessageTemplate.Queries;
using TinyCRM.DigitalPushCode;
using TinyCRM.DigitalPushCode.Queries;
using TinyCRM.DynamicDefinedTable;
using TinyCRM.DynamicDefinedTable.Queries;
using TinyCRM.ECommerce;
using TinyCRM.ECommerce.Commands;
using TinyCRM.EntityLink;
using TinyCRM.EntityLink.Commands;
using TinyCRM.EntityLink.Queries;
using TinyCRM.ExpenseItem;
using TinyCRM.ExpenseItem.Commands;
using TinyCRM.ExternalApiEndpoint;
using TinyCRM.ExternalApiEndpoint.Commands;
using TinyCRM.FeeCategory;
using TinyCRM.FeeCategory.Commands;
using TinyCRM.FeeCategoryPart;
using TinyCRM.FeeCategoryPart.Commands;
using TinyCRM.FinishRequestTicketByImport;
using TinyCRM.FinishRequestTicketByImport.Commands;
using TinyCRM.Geolocation;
using TinyCRM.Geolocation.Queries;
using TinyCRM.ImportB2BCustomersSession;
using TinyCRM.ImportB2BCustomersSession.Commands;
using TinyCRM.ImportCustomerSession;
using TinyCRM.ImportCustomerSession.Commands;
using TinyCRM.ImportRequestTicket;
using TinyCRM.ImportRequestTicket.Commands;
using TinyCRM.ImportTask;
using TinyCRM.ImportTask.Commands;
using TinyCRM.InfoList;
using TinyCRM.InfoList.Queries;
using TinyCRM.Mail;
using TinyCRM.Mail.Commands;
using TinyCRM.Mail.Events;
using TinyCRM.Mail.Queries;
using TinyCRM.MobileNotification;
using TinyCRM.MobileNotification.Commands;
using TinyCRM.NotificationCase;
using TinyCRM.NotificationCase.Queries;
using TinyCRM.NotificationChanelSetting;
using TinyCRM.NotificationChanelSetting.Command;
using TinyCRM.Outbound.CallResult;
using TinyCRM.Outbound.CallResult.Queries;
using TinyCRM.Outbound.Campaign;
using TinyCRM.Outbound.Campaign.Commands;
using TinyCRM.Outbound.Campaign.Queries;
using TinyCRM.Part;
using TinyCRM.Part.Commands;
using TinyCRM.PartBooking;
using TinyCRM.PartBooking.Commands;
using TinyCRM.PartCustomer;
using TinyCRM.PartCustomer.Commands;
using TinyCRM.PaymentRequest;
using TinyCRM.PaymentRequest.Commands;
using TinyCRM.Phase;
using TinyCRM.Phase.Queries;
using TinyCRM.Product;
using TinyCRM.Product.Queries;
using TinyCRM.RequestTicket;
using TinyCRM.RequestTicket.Commands;
using TinyCRM.RequestTicket.Queries;
using TinyCRM.RequestTicketDynamicModel;
using TinyCRM.RequestTicketDynamicModel.Queries;
using TinyCRM.ResultCode;
using TinyCRM.Retrieval;
using TinyCRM.Retrieval.Commands;
using TinyCRM.ServiceCategory;
using TinyCRM.ServiceCategory.Commands;
using TinyCRM.ServiceCategory.Queries;
using TinyCRM.ServiceType;
using TinyCRM.ServiceType.Commands;
using TinyCRM.ServiceType.Queries;
using TinyCRM.Sms;
using TinyCRM.Sms.Commands;
using TinyCRM.Sms.Events;
using TinyCRM.Sms.Queries;
using TinyCRM.SMSLog;
using TinyCRM.SMSLog.Commands;
using TinyCRM.TaskType;
using TinyCRM.TaskType.Commands;
using TinyCRM.TaskType.Queries;
using TinyCRM.Tax;
using TinyCRM.Tax.Commands;
using TinyCRM.TbCallback;
using TinyCRM.TbCallback.Commands;
using TinyCRM.TicketHotButton;
using TinyCRM.TicketHotButton.Queries;
using TinyCRM.UserAccount;
using TinyCRM.UserAccount.Queries;
using TinyCRM.UserTaskAssignmentRouting;
using TinyCRM.UserTaskAssignmentRouting.Commands;
using TinyCRM.UserTaskAssignmentRouting.Queries;
using TinyCRM.Workflow;
using TinyCRM.Workflow.Commands;
using TinyCRM.Workflow.Queries;
using Webaby.Security;
using TinyCRM.ImportRequestTicket.Queries;
using TinyCRM.ImportTask.Queries;
using TinyCRM.Outbound.ExcelImport.MassData;
using TinyCRM.Outbound.ExcelImport;


namespace TinyCRM
{
    public class AutoMapperProfile : Profile
    {
        public AutoMapperProfile()
        {
            // AspNetUser
            CreateMap<ApplicationUser, AspNetUserData>();
            CreateMap<ApplicationUser, AspNetUserListItem>();
            CreateMap<UserIdentity, UserProfileEntityData>();

            // RequestTicketDynamicModel
            CreateMap<RequestTicketDynamicModelEntity, RequestTicketDynamicModelData>();

            // Channel
            CreateMap<ChannelEntity, ChannelData>();
            CreateMap<CreateEditChannelCommand, ChannelEntity>();

            // TicketHotButton
            CreateMap<TicketHotButtonEntity, TicketHotButtonData>();

            // DynamicDefinedTable
            CreateMap<DynamicDefinedTableSchemaEntity, DynamicDefinedTableSchemaData>();
            CreateMap<DynamicDefinedTableColumnEntity, DynamicDefinedTableColumnData>();
            CreateMap<DynamicDefinedTableCellValueEntity, DynamicDefinedTableCellValueData>();

            // RequestTicket
            CreateMap<RequestTicketEntity, RequestTicketData>();
            CreateMap<ProductRequestTicketEntity, ProductRequestTicketData>();
            CreateMap<InsertRequestTicketCommand, RequestTicketEntity>();
            CreateMap<UpdateRequestTicketCommand, RequestTicketEntity>();
            CreateMap<InsertRequestTicketCallCommand, RequestTicketCallEntity>();
            CreateMap<InsertRequestTicketWebchatCommand, RequestTicketWebchatEntity>();

            // ProductRequestTicket
            CreateMap<ProductRequestTicketEntity, ProductRequestTicketData>();
            CreateMap<InsertProductRequestTicketCommand, ProductRequestTicketEntity>();
            CreateMap<UpdateProductRequestTicketCommand, ProductRequestTicketEntity>();

            // ProductExchange
            CreateMap<ProductExchangeEntity, ProductExchangeData>();
            CreateMap<InsertProductExchangeCommand, ProductExchangeEntity>();
            CreateMap<UpdateProductExchangeCommand, ProductExchangeEntity>();

            // Customer mapping
            CreateMap<CustomerEntity, CustomerData>();
            CreateMap<CustomerData, CustomerEntity>();
            CreateMap<CreateEditCustomerCommand, CustomerEntity>();
            CreateMap<CallResultEntity, CallResultData>();

            //NotificationCase
            CreateMap<NotificationCaseEntity, NotificationCaseData>();
            CreateMap<NotificationCaseData, NotificationCaseEntity>();

            // Mail
            CreateMap<CreateEditMailCommand, MailEntity>();
            CreateMap<MailEntity, MailData>();
            CreateMap<MailData, MailEntity>();
            CreateMap<MailData, MailProcessedEvent>();

            // Sms
            CreateMap<SmsEntity, SmsData>();
            CreateMap<SmsData, SmsEntity>();
            CreateMap<CreateEditSmsCommand, SmsEntity>();
            CreateMap<SmsData, SmsProcessedEvent>();

            // SMSQueue
            CreateMap<CreateEditSMSLogCommand, SMSLogEntity>();

            // Classification
            CreateMap<ClassificationEntity, ClassificationData>();

            // Geolocation
            CreateMap<GeolocationEntity, GeolocationData>();

            // Product
            CreateMap<ProductEntity, ProductData>();

            // ServiceCategory
            CreateMap<ServiceCategoryEntity, ServiceCategoryData>();
            CreateMap<CreateEditServiceCategoryCommand, ServiceCategoryEntity>();
            CreateMap<CreateEditServiceCategoryOrganizationCommand, ServiceCategoryOrganizationEntity>();

            // ServiceType
            CreateMap<ServiceTypeEntity, ServiceTypeEntity>();
            CreateMap<ServiceTypeEntity, ServiceTypeData>();
            CreateMap<InsertServiceTypeCommand, ServiceTypeEntity>();
            CreateMap<CloneServiceTypeCommand, ServiceTypeEntity>();
            CreateMap<UpdateServiceTypeCommand, ServiceTypeEntity>();

            CreateMap<LandingServiceTypeEntity, LandingServiceTypeData>();

            // Workflow
            CreateMap<WorkflowEntity, WorkflowData>();
            CreateMap<WorkflowEntity, WorkflowEntity>();
            CreateMap<WorkflowData, WorkflowEntity>();
            CreateMap<CreateEditWorkflowCommand, WorkflowEntity>();

            // TaskType
            CreateMap<TaskTypeEntity, TaskTypeData>();
            CreateMap<TaskTypeEntity, TaskTypeEntity>();
            CreateMap<TaskTypeData, TaskTypeEntity>();
            CreateMap<CreateEditTaskTypeCommand, TaskTypeEntity>();

            // WorkflowTaskType
            CreateMap<CreateEditTaskTypeInWorkflowCommand, WorkflowTaskTypeEntity>();
            CreateMap<WorkflowTaskTypeEntity, WorkflowTaskTypeData>();
            CreateMap<WorkflowTaskTypeEntity, WorkflowTaskTypeInfo>();
            CreateMap<WorkflowTaskTypeEntity, WorkflowTaskTypeEntity>();

            // BusinessResult
            CreateMap<CreateEditBusinessResultCommand, BusinessResultEntity>();
            CreateMap<BusinessResultEntity, BusinessResultData>();

            // TaskType
            CreateMap<TaskTypeEntity, TaskTypeData>();
            CreateMap<TaskTypeEntity, TaskTypeEntity>();
            CreateMap<TaskTypeData, TaskTypeEntity>();
            CreateMap<CreateEditTaskTypeCommand, TaskTypeEntity>();

            //ContentTemplate
            CreateMap<ContentTemplateEntity, ContentTemplateData>();

            //CustomerFieldConfiguration
            CreateMap<CustomerFieldConfigurationEntity, CustomerFieldConfigurationData>();

            // UserTaskAssignmentRouting
            CreateMap<CreateEditUserTaskAssignmentRoutingCommand, UserTaskAssignmentRoutingEntity>();
            CreateMap<UserTaskAssignmentRoutingEntity, UserTaskAssignmentRoutingData>();

            // AutomaticTask
            CreateMap<AutoNextTaskEntity, AutoNextTaskData>();
            CreateMap<CreateEditAutoNextTaskCommand, AutoNextTaskEntity>();
            CreateMap<UserPathSelectorEntity, UserPathSelectorData>();
            CreateMap<AutoConditionEntity, AutoConditionData>();
            CreateMap<AutoNextTaskErrorLogEntity, AutoNextTaskErrorLogData>();
            CreateMap<CreateAutoNextTaskErrorLogCommand, AutoNextTaskErrorLogEntity>();

            CreateMap<ResultCodeSuiteEntity, ResultCodeSuiteData>();
            CreateMap<ResultCodeSuiteData, ResultCodeSuiteEntity>();

            // Các mapping cơ bản từ kết quả tìm kiếm Mapper.Map
            // Chỉ giữ lại những mapping có thể compile được
              

            // Behavior
            CreateMap<CreateEditBehaviorCommand, BehaviorEntity>();

            // Building
            CreateMap<CreateEditBuildingCommand, BuildingEntity>();

            // Phase
            CreateMap<PhaseEntity, PhaseInfo>();

            // Task
            CreateMap<TaskEntity, TaskInfo>();
            CreateMap<TaskTypeEntity, TaskTypeInfo>();
            CreateMap<TaskFeedbackArguments, TaskFeedbackData>();
            CreateMap<TaskBoardRawItem, TaskBoardData>();

            // Customer
            CreateMap<CreateEditClassificationCommand, ClassificationEntity>();
            CreateMap<InsertCustomerAlternativeAddressCommand, CustomerAlternativeAddressEntity>();
            CreateMap<UpdateCustomerAlternativeAddressCommand, CustomerAlternativeAddressEntity>();
            CreateMap<InsertCustomerCommand, CustomerEntity>();
            CreateMap<UpdateCustomerCommand, CustomerEntity>();

            // CustomerContext
            CreateMap<InsertCustomerContextHistoryCommand, CustomerContextHistoryEntity>();

            // DynamicForm

            // ECommerce
            CreateMap<CreateECommerceUploadedFileCommand, ECommerceUploadedFileEntity>();

            // EntityLink
            CreateMap<CreateEditEntityLinkCommand, EntityLinkEntity>();

            // ExpenseItem
            CreateMap<UpdateExpenseItemsCommand, ExpenseItemEntity>();

            // ExternalApiEndpoint
            CreateMap<CreateEditExternalApiEndpointCommand, ExternalApiEndpointEntity>();

            // FeeCategory
            CreateMap<CreateEditFeeCategoryCommand, FeeCategoryEntity>();
            CreateMap<CreateEditFeeCategoryFormulaCommand, FeeCategoryFormulaEntity>();
            CreateMap<CreateFeeCategoryTaxCommand, FeeCategoryTaxEntity>();

            // FeeCategoryPart
            CreateMap<CreateEditFeeCategoryPartCommand, FeeCategoryPartEntity>();

            // FinishRequestTicketByImport
            CreateMap<CreateEditFinishRequestTicketByImportCommand, DoneRequestTicketImportSessionEntity>();

            // ImportB2BCustomersSession
            CreateMap<CreateEditImportB2BCustomersSessionCommand, ImportB2BCustomersSessionEntity>();

            // ImportCustomerSession
            CreateMap<CreateEditImportCustomerSessionCommand, ImportCustomerSessionEntity>();

            // ImportRequestTicket
            CreateMap<CreateEditImportRequestTicketSessionCommand, ImportRequestTicketSessionEntity>();

            // ImportTask
            CreateMap<CreateEditImportTaskSessionCommand, ImportTaskSessionEntity>();

            // MobileNotification
            CreateMap<CreateEditMobileNotificationCommand, MobileNotificationEntity>();
            CreateMap<CreateEditMobileNotificationResultCommand, MobileNotificationResultEntity>();

            // NotificationChanelSetting
            CreateMap<CreateEditCustomNotificationChannelSettingCommand, CustomNotificationChannelSettingEntity>();

            // Part
            CreateMap<CreateEditBookablePartTimeCommand, PartBookableTimeEntity>();
            CreateMap<CreateEditPartCommand, PartEntity>();

            // PartBooking
            CreateMap<CreateEditPartBookingCommand, PartBookingEntity>();

            // PartCustomer
            CreateMap<CreatePartCustomerCommand, PartCustomerEntity>();

            // PartServiceUsedHistory

            // PaymentRequest
            CreateMap<CreatePaymentRequestImportSessionCommand, PaymentRequestImportSessionEntity>();

            // Product

            // Retrieval
            CreateMap<InsertRequestTicketTracingItemCommand, RequestTicketTracingItemEntity>();
            CreateMap<InsertRetrievalCommand, RetrievalEntity>();
            CreateMap<InsertTracingItemCommand, TracingItemEntity>();
            CreateMap<UpdateRetrievalCommand, RetrievalEntity>();
            CreateMap<UpdateTracingItemCommand, TracingItemEntity>();

            // Sms
            CreateMap<CreateEditGatewayCommand, GatewayEntity>();

            // Tax
            CreateMap<CreateEditTaxCommand, TaxEntity>();

            // TbCallback
            CreateMap<CreateEditTbCallbackCallCommand, TbCallbackCallEntity>();
            CreateMap<CreateEditTbCallbackCommand, TbCallbackEntity>();

            // infolist
            CreateMap<InfoListEntity, InfoListData>();

            // ChienDich
            CreateMap<CampaignEntity, CampaignData>();
            CreateMap<CampaignExecutingTimeEntity, CampaignExecutingTimeData>();
            CreateMap<CampaignExecutingTimeEntity, CampaignExecutingTimeInfo>();
            CreateMap<CampaignData, CampaignEntity>();
            CreateMap<CreateEditCampaignCommand, CampaignEntity>();
            CreateMap<SendEmailCampaignCommand, CampaignEntity>();

            //EntityLink 
            CreateMap<EntityLinkEntity, EntityLinkData>();

            CreateMap<BehaviorEntity, BehaviorData>();
            CreateMap<DigitalChannelEntity, DigitalChannelData>();
            CreateMap<DigitalPushCodeEntity, DigitalPushCodeData>();
            CreateMap<DigitalChannelMessageTemplateEntity, DigitalChannelMessageTemplateData>();

            // ImportRequestTicket
            CreateMap<ImportRequestTicketSessionEntity, ImportRequestTicketSessionData>();
            CreateMap<CreateEditImportRequestTicketSessionCommand, ImportRequestTicketSessionEntity>();
            CreateMap<ImportRequestTicketDataItemEntity, ImportRequestTicketDataItemData>();
            CreateMap<ImportRequestTicketDataItemData, ImportRequestTicketDataItemEntity>();

            // ImportTask
            CreateMap<ImportTaskSessionEntity, ImportTaskSessionData>();
            CreateMap<CreateEditImportTaskSessionCommand, ImportTaskSessionEntity>();
            CreateMap<ImportTaskDataItemEntity, ImportTaskDataItemData>();
            CreateMap<ImportTaskDataItemData, ImportTaskDataItemEntity>();

            CreateMap<CreateImportSessionCommand, ImportSessionEntity>();
        }
    }
}