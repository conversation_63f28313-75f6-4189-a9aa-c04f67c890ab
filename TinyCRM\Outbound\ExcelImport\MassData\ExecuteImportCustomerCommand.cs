﻿using AutoMapper;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Data;
using System.Threading.Tasks;
using TinyCRM.Outbound.Campaign;
using Webaby;
using Webaby.Data;
using Webaby.Localization;
using Webaby.Security;

namespace TinyCRM.Outbound.ExcelImport.MassData
{
    public class ExecuteImportCustomerCommand : CommandBase
    {
        public Guid ImportSessionId { get; set; }

        public Guid? CampaignId { get; set; }

        public Guid UserId { get; set; }

        public bool IsQuickImport { get; set; }

        public IEnumerable<string> Execute { get; set; }

        public IEnumerable<string> Update { get; set; }

        public bool IsImportDigitalContact { get; set; }

        public bool IsAdditionalDataCus { get; set; }
    }

    internal class ExecuteImportCustomerCommandHandler : CommandHandlerBase<ExecuteImportCustomerCommand>
    {
        IUserService _userService;
        public ExecuteImportCustomerCommandHandler(IServiceProvider serviceProvider, IUserService userService) : base(serviceProvider) { _userService = userService; }

        public override async Task ExecuteAsync(ExecuteImportCustomerCommand command)
        {
            if (command.CampaignId.HasValue)
            {
                var getCampaign = await EntitySet.GetAsync<CampaignEntity>(command.CampaignId.Value);
                if (getCampaign != null && getCampaign.CampaignType == Enums.CampaignType.Digital)
                {
                    command.IsImportDigitalContact = true;
                }
            }
            var executeValues = new List<string>();
            if (command.Execute != null)
            {
                executeValues.AddRange(command.Execute);
            }
            var updateParams = new List<string>(); 
            if (command.Update != null) {
                updateParams.AddRange(command.Update);
            }
            var cmd = EntitySet.CreateDbCommand();
            cmd.CommandText = "dbo.ImportCustomer_Execute";
            cmd.CommandType = CommandType.StoredProcedure;
            cmd.AddDataAuthorizedParameters(_userService);
            cmd.Parameters.Add(DbParameterHelper.AddNullableGuid(cmd,"@ImportSessionId", command.ImportSessionId));
            cmd.Parameters.Add(DbParameterHelper.AddNullableGuid(cmd, "@UserId", command.UserId));
            cmd.Parameters.Add(DbParameterHelper.NewNullableBooleanParameter(cmd, "@IsQuickImport", command.IsQuickImport));
            cmd.Parameters.Add(DbParameterHelper.AddNullableGuid(cmd, "@CampaignId", command.CampaignId));
            cmd.Parameters.Add(DbParameterHelper.NewNullableBooleanParameter(cmd, "@IsImportDigitalContact", command.IsImportDigitalContact));
            cmd.Parameters.Add(DbParameterHelper.NewNullableBooleanParameter(cmd, "@IsAdditionalDataCus", command.IsAdditionalDataCus)); 
            cmd.Parameters.Add(DbParameterHelper.NewStructuredParameter( "@Execute", "dbo.stringList", executeValues)); 
            cmd.Parameters.Add(DbParameterHelper.NewStructuredParameter("@Update", "dbo.stringList", updateParams));  

            await EntitySet.ExecuteNonQueryAsync(cmd);
        }
    }
}
