﻿using Webaby;
using System.Data;
using System.Data.Common;
using Webaby.Data;

namespace TinyCRM.Phase.Command
{
    public class CloseAllTodoTaskStatusCommand : CommandBase
    {
        public Guid RequestTicketId { get; set; }

        public Guid ClosedBy { get; set; }
    }

    internal class CloseAllTodoTaskStatusCommandHandler : CommandHandlerBase<CloseAllTodoTaskStatusCommand>
    {
        public CloseAllTodoTaskStatusCommandHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task ExecuteAsync(CloseAllTodoTaskStatusCommand command)
        {
            DbCommand cmd = EntitySet.CreateDbCommand();
            cmd.CommandText = "dbo.CloseAllTodoTaskStatus";
            cmd.CommandType = CommandType.StoredProcedure;
            cmd.Parameters.AddRange(new[]
            {
                DbParameterHelper.AddNullableGuid(cmd, "@RequestTicketId", command.RequestTicketId),
                DbParameterHelper.AddNullableGuid(cmd, "@ClosedBy", command.ClosedBy),
            });

            await EntitySet.ExecuteNonQueryAsync(cmd);
        }
    }
}