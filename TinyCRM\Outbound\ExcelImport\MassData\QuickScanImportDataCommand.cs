﻿using AutoMapper;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Threading.Tasks;
using TinyCRM.Customer.Queries;
using Webaby;
using Webaby.Data;
using Webaby.Localization;

namespace TinyCRM.Outbound.ExcelImport.MassData
{
    public class QuickScanImportDataCommand : CommandBase
    {
        public Guid ImportSessionId { get; set; }

        public string DedupColumnDefinitions { get; set; }

        public DataTable ImportData { get; set; }
    }
    internal class QuickScanImportDataCommandHandler : CommandHandlerBase<QuickScanImportDataCommand>
    {
        public QuickScanImportDataCommandHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task ExecuteAsync(QuickScanImportDataCommand command)
        {
            var cmd = EntitySet.CreateDbCommand();
            cmd.CommandText = "dbo.ImportCustomer_QuickScanData";
            cmd.CommandType = CommandType.StoredProcedure;
            cmd.Parameters.Add(DbParameterHelper.AddNullableGuid(cmd, "@ImportSessionId", command.ImportSessionId));
            DataSet dedupColumns = CustomerDedupDefinition.GetFromJsonString(command.DedupColumnDefinitions);
            var dedupParam = new SqlParameter();
            dedupParam.ParameterName = "@CustomerDedupColumns";
            dedupParam.Value = dedupColumns.Tables["DedupDatabase"];
            dedupParam.SqlDbType = SqlDbType.Structured;
            cmd.Parameters.Add(dedupParam);

            List<string> validColumns = new List<string> {
                "Id", "ImportSessionId", "CustomerId", "Name", "SubName", "Phone1", "Phone2", "Phone3", "ContactPhone", "Email", "FacebookId", "Code", "B2BCode", "Type", "WorkAddress", "CustomerClass", "TaxNumber", "LicenseType", "License", "LicenseDate", "LicensePlace", "Avatar", "Background", "Address", "Province", "District", "Ward", "DOB", "Sex", "Job", "CreditLimit", "CMND", "OriginNation", "Nation", "BankId", "LocationId", "Residence", "Status", "Source", "Classification", "LicenseExpired", "AddressMailing", "ProvinceMailing", "DistrictMailing", "WardMailing", "AddressPermanent", "ProvincePermanent", "DistrictPermanent", "WardPermanent", "Income", "IncomeSource", "MaritalStatus", "Notes", "QualifiedProgram", "CompanyName", "CompanyAddress", "CompanyType", "CompanyPhone", "DataQuality", "AdditionalData", "AdditionalTemplateId", "IsMaster", "Appartment", "EmailValid", "Phone1Valid", "Phone2Valid", "Phone3Valid", "ContactPhoneValid", "DOBValid", "IncomeValid", "DataQualityValid", "CreditLimitValid", "TypeValid", "LicenseDateValid", "LicenseExpiredValid", "CustomerClassValid", "ErrorCode"
            };

            var toRemove = command.ImportData.Columns.Cast<DataColumn>().Select(x => x.ColumnName).Except(validColumns).ToList();

            foreach (var col in toRemove) command.ImportData.Columns.Remove(col);
            var importDataParam = new SqlParameter();
            importDataParam.ParameterName = "@ImportData";
            importDataParam.Value = command.ImportData;
            importDataParam.SqlDbType = SqlDbType.Structured;
            importDataParam.TypeName = "dbo.ImportCustomerList";
            cmd.Parameters.Add(importDataParam);
            await EntitySet.ExecuteNonQueryAsync(cmd);
        }
    }
}
