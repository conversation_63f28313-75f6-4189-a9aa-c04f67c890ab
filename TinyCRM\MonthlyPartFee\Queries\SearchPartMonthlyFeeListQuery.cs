﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using System.Linq;
using System.Threading.Tasks;
using TinyCRM.PartCustomer.Queries;
using Webaby;
using Webaby.Data;

namespace TinyCRM.MonthlyPartFee.Queries
{
    public class SearchPartMonthlyFeeListQuery : QueryBase<PartCustomerInfo>
    {
        public Guid PartCategoryId { get; set; }

        public Guid? BlockId { get; set; }

        public Guid? FloorId { get; set; }

        public string PartCode { get; set; }
    }

    public class SearchPartMonthlyFeeListQueryHandler : QueryHandlerBase<SearchPartMonthlyFeeListQuery, PartCustomerInfo>
    {
        public SearchPartMonthlyFeeListQueryHandler(IServiceProvider serviceProvider) : base(serviceProvider)
        {
        }

        public override async Task<QueryResult<PartCustomerInfo>> ExecuteAsync(SearchPartMonthlyFeeListQuery query)
        {
            int startRow = query.Pagination.Index * query.Pagination.Size + 1;
            int endRow = query.Pagination.Index * query.Pagination.Size + query.Pagination.Size;

            var cmd = EntitySet.CreateDbCommand(CommandType.StoredProcedure, "dbo.SearchAppartmentPartList");
            cmd.Parameters.Add(DbParameterHelper.AddNullableGuid(cmd, "@PartCategoryId", query.PartCategoryId));
            cmd.Parameters.Add(DbParameterHelper.AddNullableGuid(cmd, "@BlockId", query.BlockId));
            cmd.Parameters.Add(DbParameterHelper.AddNullableGuid(cmd, "@FloorId", query.FloorId));
            cmd.Parameters.Add(DbParameterHelper.AddNullableString(cmd, "@PartCode", query.PartCode));
            cmd.Parameters.Add(DbParameterHelper.AddNullableInt(cmd, "@StartRow", startRow));
            cmd.Parameters.Add(DbParameterHelper.AddNullableInt(cmd, "@EndRow", endRow));

            var result = (await EntitySet.ExecuteReadCommandAsync<PartCustomerInfo>(cmd)).ToList();
            return QueryResult.Create(result);
        }
    }
}