﻿using System;
using System.Data;
using System.Data.Common;
using System.Threading.Tasks;
using AutoMapper;
using TinyCRM.Enums;
using Webaby;
using Webaby.Data;

namespace TinyCRM.Order.Commands
{
    public class ScanReferenceObjectImportOrdersCommand : CommandBase
    {
        public Guid OrderFileId { get; set; }
    }

    internal class ScanReferenceObjectImportOrdersCommandHandler : CommandHandlerBase<ScanReferenceObjectImportOrdersCommand>
    {
        public ScanReferenceObjectImportOrdersCommandHandler(IServiceProvider serviceProvider) : base(serviceProvider)
        {
        }

        public override async Task ExecuteAsync(ScanReferenceObjectImportOrdersCommand command)
        {
            var cmd = EntitySet.CreateDbCommand(CommandType.StoredProcedure, "ec.ScanReferenceObjectImportOrders");
            cmd.Parameters.Add(DbParameterHelper.AddNullableGuid(cmd, "@OrderFileId", command.OrderFileId));

            await EntitySet.ExecuteNonQueryAsync(cmd);
        }
    }
}