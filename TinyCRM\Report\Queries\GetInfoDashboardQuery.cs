﻿using AutoMapper;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Webaby;
using Webaby.Data;
using Webaby.Localization;

namespace TinyCRM.Report.Queries
{
    public class GetInfoDashboardQuery : QueryBase<DataSet>
    {
        public DateTime? FromTime { get; set; }
        public DateTime? ToTime { get; set; }
    }

    internal class GetInfoDashboardQueryHandle : QueryHandlerBase<GetInfoDashboardQuery, DataSet>
    {
        public GetInfoDashboardQueryHandle(IServiceProvider serviceProvider)
            : base(serviceProvider) { }
        public override async Task<QueryResult<DataSet>> ExecuteAsync(GetInfoDashboardQuery query)
        {
            var cmd = EntitySet.CreateDbCommand();
            cmd.Parameters.AddRange(new[]
            {
                DbParameterHelper.NewNullableDateTimeParameter(cmd,"@FromDate", query.FromTime),
                DbParameterHelper.NewNullableDateTimeParameter(cmd, "@ToDate", query.ToTime),
            });
            cmd.CommandText = "dbo.Query_Dashboard_KGS";
            cmd.CommandType = CommandType.StoredProcedure;

            var mainQuery = await EntitySet.ExecuteReadCommandAsync(cmd);
            return QueryResult.Create(new DataSet[] { mainQuery });
        }
    }
}
