﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using TinyCRM.MonthlyPartFee.Command;
using TinyCRM.MonthlyPartFeeBatch.Queries;
using Webaby;
using Webaby.Data;

namespace TinyCRM.MonthlyPartFeeBatch.Command
{
    public class NotifyMonthlyPartFeeBatchCommand : CommandBase
    {
        public Guid MonthlyPartFeeBatchId { get; set; }

        public int BatchRoundNumber { get; set; }

        public int BreakBetweenEachPart { get; set; }
    }

    internal class NotifyMonthlyPartFeeBatchCommandHandler : CommandHandlerBase<NotifyMonthlyPartFeeBatchCommand>
    {
        public NotifyMonthlyPartFeeBatchCommandHandler(IServiceProvider serviceProvider) : base(serviceProvider)
        {
        }

        public override async Task ExecuteAsync(NotifyMonthlyPartFeeBatchCommand command)
        {
            var monthlyPartFeeBatch = await EntitySet.GetAsync<MonthlyPartFeeBatchEntity>(command.MonthlyPartFeeBatchId);
            if (monthlyPartFeeBatch != null && monthlyPartFeeBatch.Status != MonthlyPartFeeBatchStatus.Completed)
            {
                if (monthlyPartFeeBatch.Status == MonthlyPartFeeBatchStatus.CompletedDebitNote)
                {
                    monthlyPartFeeBatch.Status = MonthlyPartFeeBatchStatus.ProcessingFeeNotification;
                    await Repository.SaveAsync(monthlyPartFeeBatch);
                }

                List<MonthlyPartFeeBatchItemData> monthlyPartFeeBatchItemDataList =
                    (await QueryExecutor.ExecuteManyAsync(
                        new GetNotProcessedMonthlyPartFeeItemByBatchQuery
                        {
                            MonthlyPartFeeBatchId = monthlyPartFeeBatch.Id,
                            FeeStatus = MonthlyPartFeeBatchItemStatus.Completed,
                            NotificationStatus = MonthlyPartFeeBatchItemStatus.New,
                            BatchRoundNumber = command.BatchRoundNumber
                        })).ToList();

                while (monthlyPartFeeBatchItemDataList.Count > 0)
                {
                    foreach (var monthlyPartFeeBatchItemData in monthlyPartFeeBatchItemDataList)
                    {
                        await CommandExecutor.ExecuteAsync(
                            new NotifyMonthlyPartFeeBatchItemCommand
                            {
                                MonthlyPartFeeBatchItemId = monthlyPartFeeBatchItemData.Id
                            });
                        Thread.Sleep(command.BreakBetweenEachPart);
                    }

                    monthlyPartFeeBatchItemDataList =
                        (await QueryExecutor.ExecuteManyAsync(
                            new GetNotProcessedMonthlyPartFeeItemByBatchQuery
                            {
                                MonthlyPartFeeBatchId = monthlyPartFeeBatch.Id,
                                FeeStatus = MonthlyPartFeeBatchItemStatus.Completed,
                                NotificationStatus = MonthlyPartFeeBatchItemStatus.New,
                                BatchRoundNumber = command.BatchRoundNumber
                            })).ToList();
                }

                monthlyPartFeeBatch.MonthlyPartFeeNotifiedStatus = MonthlyPartFeeNotifiedStatus.Completed;
                await Repository.SaveAsync(monthlyPartFeeBatch);

                // Cập nhật trạng thái Batch
                var sqlCommand = EntitySet.CreateDbCommand(CommandType.StoredProcedure, "dbo.UpdateMonthlyPartFeeBatchStatus");
                sqlCommand.Parameters.Add(DbParameterHelper.AddNullableGuid(sqlCommand, "@MonthlyPartFeeBatchId", command.MonthlyPartFeeBatchId));
                sqlCommand.Parameters.Add(DbParameterHelper.AddNullableEnum(sqlCommand, "@Status", MonthlyPartFeeBatchStatus.Completed));

                await EntitySet.ExecuteNonQueryAsync(sqlCommand);
            }
        }
    }
}