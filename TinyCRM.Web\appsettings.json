{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning"
    }
  },
  "Serilog": {
    "Using": [ "Serilog.Sinks.Console", "Serilog.Sinks.File", "Serilog.Expressions" ],
    "MinimumLevel": {
      "Default": "Information",
      "Override": {
        "Microsoft": "Warning",
        "Microsoft.Hosting.Lifetime": "Information",
        "System": "Warning"
      }
    },
    "WriteTo": [
      { "Name": "Console" },
      {
        "Name": "File",
        "Args": {
          "path": "D:\\ErrorLogs\\Poptech.Cep.NetCore\\Poptech.Cep.NetCore.Log-.txt",
          "rollingInterval": "Day",
          "outputTemplate": "{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} [{Level:u3}] {Message:lj}{NewLine}{Exception}"
        },
        "Filter": [
          {
            "Name": "ByIncludingOnly",
            "Args": {
              "expression": "@l = 'Information' or @l = 'Error'"
            }
          }
        ]
      }
    ],
    "Enrich": [
      "FromLogContext",
      "WithMachineName",
      "WithProcessId",
      "WithThreadId"
    ],
    "Properties": {
      "Application": "Poptech.Cep.NetCore"
    }
  },
  "AllowedHosts": "*",
  //"DatabaseType": "SqlServer",
  "DatabaseType": "PostgreSql",
  "ConnectionStrings": {
    //"Default": "Data Source=EDGAR-LAPTOP\\MSSQLSERVER2017;Initial Catalog=CEP_NETCORE;user id=sa;password=******; TrustServerCertificate=True;"
    "Default": "Host=localhost;Port=5432;Database=cep-netcore;Username=postgres;Password=*********"
    //"Default": "Server=************;Database=cep-netcore-dev;Port=30001;User Id=postgres;Password=***********;Ssl Mode=Disable;"
  },
  "ApplicationAssemblies": [ "Webaby", "Webaby.Core", "TinyCRM", "TinyCRM.AppServices", "TinyCRM.Web" ],
  "MigrationAssemblies": [ "TinyCRM.DbMigration", "TinyCRM.DbMigration.SystemData", "TinyCRM.DbMigration.StandardSampleData" ],
  "DisableLoadingAccesses": false,
  "LoadingAccesAssemblies": [ "TinyCRM.Web" ],
  "Application": {
    "Name": "TinyCRM",
    "Url": "http://localhost:5077",
    "FaviconPath": "~/favicon.png",
    "HeaderLogo": "~/assets/images/logo.png"
  },
  "Resource": {
    "ScriptPath": "~/assets/",
    "StylePath": "~/assets/"
  },
  "ADAuth": {
    "Enabled": false,
    "Domain": ""
  },
  "DateTimeModel": {
    "FullStringFormat": "dd/MM/yyyy HH:mm:ss",
    "DateTimeFormat": "dd/MM/yyyy HH:mm",
    "DateFormat": "dd/MM/yyyy"
  },
  "ScheduledTasks": {
    "Notification": {
      "Disabled": true,
      "MaxExecuteMinutes": 30,
      "IntervalMiliseconds": 3000
    }
  },
  "DistributedLock": {
    "DistributedLockType": "SqlServer"
  }
}
