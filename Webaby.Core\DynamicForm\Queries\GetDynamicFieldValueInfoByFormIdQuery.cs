﻿using AutoMapper;
using System.Data;
using System.Data.Common;
using Webaby.Data;
using Webaby.Localization;

namespace Webaby.Core.DynamicForm.Queries
{
    public class GetDynamicFieldValueInfoByFormIdQuery : QueryBase<DynamicFieldValueInfo>
    {
        public Guid DynamicFormId { get; set; }

        public bool? Display { get; set; }

        public bool UseDefaultValueLinkedField { get; set; }
    }

    internal class GetDynamicFieldValueInfoByFormIdQueryHandler : QueryHandlerBase<GetDynamicFieldValueInfoByFormIdQuery, DynamicFieldValueInfo>
    {
        public GetDynamicFieldValueInfoByFormIdQueryHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task<QueryResult<DynamicFieldValueInfo>> ExecuteAsync(GetDynamicFieldValueInfoByFormIdQuery query)
        {
            // Store "GetDynamicFieldValueInfoByFormId" -> LinQ

            var dynamicFormValueId = Guid.NewGuid();

            var rawQuery = (from df in EntitySet.Get<DynamicFormEntity>()
                            join dfd in EntitySet.Get<DynamicFieldDefinitionEntity>() on df.Id equals dfd.DynamicFormId
                            join _dfs in EntitySet.Get<DynamicFieldSectionEntity>() on dfd.DynamicFieldSectionId equals _dfs.Id into _dfsGroup
                            from dfs in _dfsGroup.DefaultIfEmpty()
                            where df.Id == query.DynamicFormId
                            && (query.Display == null || dfd.Display == query.Display)
                            orderby dfs.DisplayOrder, dfd.Order
                            select new
                            {
                                DynamicFieldDefinition = dfd,
                                DynamicFieldSection = dfs,
                                DynamicForm = df
                            }).ToList();

            var mainQuery = rawQuery.Select(x =>
            {
                DynamicFieldValueInfo dynamicFieldValueInfo = Mapper.Map<DynamicFieldValueInfo>(x.DynamicFieldDefinition);

                dynamicFieldValueInfo.Id = Guid.NewGuid();
                dynamicFieldValueInfo.ObjectId = dynamicFormValueId;
                dynamicFieldValueInfo.FormId = x.DynamicForm.Id;
                dynamicFieldValueInfo.FieldId = x.DynamicFieldDefinition.Id;

                dynamicFieldValueInfo.Value = (x.DynamicFieldDefinition.FieldType == FieldType.Input || x.DynamicFieldDefinition.FieldType == FieldType.Static)
                                                    ? x.DynamicFieldDefinition.DefaultValue
                                                    : (x.DynamicFieldDefinition.FieldType == FieldType.Linked && query.UseDefaultValueLinkedField)
                                                        ? x.DynamicFieldDefinition.DefaultValue
                                                        : null;

                dynamicFieldValueInfo.DynamicFieldSectionId = x.DynamicFieldSection != null ? x.DynamicFieldSection.Id : (Guid?)null;
                dynamicFieldValueInfo.DynamicFieldSectionName = x.DynamicFieldSection != null ? x.DynamicFieldSection.SectionName : null;

                return dynamicFieldValueInfo;
            });
            return new QueryResult<DynamicFieldValueInfo>(mainQuery);
        }
    }
}