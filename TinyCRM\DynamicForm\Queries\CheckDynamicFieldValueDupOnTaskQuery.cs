﻿using AutoMapper;
using DocumentFormat.OpenXml.Office.Word;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TinyCRM.Phase;
using TinyCRM.RequestTicket;
using TinyCRM.ServiceCategory;
using TinyCRM.ServiceType;
using TinyCRM.TaskType;
using Webaby;
using Webaby.Core.DynamicForm;
using Webaby.Core.DynamicForm.Queries;
using Webaby.Data;
using Webaby.Localization;

namespace TinyCRM.DynamicForm.Query
{
    public class CheckDynamicFieldValueDupOnTaskQuery : QueryBase<CheckDynamicFieldValueDupOnTaskQuery.ResultDto>
    {
        public Guid TicketId { get; set; }
        public Guid TaskTypeId { get; set; }
        public List<DynamicFieldValueList> DynamicFieldValueData { get; set; }
        public List<DynamicFieldValueList> DynamicFieldValueRangeTimeData { get; set; }
        public Guid? ServiceTypeId { get; set; }

        public string DynamicTableName { get; set; }
        public Guid? DynamicFieldValueIdTemp { get; set; }
        public string FullQuery { get; set; }

        public class ResultDto
        {
            public Guid DynamicFormValueId { get; set; }
            public int Result { get; set; }
            public DateTime? Old_Start { get; set; }
            public DateTime? Old_End { get; set; }
            public DateTime? New_Start { get; set; }
        }
    }
    public class CheckDynamicFieldValueDupOnTaskQueryHandler : QueryHandlerBase<CheckDynamicFieldValueDupOnTaskQuery, CheckDynamicFieldValueDupOnTaskQuery.ResultDto>
    {
        public CheckDynamicFieldValueDupOnTaskQueryHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task<QueryResult<CheckDynamicFieldValueDupOnTaskQuery.ResultDto>> ExecuteAsync(CheckDynamicFieldValueDupOnTaskQuery query)
        {
            var sqlCommand = EntitySet.CreateDbCommand();
            sqlCommand.CommandType = CommandType.StoredProcedure;

            if (query.DynamicTableName.IsNotNullOrEmpty())
            {
                sqlCommand.CommandText = "dbo.CheckDynamicTableValid";
                sqlCommand.Parameters.Add(DbParameterHelper.AddNullableString(sqlCommand, "@DynamicTableName", query.DynamicTableName));
                sqlCommand.Parameters.Add(DbParameterHelper.AddNullableGuid(sqlCommand, "@DynamicFieldValueIdTemp", query.DynamicFieldValueIdTemp));
                sqlCommand.Parameters.Add(DbParameterHelper.AddNullableString(sqlCommand, "@FullQuery", query.FullQuery));

                var resultData = await EntitySet.ExecuteReadCommandAsync<CheckDynamicFieldValueDupOnTaskQuery.ResultDto>(sqlCommand);
                return new QueryResult<CheckDynamicFieldValueDupOnTaskQuery.ResultDto>(resultData);
            }
            else
            {
                // Store "CheckDynamicFieldValueDuplicate" -> LinQ

                var requestTickets = EntitySet.Get<RequestTicketEntity>();
                var phases = EntitySet.Get<PhaseEntity>();
                var tasks = EntitySet.Get<TaskEntity>();
                var dynamicFormValues = EntitySet.Get<DynamicFormValueEntity>();
                var dynamicFieldValues = EntitySet.Get<DynamicFieldValueEntity>();
                var dynamicFieldDefs = EntitySet.Get<DynamicFieldDefinitionEntity>();

                // 1. Lấy GroupedData (thay thế #GroupedData)
                var groupedDataQuery =
                    query.ServiceTypeId != null
                    ? (from rt in requestTickets
                       join dfield in dynamicFieldValues on rt.DynamicFormValueId equals dfield.DynamicFormValueId
                       join define in dynamicFieldDefs on dfield.DynamicFieldId equals define.Id
                       where rt.ServiceTypeId == query.ServiceTypeId
                             && define.IsCheckDuplicate
                       select new
                       {
                           dfield.DynamicFormValueId,
                           dfield.DynamicFieldId,
                           dfield.Value,
                           define.ViewHint
                       })
                    : (from rt in requestTickets
                       join p in phases on rt.Id equals p.TicketId
                       join t in tasks on p.Id equals t.PhaseId
                       join dfv in dynamicFormValues on t.Id equals dfv.ReferenceObjectId
                       join dfield in dynamicFieldValues on dfv.Id equals dfield.DynamicFormValueId
                       join define in dynamicFieldDefs on dfield.DynamicFieldId equals define.Id
                       where rt.Id == query.TicketId
                             && t.TaskTypeId == query.TaskTypeId
                             && define.IsCheckDuplicate
                       select new
                       {
                           dfield.DynamicFormValueId,
                           dfield.DynamicFieldId,
                           dfield.Value,
                           define.ViewHint
                       });

                var groupedData = await groupedDataQuery.ToListAsync();

                // 2. Check duplicate (thay thế #checkDupData)
                var checkDupData = groupedData
                    .GroupBy(g => g.DynamicFormValueId)
                    .Select(g => new
                    {
                        DynamicFormValueId = g.Key,
                        Result = query.DynamicFieldValueData.All(temp =>
                            g.Any(x => x.DynamicFieldId == temp.DynamicFieldId && x.Value == temp.Value)) ? 1 : 0
                    })
                    .OrderByDescending(x => x.Result)
                    .ToList();

                // 3. Tính StartTime / EndTime cho old data (thay #tempOldData)
                var tempOldData = groupedData
                    .Where(x => x.ViewHint == "DateTimeFull_v5")
                    .GroupBy(x => x.DynamicFormValueId)
                    .Select(g => new
                    {
                        DynamicFormValueId = g.Key,
                        StartTime = g.Select(x => DateTime.TryParse(x.Value, out var dt) ? dt : (DateTime?)null).Min(),
                        EndTime = g.Select(x => DateTime.TryParse(x.Value, out var dt) ? dt : (DateTime?)null).Max()
                    })
                    .ToList();

                // 4. Tính StartTime / EndTime cho new data (thay #tempNewData)
                var tempNewData = new
                {
                    StartTime = query.DynamicFieldValueRangeTimeData
                        .Select(x => DateTime.TryParse(x.Value, out var dt) ? dt : (DateTime?)null).Min(),
                    EndTime = query.DynamicFieldValueRangeTimeData
                        .Select(x => DateTime.TryParse(x.Value, out var dt) ? dt : (DateTime?)null).Max()
                };

                // 5. Join time range overlap (#resultCheckTime)
                var resultCheckTime = (from t1 in tempOldData
                                       where tempNewData.StartTime <= t1.EndTime
                                       select new
                                       {
                                           t1.DynamicFormValueId,
                                           Old_Start = t1.StartTime,
                                           Old_End = t1.EndTime,
                                           New_Start = tempNewData.StartTime
                                       }).ToList();

                // 6. Final result
                var finalResult = (from dup in checkDupData
                                   join result in resultCheckTime on dup.DynamicFormValueId equals result.DynamicFormValueId
                                   orderby dup.Result descending
                                   select new CheckDynamicFieldValueDupOnTaskQuery.ResultDto
                                   {
                                       DynamicFormValueId = dup.DynamicFormValueId,
                                       Result = dup.Result,
                                       Old_Start = result.Old_Start,
                                       Old_End = result.Old_End,
                                       New_Start = result.New_Start
                                   }).ToList();


                return new QueryResult<CheckDynamicFieldValueDupOnTaskQuery.ResultDto>(finalResult);
            }
        }
    }

    //---------------------------------------------------------------------------------------------------------------------------------------------

    public class CheckDynamicFieldValueDupOnDataTableTaskQuery : QueryBase<DataTable>
    {
        public Guid TicketId { get; set; }
        public Guid TaskTypeId { get; set; }
        public List<DynamicFieldValueList> DynamicFieldValueData { get; set; }
        public List<DynamicFieldValueList> DynamicFieldValueRangeTimeData { get; set; }
        public Guid? ServiceTypeId { get; set; }

        public string DynamicTableName { get; set; }
        public Guid? DynamicFieldValueIdTemp { get; set; }
        public string FullQuery { get; set; }
    }
    public class CheckDynamicFieldValueDupOnTaskDataTableQueryHandler : QueryHandlerBase<CheckDynamicFieldValueDupOnDataTableTaskQuery, DataTable>
    {
        public CheckDynamicFieldValueDupOnTaskDataTableQueryHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task<QueryResult<DataTable>> ExecuteAsync(CheckDynamicFieldValueDupOnDataTableTaskQuery query)
        {
            var sqlCommand = EntitySet.CreateDbCommand();
            sqlCommand.CommandType = CommandType.StoredProcedure;

            sqlCommand.CommandText = "dbo.CheckDynamicTableValid";
            sqlCommand.Parameters.Add(DbParameterHelper.AddNullableString(sqlCommand, "@DynamicTableName", query.DynamicTableName));
            sqlCommand.Parameters.Add(DbParameterHelper.AddNullableGuid(sqlCommand, "@DynamicFieldValueIdTemp", query.DynamicFieldValueIdTemp));
            sqlCommand.Parameters.Add(DbParameterHelper.AddNullableString(sqlCommand, "@FullQuery", query.FullQuery));

            var resultData = await EntitySet.ExecuteReadCommandAsync(sqlCommand);
            return new QueryResult<DataTable>(resultData.Tables[0]);
        }
    }

    public class DynamicFieldValueList
    {
        public Guid DynamicFieldId { get; set; }
        public string Value { get; set; }
    }
}