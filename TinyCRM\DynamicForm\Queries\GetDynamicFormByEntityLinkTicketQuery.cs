﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Webaby;
using Webaby.Data;
using AutoMapper;
using Webaby.Localization;

namespace TinyCRM.DynamicForm.Queries
{
    public class GetDynamicFormByEntityLinkTicketQuery : QueryBase<EditorControlDynamicFormData>
    {
        public Guid? RequestTicketId { get; set; }

        public int Type { get; set;}    
    }

    internal class GetDynamicFormByEntityLinkTicketQueryHandler : QueryHandlerBase<GetDynamicFormByEntityLinkTicketQuery, EditorControlDynamicFormData>
    {
        public GetDynamicFormByEntityLinkTicketQueryHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task<QueryResult<EditorControlDynamicFormData>> ExecuteAsync(GetDynamicFormByEntityLinkTicketQuery query)
        {
            var cmd = EntitySet.CreateDbCommand();
            cmd.Parameters.Add(DbParameterHelper.AddNullableGuid(cmd, "@RequestTicketId", query.RequestTicketId));
            cmd.Parameters.Add(DbParameterHelper.AddNullableInt(cmd, "@Type", query.Type));
            cmd.CommandText = "dbo.GetDynamicFormByEntityLinkTicketQuery";
            cmd.CommandType = CommandType.StoredProcedure;
            var mainQuery = await EntitySet.ExecuteReadCommandAsync<EditorControlDynamicFormData>(cmd);
            return new QueryResult<EditorControlDynamicFormData>(mainQuery);
        }
    }
}