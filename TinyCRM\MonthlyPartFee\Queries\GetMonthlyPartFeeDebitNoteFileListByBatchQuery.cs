﻿using System;
using System.Data;
using System.Data.Common;
using System.Linq;
using System.Threading.Tasks;
using Webaby;
using Webaby.Data;

namespace TinyCRM.MonthlyPartFee.Queries
{
    public class GetMonthlyPartFeeDebitNoteFileListByBatchQuery : QueryBase<MonthlyPartFeeFileData>
    {
        public Guid MonthlyPartFeeBatchId { get; set; }

        public Guid? BlockId { get; set; }

        public Guid? FloorId { get; set; }

        public string Extension { get; set; }
    }

    public class GetMonthlyPartFeeDebitNoteFileListByBatchQueryHandler : QueryHandlerBase<GetMonthlyPartFeeDebitNoteFileListByBatchQuery, MonthlyPartFeeFileData>
    {
        public GetMonthlyPartFeeDebitNoteFileListByBatchQueryHandler(IServiceProvider serviceProvider) : base(serviceProvider)
        {
        }

        public override async Task<QueryResult<MonthlyPartFeeFileData>> ExecuteAsync(GetMonthlyPartFeeDebitNoteFileListByBatchQuery query)
        {
            var cmd = EntitySet.CreateDbCommand(CommandType.StoredProcedure, "dbo.GetMonthlyPartFeeDebitNoteFileListByBatch");
            cmd.Parameters.Add(DbParameterHelper.AddNullableGuid(cmd, "@MonthlyPartFeeBatchId", query.MonthlyPartFeeBatchId));
            cmd.Parameters.Add(DbParameterHelper.AddNullableGuid(cmd, "@BlockId", query.BlockId));
            cmd.Parameters.Add(DbParameterHelper.AddNullableGuid(cmd, "@FloorId", query.FloorId));
            cmd.Parameters.Add(DbParameterHelper.AddNullableString(cmd, "@Extension", query.Extension));

            var result = (await EntitySet.ExecuteReadCommandAsync<MonthlyPartFeeFileData>(cmd)).ToList();
            await Task.CompletedTask;
            return new QueryResult<MonthlyPartFeeFileData>(result);
        }
    }
}