﻿using System;
using System.Data;
using System.Data.SqlClient;
using System.Threading.Tasks;
using TinyCRM.Outbound.CallResult;
using TinyCRM.Outbound.ProspectAssignment;
using Webaby;
using Webaby.Data;

namespace TinyCRM.Outbound.TeamLeadCampaign
{
    public class ReassignCommand : CommandBase
    {
        public Guid ProspectAssignmentId { get; set; }
        public Guid NewTeamId { get; set; }
        public Guid? NewTMRId { get; set; }
        public Guid CurrentUser { get; set; }

        public Action<string, string> ErrorCommand { get; set; }
    }
    internal class ReassignCommandHandler : CommandHandlerBase<ReassignCommand>
    {
        public ReassignCommandHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task ExecuteAsync(ReassignCommand command)
        {
            ProspectAssignmentEntity prospectAssignment = await EntitySet.GetAsync<ProspectAssignmentEntity>(command.ProspectAssignmentId);
            if (prospectAssignment != null && prospectAssignment.CallResultId.IsNotNullOrEmpty())
            {
                CallResultEntity callResult = await EntitySet.GetAsync<CallResultEntity>(prospectAssignment.CallResultId.Value);
                if (callResult.FollowUpStatus == FollowUpStatus.Win_StopFollow)
                {
                    command.ErrorCommand(string.Empty, T["Không thể chuyển liệu cho liên hệ có kết quả gọi WIN."]);
                    return;
                }
            }

            var cmd = EntitySet.CreateDbCommand();
            cmd.Parameters.Add(DbParameterHelper.AddNullableGuid(cmd,"@ProspectAssignmentId", command.ProspectAssignmentId));
            cmd.Parameters.Add(DbParameterHelper.AddNullableGuid(cmd,"@NewTeamId", command.NewTeamId));
            cmd.Parameters.Add(DbParameterHelper.AddNullableGuid(cmd,"@NewTMRId", command.NewTMRId));
            cmd.Parameters.Add(DbParameterHelper.AddNullableGuid(cmd,"@User", command.CurrentUser));

            cmd.CommandText = "telesale.Prospect_MoveToOtherTeam";
            cmd.CommandType = CommandType.StoredProcedure;

            await EntitySet.ExecuteNonQueryAsync(cmd);
        }
    }
}
