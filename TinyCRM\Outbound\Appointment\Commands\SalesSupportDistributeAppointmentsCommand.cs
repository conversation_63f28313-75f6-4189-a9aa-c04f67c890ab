﻿using System;
using System.Data;
using System.Threading.Tasks;
using Webaby;
using Webaby.Data;
using Webaby.Security;
using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Webaby.Localization;

namespace TinyCRM.Outbound.Appointment.Commands
{
    public class SalesSupportDistributeAppointmentsCommand : CommandBase
    {
        public DateTime? FromDate
        {
            get;
            set;
        }

        public DateTime? ToDate { get; set; }

        public Guid? UserId { get; set; }
    }
    public class SalesSupportDistributeAppointmentsCommandHandler : CommandHandlerBase<SalesSupportDistributeAppointmentsCommand>
    {
        public SalesSupportDistributeAppointmentsCommandHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task ExecuteAsync(SalesSupportDistributeAppointmentsCommand command)
        {
            var cmd = EntitySet.CreateDbCommand();
            cmd.Parameters.Add(DbParameterHelper.NewNullableDateTimeParameter(cmd, "@FromDate", command.FromDate));
            cmd.Parameters.Add(DbParameterHelper.NewNullableDateTimeParameter(cmd, "@ToDate", command.ToDate));
            cmd.Parameters.Add(DbParameterHelper.AddNullableGuid(cmd, "@UserId", command.UserId));
            cmd.CommandText = "telesale.SalesSupportDistributeAppointments";
            cmd.CommandType = CommandType.StoredProcedure;

            await EntitySet.ExecuteNonQueryAsync(cmd);
        }
    }
}