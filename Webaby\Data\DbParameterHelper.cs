﻿using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Primitives;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;

namespace Webaby.Data
{
    public static class DbParameterHelper
    {
        public static DbParameter NewNullableDateTimeParameter(DbCommand dbCommand, string name, DateTime? datetTimeValue)
        {
            DbParameter dbParameter = dbCommand.CreateParameter();
            dbParameter.DbType = DbType.DateTime;
            dbParameter.ParameterName = name;
            dbParameter.Value = datetTimeValue.HasValue ? (object)datetTimeValue.Value : DBNull.Value;

            return dbParameter;
        }

        public static DbParameter NewNullableTimeSpanParameter(DbCommand dbCommand, string name, TimeSpan? timeSpanValue)
        {
            DbParameter dbParameter = dbCommand.CreateParameter();
            dbParameter.DbType = DbType.Time;
            dbParameter.ParameterName = name;
            dbParameter.Value = timeSpanValue.HasValue ? (object)timeSpanValue.Value : DBNull.Value;

            return dbParameter;
        }

        public static DbParameter NewNullableBooleanParameter(DbCommand dbCommand, string name, bool? boolValue)
        {
            DbParameter dbParameter = dbCommand.CreateParameter();
            dbParameter.DbType = DbType.Boolean;
            dbParameter.ParameterName = name;
            dbParameter.Value = boolValue.HasValue ? (object)boolValue.Value : DBNull.Value;

            return dbParameter;
        }

        public static DbParameter AddNullableGuid(DbCommand dbCommand, string name, Guid? guidValue, bool emptyIsNull = true)
        {
            object value = DBNull.Value;
            if (guidValue.HasValue)
            {
                if (guidValue.Value == Guid.Empty)
                {
                    if (emptyIsNull == false)
                    {
                        value = (object)guidValue.Value;
                    }
                }
                else
                {
                    value = (object)guidValue.Value;
                }
            }

            DbParameter dbParameter = dbCommand.CreateParameter();
            dbParameter.DbType = DbType.Guid;
            dbParameter.ParameterName = name;
            dbParameter.Value = value;

            return dbParameter;
        }

        public static DbParameter AddNullableInt(DbCommand dbCommand, string name, int? intValue)
        {
            DbParameter dbParameter = dbCommand.CreateParameter();
            dbParameter.DbType = DbType.Int32;
            dbParameter.ParameterName = name;
            dbParameter.Value = intValue.HasValue ? (object)intValue.Value : DBNull.Value;

            return dbParameter;
        }

        public static DbParameter AddNullableDecimal(DbCommand dbCommand, string name, decimal? decimalValue)
        {
            DbParameter dbParameter = dbCommand.CreateParameter();
            dbParameter.DbType = DbType.Decimal;
            dbParameter.ParameterName = name;
            dbParameter.Value = decimalValue.HasValue ? (object)decimalValue.Value : DBNull.Value;

            return dbParameter;
        }

        public static DbParameter AddNullableDouble(DbCommand dbCommand, string name, double? doubleValue)
        {
            DbParameter dbParameter = dbCommand.CreateParameter();
            dbParameter.DbType = DbType.Double;
            dbParameter.ParameterName = name;
            dbParameter.Value = doubleValue.HasValue ? (object)doubleValue.Value : DBNull.Value;

            return dbParameter;
        }

        public static DbParameter AddNullableLong(DbCommand dbCommand, string name, long? longValue)
        {
            DbParameter dbParameter = dbCommand.CreateParameter();
            dbParameter.DbType = DbType.Int64;
            dbParameter.ParameterName = name;
            dbParameter.Value = longValue.HasValue ? (object)longValue.Value : DBNull.Value;

            return dbParameter;
        }

        public static DbParameter AddNullableString(DbCommand dbCommand, string name, string stringValue)
        {
            DbParameter dbParameter = dbCommand.CreateParameter();
            dbParameter.DbType = DbType.String;
            dbParameter.ParameterName = name;
            dbParameter.Value = string.IsNullOrEmpty(stringValue) ? (object)DBNull.Value : stringValue;

            return dbParameter;
        }

        public static DbParameter AddNullableEnum(DbCommand dbCommand, string name, Enum enumValue)
        {
            DbParameter dbParameter = dbCommand.CreateParameter();
            dbParameter.DbType = DbType.Int32;
            dbParameter.ParameterName = name;
            dbParameter.Value = enumValue ?? (object)DBNull.Value;

            return dbParameter;
        }

        public static SqlParameter NewIdListParameter(string name, string[] idStrings)
        {
            DataTable tbIdTable = new DataTable("IdList");
            tbIdTable.Columns.Add("Id", typeof(Guid));

            if (idStrings != null)
            {
                idStrings.ToList().ForEach(x => tbIdTable.Rows.Add(x));
            }
            return new SqlParameter(name, SqlDbType.Structured)
            {
                Value = tbIdTable
            };
        }

        public static SqlParameter NewIdListParameter(string name, List<Guid> ids)
        {
            DataTable tbIdTable = new DataTable("IdList");
            tbIdTable.Columns.Add("Id", typeof(Guid));

            if (ids != null)
            {
                ids.ForEach(x => tbIdTable.Rows.Add(x));
            }
            return new SqlParameter(name, SqlDbType.Structured)
            {
                Value = tbIdTable
            };
        }

        public static SqlParameter NewTableStringStringParameter(string name, DataTable dataTable)
        {
            return new SqlParameter(name, SqlDbType.Structured)
            {
                Value = dataTable
            };
        }

        public static SqlParameter NewIntListParameter(string name, List<int> enums)
        {
            DataTable tbEnumTable = new DataTable("IntList");
            tbEnumTable.Columns.Add("Number", typeof(int));
            if (enums != null)
            {
                enums.ForEach(x => tbEnumTable.Rows.Add(x));
            }
            return new SqlParameter(name, SqlDbType.Structured)
            {
                Value = tbEnumTable
            };
        }

        public static SqlParameter NewDateTimeListParameter(string name, List<DateTime> dateTimes)
        {
            DataTable tbDateTimeTable = new DataTable("DateTimeList");
            tbDateTimeTable.Columns.Add("DateTimeValue", typeof(DateTime));

            if (dateTimes != null)
            {
                dateTimes.ForEach(x => tbDateTimeTable.Rows.Add(x));
            }
            return new SqlParameter(name, SqlDbType.Structured)
            {
                Value = tbDateTimeTable
            };
        }

        public static SqlParameter NewKeyValueListParameter(string name, List<KeyValueSqlTableType> keyValueList)
        {
            DataTable tbIdTable = new DataTable("KeyValue");
            tbIdTable.Columns.Add("Key", typeof(Guid));
            tbIdTable.Columns.Add("Value", typeof(int));

            if (keyValueList != null)
            {
                keyValueList.ForEach(x => tbIdTable.Rows.Add(x.Key, x.Value));
            }
            return new SqlParameter(name, SqlDbType.Structured)
            {
                Value = tbIdTable
            };
        }

        public static SqlParameter NewStringListParameter(string name, List<string> enums)
        {
            DataTable tbEnumTable = new DataTable("dbo.stringList");
            tbEnumTable.Columns.Add("Value", typeof(string));
            if (enums != null)
            {
                enums.ForEach(x => tbEnumTable.Rows.Add(x));
            }
            return new SqlParameter(name, SqlDbType.Structured)
            {
                Value = tbEnumTable
            };
        }

        public static SqlParameter NewStructuredParameter<T>(string name, string type, List<T> data, string defaultIfScalar = "Id")
        {
            DataTable table = new DataTable(type);

            Type _type = typeof(T);
            bool isSimple =
                _type.IsPrimitive ||
                _type.IsEnum ||
                _type == typeof(string) ||
                _type == typeof(decimal) ||
                _type == typeof(DateTime) ||
                _type == typeof(Guid);
            var props = _type.GetProperties(BindingFlags.Public | BindingFlags.Instance);
            if (isSimple || props.Length == 0)
            {
                table.Columns.Add(defaultIfScalar, typeof(T));
            }
            else
            {
                foreach (PropertyInfo prop in props)
                {
                    Type ptype = Nullable.GetUnderlyingType(prop.PropertyType);
                    ptype = ptype != null ? ptype : prop.PropertyType;
                    table.Columns.Add(prop.Name, ptype);
                }
            }
            if (data != null && data.Count > 0)
            {
                foreach (T item in data)
                {
                    DataRow dr = table.NewRow();
                    if (isSimple || props.Length == 0)
                    {
                        dr[defaultIfScalar] = item;
                    }
                    else
                    {
                        foreach (PropertyInfo prop in props)
                        {
                            object value = prop.GetValue(item);
                            dr[prop.Name] = value ?? DBNull.Value;
                        }
                    }
                    table.Rows.Add(dr);
                }
            }
            return new SqlParameter(name, SqlDbType.Structured)
            {
                Value = table
            };
        }
    }

    public class KeyValueSqlTableType
    {
        public Guid Key { get; set; }

        public int Value { get; set; }
    }
}
