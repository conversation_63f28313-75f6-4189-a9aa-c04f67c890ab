﻿using System;
using System.Data;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Webaby;
using Webaby.Data;

namespace TinyCRM.Part.Commands
{
    public class LinkTicketTaskPartByNameCommand : CommandBase
    {
        public Guid RequestTicketId { get; set; }
    }

    internal class LinkTicketTaskPartByNameCommandHandler : CommandHandlerBase<LinkTicketTaskPartByNameCommand>
    {
        private readonly ILogger<LinkTicketTaskPartByNameCommandHandler> _logger;

        public LinkTicketTaskPartByNameCommandHandler(IServiceProvider serviceProvider, ILogger<LinkTicketTaskPartByNameCommandHandler> logger) : base(serviceProvider)
        {
            _logger = logger;
        }

        /// <summary>Link ticket task part by name</summary>
        public override async Task ExecuteAsync(LinkTicketTaskPartByNameCommand command)
        {
            var cmd = EntitySet.CreateDbCommand();
            cmd.Parameters.Add(DbParameterHelper.AddNullableGuid(cmd, "@RequestTicketId", command.RequestTicketId));

            cmd.CommandText = "dbo.LinkTicketTaskPartByName";
            cmd.CommandType = CommandType.StoredProcedure;

            await EntitySet.ExecuteNonQueryAsync(cmd);
        }
    }
}