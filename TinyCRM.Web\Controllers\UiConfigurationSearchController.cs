﻿using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using TinyCRM.AppServices.Customer;
using TinyCRM.BusinessResult.Queries;
using TinyCRM.Customer.Commands;
using TinyCRM.Customer.Queries;
using TinyCRM.Query.Command;
using TinyCRM.Query.Queries;
using TinyCRM.Web.Models;
using TinyCRM.Web.Models.RequestTicket;
using TinyCRM.Web.Models.UiConfigurationSearch;
using Webaby;
using Webaby.Caching;
using Webaby.Core.BusinessSettings;
using Webaby.Core.BusinessSettings.Command;
using Webaby.Data;
using Webaby.Security.Authorize;
using Webaby.Web;
using Webaby.Web.Attributes;
using Newtonsoft.Json;
using Webaby.BusinessSetting;
using Microsoft.EntityFrameworkCore;



namespace TinyCRM.Web.Controllers
{
    public class UiConfigurationSearchController : WebabyControllerBase
    {
        public UiConfigurationSearchController(IServiceProvider serviceProvider) : base(serviceProvider) {
            EntitySet = ServiceProvider.GetRequiredService<IEntitySet>();
            CacheProvider = ServiceProvider.GetRequiredService<ICacheProvider>();
            CustomerAppService = ServiceProvider.GetRequiredService<ICustomerAppService>();
        }
        private const int _uiConfigExpireInMinutes = 60;
        
        public IEntitySet EntitySet { get; set; }        
        public ICacheProvider CacheProvider { get; set; }        
        public ICustomerAppService CustomerAppService { get; set; }

        public IActionResult Index()
        {
            return View();
        }
        [AllowAuthenticated]
        [HttpPost]
        public IActionResult SearchFilter(GetKeyValue model)
        {
            var json_data = Configuration.GetValue<string>("ticket.filter.config");
            var default_data = JsonConvert.DeserializeObject<RequestTicketFilterConfig>(json_data);

            var dataConfigure = new ConfigureUISearchByModeModel();
            dataConfigure.LiveModel = default_data;
            string testModeCacheKey = string.Format("{0}_{1}", model.KeyValue, UserIdentity.Id);
            string dataResult_true_cache = model.KeyValue;
            if (CacheProvider.IsSet(dataResult_true_cache))
            {
                var datakhoitao = CacheProvider.Get(dataResult_true_cache) as RequestTicketFilterConfig;
                dataConfigure.LiveModel = datakhoitao;
            }
            if (CacheProvider.IsSet(testModeCacheKey))
            {
                dataConfigure.TestModel = new TestModeModel();
                dataConfigure.TestModel = CacheProvider.Get(testModeCacheKey) as TestModeModel;
                if(model.KeyValue == "configure_filter")
                {
                    return View("Partials/FilterImport", dataConfigure);
                }
                return View("Partials/ResultImport", dataConfigure);
            }

            if (model.KeyValue == "configure_filter")
            {
                return View("Partials/FilterImport", dataConfigure);
            }
            return View("Partials/ResultImport", dataConfigure);
        }
        [AllowAuthenticated]
        [HttpPost]
        public IActionResult CloneLiveToTestMode(ConfigureUISearchByModeModel model)
        {
            ConfigureUISearchByModeModel configureSearchByModeModel = new ConfigureUISearchByModeModel();
            configureSearchByModeModel.LiveModel = model.LiveModel;
            string testModeCacheKey = string.Format("{0}_{1}", model.KeyValue, UserIdentity.Id);
            if (CacheProvider.IsSet(testModeCacheKey))
            {
                CacheProvider.Invalidate(testModeCacheKey);
            }
            configureSearchByModeModel.TestModel = new TestModeModel();

            configureSearchByModeModel.TestModel.requestTicketFilterConfig = configureSearchByModeModel.LiveModel;

            var labelInfo = System.ComponentModel.TypeDescriptor.GetProperties(typeof(RequestTicketSearchModel))
    .Cast<System.ComponentModel.PropertyDescriptor>()
    .ToDictionary(p => p.Name, p => p.DisplayName);

            if(model.KeyValue == "configure_result")
            {
                foreach (var item in configureSearchByModeModel.TestModel.requestTicketFilterConfig.result)
                {
                    if (item.display.IsNullOrEmpty())
                    {
                        item.display = labelInfo[item.name];
                    }
                }
            } else
            {
                foreach (var item in configureSearchByModeModel.TestModel.requestTicketFilterConfig.filter)
                {
                    if (item.display.IsNullOrEmpty())
                    {
                        item.display = labelInfo[item.name];
                    }
                }
            }

            CacheProvider.Set(testModeCacheKey, configureSearchByModeModel.TestModel, _uiConfigExpireInMinutes);

            Info(T["Đã Clone qua phiên bản thử nghiêm."]);
            if(model.KeyValue == "configure_filter")
            {
                return View("Partials/FilterImport", configureSearchByModeModel);
            }
            return View("Partials/ResultImport", configureSearchByModeModel);
        }
        [AllowAuthenticated]
        [HttpPost]
        public IActionResult SaveTestModeResult(ConfigureUISearchByModeModel model)
        {
            string testModeCacheKey = string.Format("{0}_{1}", model.KeyValue, UserIdentity.Id);
            var testsaved = new TestModeModel();
            bool isActive = false;
            if (CacheProvider.IsSet(testModeCacheKey))
            {
                testsaved = CacheProvider.Get(testModeCacheKey) as TestModeModel;
                isActive = testsaved.IsActive;
                CacheProvider.Invalidate(testModeCacheKey);
            }
            testsaved.IsActive = isActive;
            testsaved.requestTicketFilterConfig = model.TestModel.requestTicketFilterConfig;

            CacheProvider.Set(testModeCacheKey, testsaved, _uiConfigExpireInMinutes);
            Info(T["Đã lưu vào phiên bản thử nghiệm thành công."]);
            return DefaultResult();
        }

        [AllowAuthenticated]
        public IActionResult ExportFileJsonConfig(ConfigureUISearchByModeModel model)
        {
            string exportJson = string.Empty;
            string fileName = string.Empty;

            if (model.KeyValue == "" || model.KeyValue == null) return DefaultResult();

            if (model.KeyValue == "configure_filter")
            {                
                exportJson = JsonConvert.SerializeObject(model.TestModel.requestTicketFilterConfig.filter);
                fileName = "filter_config.txt";
            }
            else
            {                
                exportJson = JsonConvert.SerializeObject(model.TestModel.requestTicketFilterConfig.result);
                fileName = "result_config.txt";
            }

            // Return the JSON string as a file
            return File(Encoding.UTF8.GetBytes(exportJson), "text/plain", fileName);
        }

        [AllowAuthenticated]
        [HttpPost]
        public IActionResult ActiveTestModelModel(bool isActive, ConfigureUISearchByModeModel model)
        {
            if (model.IsTest)
            {
                var json_data = Configuration.GetValue<string>("ticket.filter.config");
                var default_data = JsonConvert.DeserializeObject<RequestTicketFilterConfig>(json_data);
                var dataConfigure = new ConfigureUISearchByModeModel();
                dataConfigure.LiveModel = default_data;

                string testModeCacheKey = string.Format("{0}_{1}", model.KeyValue, UserIdentity.Id);
                if (CacheProvider.IsSet(testModeCacheKey))
                {
                    CacheProvider.Invalidate(testModeCacheKey);

                    dataConfigure.TestModel = new TestModeModel 
                    { 
                        requestTicketFilterConfig = model.TestModel.requestTicketFilterConfig,
                        IsActive = isActive
                    };
                    CacheProvider.Set(testModeCacheKey, dataConfigure.TestModel, _uiConfigExpireInMinutes);
                    if (isActive)
                    {
                        Info(T["Đã kích hoạt phiên bản thử nghiệm."]);
                    }
                    else
                    {
                        Info(T["Đã hủy kích hoạt phiên bản thử nghiệm."]);
                    }
                    if (model.KeyValue == "configure_filter")
                    {
                        return View("Partials/FilterImport", dataConfigure);
                    }
                    return View("Partials/ResultImport", dataConfigure);
                }
                else
                {
                    Error(T["Phiên bản thử nghiệm không tồn tại."]);
                }
            }

            return DefaultResult();
        }
        [AllowAuthenticated]
        [HttpPost]
        public async Task<IActionResult> SaveTestModelToLiveModeModel(ConfigureUISearchByModeModel model)
        {
            if (model.TestModel != null && model.TestModel.requestTicketFilterConfig != null)
            {
                string importKey = model.KeyValue;
                RequestTicketFilterConfig constructor = new RequestTicketFilterConfig
                {
                    filter = new FilterControl[] { },
                    result = new ResultColumn[] { }
                };
                try
                {
                    var default_data = Configuration.GetValue<string>("ticket.filter.config");
                    constructor = JsonConvert.DeserializeObject<RequestTicketFilterConfig>(default_data);
                    if(constructor.layout == null)
                    {
                        constructor.layout = new RequestTicketFilterConfig.LayoutConfig();
                    }
                }
                catch (Exception) { }
                if(model.KeyValue == "configure_filter")
                {
                    constructor.filter = model.TestModel.requestTicketFilterConfig.filter;
                    constructor.layout.filterDisplayRow = model.TestModel.requestTicketFilterConfig.layout.filterDisplayRow;
                }
                else
                {
                    constructor.result = model.TestModel.requestTicketFilterConfig.result;
                }

                ConfigureUISearchByModeModel configure = new ConfigureUISearchByModeModel();

                string businessValue = JsonConvert.SerializeObject(constructor);
                await CommandExecutor.ExecuteAsync(new UpdateBusinessSettingItemValueCommand { ImportKey = "ticket.filter.config", DataType = "System.String", Value = businessValue });

                if (CacheProvider.IsSet(importKey))
                {
                    CacheProvider.Invalidate(importKey);
                }

                CacheProvider.Set(importKey, model.TestModel.requestTicketFilterConfig, 10 * 365 * 24 * 60);    // 10 years

                string testModeCacheKey = string.Format("{0}_{1}", model.KeyValue, UserIdentity.Id);
                if (CacheProvider.IsSet(testModeCacheKey))
                {
                    CacheProvider.Invalidate(testModeCacheKey);
                }
                configure.LiveModel = model.TestModel.requestTicketFilterConfig;

                Info(T["Đã lưu vào phiên bản chính. Liên hệ quản trị khởi động lại hệ thống để "]);
                if (model.KeyValue == "configure_filter")
                {
                    return View("Partials/FilterImport", configure);
                }
                return View("Partials/ResultImport", configure);
            }
            else
            {
                Error(T["Phiên bản thử nghiệm không tồn tại."]);
            }

            return DefaultResult();
        }
        [AllowAuthenticated]
        [HttpPost]
        public async Task<IActionResult> SaveConfigResultColumn(GetConfigResultColumnSearchQuery model)
        {
            var testvalue = await QueryExecutor.ExecuteOneAsync(new GetValueSearchTicketQuery { Id = model.QueryId });
            List<QueryItem> QueryData = JsonConvert.DeserializeObject<List<QueryItem>>(testvalue.Value);
            QueryData.RemoveAll(x => x.name == "TicketQueryRC");
            QueryData.RemoveAll(x => x.name == "X-Requested-By");
            QueryData.RemoveAll(x => x.name == "X-Requested-ClientWindowsId");
            QueryData.RemoveAll(x => x.name == "X-Requested-FormId");

            var newdata = new QueryItem
            {
                name = "TicketQueryRC",
                value = JsonConvert.SerializeObject(model.results)
            };

            QueryData.Add(newdata);
            var change_result = JsonConvert.SerializeObject(QueryData);
            var command = new CreateEditQueryCommand
            {
                QueryId = (Guid)model.QueryId,
                QueryValue = change_result,
                CreatedBy = UserIdentity.Id
            };
            await CommandExecutor.ExecuteAsync(command);
            Info(T["Đã lưu thành công"]);
            return DefaultResult();
        }

        [AllowAuthenticated]
        [HttpPost]
        //[ValidateInput(false)]
        public async Task<IActionResult> SavedConfigPivotTable(string shellName, Guid? queryId, string configData)
        {
            var config_rt = Guid.Empty;
            var shellSearchName = shellName;
            if (queryId.HasValue)
            {
                config_rt = queryId.Value;
            } else if(shellName.IsNotNullOrEmpty())
            {
                RequestTicketShellSearchConfig requestTicketShellSearchConfig = null;

                List<RequestTicketShellSearchConfig> requestTicketShellSearchConfigList = new List<RequestTicketShellSearchConfig>();
                try
                {
                    var requestTicketShellSearchConfigs = Configuration.GetValue<string>("requestticket.shellsearch.configs");
                    requestTicketShellSearchConfigs = (await EntitySet.Get<BusinessSettingEntity>().FirstOrDefaultAsync(x => x.ImportKey == "requestticket.shellsearch.configs"))?.Value;
                    requestTicketShellSearchConfigList = JsonConvert.DeserializeObject<List<RequestTicketShellSearchConfig>>(requestTicketShellSearchConfigs);
                }
                catch (Exception) { }

                requestTicketShellSearchConfig = requestTicketShellSearchConfigList.Where(x => x.Name.IsEqualIgnoreCase(shellName) && x.RouteConfig == "List").FirstOrDefault();
                if(requestTicketShellSearchConfig != null)
                {
                    if (requestTicketShellSearchConfig.SavedQueryId.HasValue)
                    {
                        config_rt = requestTicketShellSearchConfig.SavedQueryId.Value;
                    }
                }
            }
            if(!queryId.HasValue && shellName.IsNullOrEmpty())
            {
                var keycache = "requestticket_pivot";
                if (CacheProvider.IsSet(keycache))
                {
                    CacheProvider.Invalidate(keycache);
                }
                CacheProvider.Set(keycache, configData, 365 * 24 * 60);
                Info(T["Đã lưu thành công"]);
                return DefaultResult();
            }
            var textvalue = await QueryExecutor.ExecuteOneAsync(new GetValueSearchTicketQuery { Id = config_rt });
            List<QueryItem> QueryData = JsonConvert.DeserializeObject<List<QueryItem>>(textvalue.Value);
            QueryData.RemoveAll(x => x.name == "X-Requested-By");
            QueryData.RemoveAll(x => x.name == "X-Requested-ClientWindowsId");
            QueryData.RemoveAll(x => x.name == "X-Requested-FormId");
            QueryData.RemoveAll(x => x.name == "TicketQueryPT");

            var newdata = new QueryItem
            {
                name = "TicketQueryPT",
                value = configData
            };

            QueryData.Add(newdata);
            var change_result = JsonConvert.SerializeObject(QueryData);
            var command = new CreateEditQueryCommand
            {
                QueryId = config_rt,
                QueryValue = change_result,
                CreatedBy = UserIdentity.Id,
                QueryShellSearchName = shellSearchName
            };
            await CommandExecutor.ExecuteAsync(command);
            Info(T["Đã lưu thành công"]);
            return DefaultResult();
        }

        [HttpGet]
        [AllowAuthenticated]
        public async Task<IActionResult> GetAllHiddenFilter()
        {
            var result = await QueryExecutor.ExecuteManyAsync(new GetHiddenQuery { Indentity = UserIdentity.Id });
            return Json(result.Select(x => new { Value = x.Value, Text = x.Name }));
        }

        [HttpGet]
        [AllowAuthenticated]
        public IActionResult ConfigFilterCustomer()
        {
            CustomerSearchModel model = new CustomerSearchModel();
            model.Configs = CustomerAppService.GetFieldConfiguration("CustomerSearch").ToList();
            model.Configs.Add(new CustomerFieldConfiguration
            {
                FieldName = "IsBackendCustomer",
                DataType = "System.Boolean",
                DisplayName = "Nguồn khách hàng",
                GridSize = 3,
                Order = 6
            });
            return View(model);
        }

        [HttpPost]
        [AllowAuthenticated]
        public async Task<IActionResult> ConfigFilterCustomer(List<CustomerFieldConfigurationData> model)
        {
            await CommandExecutor.ExecuteAsync(new UpdateCustomerFieldConfigCommand { listconfig = model });
            Info(T["Đã lưu thành công"]);
            return DefaultResult();
        }
    }
}