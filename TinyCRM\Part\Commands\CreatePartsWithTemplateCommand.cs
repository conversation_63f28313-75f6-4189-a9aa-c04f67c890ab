﻿using System;
using System.Data;
using System.Data.SqlClient;
using System.Threading.Tasks;
using Webaby;
using Webaby.Data;

namespace TinyCRM.Part.Commands
{
    public class CreatePartsWithTemplateCommand : CommandBase
    {
        public Guid Id { get; set; }

        public string Name { get; set; }

        public string Template { get; set; }

        public Guid CreatedBy { get; set; }
    }
    internal class CreatePartsWithTemplateCommandHandler : CommandHandlerBase<CreatePartsWithTemplateCommand>
    {
        public CreatePartsWithTemplateCommandHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task ExecuteAsync(CreatePartsWithTemplateCommand command)
        {
            var cmd = EntitySet.CreateDbCommand();
            cmd.Parameters.Add(DbParameterHelper.AddNullableGuid(cmd, "@Id", command.Id));
            cmd.Parameters.Add(DbParameterHelper.AddNullableString(cmd, "@Name", command.Name));
            cmd.Parameters.Add(DbParameterHelper.AddNullableString(cmd, "@Template", command.Template));
            cmd.Parameters.Add(DbParameterHelper.AddNullableGuid(cmd, "@CreatedBy", command.CreatedBy));

            cmd.CommandText = "dbo.CreateRootPart";
            cmd.CommandType = CommandType.StoredProcedure;

            await EntitySet.ExecuteNonQueryAsync(cmd);
        }
    }
}
