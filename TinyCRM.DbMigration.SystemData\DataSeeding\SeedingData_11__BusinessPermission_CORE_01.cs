﻿using Microsoft.EntityFrameworkCore;
using System.Reflection;
using TinyCRM.Outbound.UserAccount;
using Webaby.Data;
using Webaby.Data.Seeding;
using Webaby.Security;

namespace TinyCRM.DbMigration.SystemData.DataSeeding
{
    internal class SeedingData_11__BusinessPermission_CORE_01(MigrationDbContext dbContext) 
        : SeedingJsonData<BusinessPermissionEntity>(dbContext)
    {
        protected override Assembly ResourceAssembly => Assembly.GetExecutingAssembly();
        protected override SeedingJsonDataAction SeedingJsonDataAction => SeedingJsonDataAction.AddOrUpdate;

        private async Task HandleAccessBusinessPermission(Guid admin, Guid businessPermissionId, string controllerName, string[] actionName, bool del = true)
        {
            if (del)
            {
                await DbContext.Set<AccessBusinessPermissionEntity>()
                    .Where(x => x.BusinessPermissionId == businessPermissionId)
                    .ExecuteDeleteAsync();
                if (DbContext.ChangeTracker.HasChanges())
                {
                    await DbContext.SaveChangesAsync();
                }
            }
            var newEntities = await DbContext.Set<AccessEntity>()
                .Where(x => x.ControllerName == controllerName)
                .WhereIf(actionName?.Length > 0, x => actionName.Contains(x.ActionName))
                .Select(x => new AccessBusinessPermissionEntity
                {
                    Id = Guid.NewGuid(),
                    AccessId = x.Id,
                    BusinessPermissionId = businessPermissionId,
                    CreatedDate = DateTime.Now,
                    CreatedBy = admin,
                    Deleted = false
                })
                .ToArrayAsync();
            await DbContext.Set<AccessBusinessPermissionEntity>().AddRangeAsync(newEntities);
            if (DbContext.ChangeTracker.HasChanges())
            {
                await DbContext.SaveChangesAsync();
            }
        }

        protected override async Task AfterExecuteAsync()
        {
            var admin = Guid.Parse("00000000-1111-2222-3333-************");
            {
                var businessPermissionId = Guid.Parse("0E0180C0-4468-4A42-8C56-E212C1E6561A");
                var controllerName = "Home";
                string[] actionName = ["Index", "Dashboard", "GoAfterLogin"];
                await HandleAccessBusinessPermission(admin, businessPermissionId, controllerName, actionName);
            }

            {
                var businessPermissionId = Guid.Parse("CC9571BE-FB49-4CE0-85F6-BBDD91DAF4CC");
                var controllerName = "Localization";
                string[] actionName = ["SearchLocalizationByLanguages", "SubmitLocalizationLists", "Translate", "ClearTrackedKeyList"];
                await HandleAccessBusinessPermission(admin, businessPermissionId, controllerName, actionName);
            }

            {
                var businessPermissionId = Guid.Parse("68055971-C2D9-4949-A4BD-D4BCC4CB624C");
                var controllerName = "Report";
                string[] actionName = ["StatisticizeUpComingDeadlineTicket"];
                await HandleAccessBusinessPermission(admin, businessPermissionId, controllerName, actionName);
            }

            {
                var businessPermissionId = Guid.Parse("3079A81C-15D4-450D-9BE2-89DD03B03D8E");
                var controllerName = "Customer";
                string[] actionName = ["Index", "SearchCustomers"];
                await HandleAccessBusinessPermission(admin, businessPermissionId, controllerName, actionName);
            }

            {
                var businessPermissionId = Guid.Parse("A8E1272D-4012-4035-8B4A-9075EA29219C");
                var controllerName = "Inbound";
                string[] actionName = ["Index"];
                await HandleAccessBusinessPermission(admin, businessPermissionId, controllerName, actionName);
            }

            {
                var businessPermissionId = Guid.Parse("C477601F-9D54-41F3-9334-8E14BE3E62D1");
                var controllerName = "AdditionalDisplay";
                string[] actionName = ["ExecuteApi"];
                await HandleAccessBusinessPermission(admin, businessPermissionId, controllerName, actionName);
            }

            {
                var businessPermissionId = Guid.Parse("70C9B8FA-ACBF-43AE-80D9-2A67298F92FE");
                var controllerName = "Api.Customer.Core";
                string[] actionName = ["Customer/GetInfoAccount"];
                await HandleAccessBusinessPermission(admin, businessPermissionId, controllerName, actionName);
            }

            {
                var businessPermissionId = Guid.Parse("1A5A3AEF-A85F-4C6C-A32A-AC91DACE434A");
                var controllerName = "Api.Customer.Core";
                string[] actionName = ["Customer/GetChuKyTaiKhoan"];
                await HandleAccessBusinessPermission(admin, businessPermissionId, controllerName, actionName);
            }

            {
                var businessPermissionId = Guid.Parse("C799B68D-EBF6-4B6B-B6C6-5A3F36178F4A");
                var controllerName = "WebChat";
                string[] actionName = ["SearchWebChat", "Index"];
                await HandleAccessBusinessPermission(admin, businessPermissionId, controllerName, actionName);
            }

            {
                var businessPermissionId = Guid.Parse("AEA858D7-13AD-48B5-BD71-6B539D155D5B");
                var controllerName = "RequestTicket";
                string[] actionName = ["DeleteRequestTicketWebChat"];
                await HandleAccessBusinessPermission(admin, businessPermissionId, controllerName, actionName);
            }

            {
                var businessPermissionId = Guid.Parse("BE77038F-CB62-4F7A-A1D6-117DBC4B2AC0");
                var controllerName = "CallbackSettings";
                string[] actionName = ["Edit", "Index", "Search"];
                await HandleAccessBusinessPermission(admin, businessPermissionId, controllerName, actionName);
            }

            {
                var businessPermissionId = Guid.Parse("8B729CB3-2C4F-4924-9319-A4D1F195B174");
                var controllerName = "Callback";
                string[] actionName = ["Index", "GetCallbackDetailsByCallbackID", "Search"];
                await HandleAccessBusinessPermission(admin, businessPermissionId, controllerName, actionName);
            }

            {
                var businessPermissionId = Guid.Parse("99B92FBE-2391-43E0-B395-1F5A2A8DAEE1");
                var controllerName = "SelfServiceFlow";
                string[] actionName = ["ProductivityUser"];
                await HandleAccessBusinessPermission(admin, businessPermissionId, controllerName, actionName);
            }

            {
                var businessPermissionId = Guid.Parse("9903CD2F-AD1C-4D2C-9FA3-8405A6C60FBB");
                var controllerName = "Callback";
                string[] actionName = ["ReportSummaryCallback"];
                await HandleAccessBusinessPermission(admin, businessPermissionId, controllerName, actionName);
            }

            {
                var businessPermissionId = Guid.Parse("A290512B-3F32-4CC5-AD84-C9A5DB020B0A");
                var controllerName = "SelfServiceFlow";
                string[] actionName = ["UWCount"];
                await HandleAccessBusinessPermission(admin, businessPermissionId, controllerName, actionName);
            }

            {
                var businessPermissionId = Guid.Parse("4CD030C8-14A5-4E12-8565-4716F59A0DC0");
                var controllerName = "SelfServiceFlow";
                string[] actionName = ["UserVisitsCount"];
                await HandleAccessBusinessPermission(admin, businessPermissionId, controllerName, actionName);
            }

            {
                var businessPermissionId = Guid.Parse("090941B8-AAFC-4F50-83F2-51DA15C37A7B");
                var controllerName = "SelfServiceFlow";
                string[] actionName = ["RatingReport"];
                await HandleAccessBusinessPermission(admin, businessPermissionId, controllerName, actionName);
            }

            {
                var businessPermissionId = Guid.Parse("44F2CBAA-1C2E-44FF-A356-4065E13A0015");
                var controllerName = "RequestTicket";
                string[] actionName = ["CreateNoneCustomerTicket", "CreateByHotServiceTypes"];
                await HandleAccessBusinessPermission(admin, businessPermissionId, controllerName, actionName);
            }

            {
                var businessPermissionId = Guid.Parse("6984AAEE-0E60-47DE-AF32-5D99B706351D");
                var controllerName = "RequestTicket";
                string[] actionName = ["Index", "Search"];
                await HandleAccessBusinessPermission(admin, businessPermissionId, controllerName, actionName);
            }

            {
                var businessPermissionId = Guid.Parse("9F88E776-1ACC-4C12-8FDB-7FADCB0D0346");
                var controllerName = "RequestTicket";
                string[] actionName = ["Index", "Search"];
                await HandleAccessBusinessPermission(admin, businessPermissionId, controllerName, actionName);
            }

            {
                var businessPermissionId = Guid.Parse("67ADB29A-401D-46C4-8769-E67AACD90CC5");
                var controllerName = "RequestTicket";
                string[] actionName = ["Index", "Search"];
                await HandleAccessBusinessPermission(admin, businessPermissionId, controllerName, actionName);
            }

            {
                var businessPermissionId = Guid.Parse("DA4440EC-4E2F-4AB1-921D-4A9572070DF6");
                var controllerName = "RequestTicket";
                string[] actionName = ["ExportSearchRequestTicketResult"];
                await HandleAccessBusinessPermission(admin, businessPermissionId, controllerName, actionName);
            }

            {
                var businessPermissionId = Guid.Parse("7387EFF4-9419-4352-BE2D-5DAB78BA8A86");
                var controllerName = "RequestTicket";
                string[] actionName = ["LoadCreateEditProductRequestTicket", "GetProductRequestTicket", "DeleteProductRequestTicket", "CreateEditProductRequestTicket", "Create"];
                await HandleAccessBusinessPermission(admin, businessPermissionId, controllerName, actionName);
            }

            {
                var businessPermissionId = Guid.Parse("2EF3DDEA-413B-4744-8B32-C9E2B936A583");
                var controllerName = "RequestTicket";
                string[] actionName = ["Create"];
                await HandleAccessBusinessPermission(admin, businessPermissionId, controllerName, actionName);
            }

            {
                var businessPermissionId = Guid.Parse("C66229A1-3BEC-421C-A431-6554C8EA72EB");
                var controllerName = "RequestTicket";
                string[] actionName = ["CreateEditProductExchange",
                "CreateEditProductRequestTicket",
                "DeleteProductRequestTicket",
                "DeleteProductExchange",
                "Edit",
                "GetProductRequestTicket",
                "GetProductExchange",
                "LoadCreateEditProductRequestTicket",
                "GetRequestTicketHistories",];
                await HandleAccessBusinessPermission(admin, businessPermissionId, controllerName, actionName);
            }

            {
                var businessPermissionId = Guid.Parse("9948EC7C-D4BE-4EC9-8116-867292AEA4FD");
                var controllerName = "RequestTicket";
                string[] actionName = ["Edit", "GetRequestTicketHistories"];
                await HandleAccessBusinessPermission(admin, businessPermissionId, controllerName, actionName);
            }

            {
                var businessPermissionId = Guid.Parse("B34A2284-1BE0-4A79-A1ED-8538404F5335");
                var controllerName = "RequestTicket";
                string[] actionName = ["ExportRequestTicket"];
                await HandleAccessBusinessPermission(admin, businessPermissionId, controllerName, actionName);
            }

            {
                var businessPermissionId = Guid.Parse("B9249FB9-7443-4CF1-B2C2-143A05EDA5E8");
                var controllerName = "RequestTicket";
                string[] actionName = ["FinishRequestTicket"];
                await HandleAccessBusinessPermission(admin, businessPermissionId, controllerName, actionName);
            }

            {
                var businessPermissionId = Guid.Parse("E9B2F1FA-5F6F-4F38-AEF2-5C472BE2985D");
                var controllerName = "RequestTicket";
                string[] actionName = ["OpenRequestTicket"];
                await HandleAccessBusinessPermission(admin, businessPermissionId, controllerName, actionName);
            }

            {
                var businessPermissionId = Guid.Parse("CC4F754F-4CBD-4482-9777-2045E3302AE5");
                var controllerName = "RequestTicket";
                string[] actionName = ["DeleteRequestTicket"];
                await HandleAccessBusinessPermission(admin, businessPermissionId, controllerName, actionName);
            }

            {
                var businessPermissionId = Guid.Parse("956CEBE8-CA2D-475D-B981-2088F4E812C8");
                var controllerName = "RequestTicket";
                string[] actionName = [
                    "ExportSearchRequestTicketResultToImport",
                    "DownloadImportRequestTicketResult",
                    "GetImportRequestTicketHistories",
                    "ImportRequestTicket",
                    "ImportRequestTicketResult",
                    "CancelImportRequestTicket"
                ];
                await HandleAccessBusinessPermission(admin, businessPermissionId, controllerName, actionName);
            }

            {
                var businessPermissionId = Guid.Parse("32046267-656C-4598-9364-DA319785D037");
                var controllerName = "RequestTicket";
                string[] actionName = [
                    "ProcessDoneRequestTicketImportSessionResult",
                    "ScanDoneRequestTicketImportSessionResult",
                    "DownloadDoneRequestTicketImportSessionResult",
                    "FinishRequestTicketByImport"
                ];
                await HandleAccessBusinessPermission(admin, businessPermissionId, controllerName, actionName);
            }

            {
                var businessPermissionId = Guid.Parse("E9F4DD7D-1329-4F47-BCA6-1B7BE468A076");
                var controllerName = "Task";
                string[] actionName = ["OpenAddTaskScreen", "CreateTask", "OpenTaskManager"];
                await HandleAccessBusinessPermission(admin, businessPermissionId, controllerName, actionName);

                controllerName = "RequestTicket";
                actionName = ["GetUserListForTicket"];
                await HandleAccessBusinessPermission(admin, businessPermissionId, controllerName, actionName, false);
            }

            {
                var businessPermissionId = Guid.Parse("ECEC7A1F-1095-43D2-B40F-B14A0BD59973");
                var controllerName = "Task";
                string[] actionName = [
                    "UpdatePhase",
                    "UpdateTaskFeedback",
                    "DownloadFeedback",
                    "RemoveFeedbackFile",
                    "GetTaskHistory",
                    "OpenTaskFeedbackPopup",
                    "UpdateTask",
                    "OpenTaskPopup",
                ];
                await HandleAccessBusinessPermission(admin, businessPermissionId, controllerName, actionName);

                controllerName = "RequestTicket";
                actionName = [
                    "GetProductExchange",
                    "GetRequestTicketHistories",
                    "GetProductRequestTicket",
                    "Edit",
                ];
                await HandleAccessBusinessPermission(admin, businessPermissionId, controllerName, actionName, false);
            }

            {
                var businessPermissionId = Guid.Parse("378697F3-FE55-41DA-9922-BAAE9BF7B391");
                var controllerName = "Task";
                string[] actionName = [
                    "UpdateTaskFeedback",
                    "DownloadFeedback",
                    "UpdateTask",
                    "OpenTaskPopup",
                    "RemoveFeedbackFile",
                    "OpenTaskFeedbackPopup",
                    "GetTaskHistory",
                ];
                await HandleAccessBusinessPermission(admin, businessPermissionId, controllerName, actionName);

                controllerName = "RequestTicket";
                actionName = [
                    "GetRequestTicketHistories",
                    "Edit",
                    "GetProductExchange",
                    "GetProductRequestTicket",
                ];
                await HandleAccessBusinessPermission(admin, businessPermissionId, controllerName, actionName, false);
            }

            {
                var businessPermissionId = Guid.Parse("25007548-C73A-4000-A27D-940E8CE7C59C");
                var controllerName = "RequestTicket";
                string[] actionName = [
                    "GetImportTaskListHistories",
                    "DownloadImportTaskResult",
                    "ExportSearchTaskResultToImport",
                    "ImportTaskList",
                    "ImportTaskListResult",
                    "CancelImportTaskList",
                ];
                await HandleAccessBusinessPermission(admin, businessPermissionId, controllerName, actionName);
            }

            {
                var businessPermissionId = Guid.Parse("9C260635-2660-4E67-898E-020DFAADBAA0");
                var controllerName = "Task";
                string[] actionName = [
                    "DownloadFeedback",
                ];
                await HandleAccessBusinessPermission(admin, businessPermissionId, controllerName, actionName);
            }

            {
                var businessPermissionId = Guid.Parse("41ECE934-C0E1-47CF-B440-DD80CEDEF987");
                var controllerName = "Task";
                string[] actionName = [
                    "DeleteTask",
                ];
                await HandleAccessBusinessPermission(admin, businessPermissionId, controllerName, actionName);
            }

            {
                var businessPermissionId = Guid.Parse("6357D489-BBD6-4C18-9987-0BE81426A398");
                var controllerName = "Report";
                string[] actionName = [
                    "ReportRequestTicketByServiceType",
                    "GetReportRequestTicketByServiceType",
                ];
                await HandleAccessBusinessPermission(admin, businessPermissionId, controllerName, actionName);
            }

            {
                var businessPermissionId = Guid.Parse("FFEFCDC3-2604-4380-8F82-0C487D819461");
                var controllerName = "Report";
                string[] actionName = [
                    "RequestTicketSummaryByServiceType",
                ];
                await HandleAccessBusinessPermission(admin, businessPermissionId, controllerName, actionName);
            }

            {
                var businessPermissionId = Guid.Parse("5DFDEB4A-6589-4D0A-81EE-491813C26211");
                var controllerName = "Report";
                string[] actionName = [
                    "ReportRequestTicketByCustomerClass",
                    "GetReportRequestTicketByCustomerClass",
                ];
                await HandleAccessBusinessPermission(admin, businessPermissionId, controllerName, actionName);
            }

            {
                var businessPermissionId = Guid.Parse("20A7A22F-0FB1-4084-A888-D6A475E05234");
                var controllerName = "Report";
                string[] actionName = [
                    "GetReportRequestTicketByServiceCategoryLevel1",
                    "ReportRequestTicketByServiceCategoryLevel1",
                ];
                await HandleAccessBusinessPermission(admin, businessPermissionId, controllerName, actionName);
            }

            {
                var businessPermissionId = Guid.Parse("FEED4ACC-E112-429E-9B52-F69E759C8829");
                var controllerName = "Report";
                string[] actionName = [
                    "ReportRequestTicketBySourceChannel",
                    "GetReportRequestTicketBySourceChannel",
                ];
                await HandleAccessBusinessPermission(admin, businessPermissionId, controllerName, actionName);
            }

            {
                var businessPermissionId = Guid.Parse("56B8560F-1493-4DDE-A678-F8623C3ED2FD");
                var controllerName = "Report";
                string[] actionName = [
                    "RequestTicketSummaryByOwner",
                 ];
                await HandleAccessBusinessPermission(admin, businessPermissionId, controllerName, actionName);
            }

            {
                var businessPermissionId = Guid.Parse("8B70C6F0-D9E9-4646-8B5D-A740DC525B54");
                var controllerName = "Report";
                string[] actionName = [
                    "ExchangeDataReport",
                ];
                await HandleAccessBusinessPermission(admin, businessPermissionId, controllerName, actionName);
            }

            {
                var businessPermissionId = Guid.Parse("0F8B9BC1-B814-404A-A97B-77ADA8D13464");
                var controllerName = "Report";
                string[] actionName = [
                    "ThacMacDeNghiVeBaoBi",
                    "ThacMacDeNghiVeBaoBiDownload",
                ];
                await HandleAccessBusinessPermission(admin, businessPermissionId, controllerName, actionName);
            }

            {
                var businessPermissionId = Guid.Parse("5A3A79F4-240A-4E9C-99A0-85CDD085AC48");
                var controllerName = "Report";
                string[] actionName = [
                    "KhieuNaiDichVu",
                    "KhieuNaiDichVuDownload",
                ];
                await HandleAccessBusinessPermission(admin, businessPermissionId, controllerName, actionName);
            }

            {
                var businessPermissionId = Guid.Parse("10F59B87-44D5-4661-8F23-4598452C02D1");
                var controllerName = "Report";
                string[] actionName = [
                    "WeekMonthlySummary",
                ];
                await HandleAccessBusinessPermission(admin, businessPermissionId, controllerName, actionName);
            }

            {
                var businessPermissionId = Guid.Parse("C37BAFA3-BF87-47AE-B84F-0160CF3A3892");
                var controllerName = "Report";
                string[] actionName = [
                    "Behaviors",
                 ];
                await HandleAccessBusinessPermission(admin, businessPermissionId, controllerName, actionName);
            }

            {
                var businessPermissionId = Guid.Parse("C253A1D5-CD10-4D90-BE8E-01BE144564A1");
                var controllerName = "Report";
                string[] actionName = [
                    "TinhHinhGiaiQuyetKhieuNaiTheoKhuVuc",
                    "TinhHinhGiaiQuyetKhieuNaiTheoKhuVucDownLoad",
                ];
                await HandleAccessBusinessPermission(admin, businessPermissionId, controllerName, actionName);
            }

            {
                var businessPermissionId = Guid.Parse("12FD3008-140F-41C9-9861-14E7A1651D1E");
                var controllerName = "Report";
                string[] actionName = ["ExportWVDVKH01F1"];
                await HandleAccessBusinessPermission(admin, businessPermissionId, controllerName, actionName);
            }
            {
                var businessPermissionId = Guid.Parse("4E48D3E2-B782-4F4D-83D4-296F9E51C57C");
                var controllerName = "Report";
                string[] actionName = ["DownloadBaoCaoTheoDoiSoLuongKhieuNai", "TheoDoiSoLuongKhieuNai"];
                await HandleAccessBusinessPermission(admin, businessPermissionId, controllerName, actionName);
            }
            {
                var businessPermissionId = Guid.Parse("5F3B5D33-127A-4D7F-A28C-2B6669C07506");
                var controllerName = "Report";
                string[] actionName = ["TopComplainedProducts"];
                await HandleAccessBusinessPermission(admin, businessPermissionId, controllerName, actionName);
            }
            {
                var businessPermissionId = Guid.Parse("742EC474-C446-45B5-B992-2EE8FBA62D8C");
                var controllerName = "Report";
                string[] actionName = ["KhieuNaiSanPham", "KhieuNaiSanPhamDownload"];
                await HandleAccessBusinessPermission(admin, businessPermissionId, controllerName, actionName);
            }
            {
                var businessPermissionId = Guid.Parse("CC608471-9460-40BB-8077-FCD1394AC20F");
                var controllerName = "Report";
                string[] actionName = ["F3Report"];
                await HandleAccessBusinessPermission(admin, businessPermissionId, controllerName, actionName);
            }
            {
                var businessPermissionId = Guid.Parse("02F99ABB-87F8-4B88-B6CC-E1AAB88A6AF5");
                var controllerName = "Report";
                string[] actionName = ["GetReportF4", "GetReportF4Dynamic", "GetReportF4Index"];
                await HandleAccessBusinessPermission(admin, businessPermissionId, controllerName, actionName);
            }
            {
                var businessPermissionId = Guid.Parse("04D0C2A8-DCEC-413D-AC33-EB944F2CB05A");
                var controllerName = "Report";
                string[] actionName = ["SourceChancel"];
                await HandleAccessBusinessPermission(admin, businessPermissionId, controllerName, actionName);
            }
            {
                var businessPermissionId = Guid.Parse("B804F049-AA7C-4911-88F6-B757B83414B2");
                var controllerName = "Report";
                string[] actionName = ["CustomerComplainedAnalysis"];
                await HandleAccessBusinessPermission(admin, businessPermissionId, controllerName, actionName);
            }
            {
                var businessPermissionId = Guid.Parse("320B47D6-BFB2-4438-B8F5-69CF8A6227ED");
                var controllerName = "Report";
                string[] actionName = ["RequestTicketOwnershipSummaryReport", "GetRequestTicketOwnershipSummaryReport"];
                await HandleAccessBusinessPermission(admin, businessPermissionId, controllerName, actionName);
            }
            {
                var businessPermissionId = Guid.Parse("D685FAC0-1220-4F04-B13D-19159FA2F0F8");
                var controllerName = "MbReport";
                string[] actionName = ["BaoCaoChiTietTraSoat"];
                await HandleAccessBusinessPermission(admin, businessPermissionId, controllerName, actionName);
            }

            {
                var businessPermissionId = Guid.Parse("DAC509B2-51D9-4CC8-AEBA-3E5B597A09DD");
                var controllerName = "MbReport";
                string[] actionName = ["BaoCaoChiTietHoTroThe"];
                await HandleAccessBusinessPermission(admin, businessPermissionId, controllerName, actionName);
            }
            {
                var businessPermissionId = Guid.Parse("31FE0226-B1FB-4E35-A3A6-F7E09B87EFDC");
                var controllerName = "MbReport";
                string[] actionName = ["BaoCaoTongHopSLA"];
                await HandleAccessBusinessPermission(admin, businessPermissionId, controllerName, actionName);
            }
            {
                var businessPermissionId = Guid.Parse("37ECA02B-26DB-4956-86A8-07EBDB83D87E");
                var controllerName = "MbReport";
                string[] actionName = ["BaoCaoChiTietHoTroKhoaTheTamThoi"];
                await HandleAccessBusinessPermission(admin, businessPermissionId, controllerName, actionName);
            }
            {
                var businessPermissionId = Guid.Parse("7BE99356-7407-469F-9039-9CD357D865BE");
                var controllerName = "MbReport";
                string[] actionName = ["BaoCaoSLATraSoat"];
                await HandleAccessBusinessPermission(admin, businessPermissionId, controllerName, actionName);
            }
            {
                var businessPermissionId = Guid.Parse("1A2E7A80-CCAD-4FA3-8966-A368F73111A0");
                var controllerName = "MbReport";
                string[] actionName = ["BaoCaoChiTietSLAChamXuLyKhieuNai", "BaoCaoChiTietSLAChamXuLy"];
                await HandleAccessBusinessPermission(admin, businessPermissionId, controllerName, actionName);
            }
            {
                var businessPermissionId = Guid.Parse("B74393DA-E32D-40A5-A958-B61B08294568");
                var controllerName = "MbReport";
                string[] actionName = ["BaoCaoChiTietSLAChamXuLy", "BaoCaoChiTietSLAChamXuLyTraSoat"];
                await HandleAccessBusinessPermission(admin, businessPermissionId, controllerName, actionName);
            }
            {
                var businessPermissionId = Guid.Parse("77F7BBDA-5503-4043-AE63-BF65409C881F");
                var controllerName = "MbReport";
                string[] actionName = ["BaoCaoNangSuatLaoDong02"];
                await HandleAccessBusinessPermission(admin, businessPermissionId, controllerName, actionName);
            }
            {
                var businessPermissionId = Guid.Parse("611E604E-3F5C-45AD-9238-C360F31829D3");
                var controllerName = "MbReport";
                string[] actionName = ["BaoCaoChiTietSLAChamXuLyHoTroThe", "BaoCaoChiTietSLAChamXuLy"];
                await HandleAccessBusinessPermission(admin, businessPermissionId, controllerName, actionName);
            }
            {
                var businessPermissionId = Guid.Parse("A58DD182-96E9-4213-9833-4BBA03EA6140");
                var controllerName = "MbReport";
                string[] actionName = ["BaoCaoChiTietHoTroNuotThe"];
                await HandleAccessBusinessPermission(admin, businessPermissionId, controllerName, actionName);
            }
            {
                var businessPermissionId = Guid.Parse("A69E1851-77AA-4A33-8F47-4CDC63A86C32");
                var controllerName = "Report";
                string[] actionName = ["ReportByServiceCategory"];
                await HandleAccessBusinessPermission(admin, businessPermissionId, controllerName, actionName);
            }

            {
                var businessPermissionId = Guid.Parse("B298998A-48DF-4AFF-BACE-1B51B30F43C1");
                var controllerName = "Report";
                string[] actionName = [
                    "GetRequestTicketSLADetailReport",
                    "RequestTicketSLADetailReport",
                ];
                await HandleAccessBusinessPermission(admin, businessPermissionId, controllerName, actionName);
            }

            {
                var businessPermissionId = Guid.Parse("6E10194B-099C-4EAE-8050-3601124BE2FE");
                var controllerName = "MbReport";
                string[] actionName = [
                    "BaoCaoSLAKhieuNai",
                 ];
                await HandleAccessBusinessPermission(admin, businessPermissionId, controllerName, actionName);
            }

            {
                var businessPermissionId = Guid.Parse("B59149FE-CC1C-4E2D-94AD-F1CB375D3287");
                var controllerName = "MbReport";
                string[] actionName = [
                    "BaoCaoChiTietSLAChamXuLy",
                    "BaoCaoChiTietSLAChamXuLyHoTroNHDT",
                ];
                await HandleAccessBusinessPermission(admin, businessPermissionId, controllerName, actionName);
            }

            {
                var businessPermissionId = Guid.Parse("6EB0BCD8-BCC1-46E8-914C-B277605D43B6");
                var controllerName = "MbReport";
                string[] actionName = [
                    "BaoCaoChiTietHoTroDoiDiem",
                ];
                await HandleAccessBusinessPermission(admin, businessPermissionId, controllerName, actionName);
            }

            {
                var businessPermissionId = Guid.Parse("ADD82C73-E0DC-43F4-8439-9F7AE3915F83");
                var controllerName = "MbReport";
                string[] actionName = [
                    "BaoCaoSLAChiTiet",
                ];
                await HandleAccessBusinessPermission(admin, businessPermissionId, controllerName, actionName);
            }

            {
                var businessPermissionId = Guid.Parse("6F78AB72-31DC-4FCE-B87D-707CF27CC751");
                var controllerName = "Report";
                string[] actionName = [
                    "RequestTicketProcessStatusReport",
                    "GetRequestTicketProssStatusReport",
                ];
                await HandleAccessBusinessPermission(admin, businessPermissionId, controllerName, actionName);
            }

            {
                var businessPermissionId = Guid.Parse("B8DF78F9-E40D-428C-9BB0-40F82AFBD69C");
                var controllerName = "MbReport";
                string[] actionName = [
                    "BaoCaoChiTietHoTroKichHoatThe",
                ];
                await HandleAccessBusinessPermission(admin, businessPermissionId, controllerName, actionName);
            }

            {
                var businessPermissionId = Guid.Parse("99F22E3D-4AD2-4D2C-A022-CCFE05090821");
                var controllerName = "MbReport";
                string[] actionName = [
                    "BaoCaoChiTietHoTroKhoaNHDT",
                ];
                await HandleAccessBusinessPermission(admin, businessPermissionId, controllerName, actionName);
            }

            {
                var businessPermissionId = Guid.Parse("B4377A38-1318-412B-8473-95BF3AB5FEC7");
                var controllerName = "MbReport";
                string[] actionName = [
                    "BaoCaoChiTietHoTroKTTT",
                ];
                await HandleAccessBusinessPermission(admin, businessPermissionId, controllerName, actionName);
            }

            {
                var businessPermissionId = Guid.Parse("1F36B46B-8DF4-4D27-89AA-D4F4DA529CD0");
                var controllerName = "Report";
                string[] actionName = [
                    "GetTaskSumaryForCoordinator",
                ];
                await HandleAccessBusinessPermission(admin, businessPermissionId, controllerName, actionName);
            }

            {
                var businessPermissionId = Guid.Parse("ACFB2630-1D14-4BBF-8710-AD5075C51038");
                var controllerName = "Report";
                string[] actionName = [
                    "TaskDataConversion",
                ];
                await HandleAccessBusinessPermission(admin, businessPermissionId, controllerName, actionName);
            }

            {
                var businessPermissionId = Guid.Parse("1088C4ED-6EE0-4E7A-8F93-4A7D393D0998");
                var controllerName = "Report";
                string[] actionName = [
                    "RequestTicketDataConversion",
                ];
                await HandleAccessBusinessPermission(admin, businessPermissionId, controllerName, actionName);
            }

            {
                var businessPermissionId = Guid.Parse("FE18CF80-F4AA-480F-85AB-DA1AC08B832F");
                var controllerName = "Report";
                string[] actionName = [
                    "GetTaskSumaryForOwner",
                ];
                await HandleAccessBusinessPermission(admin, businessPermissionId, controllerName, actionName);
            }

            {
                var businessPermissionId = Guid.Parse("6667D331-01C7-4E94-8C86-BA9965987A20");
                var controllerName = "InboundReport";
                string[] actionName = [
                    "SummaryTicketByServiceTypeAndWeekInYear",
                ];
                await HandleAccessBusinessPermission(admin, businessPermissionId, controllerName, actionName);
            }

            {
                var businessPermissionId = Guid.Parse("044CFF71-951C-4387-8D61-92239EC9F45F");
                var controllerName = "SalesSupport";
                string[] actionName = [
                    "GetDetailContactInfo",
                    "GetContactInfo",
                    "ContactInfo"
                ];
                await HandleAccessBusinessPermission(admin, businessPermissionId, controllerName, actionName);
            }

            {
                var businessPermissionId = Guid.Parse("B6B4BEF4-57CC-4426-BE75-06C8F37A1061");
                var controllerName = "Survey";
                string[] actionName = [
                    "ViewSurveyResultByProspectAssignmentId"
                ];
                await HandleAccessBusinessPermission(admin, businessPermissionId, controllerName, actionName);
            }

            {
                var businessPermissionId = Guid.Parse("D7A49F2F-7AF4-4436-AFA8-0B56492AE031");
                var controllerName = "SurveyManagement";
                string[] actionName = [
                    "CreateEditQuestion",
                    "CreateEditQuestionSection",
                    "CreateEdit",
                    "CreateEditAnswer",
                    "DeleteAnswer",
                    "DeleteQuestion",
                    "DeleteQuestionSection",
                    "GetQuestionSectionListInSurvey",
                    "Index",
                    "SearchSurveyList"
                ];
                await HandleAccessBusinessPermission(admin, businessPermissionId, controllerName, actionName);
            }

            {
                var businessPermissionId = Guid.Parse("49D20CF2-614A-4AF5-AF47-27586B2B15A0");
                var controllerName = "SurveyAnswerSuiteManagement";
                string[] actionName = [
                    "DeleteAnswerSuite",
                    "CreateEdit",
                    "Index",
                    "SearchSurveyAnswerListInSuite",
                    "SearchSurveyAnswerSuites"
                ];
                await HandleAccessBusinessPermission(admin, businessPermissionId, controllerName, actionName);
            }

            {
                var businessPermissionId = Guid.Parse("50AD6215-FF5F-4FD6-B23E-FCC3CB648D0F");
                var controllerName = "Cache";
                string[] actionName = [
                    "ClearCache",
                    "Index"
                ];
                await HandleAccessBusinessPermission(admin, businessPermissionId, controllerName, actionName);
            }

            {
                var businessPermissionId = Guid.Parse("576D4DED-E043-46AA-B824-5764CC27F128");
                var controllerName = "ServiceCategory";
                string[] actionName = [
                    "CreateEdit",
                    "CreateEditServiceCategory",
                    "ServiceCategory"
                ];
                await HandleAccessBusinessPermission(admin, businessPermissionId, controllerName, actionName);
            }

            {
                var businessPermissionId = Guid.Parse("06925BBB-0529-4789-871C-79D57B55A3A1");
                var controllerName = "ServiceCategory";
                string[] actionName = [
                    "DeleteServiceCategory",
                    "DeleteServiceType"
                ];
                await HandleAccessBusinessPermission(admin, businessPermissionId, controllerName, actionName);
            }

            {
                var businessPermissionId = Guid.Parse("DD32A5E4-F79A-4BEC-819C-3F06883ACC2F");
                var controllerName = "ServiceCategory";
                string[] actionName = [
                    "Index",
                    "Search",
                    "SearchServiceCategory"
                ];
                await HandleAccessBusinessPermission(admin, businessPermissionId, controllerName, actionName);
            }

            {
                var businessPermissionId = Guid.Parse("2C9E4E4E-5BCF-4E0B-9F0D-BAD40EBEA1F5");
                var controllerName = "ServiceCategory";
                string[] actionName = [
                    "Index",
                    "Search",
                    "SearchServiceCategory"
                ];
                await HandleAccessBusinessPermission(admin, businessPermissionId, controllerName, actionName);
            }

            {
                var businessPermissionId = Guid.Parse("216D4BC9-10A1-458B-80A3-9697A263493A");
                var controllerName = "Channel";
                string[] actionName = [
                    "CreateEdit"
                ];
                await HandleAccessBusinessPermission(admin, businessPermissionId, controllerName, actionName);
            }

            {
                var businessPermissionId = Guid.Parse("B7C975B1-E3C2-4283-9463-AA00AAA35596");
                var controllerName = "Channel";
                string[] actionName = ["Index", "SearchChannels"];
                await HandleAccessBusinessPermission(admin, businessPermissionId, controllerName, actionName);
            }

            {
                var businessPermissionId = Guid.Parse("97308EB8-**************-CFD720436661");
                var controllerName = "Channel";
                string[] actionName = ["Delete"];
                await HandleAccessBusinessPermission(admin, businessPermissionId, controllerName, actionName);
            }

            {
                var businessPermissionId = Guid.Parse("4CC91994-AABB-4448-AD74-0D8D6F344E40");
                var controllerName = "Behavior";
                string[] actionName = ["Index", "SearchBehaviors"];
                await HandleAccessBusinessPermission(admin, businessPermissionId, controllerName, actionName);
            }

            {
                var businessPermissionId = Guid.Parse("A0E41AAD-5527-4357-A8BB-2D3B50A2C3E2");
                var controllerName = "Behavior";
                string[] actionName = ["Delete"];
                await HandleAccessBusinessPermission(admin, businessPermissionId, controllerName, actionName);
            }

            {
                var businessPermissionId = Guid.Parse("3DB9F62C-678C-4F0C-8F40-EA76BF430AEB");
                var controllerName = "Behavior";
                string[] actionName = ["CreateEdit"];
                await HandleAccessBusinessPermission(admin, businessPermissionId, controllerName, actionName);
            }

            {
                var businessPermissionId = Guid.Parse("365AB7FC-7F19-4F1B-80D3-010FAD6AD2AD");
                var controllerName = "ContentTemplate";
                string[] actionName = ["CreateEditAutoCompleteContentTemplate"];
                await HandleAccessBusinessPermission(admin, businessPermissionId, controllerName, actionName);
            }

            {
                var businessPermissionId = Guid.Parse("1DAD4638-108F-4090-B4BF-32FF9E156E68");
                var controllerName = "ContentTemplate";
                string[] actionName = [
                    "DeleteAutoCompleteContentTemplate",
                    "CreateEditAutoCompleteContentTemplate",
                    "AutoComplete",
                    "GetAutoCompleteContentTemplateList"
                ];
                await HandleAccessBusinessPermission(admin, businessPermissionId, controllerName, actionName);
            }

            {
                var businessPermissionId = Guid.Parse("50528DED-6351-4080-804A-450BFCD5EC74");
                var controllerName = "ContentTemplate";
                string[] actionName = [
                    "AutoComplete",
                    "GetAutoCompleteContentTemplateList",
                    "GetAutoComplete"
                ];
                await HandleAccessBusinessPermission(admin, businessPermissionId, controllerName, actionName);
            }

            {
                var businessPermissionId = Guid.Parse("59D08ADC-1F6B-40B1-A607-BCB7C1D5C1A7");
                var controllerName = "ContentTemplate";
                string[] actionName = ["DeleteAutoCompleteContentTemplate"];
                await HandleAccessBusinessPermission(admin, businessPermissionId, controllerName, actionName);
            }

            {
                var businessPermissionId = Guid.Parse("********-CF04-4735-8D28-3E4742BCF30A");
                var controllerName = "CallResult";
                string[] actionName = [
                    "Index",
                    "GetCallStrategies",
                    "DeleteResultCodeSuite",
                    "CreateEditCallStrategy",
                    "CreateEditResultCodeSuite",
                    "CreateEdit",
                    "DeleteCallResult",
                    "DeleteCallStrategy",
                    "SearchResultCodeSuite",
                    "SearchResultCodeSuiteDropdownList",
                    "SearchDropdownList",
                    "SearchCallResult",
                    "ResultCodeSuite",
                    "OpenCreateEditCallStrategy",
                    "OpenCreateEditResultCodeSuite"
                ];
                await HandleAccessBusinessPermission(admin, businessPermissionId, controllerName, actionName);
            }

            {
                var businessPermissionId = Guid.Parse("46DF9802-B328-4734-935A-4F931F0F98D3");
                var controllerName = "DynamicForm";
                string[] actionName = [
                    "CreateEditDynamicField",
                    "CreateEdit",
                    "ExportWithTemplate",
                    "DeleteDynamicField",
                    "GetDynamicFormList",
                    "Index",
                    "PreviewDataStruct",
                    "LoadDocumentFieldList",
                    "LoadDynamicFieldList",
                    "LoadCreateDynamicLinkedField",
                    "LoadCreateEditDynamicField",
                    "LoadStaticFieldList"
                ];
                await HandleAccessBusinessPermission(admin, businessPermissionId, controllerName, actionName);
            }

            {
                var businessPermissionId = Guid.Parse("114CC664-A9F3-4E5A-B848-99AE495EC5FE");
                var controllerName = "ContentTemplate";
                string[] actionName = ["Search", "Index"];
                await HandleAccessBusinessPermission(admin, businessPermissionId, controllerName, actionName);
            }

            {
                var businessPermissionId = Guid.Parse("********-2697-487F-8525-B0F900D33D90");
                var controllerName = "ContentTemplate";
                string[] actionName = ["CreateEdit", "OpenCreateEdit", "Upload"];
                await HandleAccessBusinessPermission(admin, businessPermissionId, controllerName, actionName);
            }

            {
                var businessPermissionId = Guid.Parse("CBD7AD26-7564-414A-B2D9-EB50C51363CE");
                var controllerName = "ContentTemplate";
                string[] actionName = ["Delete"];
                await HandleAccessBusinessPermission(admin, businessPermissionId, controllerName, actionName);
            }

            {
                var businessPermissionId = Guid.Parse("19F9576A-654D-4D65-AF0C-0E49FCA1E0DE");
                var controllerName = "DueTime";
                string[] actionName = [
                    "SearchDropdownList",
                    "SearchServiceTypeTaskTypeByDueTimeId",
                    "Search",
                    "GetServiceType",
                    "OpenCreateEdit",
                    "Index",
                    "CreateEdit"
                ];
                await HandleAccessBusinessPermission(admin, businessPermissionId, controllerName, actionName);
            }

            {
                var businessPermissionId = Guid.Parse("5883CDA2-F3E7-476F-A402-B707C09EF809");
                var controllerName = "DueTime";
                string[] actionName = ["DeleteDuetime"];
                await HandleAccessBusinessPermission(admin, businessPermissionId, controllerName, actionName);
            }

            {
                var businessPermissionId = Guid.Parse("531F2132-8E90-4134-B0B9-1B3738642852");
                var controllerName = "Workflow";
                string[] actionName = [
                    "ConfigureAutoNextTask",
                    "CreateEdit",
                    "CreateEditTaskType",
                    "CreateEditTaskTypeInWorkflow",
                    "CreateEditTaskTypeListInWorkflow",
                    "DeleteTaskTypeInWorkflow",
                    "DeleteWorkflow",
                    "Index",
                    "SaveConfigureAutoNextTask",
                    "GetTaskTypeListInWorkflow",
                    "SearchWorkflow"
                ];
                await HandleAccessBusinessPermission(admin, businessPermissionId, controllerName, actionName);
            }

            {
                var businessPermissionId = Guid.Parse("4E2121D2-33FB-445D-9D59-232FA7F72C07");
                var controllerName = "NotificationChanelSetting";
                string[] actionName = [
                    "CustomNotificationChannelSetting",
                    "Index",
                    "OpenSelectContentTemplate",
                    "LoadCustomNotificationChannelSettingList",
                    "Search"
                ];
                await HandleAccessBusinessPermission(admin, businessPermissionId, controllerName, actionName);
            }

            {
                var businessPermissionId = Guid.Parse("39BD8B07-8D9A-49B3-AA03-99463F7ADFFD");
                var controllerName = "NotificationChanelSetting";
                string[] actionName = [
                    "CreateEditCustomNotificationChannelSetting",
                    "DeleteCustomNotificationChannelSetting",
                    "SaveSetting",
                    "SearchContentTemplate"
                ];
                await HandleAccessBusinessPermission(admin, businessPermissionId, controllerName, actionName);
            }

            {
                var businessPermissionId = Guid.Parse("74432C23-7142-401A-9CE2-65812772A179");
                var controllerName = "NotificationChanelSetting";
                string[] actionName = [
                    "LoadAlternativeNotiChannelContentTemplateList",
                    "OpenAlternativeNotiChannelContentTemplate",
                    "SaveAlternativeNotiChannelContentTemplateOrder",
                    "DeleteAlternativeNotiChannelContentTemplate",
                    "CreateEditAlternativeNotiChannelContentTemplate"
                ];
                await HandleAccessBusinessPermission(admin, businessPermissionId, controllerName, actionName);
            }

            {
                var businessPermissionId = Guid.Parse("937B59A8-EE83-4260-BF8E-0D37E786C341");
                var controllerName = "TaskType";
                string[] actionName = ["SearchTaskType", "Index", "DeleteTaskType", "CreateEdit"];
                await HandleAccessBusinessPermission(admin, businessPermissionId, controllerName, actionName);
            }

            {
                var businessPermissionId = Guid.Parse("9FD1DD0D-4A3F-4C97-B16D-F68784B42033");
                var controllerName = "DynamicTable";
                string[] actionName = [
                    "CreateEdit",
                    "Index",
                    "GetDataTypeList",
                    "DynamicTableColumnCreateEdit",
                    "DynamicTableColumnDelete",
                    "DynamicTableColumnSearch",
                    "DynamicTableDelete",
                    "Search"
                ];
                await HandleAccessBusinessPermission(admin, businessPermissionId, controllerName, actionName);
            }

            {
                var businessPermissionId = Guid.Parse("50E0DA42-B1D3-419E-9441-F75880669214");
                var controllerName = "UiConfigurationSearch";
                string[] actionName = ["Index"];
                await HandleAccessBusinessPermission(admin, businessPermissionId, controllerName, actionName);
            }

            {
                var businessPermissionId = Guid.Parse("5F5169F5-2B0B-4DFA-9F25-2D20C7ADBE91");
                var controllerName = "UiConfiguration";
                string[] actionName = ["ConfigureCreateEditRequestTicket"];
                await HandleAccessBusinessPermission(admin, businessPermissionId, controllerName, actionName);
            }

            {
                var businessPermissionId = Guid.Parse("2966022A-1256-4CD2-83A1-71E1DC346672");
                var controllerName = "UserManagement";
                string[] actionName = ["CreateUser"];
                await HandleAccessBusinessPermission(admin, businessPermissionId, controllerName, actionName);
            }

            {
                var businessPermissionId = Guid.Parse("25ED0CD9-A138-4C07-A1BD-7769814A0A9C");
                var controllerName = "UserManagement";
                string[] actionName = ["Index", "Search"];
                await HandleAccessBusinessPermission(admin, businessPermissionId, controllerName, actionName);
            }

            {
                var businessPermissionId = Guid.Parse("A09ECD75-B17D-4AAE-85CC-D39ABCBEB3F7");
                var controllerName = "UserManagement";
                string[] actionName = ["EditUser", "UnlockUser"];
                await HandleAccessBusinessPermission(admin, businessPermissionId, controllerName, actionName);
            }

            {
                var businessPermissionId = Guid.Parse("719E67A6-9D7B-4803-BED3-1106701288B7");
                var controllerName = "Organization";
                string[] actionName = ["DeleteOrganization"];
                await HandleAccessBusinessPermission(admin, businessPermissionId, controllerName, actionName);
            }

            {
                var businessPermissionId = Guid.Parse("207D5DDC-ED4F-4DB7-A3C7-3837EFC517CA");
                var controllerName = "Organization";
                string[] actionName = ["AddTaskRelate", "RemoveTaskRelate", "UpdateOrganization"];
                await HandleAccessBusinessPermission(admin, businessPermissionId, controllerName, actionName);

                controllerName = "Report";
                actionName = ["GetReportByServiceCategory"];
                await HandleAccessBusinessPermission(admin, businessPermissionId, controllerName, actionName, false);
            }

            {
                var businessPermissionId = Guid.Parse("E084C28D-9DED-4BB5-B8AE-52E86E2D1CAF");
                var controllerName = "Organization";
                string[] actionName = ["Index"];
                await HandleAccessBusinessPermission(admin, businessPermissionId, controllerName, actionName);
            }

            {
                var businessPermissionId = Guid.Parse("2BE947A9-193C-428E-B2FF-AF606486B685");
                var controllerName = "Organization";
                string[] actionName = ["CreateOrganization"];
                await HandleAccessBusinessPermission(admin, businessPermissionId, controllerName, actionName);
            }

            {
                var businessPermissionId = Guid.Parse("E3DD52E2-4D7F-4204-AB0F-3BD17755BF49");
                var controllerName = "RoleManagement";
                string[] actionName = ["SearchRole", "Index"];
                await HandleAccessBusinessPermission(admin, businessPermissionId, controllerName, actionName);
            }

            {
                var businessPermissionId = Guid.Parse("321F5CD1-C7D1-407D-A364-94FDB5CC55EC");
                var controllerName = "BusinessPermission";
                string[] actionName = ["Index", "GetAllPermissionTree", "SaveBusinessPermissionList"];
                await HandleAccessBusinessPermission(admin, businessPermissionId, controllerName, actionName);

                controllerName = "RoleManagement";
                actionName = ["CreateEditRole"];
                await HandleAccessBusinessPermission(admin, businessPermissionId, controllerName, actionName, false);
            }

            {
                var businessPermissionId = Guid.Parse("4756F10F-0BAB-4276-9134-5FD30BD34F25");
                var controllerName = "Gateway";
                string[] actionName = ["CreateEditGateway"];
                await HandleAccessBusinessPermission(admin, businessPermissionId, controllerName, actionName);
            }

            {
                var businessPermissionId = Guid.Parse("A88D34A8-1E9B-4FD9-B14E-83F7E15D6334");
                var controllerName = "Gateway";
                string[] actionName = ["Index", "SearchGateways", "LoadListSmsIntergration"];
                await HandleAccessBusinessPermission(admin, businessPermissionId, controllerName, actionName);
            }

            {
                var businessPermissionId = Guid.Parse("0EBD002B-BBC1-4667-AA12-A724F280F627");
                var controllerName = "DatabaseDMV";
                string[] actionName = ["IndexMissing", "MostExpensiveStatement", "IndexStatistic", "GetIndexStatisticServerStartedTimeList", "GetIndexMissingServerStartedTimeList", "GetMostExpensiveStatementServerStartedTimeList"];
                await HandleAccessBusinessPermission(admin, businessPermissionId, controllerName, actionName);

                controllerName = "BusinessSettings";
                actionName = ["Index", "Update"];
                await HandleAccessBusinessPermission(admin, businessPermissionId, controllerName, actionName, false);
            }

            {
                var businessPermissionId = Guid.Parse("F1B17334-8C8D-41D5-A68A-6D0E6B63C749");
                var controllerName = "KnowledgeBase";
                string[] actionName = ["CreateEdit", "OpenCreateEdit"];
                await HandleAccessBusinessPermission(admin, businessPermissionId, controllerName, actionName);
            }

            {
                var businessPermissionId = Guid.Parse("9F2A837A-C243-4050-A9EA-6E0F95EB1705");
                var controllerName = "KnowledgeBase";
                string[] actionName = [
                    "OpenCreateEdit",
                    "Search",
                    "SearchDropdownList",
                    "TreePage",
                    "Download",
                    "GetKnowledgeItems",
                    "Index",
                ];
                await HandleAccessBusinessPermission(admin, businessPermissionId, controllerName, actionName);
            }

            {
                var businessPermissionId = Guid.Parse("C055E806-BBCF-461C-B8AC-14A07B83DA49");
                var controllerName = "EquipmentSystem";
                string[] actionName = ["EquipmentSystemInitiation"];
                await HandleAccessBusinessPermission(admin, businessPermissionId, controllerName, actionName);
            }

            {
                var businessPermissionId = Guid.Parse("BA1752D8-3D16-4042-BFC4-AB6969F6481D");
                var controllerName = "EquipmentSystem";
                string[] actionName = [
                    "SearchRepairPart",
                    "SearchWithMaintenance",
                    "Search",
                    "PartMaintenanceDetail",
                    "ImportPart",
                    "ImportPart_V2",
                    "ImportPart_V2_Result",
                    "ImportPart_V2_Template",
                    "ImportPartDetail",
                    "GetRootList",
                    "GetSearchPartServiceTypeConfigDropdownList",
                    "Index",
                    "GetEquipmentHistories",
                    "GetOperationStatus",
                    "GetPartByParentDropdownList",
                    "GetPartByPartCategoryDropdownList",
                    "GetPartByPartTypeDropdownList",
                    "GetPartErrorCategoryByLevel",
                    "GetPartErrorFixByDropDown",
                    "GetPartList",
                    "GetPartTemplate",
                    "GetPartTypeDropdownList",
                    "GetPathOfPartById",
                    "EquipmentSystemInitiation",
                    "GetAppartments",
                    "CreateEdit",
                    "Delete",
                ];
                await HandleAccessBusinessPermission(admin, businessPermissionId, controllerName, actionName);
            }

            {
                var businessPermissionId = Guid.Parse("E777E609-929F-4D5B-BC3B-AF2AA3E6779D");
                var controllerName = "PartManagement";
                string[] actionName = ["ReportPartStatus"];
                await HandleAccessBusinessPermission(admin, businessPermissionId, controllerName, actionName);
            }

            {
                var businessPermissionId = Guid.Parse("20023496-7CA9-4B70-AC7F-ED43DE908494");
                var controllerName = "RequestTicket";
                string[] actionName = ["CustomerCreateTicket"];
                await HandleAccessBusinessPermission(admin, businessPermissionId, controllerName, actionName);
            }

            {
                var businessPermissionId = Guid.Parse("35ED1B5D-C97E-46F0-AA39-6577E43197B6");
                var controllerName = "ServiceCategory";
                string[] actionName = ["CreateEditBusinessResult"];
                await HandleAccessBusinessPermission(admin, businessPermissionId, controllerName, actionName);
            }

            {
                var businessPermissionId = Guid.Parse("F98E5EF8-68C0-4EA8-A876-66479BB04E2C");
                var controllerName = "Campaign";
                string[] actionName = ["ReportDetailCallCampaign"];
                await HandleAccessBusinessPermission(admin, businessPermissionId, controllerName, actionName);
            }

            {
                var businessPermissionId = Guid.Parse("D6169EC8-CB79-4560-A5BB-220DBD1D4D10");
                var controllerName = "Campaign";
                string[] actionName = ["ReportNumberOfCallByCallResult"];
                await HandleAccessBusinessPermission(admin, businessPermissionId, controllerName, actionName);
            }

            {
                var businessPermissionId = Guid.Parse("D746F13A-C070-4FE4-883B-83BE6B8115FD");
                var controllerName = "UserTaskAssignmentRouting";
                string[] actionName = [];
                await HandleAccessBusinessPermission(admin, businessPermissionId, controllerName, actionName);
            }

            {
                var businessPermissionId = Guid.Parse("7DFA89FA-F3F0-422C-8D83-9F56F5204AA3");
                var controllerName = "Customer";
                string[] actionName = ["CreateRequestTicketListByCustomerBatch"];
                await HandleAccessBusinessPermission(admin, businessPermissionId, controllerName, actionName);
            }

            {
                var businessPermissionId = Guid.Parse("B897556C-27D4-4E7F-BF5C-C49AA031DA5E");
                var controllerName = "DynamicForm";
                string[] actionName = ["SystemDynamicFormValues"];
                await HandleAccessBusinessPermission(admin, businessPermissionId, controllerName, actionName);
            }

            {
                var businessPermissionId = Guid.Parse("17D53E7E-649B-4BFF-978C-30C6982AE08D");
                var controllerName = "VirtualOffice";
                string[] actionName = [];
                await HandleAccessBusinessPermission(admin, businessPermissionId, controllerName, actionName);
            }

            {
                var businessPermissionId = Guid.Parse("1CF61752-E9AB-4932-9180-E809DF1B6296");
                var controllerName = "VirtualOffice";
                await DbContext.Set<AccessBusinessPermissionEntity>()
                    .Where(x => x.BusinessPermissionId == businessPermissionId)
                    .ExecuteDeleteAsync();
                if (DbContext.ChangeTracker.HasChanges())
                {
                    await DbContext.SaveChangesAsync();
                }
                var newEntities = await DbContext.Set<AccessEntity>()
                .Where(x => x.ControllerName == controllerName)
                .Where(x => !x.ActionName.StartsWith("Admin"))
                .Select(x => new AccessBusinessPermissionEntity
                {
                    Id = Guid.NewGuid(),
                    AccessId = x.Id,
                    BusinessPermissionId = businessPermissionId,
                    CreatedDate = DateTime.Now,
                    CreatedBy = admin,
                    Deleted = false
                })
                .ToArrayAsync();
                await DbContext.AddRangeAsync(newEntities);
                if (DbContext.ChangeTracker.HasChanges())
                {
                    await DbContext.SaveChangesAsync();
                }
            }

            {
                var businessPermissionId = Guid.Parse("B6A67E7E-DB03-45D2-8E8D-860BA43A127E");
                var controllerName = "ExcelImport";
                string[] actionName = ["ImportFWD_Step1", "ImportFWD_Step3"];
                await HandleAccessBusinessPermission(admin, businessPermissionId, controllerName, actionName);
            }

            {
                var businessPermissionId = Guid.Parse("30E1E118-7BB1-4C0D-944F-3503B48598AA");
                var controllerName = "ReportPayment";
                string[] actionName = ["Index"];
                await HandleAccessBusinessPermission(admin, businessPermissionId, controllerName, actionName);
            }

            {
                var businessPermissionId = Guid.Parse("6DFBB3AB-A281-47C6-B835-73A8AAC1078F");
                var controllerName = "PolicyManage";
                string[] actionName = ["Index"];
                await HandleAccessBusinessPermission(admin, businessPermissionId, controllerName, actionName);
            }

            {
                var businessPermissionId = Guid.Parse("1689DD7B-6240-4A3F-A6AF-F53660576C1E");
                var controllerName = "LoggerManage";
                string[] actionName = ["Index"];
                await HandleAccessBusinessPermission(admin, businessPermissionId, controllerName, actionName);
            }

            {
                await DbContext.Set<AccessBusinessPermissionEntity>()
                    .Where(x => x.AccessId == Guid.Parse("D50D424A-7638-41B5-A81A-15CC44B85EA5"))
                    .ExecuteDeleteAsync();
                if (DbContext.ChangeTracker.HasChanges())
                {
                    await DbContext.SaveChangesAsync();
                }
                var newEntities = new AccessBusinessPermissionEntity
                {
                    Id = Guid.NewGuid(),
                    AccessId = Guid.Parse("D50D424A-7638-41B5-A81A-15CC44B85EA5"),
                    BusinessPermissionId = Guid.Parse("378697f3-fe55-41da-9922-baae9bf7b391"),
                    CreatedDate = DateTime.Now,
                    CreatedBy = admin,
                    Deleted = false
                };
                await DbContext.AddRangeAsync(newEntities);
                if (DbContext.ChangeTracker.HasChanges())
                {
                    await DbContext.SaveChangesAsync();
                }
            }

            {
                await DbContext.Set<AccessBusinessPermissionEntity>()
                    .Where(x => x.AccessId == Guid.Parse("22B737F4-554E-418C-A4A4-09658789B41E"))
                    .ExecuteDeleteAsync();
                if (DbContext.ChangeTracker.HasChanges())
                {
                    await DbContext.SaveChangesAsync();
                }
                var newEntities = new AccessBusinessPermissionEntity
                {
                    Id = Guid.NewGuid(),
                    AccessId = Guid.Parse("22B737F4-554E-418C-A4A4-09658789B41E"),
                    BusinessPermissionId = Guid.Parse("ecec7a1f-1095-43d2-b40f-b14a0bd59973"),
                    CreatedDate = DateTime.Now,
                    CreatedBy = admin,
                    Deleted = false
                };
                await DbContext.AddRangeAsync(newEntities);
                if (DbContext.ChangeTracker.HasChanges())
                {
                    await DbContext.SaveChangesAsync();
                }
            }

            {
                var businessPermissionId = Guid.Parse("848A050F-F97A-47BD-BA4D-2906A3E79BCD");
                var controllerName = "OutboundReport";
                string[] actionName = ["CallResultFunnelSumary"];
                await HandleAccessBusinessPermission(admin, businessPermissionId, controllerName, actionName);
            }

            {
                var businessPermissionId = Guid.Parse("397B8D7E-C572-45CC-8AE9-BA03E34844AD");
                var controllerName = "OutboundReport";
                string[] actionName = ["CallResultFunnelSumary"];
                await HandleAccessBusinessPermission(admin, businessPermissionId, controllerName, actionName);
            }
        }
    }
}
