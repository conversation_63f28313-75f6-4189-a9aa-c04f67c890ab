﻿using System;
using System.Data;
using System.Linq;
using System.Globalization;
using System.Threading.Tasks;
using TinyCRM.Enums;
using Webaby;
using Webaby.Data;
using Webaby.Localization;
using Webaby.Validation.PhoneNumber;
using Webaby.Validation.Email;

namespace TinyCRM.ImportCustomerSession.Commands
{
    public class InsertImportStagingCustomersCommand : CommandBase
    {
        public Guid ImportCustomerSessionId { get; set; }

        public DataTable DataTable { get; set; }
    }

    internal class InsertImportStagingCustomersCommandHandler : CommandHandlerBase<InsertImportStagingCustomersCommand>
    {
        IText _text { get; set; }
        public InsertImportStagingCustomersCommandHandler(IServiceProvider serviceProvider, IText text) : base(serviceProvider) { _text = text; }

        public override async Task ExecuteAsync(InsertImportStagingCustomersCommand command)
        {
            var infoColumnMapping = new dynamic[] {

                new { DbColumn = "Id", DataType = typeof(Guid)},
                new { DbColumn = "ImportSessionId", DataType = typeof(Guid)},

                new { DbColumn = "Name", DataType = typeof(string)},
                new { DbColumn = "Code", DataType = typeof(string)},
                new { DbColumn = "B2BCode", DataType = typeof(string)},
                new { DbColumn = "Address", DataType = typeof(string)},

                new { DbColumn = "Phone1", DataType = typeof(string)},
                new { DbColumn = "Phone1Valid", DataType = typeof(string)},

                new { DbColumn = "Phone2", DataType = typeof(string)},
                new { DbColumn = "Phone2Valid", DataType = typeof(string)},

                new { DbColumn = "Phone3", DataType = typeof(string)},
                new { DbColumn = "Phone3Valid", DataType = typeof(string)},

                new { DbColumn = "ContactPhone", DataType = typeof(string)},
                new { DbColumn = "ContactPhoneValid", DataType = typeof(string)},

                new { DbColumn = "Email", DataType = typeof(string)},
                new { DbColumn = "EmailValid", DataType = typeof(string)},

                new { DbColumn = "FacebookId", DataType = typeof(string)},
                new { DbColumn = "WorkAddress", DataType = typeof(string)},
                new { DbColumn = "Job", DataType = typeof(string)},

                new { DbColumn = "Type", DataType = typeof(string)},
                new { DbColumn = "TypeValid", DataType = typeof(int)},

                new { DbColumn = "SubName", DataType = typeof(string)},
                new { DbColumn = "CustomerClass", DataType = typeof(string)},
                new { DbColumn = "CustomerClassValid", DataType = typeof(bool?)},

                new { DbColumn = "CreditLimit", DataType = typeof(string)},
                new { DbColumn = "CreditLimitValid", DataType = typeof(long)},

                new { DbColumn = "Sex", DataType = typeof(string)},

                new { DbColumn = "DOB", DataType = typeof(string)},
                new { DbColumn = "DOBValid", DataType = typeof(DateTime)},

                new { DbColumn = "TaxNumber", DataType = typeof(string)},
                new { DbColumn = "LicenseType", DataType = typeof(string)},
                new { DbColumn = "License", DataType = typeof(string)},

                new { DbColumn = "LicenseDate", DataType = typeof(string)},
                new { DbColumn = "LicenseDateValid", DataType = typeof(DateTime)},

                new { DbColumn = "Avatar", DataType = typeof(string)},
                new { DbColumn = "Background", DataType = typeof(string)},

                new { DbColumn = "LicenseExpired", DataType = typeof(string)},
                new { DbColumn = "LicenseExpiredValid", DataType = typeof(DateTime)},

                new { DbColumn = "LicensePlace", DataType = typeof(string)},
                new { DbColumn = "OriginNation", DataType = typeof(string)},
                new { DbColumn = "Nation", DataType = typeof(string)},
                new { DbColumn = "BankId", DataType = typeof(string)},
                new { DbColumn = "LocationId", DataType = typeof(string)},
                new { DbColumn = "Residence", DataType = typeof(string)},
                new { DbColumn = "Status", DataType = typeof(string)},
                new { DbColumn = "CMND", DataType = typeof(string)},

                new { DbColumn = "WarningMessages", DataType = typeof(string)}
            };

            var dataTable = new DataTable();
            foreach (var mapping in infoColumnMapping)
            {
                dataTable.Columns.Add(mapping.DbColumn, Nullable.GetUnderlyingType(mapping.DataType) ?? mapping.DataType);
            }

            DateTime minDate = DateTime.MinValue;
            DateTime.TryParseExact("01/01/1753 00:00:00", "dd/MM/yyyy HH:mm:ss", null, DateTimeStyles.None, out minDate);

            DateTime maxDate = DateTime.MaxValue;
            DateTime.TryParseExact("31/12/9999 23:59:59", "dd/MM/yyyy HH:mm:ss", null, DateTimeStyles.None, out maxDate);

            for (int i = 0; i < command.DataTable.Rows.Count; i++)
            {
                DataRow inputDataRow = dataTable.NewRow();

                string errorMessages = string.Empty, warningMessages = string.Empty; ;

                foreach (var infoColumnMappingItem in infoColumnMapping)
                {
                    string colName = (string)infoColumnMappingItem.DbColumn;
                    string colValue = string.Empty;
                    if (command.DataTable.Columns.Contains(colName.ToUpper()))
                    {
                        colValue = command.DataTable.Rows[i][colName.ToUpper()].ToString();
                    }

                    switch (colName)
                    {
                        case "Id":
                            {
                                inputDataRow[colName] = Guid.NewGuid();
                                break;
                            }
                        case "ImportSessionId":
                            {
                                inputDataRow[colName] = command.ImportCustomerSessionId;
                                break;
                            }

                        case "Name":
                            {
                                if (colValue.IsNullOrEmpty())
                                {
                                    errorMessages += (errorMessages.IsNullOrEmpty() ? string.Empty : "; ") + string.Format("{0} --> Thiếu thông tin Tên khách hàng", colName);
                                    inputDataRow[colName] = string.Empty;
                                }
                                inputDataRow[colName] = colValue;
                                break;
                            }
                        case "Code":
                            {
                                if (colValue.IsNullOrEmpty())
                                {
                                    errorMessages += (errorMessages.IsNullOrEmpty() ? string.Empty : "; ") + string.Format("{0} --> Thiếu thông tin CIF", colName);
                                    inputDataRow[colName] = string.Empty;
                                }
                                inputDataRow[colName] = colValue;
                                break;
                            }

                        case "Type":
                            {
                                int tempType = 0;
                                if (int.TryParse(colValue, out tempType))
                                {
                                    if (tempType != CustomerType.B2C.GetHashCode() && tempType != CustomerType.B2B.GetHashCode())
                                    {
                                        errorMessages += (errorMessages.IsNullOrEmpty() ? string.Empty : "; ") + string.Format("{0}: {1} --> Thông tin loại khách hàng không hợp lệ (Chỉ 1 hoặc 2)", colName, colValue);
                                    }
                                    else
                                    {
                                        inputDataRow[colName + "Valid"] = tempType;
                                    }
                                }
                                else
                                {
                                    errorMessages += (errorMessages.IsNullOrEmpty() ? string.Empty : "; ") + string.Format("{0}: {1} --> Thông tin loại khách hàng không hợp lệ (Chỉ 1 hoặc 2)", colName, colValue);
                                }
                                inputDataRow[colName] = colValue;
                                break;
                            }

                        case "CreditLimit":
                            {
                                inputDataRow[colName] = colValue;
                                long tempCreditLimit = 0;
                                if (long.TryParse(colValue, out tempCreditLimit))
                                {
                                    inputDataRow[colName + "Valid"] = tempCreditLimit;
                                }
                                else
                                {
                                    warningMessages += (warningMessages.IsNullOrEmpty() ? string.Empty : "; ") + string.Format("{0}: {1} --> Dữ liệu không hợp lý với kiểu dữ liệu Int64.", colName, colValue);
                                }
                                break;
                            }

                        case "Email":
                            {
                                if (colValue.IsNotNullOrEmpty())
                                {
                                    if (!EmailStringHandle.Validate(colValue.Trim()))
                                    {
                                        warningMessages += (warningMessages.IsNullOrEmpty() ? string.Empty : "; ") + string.Format("{0}: {1} --> Thông tin Email không hợp lệ", colName, colValue);
                                    }
                                    else
                                    {
                                        inputDataRow["EmailValid"] = colValue.Trim();
                                    }
                                }
                                inputDataRow[colName] = colValue;
                                break;
                            }

                        case "Dob":
                        case "LicenseDate":
                        case "LicenseExpire":
                            {
                                inputDataRow[colName] = colValue;
                                if (colValue.IsNotNullOrEmpty())
                                {
                                    DateTime dt = new DateTime();
                                    if (DateTime.TryParseExact(colValue, "dd/MM/yyyy HH:mm:ss", null, DateTimeStyles.None, out dt))
                                    {
                                        if (dt > minDate && dt < maxDate)
                                        {
                                            inputDataRow[colName + "Valid"] = dt;
                                        }
                                        else
                                        {
                                            warningMessages += (warningMessages.IsNullOrEmpty() ? string.Empty : "; ") + string.Format("{0}: {1} --> Chỉ nên có giá trị từ 01/01/1753 00:00:00 đến 31/12/9999 23:59:59", colName, colValue);
                                        }
                                    }
                                    else
                                    {
                                        warningMessages += (warningMessages.IsNullOrEmpty() ? string.Empty : "; ") + string.Format("{0}: {1} --> Không hợp lệ (Định dạng hợp lệ: dd/MM/yyyy HH:mm:ss)", colName, colValue);
                                    }
                                }
                                break;
                            }

                        case "CustomerClass":
                            {
                                if (colValue != "0" && colValue == "1")
                                {
                                    warningMessages += (warningMessages.IsNullOrEmpty() ? string.Empty : "; ") + string.Format("{0}: {1} --> Chỉ nên 0 hoặc 1", colName, colValue);
                                }

                                if (colValue == "1")
                                {
                                    inputDataRow[colName + "Valid"] = true;
                                }
                                else
                                {
                                    inputDataRow[colName + "Valid"] = false;
                                }
                                break;
                            }

                        case "Phone1":
                        case "Phone2":
                        case "Phone3":
                        case "ContactPhone":
                            {
                                inputDataRow[colName] = colValue;
                                inputDataRow[colName + "Valid"] = PhoneNumberValidator.Input(colValue, _text).Output;
                                break;
                            }
                        default:
                            {
                                if (!colName.Contains("Valid"))
                                {
                                    inputDataRow[colName] = colValue;
                                }
                                break;
                            }
                    }
                }
                inputDataRow["WarningMessages"] = warningMessages;

                dataTable.Rows.Add(inputDataRow);
            }

            var mappingColumnCollection = infoColumnMapping.Select(x => new[] { (string)x.DbColumn, (string)x.DbColumn }).ToList();

            if (!mappingColumnCollection.Any(x => x[0] == "IsDupInternal"))
            {
                mappingColumnCollection.Add(new[] { "IsDupInternal", "IsDupInternal" });
                dataTable.Columns.Add("IsDupInternal", typeof(bool));
                foreach (DataRow r in dataTable.Rows)
                {
                    r["IsDupInternal"] = false;
                }
            }
            if (!mappingColumnCollection.Any(x => x[0] == "WarningCode"))
            {
                mappingColumnCollection.Add(new[] { "WarningCode", "WarningCode" });
                dataTable.Columns.Add("WarningCode", typeof(int));
                foreach (DataRow r in dataTable.Rows)
                {
                    r["WarningCode"] = 0;
                }
            }

            Repository.BulkInsertAll(dataTable, "dbo.ImportCustomerRaw", mappingColumnCollection);
        }
    }
}