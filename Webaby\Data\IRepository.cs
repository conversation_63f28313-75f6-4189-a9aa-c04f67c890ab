﻿using Microsoft.EntityFrameworkCore.Query;
using System.Data;
using System.Linq.Expressions;

namespace Webaby.Data
{
    public interface IRepository
    {
        Task DeleteAsync(params IEntity[] entities);

        Task DeleteAsync(Guid? auditSessionId, params IEntity[] entities);

        Task DeleteAsync(IEnumerable<IEntity> entities, Guid? auditSessionId = null);

        Task UndeleteAsync(params IEntity[] entities);

        Task UndeleteAsync(Guid? auditSessionId, params IEntity[] entities);

        Task UndeleteAsync(IEnumerable<IEntity> entities, Guid? auditSessionId = null);

        Task DeleteAsync<TEntity>(params Guid[] ids) where TEntity : class, IEntity;

        Task DeleteAsync<TEntity>(Guid? auditSessionId, params Guid[] ids) where TEntity : class, IEntity;

        Task DeleteAsync<TEntity>(IEnumerable<Guid> ids, Guid? auditSessionId = null) where TEntity : class, IEntity;

        Task SaveAsync(params IEntity[] entities);

        Task SaveAsync(Guid? auditSessionId, params IEntity[] entities);

        Task SaveAsync(IEnumerable<IEntity> entities, Guid? auditSessionId = null);

        Task InsertAsync(IEnumerable<IEntity> entities, Guid? auditSessionId = null);

        Task UpdateWithoutAuditAsync(IEnumerable<IEntity> entities);

        Task<int> UpdateEntitiesAsync<TEntity>(Expression<Func<TEntity, bool>> predicate, Expression<Func<SetPropertyCalls<TEntity>, SetPropertyCalls<TEntity>>> setProperties) where TEntity : class, IEntity;
        Task<int> DeleteEntitiesAsync<TEntity>(Expression<Func<TEntity, bool>> predicate) where TEntity : class, IEntity;
        Task DeleteFromAsync<TEntity>(IQueryable<TEntity> queryable) where TEntity : class, IEntity;

        void BulkInsertAll(DataTable dataTable, string destinationTable, List<string[]> columnMappingCollection, int bulkCopyTimeout = 0);

        Task InsertFromAsync<TEntity>(IQueryable<TEntity> queryable) where TEntity : class, IEntity;
        Task InsertFromAsync<TSource, TTarget>(IQueryable<TSource> queryable, Func<LinqToDB.IDataContext, LinqToDB.ITable<TTarget>> target, Expression<Func<TSource, TTarget>> selector) where TTarget : class, IEntity;
    }
}