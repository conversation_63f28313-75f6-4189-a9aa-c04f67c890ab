﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Threading.Tasks;
using TinyCRM.Outbound.AgentFieldSaleCouple.Queries;
using TinyCRM.Outbound.LeadAssignment;
using TinyCRM.Outbound.Organization;
using TinyCRM.Outbound.Organization.Queries;
using TinyCRM.Outbound.SlotTime;
using Webaby;
using Webaby.Core.Organization;
using Webaby.Data;
using Webaby.Security;

namespace TinyCRM.Outbound.Appointment.Commands
{
    public class DistributeAppointmentCommand : CommandBase
    {
        public Guid AppointmentId
        {
            get;
            set;
        }
    }
    public class DistributeAppointmentCommandHandler : CommandHandlerBase<DistributeAppointmentCommand>
    {
        IUserService _userService;
        IConfiguration _configuration;

        public DistributeAppointmentCommandHandler(IServiceProvider serviceProvider, IConfiguration configuration, IUserService userService) : base(serviceProvider) { 
            _userService = userService;
            _configuration = configuration;
        }

        public override async Task ExecuteAsync(DistributeAppointmentCommand command)
        {
            var appointmentEntity = await EntitySet.GetAsync<AppointmentEntity>(command.AppointmentId);
            var leadAssignmentEntity = (await EntitySet.GetAsync<LeadAssignmentEntity>()).Where(la => la.WaitingAppointmentId == command.AppointmentId).SingleOrDefault();

            #region Assign Lead to FieldSale            // Get DMO Teams by Ward, District, Province of Appointment
            List<OrganizationEntity> dmoOrganizations = new List<OrganizationEntity>();

            var organizationWorkingAreaQuery = await EntitySet.GetAsync<OrganizationWorkingAreaEntity>();
            var organizationQuery = await EntitySet.GetAsync<OrganizationEntity>();

            if (appointmentEntity.WardId.HasValue && appointmentEntity.WardId.Value != Guid.Empty)
            {
                dmoOrganizations = (from org in organizationQuery
                                    join orgwa in organizationWorkingAreaQuery on org.Id equals orgwa.OrganizationId
                                    where orgwa.WardId == appointmentEntity.WardId.Value
                                    && org.OrganizationType == OrganizationTypes.DMOTeam
                                    select org).ToList();

                // If cannot find DMO Teams by Ward, continue find by District
                if (dmoOrganizations.Count == 0)
                {
                    dmoOrganizations = (from org in organizationQuery
                                        join orgwa in organizationWorkingAreaQuery on org.Id equals orgwa.OrganizationId
                                        where orgwa.DistrictId == appointmentEntity.DistrictId.Value
                                        && org.OrganizationType == OrganizationTypes.DMOTeam
                                        select org).ToList();
                }

                // Expression always false
                // If cannot find DMO Teams by District, continue find by Province
                //if (dmoOrganizations == null)
                //{
                //    dmoOrganizations = (from org in organizationQuery
                //                        join orgwa in organizationWorkingAreaQuery on org.Id equals orgwa.OrganizationId
                //                        where orgwa.ProvinceId == appointmentEntity.ProvinceId.Value
                //                        && org.OrganizationType == OrganizationTypes.DMOTeam
                //                        select org).ToList();
                //}
            }
            else if (appointmentEntity.DistrictId.HasValue && appointmentEntity.DistrictId.Value != Guid.Empty)
            {
                dmoOrganizations = (from org in organizationQuery
                                    join orgwa in organizationWorkingAreaQuery on org.Id equals orgwa.OrganizationId
                                    where orgwa.DistrictId == appointmentEntity.DistrictId.Value
                                    && org.OrganizationType == OrganizationTypes.DMOTeam
                                    select org).ToList();

                // Expression always false
                // If cannot find DMO Teams by District, continue find by Province
                //if (dmoOrganizations == null)
                //{
                //    dmoOrganizations = (from org in organizationQuery
                //                        join orgwa in organizationWorkingAreaQuery on org.Id equals orgwa.OrganizationId
                //                        where orgwa.ProvinceId == appointmentEntity.ProvinceId.Value
                //                        && org.OrganizationType == OrganizationTypes.DMOTeam
                //                        select org).ToList();
                //}
            }
            else
            {
                dmoOrganizations = (from org in organizationQuery
                                    join orgwa in organizationWorkingAreaQuery on org.Id equals orgwa.OrganizationId
                                    where orgwa.ProvinceId == appointmentEntity.ProvinceId.Value
                                    && org.OrganizationType == OrganizationTypes.DMOTeam
                                    select org).ToList();
            }

            #region Get Slot Time Range

            DateTime fromTime = appointmentEntity.MeetDate.Date;
            DateTime toTime = appointmentEntity.MeetDate.Date.AddDays(1).AddSeconds(-1);

            var slotTimeQuery = await EntitySet.GetAsync<SlotTimeEntity>();
            var slotTime = (from st in slotTimeQuery
                            where st.FromTime <= DateTime.Now.TimeOfDay && DateTime.Now.TimeOfDay < st.ToTime
                            select st).SingleOrDefault();

            if (slotTime != null)
            {
                fromTime = DateTime.Now.Date.Add(slotTime.FromTime);
                toTime = DateTime.Now.Date.Add(slotTime.ToTime);
            }

            #endregion

            // If found DMO Teams
            if (dmoOrganizations.Count > 0)
            {                // Get DMO team assignement infos
                var cmd = EntitySet.CreateDbCommand();
                cmd.Parameters.Add(DbParameterHelper.NewNullableDateTimeParameter(cmd, "@MeetDate", appointmentEntity.MeetDate));
                cmd.Parameters.Add(DbParameterHelper.NewNullableDateTimeParameter(cmd, "@SlotFromTime", fromTime));
                cmd.Parameters.Add(DbParameterHelper.NewNullableDateTimeParameter(cmd, "@SlotToTime", toTime));
                cmd.Parameters.Add(DbParameterHelper.NewIdListParameter(  "@IdList", dmoOrganizations.Select(org => org.Id).ToList()));
                cmd.CommandText = "telesale.lead_GetAssignedAppointmentInfos";
                cmd.CommandType = CommandType.StoredProcedure;
                var dmoTeamInfos = await EntitySet.ExecuteReadCommandAsync<FieldSaleTeamInfo>(cmd);

                // Only get the DMO team that have Field Sales
                dmoTeamInfos = (from dmoteam in dmoTeamInfos
                                where dmoteam.FieldSaleCount > 0
                                select dmoteam);                // Find Field Sale couples
                cmd = EntitySet.CreateDbCommand();
                cmd.Parameters.Add(DbParameterHelper.AddNullableGuid(cmd, "@AgentId", _userService.GetCurrentUser().Id));
                cmd.Parameters.Add(DbParameterHelper.NewNullableDateTimeParameter(cmd, "@MeetDate", appointmentEntity.MeetDate));
                cmd.Parameters.Add(DbParameterHelper.NewNullableDateTimeParameter(cmd, "@SlotFromTime", fromTime));
                cmd.Parameters.Add(DbParameterHelper.NewNullableDateTimeParameter(cmd, "@SlotToTime", toTime));
                cmd.CommandText = "telesale.lead_GetCoupleFieldSales";
                cmd.CommandType = CommandType.StoredProcedure;
                var coupleFieldSales = await EntitySet.ExecuteReadCommandAsync<FieldSaleAppointmentInfo>(cmd);

                int appointmentsPerDay = _configuration.GetValue<int>("AppointmentsPerDay");
                int appointmentsPerSlot = _configuration.GetValue<int>("AppointmentsPerSlot");

                coupleFieldSales = (from cfs in coupleFieldSales
                                    where cfs.DayAppointmentCount < appointmentsPerDay
                                    select cfs);

                // If found coupled Field Sale teams
                if (coupleFieldSales.Count() > 0)
                {
                    // Round robin, get Field Sale that appointments can be assigned largest
                    var coupleFieldSale = (from cfst in coupleFieldSales
                                           orderby cfst.DayAppointmentCount
                                           select cfst).FirstOrDefault();                    // Assign Lead to found DMO.
                    leadAssignmentEntity.AssignedFieldSaleTeamId = coupleFieldSale.OrganizationId;
                    leadAssignmentEntity.AssignedFieldSaleTeamDate = DateTime.Now;
                    leadAssignmentEntity.AssignedFieldSaleId = coupleFieldSale.FieldSaleId;
                    leadAssignmentEntity.AssignedFieldSaleDate = DateTime.Now;
                    await Repository.SaveAsync(leadAssignmentEntity);
                }
                else // Not found coupled Field Sale team
                {
                    // Round robin, get the team that appointments can be assigned largest
                    var dmoOrganization = (from cfst in dmoTeamInfos
                                           where cfst.SlotTimeAppointmentCount * cfst.FieldSaleCount < cfst.FieldSaleCount * appointmentsPerSlot
                                           orderby (float)cfst.DayAppointmentCount / (cfst.FieldSaleCount * appointmentsPerDay), (cfst.FieldSaleCount * appointmentsPerDay) descending
                                           select cfst).FirstOrDefault();

                    if (dmoOrganization == null)
                    {
                        dmoOrganization = (from cfst in dmoTeamInfos
                                           orderby (float)cfst.DayAppointmentCount / (cfst.FieldSaleCount * appointmentsPerDay), (cfst.FieldSaleCount * appointmentsPerDay) descending
                                           select cfst).FirstOrDefault();
                    }                    // Assign Lead to found DMO team.
                    leadAssignmentEntity.AssignedFieldSaleTeamId = dmoOrganization.Id;
                    leadAssignmentEntity.AssignedFieldSaleTeamDate = DateTime.Now;
                    await Repository.SaveAsync(leadAssignmentEntity);
                }
            }

            #endregion
        }
    }
}