﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using System.Threading.Tasks;
using Webaby;
using Webaby.Data;

namespace Webaby.Core.Organization.Queries
{
    public class GetTicketServiceTypeDefaultOrganizationQuery : QueryBase<TicketServiceTypeDefaultOrganization>
    {
        public Guid OrganizationId { get; set; }
    }

    internal class GetTicketServiceTypeDefaultOrganizationQueryHandler : QueryHandlerBase<GetTicketServiceTypeDefaultOrganizationQuery, TicketServiceTypeDefaultOrganization>
    {
        public GetTicketServiceTypeDefaultOrganizationQueryHandler(IServiceProvider serviceProvider) : base(serviceProvider)
        {
        }

        public override async Task<QueryResult<TicketServiceTypeDefaultOrganization>> ExecuteAsync(GetTicketServiceTypeDefaultOrganizationQuery query)
        {
            var cmd = EntitySet.CreateDbCommand(CommandType.StoredProcedure, "dbo.GetTicketServiceTypeDefaultOrganization");
            cmd.Parameters.Add(DbParameterHelper.AddNullableGuid(cmd, "@OrganizationId", query.OrganizationId));

            var mainQuery = await EntitySet.ExecuteReadCommandAsync<TicketServiceTypeDefaultOrganization>(cmd);
            await Task.CompletedTask;
            return QueryResult.Create(mainQuery);
        }
    }

    public class TicketServiceTypeDefaultOrganization
    {
        public string Level1 { get; set; }

        public string Level2 { get; set; }

        public string Level3 { get; set; }

        public string Level4 { get; set; }
    }
}