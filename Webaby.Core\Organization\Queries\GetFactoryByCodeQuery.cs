﻿using System.Data;
using System.Data.Common;
using System.Threading.Tasks;
using Webaby;
using Webaby.Core.Organization.Queries;
using Webaby.Data;

namespace Webaby.Core.Organization.Queries
{
    public class GetFactoryByCodeQuery : QueryBase<OrganizationData>
    {
        public GetFactoryByCodeQuery(string code)
        {
            Code = code;
        }

        public string Code { get; set; }
    }

    internal class GetFactoryByCodeQueryHandler : QueryHandlerBase<GetFactoryByCodeQuery, OrganizationData>
    {
        public GetFactoryByCodeQueryHandler(IServiceProvider serviceProvider) : base(serviceProvider)
        {
        }

        public override async Task<QueryResult<OrganizationData>> ExecuteAsync(GetFactoryByCodeQuery query)
        {
            var cmd = EntitySet.CreateDbCommand(CommandType.StoredProcedure, "GetFactoryByCode");
            cmd.Parameters.Add(DbParameterHelper.AddNullableString(cmd, "@code", query.Code));

            var mainQuery = await EntitySet.ExecuteReadCommandAsync<OrganizationData>(cmd);
            await Task.CompletedTask;
            return QueryResult.Create(mainQuery);
        }
    }
}