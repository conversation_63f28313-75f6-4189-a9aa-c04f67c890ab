﻿using System;
using System.Data;
using System.Data.SqlClient;
using System.Threading.Tasks;
using Webaby;
using Webaby.Data;

namespace TinyCRM.Outbound.ExcelImport.Queries
{
    public class GetImportFWDContractResultQuery : QueryBase<FWDContractImportResult>
    {
        public Guid ImportSessionId { get; set; }

        public Guid? CampaignId { get; set; }
    }
    internal class GetImportFWDContractResultQueryHandler : QueryHandlerBase<GetImportFWDContractResultQuery, FWDContractImportResult>
    {
        public GetImportFWDContractResultQueryHandler(IServiceProvider serviceProvider) : base(serviceProvider)
        {
        }
        public override async Task<QueryResult<FWDContractImportResult>> ExecuteAsync(GetImportFWDContractResultQuery query)
        {
            var command = EntitySet.CreateDbCommand();
            command.CommandText = "fwd.ImportFWDContract_Summary";
            command.CommandType = CommandType.StoredProcedure;

            command.Parameters.Add(DbParameterHelper.AddNullableGuid(command, "@ImportSessionId", query.ImportSessionId));

            var result = await EntitySet.ExecuteReadCommandAsync<FWDContractImportResult>(command);
            return QueryResult.Create(result);
        }
    }
}
