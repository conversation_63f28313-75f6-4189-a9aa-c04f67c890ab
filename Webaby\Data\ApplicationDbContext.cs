﻿using Dapper;
using LinqToDB.EntityFrameworkCore;
using Microsoft.AspNetCore.Rewrite;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Query;
using Microsoft.Extensions.Configuration;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Data;
using System.Data.Common;
using System.Linq.Expressions;
using System.Reflection;
using Webaby.Language;
using Webaby.Security;

namespace Webaby.Data
{
    public class ApplicationDbContext : IEntitySet, IRepository, ILocalTransactionManager, ISqlExecutor
    {
        private readonly string _connectionString = string.Empty;
        private readonly IConfiguration _configuration;

        private readonly ReadDatabaseContext _readDbContext;
        private readonly WriteDatabaseContext _writeDbContext;
        private readonly DatabaseContext _databaseContext;

        private readonly Lazy<IUserService> _userAppService;

        private ILocalTransaction _localTransaction = null;

        private LinqToDB.IDataContext _linqToDBContext = null;

        public ApplicationDbContext(ReadDatabaseContext readDbContext, WriteDatabaseContext writeDbContext, DatabaseContext databaseContext, Lazy<IUserService> userAppService, IConfiguration configuration)
        {
            _readDbContext = readDbContext;
            _readDbContext.ChangeTracker.QueryTrackingBehavior = QueryTrackingBehavior.NoTracking;

            _writeDbContext = writeDbContext;
            _databaseContext = databaseContext;
            _userAppService = userAppService;
            
            _configuration = configuration;
            _connectionString = _configuration.GetConnectionString("Default") ?? throw new InvalidOperationException("Connection string 'Default' is not configured.");
        }

        #region IEntitySet

        public async Task<TEntity> GetAsync<TEntity>(Guid id, Boolean includeDeleted = false) where TEntity : class, IEntity
        {
            if (id == Guid.Empty)
            {
                return null;
            }
            var entitySet = await GetAsync<TEntity>(includeDeleted);
            return await entitySet.AsNoTracking().FirstOrDefaultAsync(FindByIdPredicate<TEntity>(id));
        }

        public async Task<IQueryable<TEntity>> GetAsync<TEntity>(Boolean includeDeleted = false) where TEntity : class, IEntity
        {
            return await GetSet<TEntity>(includeDeleted);
        }

        public TEntity Get<TEntity>(Guid id, Boolean includeDeleted = false) where TEntity : class, IEntity
        {
            if (id == Guid.Empty)
            {
                return null;
            }
            var entitySet = GetSetSync<TEntity>(includeDeleted);
            return entitySet.AsNoTracking().FirstOrDefault(FindByIdPredicate<TEntity>(id));
        }

        public IQueryable<TEntity> Get<TEntity>(Boolean includeDeleted = false) where TEntity : class, IEntity
        {
            return GetSetSync<TEntity>(includeDeleted);
        }

        public async Task<bool> ExistAsync<TEntity>(Guid id, bool includeDeleted = false) where TEntity : class, IEntity
        {
            var entitySet = await GetAsync<TEntity>(includeDeleted);

            bool exist = false;
            exist = entitySet.Any(x => x.Id == id);
            return id != Guid.Empty && exist;
        }

        public async Task<IEnumerable<TResult>> ManyAsync<TResult>(string query, params object[] args) where TResult : class
        {
            // UseTransactionIfExisted();
            var readDbContext = GetReadDbContext();

            return await readDbContext.Set<TResult>()
                                        .FromSqlRaw(query, args)
                                        .ToListAsync();
        }

        public async Task<IEnumerable<TResult>> ExecuteReadCommandAsync<TResult>(DbCommand command, bool useDefaultTimeout = true)
        {
            var readDbContext = GetReadDbContext();

            command.Connection = readDbContext.Database.GetDbConnection();
            command.CommandTimeout = useDefaultTimeout ? DatabaseCommandtimeout.Default : command.CommandTimeout;

            SetDbCommandTransactionIfExisted(command);

            IEnumerable<TResult> result = new List<TResult>();
            try
            {
                if (command.Connection.State != ConnectionState.Open)
                {
                    command.Connection.Open();
                }

                var dynamicParams = new DynamicParameters();
                foreach (DbParameter param in command.Parameters)
                {
                    dynamicParams.Add(param.ParameterName, param.Value, param.DbType, param.Direction, param.Size);
                }

                bool isPostgreSql = command.Connection is Npgsql.NpgsqlConnection;

                string commandText = isPostgreSql ?
                                        $"SELECT * FROM {command.CommandText}({string.Join(", ", command.Parameters.Cast<DbParameter>().Select(p => "@p_" + p.ParameterName))})"
                                        : command.CommandText;

                result = await command.Connection.QueryAsync<TResult>
                (
                    sql: commandText,
                    param: dynamicParams,
                    commandTimeout: command.CommandTimeout,
                    commandType: isPostgreSql ? CommandType.Text : command.CommandType
                );

                return result;
            }
            finally
            {
                CloseDbCommandConnection(command);
            }
        }

        public async Task<DataSet> ExecuteReadCommandAsync(DbCommand command, bool useDefaultTimeout = true)
        {
            var readDbContext = GetReadDbContext();

            command.Connection = readDbContext.Database.GetDbConnection();
            command.CommandTimeout = useDefaultTimeout ? DatabaseCommandtimeout.Default : command.CommandTimeout;

            SetDbCommandTransactionIfExisted(command);

            try
            {
                if (command.Connection.State != ConnectionState.Open)
                {
                    await command.Connection.OpenAsync();
                }
                SqlDataAdapter da = new SqlDataAdapter(command as SqlCommand);
                DataSet result = new DataSet();
                da.Fill(result);
                return result;
            }
            finally
            {
                CloseDbCommandConnection(command);
            }
        }

        public async Task<Int32> ExecuteNonQueryAsync(DbCommand command, bool useDefaultTimeout = true)
        {
            var readDbContext = GetReadDbContext();

            command.Connection = readDbContext.Database.GetDbConnection();
            command.CommandTimeout = useDefaultTimeout ? DatabaseCommandtimeout.Default : command.CommandTimeout;

            SetDbCommandTransactionIfExisted(command);

            try
            {
                if (command.Connection.State != ConnectionState.Open)
                {
                    await command.Connection.OpenAsync();
                }
                var result = await command.ExecuteNonQueryAsync();
                return result;
            }
            finally
            {
                CloseDbCommandConnection(command);
            }
        }

        public async Task<int> ExecuteAsync(string query, params object[] args)
        {
            var readDbContext = GetReadDbContext();
            return await readDbContext.Database.ExecuteSqlRawAsync(query, args);
        }

        public DbCommand CreateDbCommand()
        {
            var readDbContext = GetReadDbContext();
            return readDbContext.Database.GetDbConnection().CreateCommand();
        }

        public DbCommand CreateDbCommand(CommandType commandType, string commandText)
        {
            var readDbContext = GetReadDbContext();

            DbCommand dbCommand = readDbContext.Database.GetDbConnection().CreateCommand();
            dbCommand.CommandType = commandType;
            dbCommand.CommandText = commandText;
            return dbCommand;
        }

        public void BulkInsertAllAsync<T>(IEnumerable<T> entities, int bulkCopyTimeout = 0)
        {
            // SqlBulkCopy is used ONLY for SQL Server
            // Need to implement for other database types if needed

            var conn = _readDbContext.Database.GetDbConnection() as SqlConnection;
            if (conn.State != ConnectionState.Open)
            {
                conn.Open();
            }

            entities = entities.ToArray();
            Type t = typeof(T);

            var tableAttribute = (TableAttribute)t.GetCustomAttributes(typeof(TableAttribute), false).Single();
            SqlBulkCopy bulkCopy = null;
            bulkCopy = new SqlBulkCopy(conn) { DestinationTableName = tableAttribute.Name };

            var properties = t.GetProperties().Where(EventTypeFilter).ToArray();
            var table = new DataTable();

            foreach (var property in properties)
            {
                Type propertyType = property.PropertyType;
                if (propertyType.IsGenericType &&
                    propertyType.GetGenericTypeDefinition() == typeof(Nullable<>))
                {
                    propertyType = Nullable.GetUnderlyingType(propertyType);
                }

                // Only correct in case the mapping is Enum property and database column is nvarchar
                // This is only quick fix when only TinNhanDichVuEntity is the only class using this method.
                // Need to modified if a Enum property is mapped to a column of type int/byte
                if (propertyType.IsEnum)
                {
                    propertyType = typeof(String);
                }

                table.Columns.Add(new DataColumn(property.Name, propertyType));
            }

            foreach (var entity in entities)
            {
                table.Rows.Add(properties.Select(property => GetPropertyValue(property.GetValue(entity, null))).ToArray());
            }

            if (typeof(IEntity).IsAssignableFrom(t))
            {
                foreach (DataRow row in table.Rows)
                {
                    row["Id"] = Guid.NewGuid();
                }
            }

            bulkCopy.BulkCopyTimeout = DatabaseCommandtimeout.Default;
            if (bulkCopyTimeout > 0)
            {
                bulkCopy.BulkCopyTimeout = bulkCopyTimeout;
            }

            bulkCopy.WriteToServer(table);
            conn.Close();
        }

        private static IQueryable<TEntity> NotDeleted<TEntity>(IQueryable<TEntity> queryable)
        {
            var entityParam = Expression.Parameter(typeof(TEntity));
            var getDeletedProperty = Expression.PropertyOrField(entityParam, "Deleted");
            var notDeleted = Expression.Not(getDeletedProperty);
            var lambda = Expression.Lambda<Func<TEntity, Boolean>>(notDeleted, entityParam);
            return queryable.Where(lambda);
        }

        public DatabaseContext GetReadDbContext()
        {
            if (_localTransaction != null && _localTransaction.IsCompleted == false)
            {
                return _databaseContext;
            }
            return _readDbContext;
        }
        public DatabaseContext GetWriteDbContext()
        {
            if (_localTransaction != null && _localTransaction.IsCompleted == false)
            {
                return _databaseContext;
            }
            return _writeDbContext;
        }

        public LinqToDB.IDataContext GetLinqToDBContext()
        {
            if (_linqToDBContext == null)
            {
                _linqToDBContext = GetReadDbContext().CreateLinqToDBContext();
            }
            return _linqToDBContext;
        }

        #endregion

        #region IRepository

        public async Task DeleteAsync(params IEntity[] entities)
        {
            await DeleteAsync((IEnumerable<IEntity>)entities);
        }

        public async Task DeleteAsync(Guid? auditSessionId, params IEntity[] entities)
        {
            await DeleteAsync((IEnumerable<IEntity>)entities, auditSessionId);
        }

        public async Task DeleteAsync(IEnumerable<IEntity> entities, Guid? auditSessionId = null)
        {
            var writeContext = GetWriteDbContext();
            var readContext = GetReadDbContext();

            foreach (var entity in entities)
            {
                if (entity.Id == Guid.Empty) continue;
                var entityType = entity.GetType();
                var finder = CreateFinder(entityType, readContext, entity.Id);
                var oldVersion = (IEntity)finder.GetResult();
                if (oldVersion == null) continue;

                var deleteEntity = entity as ISoftDeleteEnabledEntity;
                if (deleteEntity != null)
                {
                    deleteEntity.Deleted = true;
                    deleteEntity.DeletedDate = GetCurrentTime();

                    if (_userAppService != null)
                    {
                        var deletedByEntity = entity as IDeletedByEnabledEntity;
                        if (deletedByEntity != null)
                        {
                            var currentUser = await _userAppService.Value.GetCurrentUserAsync();
                            deletedByEntity.DeletedBy = currentUser.Id;
                        }
                    }

                    writeContext.Update(deleteEntity);
                    AuditEntityChange(entity, oldVersion, AuditAction.Delete, writeContext, auditSessionId);
                }
                else
                {
                    writeContext.Remove(entity);
                    AuditEntityChange(entity, null, AuditAction.Delete, writeContext, auditSessionId);
                }
            }
            await writeContext.SaveChangesAsync();
            writeContext.ChangeTracker.Clear();
        }

        public async Task DeleteAsync<TEntity>(params Guid[] ids) where TEntity : class, IEntity
        {
            await DeleteAsync<TEntity>((IEnumerable<Guid>)ids);
        }

        public async Task DeleteAsync<TEntity>(Guid? auditSessionId, params Guid[] ids) where TEntity : class, IEntity
        {
            await DeleteAsync<TEntity>((IEnumerable<Guid>)ids, auditSessionId);
        }

        public async Task DeleteAsync<TEntity>(IEnumerable<Guid> ids, Guid? auditSessionId = null) where TEntity : class, IEntity
        {
            var arrays = ids.Where(x => x != Guid.Empty).ToArray();
            var deletedEntities = (from de in await GetAsync<TEntity>()
                                   where arrays.Contains(de.Id)
                                   select de).ToList();
            await DeleteAsync(deletedEntities, auditSessionId);
        }

        public async Task UndeleteAsync(params IEntity[] entities)
        {
            await UndeleteAsync((IEnumerable<IEntity>)entities);
        }

        public async Task UndeleteAsync(Guid? auditSessionId, params IEntity[] entities)
        {
            await UndeleteAsync((IEnumerable<IEntity>)entities, auditSessionId);
        }

        public async Task UndeleteAsync(IEnumerable<IEntity> entities, Guid? auditSessionId = null)
        {
            var writeContext = GetWriteDbContext();
            var readContext = GetReadDbContext();

            foreach (var entity in entities)
            {
                var entityType = entity.GetType();
                var set = readContext.GetType().GetMethod("Set", Type.EmptyTypes)!.MakeGenericMethod(entityType).Invoke(readContext, null);

                var findType = typeof(FindEntityByKey<>).MakeGenericType(entityType);
                var instance = (IGenericTypeProxy)Activator.CreateInstance(findType, set, entity.Id)!;

                var oldVersion = (IEntity)instance.GetResult();

                var modifiedDateEntity = entity as IModifiedDateEnabledEntity;
                if (modifiedDateEntity != null)
                {
                    modifiedDateEntity.ModifiedDate = GetCurrentTime();
                }

                if (_userAppService != null)
                {
                    var modifiedByEntity = entity as IModifiedByEnabledEntity;
                    if (modifiedByEntity != null && !modifiedByEntity.ModifiedBy.HasValue)
                    {
                        var currentUser = await _userAppService.Value.GetCurrentUserAsync();
                        modifiedByEntity.ModifiedBy = currentUser.Id;
                    }
                }
                var deletedEntity = entity as ISoftDeleteEnabledEntity;
                if (deletedEntity != null)
                {
                    deletedEntity.Deleted = false;
                    deletedEntity.DeletedDate = null;
                }

                var deletedByEntity = entity as IDeletedByEnabledEntity;
                if (deletedByEntity != null)
                {
                    deletedByEntity.DeletedBy = null;
                }

                writeContext.Attach(entity);
                AuditEntityChange(entity, oldVersion, AuditAction.Update, writeContext, auditSessionId);
            }
            await writeContext.SaveChangesAsync();
            writeContext.ChangeTracker.Clear();
        }

        public async Task SaveAsync(params IEntity[] entities)
        {
            await SaveAsync((IEnumerable<IEntity>)entities);
        }

        public async Task SaveAsync(Guid? auditSessionId, params IEntity[] entities)
        {
            await SaveAsync((IEnumerable<IEntity>)entities, auditSessionId);
        }

        public async Task SaveAsync(IEnumerable<IEntity> entities, Guid? auditSessionId = null)
        {
            var writeContext = GetWriteDbContext();

            var entityArray = entities.Where(x => x != null).ToArray();
            foreach (var entity in entityArray)
            {
                var entityType = entity.GetType();

                if (entity.Id == Guid.Empty)
                {
                    InsertEntity(writeContext, entity, auditSessionId);
                }
                else
                {
                    var finder = CreateFinder(entityType, writeContext, entity.Id);
                    var oldVersion = (IEntity)finder.GetResult();
                    if (oldVersion == null)
                    {
                        InsertEntity(writeContext, entity, auditSessionId);
                        continue;
                    }
                    var modifiedDateEntity = entity as IModifiedDateEnabledEntity;
                    if (modifiedDateEntity != null)
                    {
                        modifiedDateEntity.ModifiedDate = GetCurrentTime();
                    }

                    if (_userAppService != null)
                    {
                        var modifiedByEntity = entity as IModifiedByEnabledEntity;
                        if (modifiedByEntity != null)
                        {
                            try
                            {
                                var currentUser = await _userAppService.Value.GetCurrentUserAsync();
                                modifiedByEntity.ModifiedBy = currentUser.Id;
                            }
                            catch
                            {
                                modifiedByEntity.ModifiedBy = Guid.Parse("C799B68D-EBF6-4B6B-B6C6-5A3F36178F4A");// DefaultUserService -> AnonymousUserId
                            }
                        }
                    }

                    var deletedEntity = entity as ISoftDeleteEnabledEntity;
                    if (deletedEntity != null)
                    {
                        deletedEntity.Deleted = ((ISoftDeleteEnabledEntity)oldVersion).Deleted;
                    }

                    var deletedByEntity = entity as IDeletedByEnabledEntity;
                    if (deletedByEntity != null)
                    {
                        deletedByEntity.DeletedBy = ((IDeletedByEnabledEntity)oldVersion).DeletedBy;
                    }

                    writeContext.Update(entity);

                    AuditEntityChange(entity, oldVersion, AuditAction.Update, writeContext, auditSessionId);
                }
            }

            await writeContext.SaveChangesAsync();
            writeContext.ChangeTracker.Clear();
        }

        public async Task InsertAsync(IEnumerable<IEntity> entities, Guid? auditSessionId = null)
        {
            var writeContext = GetWriteDbContext();

            var entityArray = entities.Where(x => x != null).ToArray();
            foreach (var entity in entityArray)
            {
                InsertEntity(writeContext, entity, auditSessionId);
            }

            await writeContext.SaveChangesAsync();
            writeContext.ChangeTracker.Clear();
        }

        public async Task UpdateWithoutAuditAsync(IEnumerable<IEntity> entities)
        {
            var writeContext = GetWriteDbContext();

            var entityArray = entities.Where(x => x != null).ToArray();
            foreach (var entity in entityArray)
            {
                var modifiedDateEntity = entity as IModifiedDateEnabledEntity;
                if (modifiedDateEntity != null)
                {
                    modifiedDateEntity.ModifiedDate = GetCurrentTime();
                }

                if (_userAppService != null)
                {
                    var modifiedByEntity = entity as IModifiedByEnabledEntity;
                    if (modifiedByEntity != null)
                    {
                        try
                        {
                            var currentUser = await _userAppService.Value.GetCurrentUserAsync();
                            modifiedByEntity.ModifiedBy = currentUser.Id;
                        }
                        catch
                        {
                            modifiedByEntity.ModifiedBy = Guid.Parse("C799B68D-EBF6-4B6B-B6C6-5A3F36178F4A");// DefaultUserService -> AnonymousUserId
                        }
                    }
                }

                writeContext.Update(entity);
            }

            await writeContext.SaveChangesAsync();
            writeContext.ChangeTracker.Clear();
        }

        public async Task<int> UpdateEntitiesAsync<TEntity>(Expression<Func<TEntity, bool>> predicate, Expression<Func<SetPropertyCalls<TEntity>, SetPropertyCalls<TEntity>>> setProperties) where TEntity : class, IEntity
        {
            var writeContext = GetWriteDbContext();

            int result = await writeContext.Set<TEntity>()
                                             .Where(predicate)
                                             .ExecuteUpdateAsync(setProperties);
            writeContext.ChangeTracker.Clear();

            return result;
        }

        public async Task<int> DeleteEntitiesAsync<TEntity>(Expression<Func<TEntity, bool>> predicate) where TEntity : class, IEntity
        {
            var writeContext = GetWriteDbContext();

            int result = await writeContext.Set<TEntity>()
                                             .Where(predicate)
                                             .ExecuteDeleteAsync();
            writeContext.ChangeTracker.Clear();

            return result;
        }

        public async Task DeleteFromAsync<TEntity>(IQueryable<TEntity> queryable) where TEntity : class, IEntity
        {
            var writeContext = GetWriteDbContext();
            await queryable.ExecuteDeleteAsync();
            writeContext.ChangeTracker.Clear();
            //var writeContext = GetWriteDbContext();
            //using var lctx = LinqToDB.EntityFrameworkCore.LinqToDBForEFTools.CreateLinqToDBContext(writeContext);
            //await LinqToDB.LinqExtensions.DeleteAsync(queryable);
        }

        public void BulkInsertAll(DataTable dataTable, string destinationTable, List<string[]> mappingCollection, int bulkCopyTimeout = 0)
        {
            // SqlBulkCopy is used ONLY for SQL Server
            // Need to implement for other database types if needed
            using (var bulkCopy = new SqlBulkCopy(_connectionString, SqlBulkCopyOptions.KeepNulls))
            {
                bulkCopy.BulkCopyTimeout = DatabaseCommandtimeout.Default;
                if (bulkCopyTimeout > 0)
                {
                    bulkCopy.BulkCopyTimeout = bulkCopyTimeout;
                }

                if (mappingCollection != null)
                {
                    foreach (string[] mapping in mappingCollection)
                    {
                        bulkCopy.ColumnMappings.Add(new SqlBulkCopyColumnMapping(mapping[0], mapping[1]));
                    }
                }

                bulkCopy.DestinationTableName = destinationTable;
                bulkCopy.WriteToServer(dataTable);
            }
        }

        public async Task InsertFromAsync<TEntity>(IQueryable<TEntity> queryable) where TEntity : class, IEntity
        {
            var writeContext = GetWriteDbContext();
            using var lctx = LinqToDB.EntityFrameworkCore.LinqToDBForEFTools.CreateLinqToDBContext(writeContext);
            await LinqToDB.LinqExtensions.InsertAsync(queryable, LinqToDB.DataExtensions.GetTable<TEntity>(lctx), x => x);
        }

        public async Task InsertFromAsync<TSource, TTarget>(IQueryable<TSource> queryable, Func<LinqToDB.IDataContext, LinqToDB.ITable<TTarget>> target, Expression<Func<TSource, TTarget>> selector) where TTarget : class, IEntity
        {
            var writeContext = GetWriteDbContext();
            using var lctx = LinqToDB.EntityFrameworkCore.LinqToDBForEFTools.CreateLinqToDBContext(writeContext, _localTransaction?.DbContextTransaction);
            await LinqToDB.LinqExtensions.InsertAsync(queryable, target(lctx), selector);
        }

        #endregion

        #region ISqlExecutor

        public dynamic Proc { get; private set; }

        public async Task<bool> IsObjectExistingAsync(string name)
        {
            var objectId = await ObjectIdAsync(name);
            return objectId != null;
        }

        public async Task<int?> ObjectIdAsync(string name)
        {
            SqlObjectId sqlObjectId = await OneAsync<SqlObjectId>("select object_id({0}) ObjectId", name);
            if (sqlObjectId != null)
            {
                return sqlObjectId.ObjectId;
            }
            return null;
        }

        public async Task<TResult> OneAsync<TResult>(string query, params object[] args) where TResult : class
        {
            var manyEntities = await ManyAsync<TResult>(query, args);
            return manyEntities.FirstOrDefault();
        }

        public async Task<TResult> Func<TResult>(string name, params object[] args) where TResult : class
        {
            return await OneAsync<TResult>(GenerateFunctionCall(name, args.Length), args);
        }

        private object GetPropertyValue(object o)
        {
            if (o == null) return DBNull.Value;
            return o;
        }

        private static String GenerateFunctionCall(String name, Int32 argumentCount)
        {
            return string.Format("select {0}({1})", name, String.Join(", ", Enumerable.Range(0, argumentCount).Select(x => "{" + x + "}")));
        }

        #endregion

        #region Transactions

        public ILocalTransaction CreateTransaction(IsolationLevel level = IsolationLevel.ReadCommitted)
        {
            if (_localTransaction != null && _localTransaction.IsCompleted == false)
            {
                throw new InvalidOperationException("Transaction already created");
            }

            var dbTransaction = _databaseContext.Database.BeginTransaction(level);
            _localTransaction = new LocalTransaction(dbTransaction);

            if (_linqToDBContext != null)
            {
                _linqToDBContext.Dispose();
            }

            return _localTransaction;
        }

        #endregion

        private async Task<IQueryable<TEntity>> GetSet<TEntity>(Boolean includeDeleted) where TEntity : class, IEntity
        {
            var readContext = GetReadDbContext();

            var set = readContext.Set<TEntity>().AsNoTracking().Select(x => x);

            if (typeof(IDataAuthorizedEnabledEntity).IsAssignableFrom(typeof(TEntity)))
            {
                set = await DataAuthorized(set);
            }

            if (includeDeleted || !typeof(ISoftDeleteEnabledEntity).IsAssignableFrom(typeof(TEntity)))
            {
                return set;
            }
            return NotDeleted(set);
        }

        private IQueryable<TEntity> GetSetSync<TEntity>(Boolean includeDeleted) where TEntity : class, IEntity
        {
            var readContext = GetReadDbContext();

            var set = readContext.Set<TEntity>().AsNoTracking().Select(x => x);

            if (typeof(IDataAuthorizedEnabledEntity).IsAssignableFrom(typeof(TEntity)))
            {
                set = DataAuthorizedSync(set);
            }

            if (includeDeleted || !typeof(ISoftDeleteEnabledEntity).IsAssignableFrom(typeof(TEntity)))
            {
                return set;
            }
            return NotDeleted(set);
        }

        private async Task<IQueryable<TEntity>> DataAuthorized<TEntity>(IQueryable<TEntity> queryable)
        {
            Guid dataAuthorizedId = Guid.Empty;
            var currentUser = await _userAppService.Value.GetCurrentUserAsync();
            if (currentUser != null && currentUser.DataAuthorizedId.IsNotNullOrEmpty())
            {
                dataAuthorizedId = currentUser.DataAuthorizedId.Value;
            }

            if (dataAuthorizedId == Guid.Empty)
            {
                return queryable;
            }

            var entityParam = Expression.Parameter(typeof(TEntity));
            var dataAuthorizedProperty = Expression.PropertyOrField(entityParam, "DataAuthorizedId");

            var dataAuthorized = Expression.Equal(dataAuthorizedProperty, Expression.Constant(dataAuthorizedId, typeof(Guid?)));
            var lambda = Expression.Lambda<Func<TEntity, Boolean>>(dataAuthorized, entityParam);

            return queryable.Where(lambda);
        }

        private IQueryable<TEntity> DataAuthorizedSync<TEntity>(IQueryable<TEntity> queryable)
        {
            Guid dataAuthorizedId = Guid.Empty;
            var currentUser = _userAppService.Value.GetCurrentUser();
            if (currentUser != null && currentUser.DataAuthorizedId.IsNotNullOrEmpty())
            {
                dataAuthorizedId = currentUser.DataAuthorizedId.Value;
            }

            if (dataAuthorizedId == Guid.Empty)
            {
                return queryable;
            }

            var entityParam = Expression.Parameter(typeof(TEntity));
            var dataAuthorizedProperty = Expression.PropertyOrField(entityParam, "DataAuthorizedId");

            var dataAuthorized = Expression.Equal(dataAuthorizedProperty, Expression.Constant(dataAuthorizedId, typeof(Guid?)));
            var lambda = Expression.Lambda<Func<TEntity, Boolean>>(dataAuthorized, entityParam);

            return queryable.Where(lambda);
        }

        private async void InsertEntity(DatabaseContext writeContext, IEntity entity, Guid? auditSessionId = null)
        {
            if (entity.Id == Guid.Empty) entity.Id = Guid.NewGuid();
            var createdDateEntity = entity as ICreatedDateEnabledEntity;
            if (createdDateEntity != null && createdDateEntity.CreatedDate == DateTime.MinValue)
            {
                createdDateEntity.CreatedDate = GetCurrentTime();
            }
            if (_userAppService != null)
            {
                var currentUser = await _userAppService.Value.GetCurrentUserAsync();

                var createdByEntity = entity as ICreatedByEnabledEntity;
                if (createdByEntity != null && createdByEntity.CreatedBy == Guid.Empty)
                {
                    try
                    {
                        createdByEntity.CreatedBy = currentUser.Id;
                    }
                    catch
                    {
                        createdByEntity.CreatedBy = Guid.Parse("C799B68D-EBF6-4B6B-B6C6-5A3F36178F4A");// DefaultUserService -> AnonymousUserId
                    }
                }

                if (currentUser != null && currentUser.DataAuthorizedId.IsNotNullOrEmpty())
                {
                    var dataAuthorizedEnabledEntity = entity as IDataAuthorizedEnabledEntity;
                    if (dataAuthorizedEnabledEntity != null && dataAuthorizedEnabledEntity.DataAuthorizedId.IsNullOrEmpty())
                    {
                        try
                        {
                            dataAuthorizedEnabledEntity.DataAuthorizedId = currentUser.DataAuthorizedId.Value;
                        }
                        catch
                        {
                            dataAuthorizedEnabledEntity.DataAuthorizedId = null;
                        }
                    }
                }
            }

            writeContext.Add(entity);

            AuditEntityChange(entity, null, AuditAction.Insert, writeContext, auditSessionId);
        }

        private async void AuditEntityChange(IEntity entity, IEntity oldVersion, AuditAction action, DatabaseContext context, Guid? auditSessionId = null)
        {
            AuditEntityChangeAttribute auditAttr = (AuditEntityChangeAttribute)Attribute.GetCustomAttribute(entity.GetType(), typeof(AuditEntityChangeAttribute));
            if (auditAttr != null)
            {
                Type typeBaseField = typeof(AuditFieldChangeEntity);
                var fStogare = auditAttr.FieldStorage;
                if (!string.IsNullOrWhiteSpace(fStogare))
                {
                    IEnumerable<Type> extTye = AppDomain.CurrentDomain.GetAssemblies().SelectMany(x => x.GetTypes()).Where(x => typeBaseField.IsAssignableFrom(x) && x.Name == fStogare);
                    if (extTye.Any())
                    {
                        typeBaseField = extTye.First();
                    }
                }

                Guid auditEntityId = Guid.NewGuid();

                bool hasUpdate = false;

                #region Audit Values

                PropertyInfo[] entityProperties = entity.GetType().GetProperties();
                foreach (PropertyInfo pi in entityProperties)
                {
                    if (Attribute.IsDefined(pi, typeof(AuditFieldChangeAttribute)))
                    {
                        if (action == AuditAction.Insert)
                        {
                            string newValue = pi.GetValue(entity) == null ? string.Empty : pi.GetValue(entity).ToString();
                            if (newValue.IsNotNullOrEmpty())
                            {
                                var obj = Activator.CreateInstance(typeBaseField);
                                AuditFieldChangeEntity auditValueEntity = obj as AuditFieldChangeEntity;
                                auditValueEntity.Id = Guid.NewGuid();
                                auditValueEntity.AuditId = auditEntityId;
                                auditValueEntity.MemberName = pi.Name;
                                auditValueEntity.NewValue = newValue;

                                context.Add(obj);
                                hasUpdate = true;
                            }
                        }
                        else if (action == AuditAction.Delete)
                        {
                            var obj = Activator.CreateInstance(typeBaseField);
                            AuditFieldChangeEntity auditValueEntity = obj as AuditFieldChangeEntity;
                            auditValueEntity.Id = Guid.NewGuid();
                            auditValueEntity.AuditId = auditEntityId;
                            auditValueEntity.MemberName = pi.Name;
                            auditValueEntity.OldValue = pi.GetValue(entity) == null ? string.Empty : pi.GetValue(entity).ToString();

                            context.Add(obj);
                            hasUpdate = true;
                        }
                        else
                        {
                            string newValue = pi.GetValue(entity) == null ? string.Empty : pi.GetValue(entity).ToString();
                            string oldValue = pi.GetValue(oldVersion) == null ? string.Empty : pi.GetValue(oldVersion).ToString();

                            // Only log if new value is defference old value
                            if (newValue != oldValue)
                            {
                                var obj = Activator.CreateInstance(typeBaseField);
                                AuditFieldChangeEntity auditValueEntity = obj as AuditFieldChangeEntity;
                                auditValueEntity.Id = Guid.NewGuid();
                                auditValueEntity.AuditId = auditEntityId;
                                auditValueEntity.MemberName = pi.Name;
                                auditValueEntity.OldValue = oldValue;
                                auditValueEntity.NewValue = newValue;

                                context.Add(obj);
                                hasUpdate = true;
                            }
                        }
                    }
                }

                #endregion

                if (hasUpdate || action == AuditAction.Delete)
                {
                    #region Audit Entity

                    var typeBaseAudit = typeof(AuditEntityChangeEntity);
                    var eStogare = auditAttr.EntityStorage;
                    if (!string.IsNullOrWhiteSpace(eStogare))
                    {
                        IEnumerable<Type> extTye = AppDomain.CurrentDomain.GetAssemblies().SelectMany(x => x.GetTypes()).Where(x => typeBaseAudit.IsAssignableFrom(x) && x.Name == eStogare);
                        if (extTye.Any())
                        {
                            typeBaseAudit = extTye.First();
                        }
                    }

                    var obj = Activator.CreateInstance(typeBaseAudit);
                    AuditEntityChangeEntity auditEntity = obj as AuditEntityChangeEntity;
                    auditEntity.Id = auditEntityId;
                    TableAttribute tableAttr = (TableAttribute)Attribute.GetCustomAttribute(entity.GetType(), typeof(TableAttribute));
                    if (tableAttr != null)
                    {
                        auditEntity.TableName = tableAttr.Name;
                    }
                    auditEntity.KeyValue = entity.Id;
                    auditEntity.Action = action;
                    auditEntity.ModifiedDate = DateTime.Now;
                    auditEntity.AuditSessionId = auditSessionId;
                    if (_userAppService != null)
                    {
                        try
                        {
                            var currentUser = await _userAppService.Value.GetCurrentUserAsync();
                            auditEntity.ModifiedBy = currentUser.Id;
                        }
                        catch
                        {
                            auditEntity.ModifiedBy = Guid.Parse("C799B68D-EBF6-4B6B-B6C6-5A3F36178F4A");// DefaultUserService -> AnonymousUserId
                        }
                    }

                    if (action == AuditAction.Insert)
                    {
                        var createdByEntity = entity as ICreatedByEnabledEntity;
                        if (createdByEntity != null && createdByEntity.CreatedBy != Guid.Empty)
                        {
                            auditEntity.ModifiedBy = createdByEntity.CreatedBy;
                        }
                    }
                    else if (action == AuditAction.Update)
                    {
                        var modifiedByEntity = entity as IModifiedByEnabledEntity;
                        if (modifiedByEntity != null && modifiedByEntity.ModifiedBy.IsNotNullOrEmpty())
                        {
                            auditEntity.ModifiedBy = modifiedByEntity.ModifiedBy.Value;
                        }
                    }
                    else
                    {
                        var deletedByEntity = entity as IDeletedByEnabledEntity;
                        if (deletedByEntity != null && deletedByEntity.DeletedBy.IsNotNullOrEmpty())
                        {
                            auditEntity.ModifiedBy = deletedByEntity.DeletedBy.Value;
                        }
                    }

                    context.Add(auditEntity);

                    #endregion
                }
            }
        }

        private void SetDbCommandTransactionIfExisted(DbCommand command)
        {
            if (_localTransaction != null && _localTransaction.IsCompleted == false)
            {
                command.Transaction = _localTransaction.DbTransaction;
            }
        }

        private void CloseDbCommandConnection(DbCommand command)
        {
            if (_localTransaction == null || _localTransaction.IsCompleted)
            {
                if (command.Connection != null && command.Connection.State == ConnectionState.Open)
                {
                    command.Connection.Close();
                }
            }
        }

        private class SqlObjectId
        {
            public Int32? ObjectId { get; set; }
        }

        private bool EventTypeFilter(System.Reflection.PropertyInfo p)
        {
            if (p.Name.Equals("IsNew")) return false;
            var attribute = Attribute.GetCustomAttribute(p, typeof(AssociationAttribute)) as AssociationAttribute;

            if (attribute == null) return true;
            if (attribute.IsForeignKey == false) return true;

            return false;
        }

        internal static Expression<Func<T, Boolean>> FindByIdPredicate<T>(Guid id)
        {
            var param = Expression.Parameter(typeof(T));
            var idGetter = Expression.PropertyOrField(param, "Id");
            var compare = Expression.Equal(idGetter, Expression.Constant(id));
            var lambda = Expression.Lambda(compare, param);
            return (Expression<Func<T, Boolean>>)lambda;
        }

        private static IGenericTypeProxy CreateFinder(Type entityType, DbContext context, Guid id)
        {
            var genericType = typeof(FindEntityByKey<>).MakeGenericType(entityType);
            var dbSet = typeof(DbContext)
                .GetMethod(nameof(DbContext.Set), Type.EmptyTypes)!
                .MakeGenericMethod(entityType)
                .Invoke(context, null);

            var ctor = genericType.GetConstructor(new[] { dbSet.GetType(), typeof(Guid) });
            var instance = (IGenericTypeProxy)ctor.Invoke(new[] { dbSet, id });
            return instance;
        }


        private DateTime GetCurrentTime()
        {
            return DateTime.Now;
        }
    }

    internal class FindEntityByKey<TEntity> : IGenericTypeProxy where TEntity : class, IEntity
    {
        private readonly TEntity _result;

        public FindEntityByKey(DbSet<TEntity> queryable, Guid id)
        {
            _result = queryable.AsNoTracking().FirstOrDefault(ApplicationDbContext.FindByIdPredicate<TEntity>(id));
        }

        public object GetResult()
        {
            return _result;
        }
    }

    public class DatabaseCommandtimeout
    {
        public static int Default
        {
            get
            {
                return 30;
            }
        }

        public static int Medium
        {
            get
            {
                return 60;
            }
        }

        public static int Slow
        {
            get
            {
                return 180;
            }
        }

        public static int VerySlow
        {
            get
            {
                return 600;
            }
        }
    }
}