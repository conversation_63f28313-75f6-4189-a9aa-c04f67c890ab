﻿using AutoMapper;
using Microsoft.EntityFrameworkCore;
using System;
using System.Data;
using System.Threading.Tasks;
using Webaby;
using Webaby.Data;
using Webaby.Localization;

namespace TinyCRM.Outbound.ContactCall.Commands
{
    public class UpdateResultContactCallCommand : CommandBase
    {
        public Guid Id
        {
            get;
            set;
        }

        public Guid CallResult
        {
            get;
            set;
        }

        public Guid CallReason
        {
            get;
            set;
        }

        public Guid? ProductDiscussed
        {
            get;
            set;
        }

        public string CallStatus
        {
            get;
            set;
        }
    }

    public class UpdateResultContactCallCommandHandler : CommandHandlerBase<UpdateResultContactCallCommand>
    {
        public UpdateResultContactCallCommandHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task ExecuteAsync(UpdateResultContactCallCommand command)
        {
            var cmd = EntitySet.CreateDbCommand();
            cmd.CommandTimeout = 3600;
            cmd.CommandType = CommandType.Text;
            cmd.CommandText = "Update ContactCall Set CallResult = @CallResult, CallReason = @CallReason, ProductDiscussed = @ProductDiscussed, CallStatus = @CallStatus Where Id = @Id";

            cmd.Parameters.Add(DbParameterHelper.AddNullableGuid(cmd, "@Id", command.Id));
            cmd.Parameters.Add(DbParameterHelper.AddNullableGuid(cmd, "@CallResult", command.CallResult));
            cmd.Parameters.Add(DbParameterHelper.AddNullableGuid(cmd, "@CallReason", command.CallReason));
            cmd.Parameters.Add(DbParameterHelper.AddNullableGuid(cmd, "@ProductDiscussed", command.ProductDiscussed));
            cmd.Parameters.Add(DbParameterHelper.AddNullableString(cmd, "@CallStatus", command.CallStatus));

            await EntitySet.ExecuteNonQueryAsync(cmd);
        }
    }
}