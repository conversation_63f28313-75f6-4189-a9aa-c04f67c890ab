﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using System.Linq;
using System.Threading.Tasks;
using TinyCRM.PartCustomer.Queries;
using Webaby;
using Webaby.Data;

namespace TinyCRM.MonthlyPartFeeBatch.Queries
{
    public class GetNotProcessedMonthlyPartFeeItemByBatchQuery : QueryBase<MonthlyPartFeeBatchItemData>
    {
        public Guid MonthlyPartFeeBatchId { get; set; }

        public MonthlyPartFeeBatchItemStatus? FeeStatus { get; set; }

        public MonthlyPartFeeBatchItemStatus? DebitNoteStatus { get; set; }

        public MonthlyPartFeeBatchItemStatus? NotificationStatus { get; set; }

        public int BatchRoundNumber { get; set; }
    }

    public class GetNotProcessedMonthlyPartFeeItemByBatchQueryHandler : QueryHandlerBase<GetNotProcessedMonthlyPartFeeItemByBatchQuery, MonthlyPartFeeBatchItemData>
    {
        public GetNotProcessedMonthlyPartFeeItemByBatchQueryHandler(IServiceProvider serviceProvider) : base(serviceProvider)
        {
        }

        public override async Task<QueryResult<MonthlyPartFeeBatchItemData>> ExecuteAsync(GetNotProcessedMonthlyPartFeeItemByBatchQuery query)
        {
            var cmd = EntitySet.CreateDbCommand(CommandType.StoredProcedure, "dbo.GetNotProcessedMonthlyPartFeeItemByBatch");
            cmd.Parameters.Add(DbParameterHelper.AddNullableGuid(cmd, "@MonthlyPartFeeBatchId", query.MonthlyPartFeeBatchId));
            cmd.Parameters.Add(DbParameterHelper.AddNullableEnum(cmd, "@FeeStatus", query.FeeStatus));
            cmd.Parameters.Add(DbParameterHelper.AddNullableEnum(cmd, "@DebitNoteStatus", query.DebitNoteStatus));
            cmd.Parameters.Add(DbParameterHelper.AddNullableEnum(cmd, "@NotificationStatus", query.NotificationStatus));
            cmd.Parameters.Add(DbParameterHelper.AddNullableInt(cmd, "@BatchRoundNumber", query.BatchRoundNumber));

            var result = (await EntitySet.ExecuteReadCommandAsync<MonthlyPartFeeBatchItemData>(cmd)).ToList();
            return QueryResult.Create(result);
        }
    }
}