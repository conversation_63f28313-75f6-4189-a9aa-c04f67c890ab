﻿using Webaby.Data;
using AutoMapper;
using Webaby.Localization;
using Webaby;
using System;
using System.Data;
using System.Threading.Tasks;

namespace TinyCRM.Customer.Queries
{
    public class GetCustomerByPhoneQuery : QueryBase<CustomerData>
    {
        public GetCustomerByPhoneQuery(string phone)
        {
            Phone = phone;
        }

        public string Phone { get; private set; }
    }

    internal class GetCustomerByPhoneQueryHandler : QueryHandlerBase<GetCustomerByPhoneQuery, CustomerData>
    {
        public GetCustomerByPhoneQueryHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task<QueryResult<CustomerData>> ExecuteAsync(GetCustomerByPhoneQuery query)
        {
            var cmd = EntitySet.CreateDbCommand();
            cmd.Parameters.Add(DbParameterHelper.AddNullableString(cmd, "@phone", query.Phone));
            cmd.CommandText = "GetCustomerByPhone";
            cmd.CommandType = CommandType.StoredProcedure;
            var mainQuery = await EntitySet.ExecuteReadCommandAsync<CustomerData>(cmd);
            return new QueryResult<CustomerData>(mainQuery);
        }
    }
}
