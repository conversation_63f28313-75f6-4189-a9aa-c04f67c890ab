﻿using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Webaby;
using Webaby.Data;

namespace TinyCRM.KnowledgeBase.Queries
{
    public class GetKnowledgeItemByIdDeepQuery : QueryBase<KnowledgeItemEntity>
    {
        public GetKnowledgeItemByIdDeepQuery(Guid id)
        {
            Id = id;
        }

        public Guid Id { get; set; }

    }

    internal class GetKnowledgeItemByIdDeepQueryHandler : QueryHandlerBase<GetKnowledgeItemByIdDeepQuery, KnowledgeItemEntity>
    {
        public GetKnowledgeItemByIdDeepQueryHandler(IServiceProvider serviceProvider)
            : base(serviceProvider)
        {
        }

        public override async Task<QueryResult<KnowledgeItemEntity>> ExecuteAsync(GetKnowledgeItemByIdDeepQuery query)
        {
            var queryText = @"WITH cte AS
                            (
	                            SELECT * FROM dbo.KnowledgeItem
	                            WHERE Id=@Root
	                            UNION ALL
	                            SELECT child.* FROM dbo.KnowledgeItem child
	                            JOIN cte parent ON parent.Id = child.ParentId
                            )
                            SELECT * FROM cte";
            var cmd = EntitySet.CreateDbCommand(System.Data.CommandType.Text, queryText);
            cmd.CommandType = System.Data.CommandType.Text;
            cmd.Parameters.Add(DbParameterHelper.AddNullableGuid(cmd, "@Root", query.Id));
            var data = await EntitySet.ExecuteReadCommandAsync<KnowledgeItemEntity>(cmd);
            return QueryResult.Create(data);
        }
    }
}
