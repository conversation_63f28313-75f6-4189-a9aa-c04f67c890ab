﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using Poptech.CEP.ClientIntegration.DynamicForm;
using QRCoder;
using System.Collections.Specialized;
using System.Drawing;
using System.Drawing.Imaging;
using System.Net.Mime;
using System.Reflection;
using System.Text;
using System.Text.RegularExpressions;
using System.Web;
using TinyCRM.AppServices.DynamicForm;
using TinyCRM.AppServices.DynamicForm.Dto;
using TinyCRM.CustomerVersionName.Queries;
using TinyCRM.DynamicDefinedTable;
using TinyCRM.DynamicDefinedTable.Commands;
using TinyCRM.DynamicDefinedTable.Queries;
using TinyCRM.DynamicForm;
using TinyCRM.DynamicForm.Command;
using TinyCRM.DynamicForm.Queries;
using TinyCRM.DynamicForm.Query;
using TinyCRM.DynamicTable;
using TinyCRM.EntityLink.Queries;
using TinyCRM.InfoList.Queries;
using TinyCRM.Outbound.ExcelFormula.Queries;
using TinyCRM.Outbound.Sale.Queries;
using TinyCRM.RequestTicket.Queries;
using TinyCRM.UserAccount.Queries;
using TinyCRM.Web.Models;
using TinyCRM.Web.Models.DynamicForm;
using Webaby;
using Webaby.Caching;
using Webaby.Core.DynamicForm;
using Webaby.Core.DynamicForm.Commands;
using Webaby.Core.DynamicForm.Queries;
using Webaby.Core.File.Commands;
using Webaby.Core.File.Queries;
using Webaby.Data;
using Webaby.EntityData;
using Webaby.Excel;
using Webaby.Security.Authorize;
using Webaby.Web;


namespace TinyCRM.Web.Controllers
{
    public class DynamicFormController : WebabyControllerBase
    {
        private readonly IEntitySet _entitySet;
        private readonly ICache _cache;
        private readonly IDynamicFormUtility _dynamicFormUtility;
        private readonly IDynamicDefinedTableUtility _dynamicDefinedTableUtility;
        private readonly IDynamicFormAppService _dynamicFormAppService;
        private readonly IConfiguration _configuration;

        public DynamicFormController(IServiceProvider serviceProvider,
            IEntitySet entitySet,
            ICache cache,
            IDynamicFormUtility dynamicFormUtility,
            IDynamicDefinedTableUtility dynamicDefinedTableUtility,
            IDynamicFormAppService dynamicFormAppService,
            IConfiguration configuration) : base(serviceProvider)
        {
            _entitySet = entitySet;
            _cache = cache;
            _dynamicFormUtility = dynamicFormUtility;
            _dynamicDefinedTableUtility = dynamicDefinedTableUtility;
            _dynamicFormAppService = dynamicFormAppService;
            _configuration = configuration;
        }

        public string StaticContractFieldList => _configuration.GetValue<string>("dynamicfield.linked.entities");
        public string Mapping360FieldList => _configuration.GetValue<string>("mapping.360.field.list");
        public string TemplateNameLinkField => _configuration.GetValue<string>("template.name.link.field");
        public string FwdDynamicfieldIconFormula => _configuration.GetValue<string>("fwd.dynamicfield.icon.formula");
        public string FwdDynamicfieldColorFormula => _configuration.GetValue<string>("fwd.dynamicfield.color.formula");
        public string FwdDynamicFieldGroupIconFormula => _configuration.GetValue<string>("fwd.dynamicfield.group.icon.formula");
        public string FwdDynamicFieldGroupColorFormula => _configuration.GetValue<string>("fwd.dynamicfield.group.color.formula");
        public string FwdDynamicFieldGroupCustomStyleFormula => _configuration.GetValue<string>("fwd.dynamicfield.group.customstyle.formula");
        public string FwdDynamictableIconFormula => _configuration.GetValue<string>("fwd.dynamictable.icon.formula");
        public string FwdDynamictableColorFormula => _configuration.GetValue<string>("fwd.dynamictable.color.formula");
        public string DynamicFormCustomerVersionFilterStaticDynamicFieldName => _configuration.GetValue<string>("dynamicform.customerversion.filter.staticdynamicfieldname");
        public string BankList => _configuration.GetValue<string>("bank.list");

        private class SelectGroupItem
        {
            public string id { get; set; }
            public string text { get; set; }
            public List<ChildItem> children { get; set; }
        }
        private class ChildItem
        {
            public string id { get; set; }
            public string text { get; set; }
            [JsonIgnore]
            public string parentid { get; set; }
        }

        private class TableForSelectGroup
        {
            public string value { get; set; }
            public string text { get; set; }
            public string groupname { get; set; }
        }

        private Task<Dictionary<string, string>> _typeChoicesTask;
        private Task<Dictionary<string, string>> TypeChoices
            => _typeChoicesTask ?? GetTypeChoicesAsync();

        private async Task<Dictionary<string, string>> GetTypeChoicesAsync()
        {
            Dictionary<string, string> results = new Dictionary<string, string>
                {
                    { "System.String", "Text" },
                    { "System.Collections.Generic.List`1[System.String]", "Text List" },
                    { "System.Nullable`1[System.Int32]", "Integer" },
                    { "System.Nullable`1[System.Int64]", "Big Integer" },
                    { "System.Nullable`1[System.Double]", "Double" },
                    { "System.Nullable`1[System.Boolean]", "Boolean" },
                    { "System.Nullable`1[System.DateTime]", "DateTime" },
                    { "System.Nullable`1[System.Guid]", "Guid" },
                    { "System.Collections.Generic.List`1[System.Nullable`1[System.Guid]]", "Guid List" },
                    { "System.Nullable`1[TinyCRM.Enums.Difficulty]", "Difficulty" },
                    { "Webaby.Core.File.Queries.FileData", "File" },
                    { "System.Collections.Generic.List`1[Webaby.Core.File.Queries.FileData]", "Multi-file" },
                    { "Webaby.Core.Organization.Queries.OrganizationData", "Organization" },
                    { "TinyCRM.DynamicForm.Queries.EditorControlDynamicFormData", "DynamicForm" },
                    { "TinyCRM.RequestTicket.Queries.RequestTicketData", "RequestTicket" },
                    { "TinyCRM.UserAccount.UserProfileEntityData", "UserProfile" },
                    //{ "Webaby.Core.PredefinedList.Queries.PredefinedListItemData", "Predefined List" },
                    //{ "TinyCRM.DetailPayment.Queries.DetailPaymentListItemData", "Detail Payment List" },
                    //{ "TinyCRM.FlightRoute.Queries.FlightRouteListItemData", "Flight Route List" },
                    //{ "TinyCRM.TripRoute.Queries.TripRouteListItemData", "Trip Route List" },
                    //{ "TinyCRM.Maintenance.Queries.MaintenanceListItemData", "Maintenance List" },
                    //{ "TinyCRM.EastSpring.DetailTripBudget.Queries.DetailTripBudgetListItemData", "Detail Trip Budget List" },
                    //{ "TinyCRM.GridDynamicField.DynamicFieldKnowledgeGrid.Queries.DynamicFieldKnowledgeGridData", "KnowledgeBase List" },
                    //{ "TinyCRM.GridDynamicField.DynamicFieldFinishStepConentGrid.Queries.DynamicFieldDynamicFieldFinishStepConentGridData", "Danh sách Công việc" },
                    //{ "TinyCRM.GridDynamicField.DynamicFieldVerifierListItem.Queries.DynamicFieldVerifierGridData", "Danh sách thành viên" },
                    //{ "TinyCRM.GridDynamicField.DynamicFieldVerifyResultGrid.Queries.DynamicFieldVerifyResultGridData", "Kết quả thẩm định" }
                };

            var type = typeof(IGridData);
            var assemblies = AppDomain.CurrentDomain.GetAssemblies().Where(a => a.FullName.Contains("Webaby", StringComparison.OrdinalIgnoreCase) || a.FullName.Contains("TinyCRM", StringComparison.OrdinalIgnoreCase)).ToList();
            foreach (Assembly asm in assemblies)
            {
                foreach (Type gridDataType in asm.GetTypes())
                {
                    if (type.IsAssignableFrom(gridDataType))
                    {
                        EntityDataEditorAttribute entityDataEditorAttribute = (EntityDataEditorAttribute)Attribute.GetCustomAttribute(gridDataType, typeof(EntityDataEditorAttribute));
                        if (entityDataEditorAttribute != null && entityDataEditorAttribute.ImportKey.IsNotNullOrEmpty())
                        {
                            string entityDataEditorSettingJson = Configuration.GetValue<string>(entityDataEditorAttribute.ImportKey);
                            if (entityDataEditorSettingJson.IsNotNullOrEmpty())
                            {
                                EntityDataEditorSetting entityDataEditorSetting = JsonConvert.DeserializeObject<EntityDataEditorSetting>(entityDataEditorSettingJson);
                                if (entityDataEditorSetting != null && entityDataEditorSetting.EditorTemplates != null && entityDataEditorSetting.EditorTemplates.Count > 0)
                                {
                                    results.Add(gridDataType.FullName, entityDataEditorSetting.DisplayName);
                                }
                            }
                        }
                    }
                }
            }

            var userDefinedTableList = await QueryExecutor.ExecuteManyAsync(new GetDynamicDefinedTableSchemaQuery { });
            if (userDefinedTableList.Any())
            {
                results.Add("TinyCRM.AppServices.DynamicDefinedTable.DynamicDefinedTableGridData", T["Danh sách tự định nghĩa"]);
            }

            return results;
        }

        private Task<Dictionary<string, List<Tuple<string, string>>>> TypeInputChoices
            => _cachedTypeChoices ??= GetTypeInputChoices();    // cache 1 lần

        private Task<Dictionary<string, List<Tuple<string, string>>>> _cachedTypeChoices;

        private async Task<Dictionary<string, List<Tuple<string, string>>>> GetTypeInputChoices()
        {
            var typeInputChoices = new Dictionary<string, List<Tuple<string, string>>>
            {
                { "System.String", new List<Tuple<string, string>> { new Tuple<string, string>("String", "Textbox"), new Tuple<string, string>("MultilineText", "Textarea"), new Tuple<string, string>("RichMultilineText", "Rich Textarea"), new Tuple<string, string>("SelectField", "Select"), new Tuple<string, string>("Select_v5", "Select_v5"), new Tuple<string, string>("CheckInLocation", "CheckInLocation"), new Tuple<string, string>("LinkTicket", "LinkTicket"), new Tuple<string, string>("Group_Select_v5", "Group_Select_v5"), new Tuple<string, string>("LinkHref-v5", "LinkHref-v5"), new Tuple<string, string>("CalculatedxQRCode", "Calculated x QRCode"), new Tuple<string, string>("ReferenceTicket", "Reference Ticket") , new Tuple<string, string>("ReferenceTicketGetValueOnTable", "Reference Ticket Get Value On Table") } },
                { "System.Collections.Generic.List`1[System.String]", new List<Tuple<string, string>> { new Tuple<string, string>("MultiSelectField", "Multi Select"), new Tuple<string, string>("MultiSelect_v5", "Multi Select_v5") } },
                { "System.Nullable`1[System.Int32]", new List<Tuple<string, string>> { new Tuple<string, string>("Int32-v5", "Integer"), new Tuple<string, string>("Select_v5", "Select (From Load URL)"), new Tuple<string, string>("SelectField", "Select (From Defined Options)"), new Tuple<string, string>("ReferenceTicketGetValueOnTable", "Reference Ticket Get Value On Table") } },
                { "System.Nullable`1[System.Int64]", new List<Tuple<string, string>> { new Tuple<string, string>("Int64-v5", "Big Integer"), new Tuple<string, string>("ReferenceTicketGetValueOnTable", "Reference Ticket Get Value On Table"), new Tuple<string, string>("CalculatedRequestTicket", "Calculate RequestTicket") } },
                { "System.Nullable`1[System.Double]", new List<Tuple<string, string>> { new Tuple<string, string>("Double-v5", "Double"), new Tuple<string, string>("ReferenceTicketGetValueOnTable", "Reference Ticket Get Value On Table") } },
                { "System.Nullable`1[System.Boolean]", new List<Tuple<string, string>> { new Tuple<string, string>("Boolean-v5", "Checkbox"), new Tuple<string, string>("BooleanSwitch_v5", "Switch")} },
                { "System.Nullable`1[System.DateTime]", new List<Tuple<string, string>> { new Tuple<string, string>("DateTime-v5", "Date"), new Tuple<string, string>("Time_v5", "Time"), new Tuple<string, string>("DateTimeFull_v5", "Date & Time") } },
                { "System.Nullable`1[System.Guid]", new List<Tuple<string, string>> { new Tuple<string, string>("Select_v5", "Select") }},
                { "System.Collections.Generic.List`1[System.Nullable`1[System.Guid]]", new List<Tuple<string, string>> { new Tuple<string, string>("MultiSelect_v5", "Multi-Select") }},
                { "System.Nullable`1[TinyCRM.Enums.Difficulty]", new List<Tuple<string, string>> { new Tuple<string, string>("Enum_v5", "Enum"), new Tuple<string, string>("MultiEnum_v5", "Multi-enum") } },
                { "Webaby.Core.File.Queries.FileData", new List<Tuple<string, string>> { new Tuple<string, string>("File_v5", "File"), new Tuple<string, string>("LinkedFile_v5", "Linked-File") } },
                { "System.Collections.Generic.List`1[Webaby.Core.File.Queries.FileData]", new List<Tuple<string, string>> { new Tuple<string, string>("MultipleFile_v5", "Multi-file"), new Tuple<string, string>("LinkedFileList_v5", "Multi Linked-File") } },
                { "Webaby.Core.Organization.Queries.OrganizationData", new List<Tuple<string, string>> { new Tuple<string, string>("SelectTreeEntity-v5", "SelectTree") } },
                { "TinyCRM.DynamicForm.Queries.EditorControlDynamicFormData", new List<Tuple<string, string>> { new Tuple<string, string>("SelectTreeEntity-v5", "SelectTree") } },
                { "TinyCRM.RequestTicket.Queries.RequestTicketData", new List<Tuple<string, string>> { new Tuple<string, string>("Linked-Ticket", "Linked-Ticket") } },
                { "TinyCRM.UserAccount.UserProfileEntityData", new List<Tuple<string, string>> { new Tuple<string, string>("UserProfile", "UserProfile") } },
                //{ "Webaby.Core.PredefinedList.Queries.PredefinedListItemData", new List<Tuple<string, string>> { new Tuple<string, string>("PredefinedList", "List"), new Tuple<string, string>("PredefinedFullList", "Checklist"), new Tuple<string, string>("PredefinedFullList_Repair", "Checklist(include repair)") } },
                //{ "TinyCRM.DetailPayment.Queries.DetailPaymentListItemData", new List<Tuple<string, string>> { new Tuple<string, string>("DetailPaymentList", "List") } },
                //{ "TinyCRM.FlightRoute.Queries.FlightRouteListItemData", new List<Tuple<string, string>> { new Tuple<string, string>("FlightRouteList", "List") } },
                //{ "TinyCRM.TripRoute.Queries.TripRouteListItemData", new List<Tuple<string, string>> { new Tuple<string, string>("TripRouteList", "List") } },
                //{ "TinyCRM.Maintenance.Queries.MaintenanceListItemData", new List<Tuple<string, string>> { new Tuple<string, string>("MaintenanceCheckList", "Checklist"), new Tuple<string, string>("MaintenanceCheckListRepair", "Checklist(include repair)") } },
                //{ "TinyCRM.EastSpring.DetailTripBudget.Queries.DetailTripBudgetListItemData", new List<Tuple<string, string>> { new Tuple<string, string>("DetailTripBudgetList", "List") } },
                //{ "TinyCRM.GridDynamicField.DynamicFieldKnowledgeGrid.Queries.DynamicFieldKnowledgeGridData", new List<Tuple<string, string>> { new Tuple<string, string>("KnowledgeItemList", "List") } },
                //{ "TinyCRM.GridDynamicField.DynamicFieldFinishStepConentGrid.Queries.DynamicFieldDynamicFieldFinishStepConentGridData", new List<Tuple<string, string>> { new Tuple<string, string>("FinishStepConentList", "Công việc đã thực hiện"), new Tuple<string, string>("NotAppliedJobList", "Công việc Không áp dụng"), new Tuple<string, string>("PlannedJobList", "Công việc thuộc kế hoạch"), new Tuple<string, string>("NotEnoughConditionJobList", "Công việc chưa đủ điều kiện"), new Tuple<string, string>("JobListTotalBudget", "Tổng giá trị các phần công việc") } },
                //{ "TinyCRM.GridDynamicField.DynamicFieldVerifierListItem.Queries.DynamicFieldVerifierGridData", new List<Tuple<string, string>> { new Tuple<string, string>("VerifierList", "Tổ thẩm định") } },
                //{ "TinyCRM.GridDynamicField.DynamicFieldVerifyResultGrid.Queries.DynamicFieldVerifyResultGridData", new List<Tuple<string, string>> { new Tuple<string, string>("VerifyResultList", "Kết quả thẩm định") } }
            };

            var type = typeof(IGridData);
            var assemblies = AppDomain.CurrentDomain.GetAssemblies().Where(a => a.FullName.Contains("Webaby", StringComparison.OrdinalIgnoreCase) || a.FullName.Contains("TinyCRM", StringComparison.OrdinalIgnoreCase)).ToList();
            foreach (Assembly asm in assemblies)
            {
                foreach (Type gridDataType in asm.GetTypes())
                {
                    if (type.IsAssignableFrom(gridDataType))
                    {
                        EntityDataEditorAttribute entityDataEditorAttribute = (EntityDataEditorAttribute)Attribute.GetCustomAttribute(gridDataType, typeof(EntityDataEditorAttribute));
                        if (entityDataEditorAttribute != null && entityDataEditorAttribute.ImportKey.IsNotNullOrEmpty())
                        {
                            string entityDataEditorSettingJson = Configuration.GetValue<string>(entityDataEditorAttribute.ImportKey);
                            if (entityDataEditorSettingJson.IsNotNullOrEmpty())
                            {
                                EntityDataEditorSetting entityDataEditorSetting = JsonConvert.DeserializeObject<EntityDataEditorSetting>(entityDataEditorSettingJson);
                                if (entityDataEditorSetting != null && entityDataEditorSetting.EditorTemplates != null && entityDataEditorSetting.EditorTemplates.Count > 0)
                                {
                                    List<Tuple<string, string>> tuples = new List<Tuple<string, string>>();
                                    foreach (var editorTemplate in entityDataEditorSetting.EditorTemplates)
                                    {
                                        tuples.Add(new Tuple<string, string>(editorTemplate.EditorTemplate, editorTemplate.Name));
                                    }
                                    typeInputChoices.Add(gridDataType.FullName, tuples);
                                }
                            }
                        }
                    }
                }
            }

            var userDefinedTableList = await QueryExecutor.ExecuteManyAsync(new GetDynamicDefinedTableSchemaQuery { });
            if (userDefinedTableList.Count() > 0)
            {
                typeInputChoices.Add("TinyCRM.AppServices.DynamicDefinedTable.DynamicDefinedTableGridData", new List<Tuple<string, string>> { new Tuple<string, string>("DynamicDefinedTable", "Mặc định"), new Tuple<string, string>("DynamicDefinedTablePivotColumns", "Pivot cột"), new Tuple<string, string>("DynamicDefinedTableReverseColumns2Rows", "Đảo cột thành dòng") });
            }

            return typeInputChoices;
        }
        private Dictionary<string, string> OperatorType
        {
            get
            {
                Dictionary<string, string> results = new Dictionary<string, string>
                {
                    { "nt", "↑" },
                    { ">=", ">=" },
                    { ">", ">" },
                    { "<=", "<=" },
                    { "<", "<" },
                    { "=", "=" },
                    { "<>", "<>" }
                };

                return results;
            }
        }
        [HttpGet]
        public IActionResult Index()
        {
            var model = new DynamicFormSearchModel();
            return View(model);
        }

        [HttpPost]
        public async Task<IActionResult> GetDynamicFormList(DynamicFormSearchModel model)
        {
            if (ModelState.IsValid)
            {
                if (model.PageSize <= 0) model.PageSize = 25;
                var result = await QueryExecutor.ExecuteAsync(new GetDynamicFormQuery
                {
                    Name = model.Name,
                    Pagination = new Pagination(model.PageIndex, model.PageSize)
                });

                return View("Partials/DynamicFormList", new DynamicFormListModel
                {
                    Total = result.Total,
                    Items = result.Many,
                    PageIndex = model.PageIndex,
                    PageSize = model.PageSize
                });
            }
            return DefaultResult();
        }

        [HttpGet]
        public async Task<IActionResult> CreateEdit(Guid? formId)
        {
            var businessSettings = StaticContractFieldList;
            DynamicFormModel model = new DynamicFormModel();
            var listStaticFieldsInSettings = string.IsNullOrEmpty(businessSettings) ? new List<StaticFieldGroup>()
                : JsonConvert.DeserializeObject<List<StaticFieldGroup>>(businessSettings);

            if (formId.HasValue)
            {
                var dynamicForm = await QueryExecutor.ExecuteOneAsync(new GetDynamicFormByIdQuery { Id = formId.Value });
                if (dynamicForm != null)
                {
                    model.Id = dynamicForm.Id;
                    model.Name = dynamicForm.Name;
                    model.NumerOfColumn = dynamicForm.NumberOfColumn;
                    model.Code = dynamicForm.Code;
                    model.FormStyle = dynamicForm.FormStyle;
                    model.FormOverwrite = dynamicForm.FormOverwrite;
                    model.TurnOffWarning = dynamicForm.TurnOffWarning;
                    model.SavedIntegratedHandler = dynamicForm.SavedIntegratedHandler;
                    model.DynamicFormValueCount = (await QueryExecutor.ExecuteAsync(new GetDynamicFormValueByFormIdQuery { FormId = dynamicForm.Id })).Total;
                    model.IsPrivate = dynamicForm.IsPrivate;
                    var dynamicFields = await QueryExecutor.ExecuteManyAsync(new TinyCRM.DynamicForm.Queries.GetDynamicFieldByFormIdQuery { FormId = formId.Value });

                    var dynamicFieldsList = dynamicFields.ToList();
                    for (int i = 0; i < dynamicFieldsList.Count; i++)
                    {
                        dynamicFieldsList[i].Order = i + 1;
                    }

                    if (dynamicForm.DisplayDocumentFileId.HasValue)
                    {
                        model.DisplayDocument = new FileData { Id = dynamicForm.DisplayDocumentFileId.Value };
                        var displayDocumentVariables = await QueryExecutor.ExecuteManyAsync(new GetAllVariableOfSaleTemplateQuery { FileId = dynamicForm.DisplayDocumentFileId.Value });
                        model.DisplayDocumentVariable = displayDocumentVariables.Where(x => dynamicFieldsList.All(y => y.Name != x));
                    }
                    if (dynamicForm.ExcelDataFileId.HasValue)
                    {
                        model.ExcelData = new FileData { Id = dynamicForm.ExcelDataFileId.Value };
                    }
                    ViewBag.StaticFieldGroups = listStaticFieldsInSettings.Select(x => new StaticFieldGroup
                    {
                        Name = x.Name,
                        StaticFieldList =
                            x.StaticFieldList.Where(
                                y =>
                                    !dynamicFieldsList.Any(
                                        z =>
                                            z.SourceFieldName == y.SourceFieldName && z.SourceTableName == y.SourceTableName)).ToList(),
                    }).ToList();
                    Func<string, string> getFieldDescriptions = type =>
                    {
                        switch (type)
                        {
                            case "TinyCRM.GridDynamicField.DynamicFieldFinishStepConentGrid.Queries.DynamicFieldDynamicFieldFinishStepConentGridData":
                                return "ApprovedDocumentFileName => " + T.Scope("DynamicFieldTable")["Văn bản phê duyệt"] + "\n" +
                                "Title => " + T.Scope("DynamicFieldTable")["Nội dung/Tên gói thầu"] + "\n" +
                                "Organization => " + T.Scope("DynamicFieldTable")["Đơn vị xử lý"] + "\n" +
                                "Budget => " + T.Scope("DynamicFieldTable")["Giá trị"] + "\n" +
                                "SourceOfBudget => " + T.Scope("DynamicFieldTable")["Nguồn vốn"] + "\n" +
                                "ChooseFormality => " + T.Scope("DynamicFieldTable")["Hình thức lựa chọn"] + "\n" +
                                "ChooseMethod => " + T.Scope("DynamicFieldTable")["Phương thức lựa chọn"] + "\n" +
                                "ChooseStartTime => " + T.Scope("DynamicFieldTable")["Thời gian bắt đầu"] + "\n" +
                                "ContractType => " + T.Scope("DynamicFieldTable")["Loại hợp đồng"] + "\n" +
                                "ContractDuration => " + T.Scope("DynamicFieldTable")["Thời gian thực hiện"] + "\n" +
                                "";
                            case "System.Collections.Generic.List`1[Webaby.Core.File.Queries.FileData]":
                                return "FileName";
                            case "TinyCRM.GridDynamicField.DynamicFieldKnowledgeGrid.Queries.DynamicFieldKnowledgeGridData":
                                return "Name";
                            case "Webaby.Core.PredefinedList.Queries.PredefinedListItemData":
                                return "CheckResult, YES, NO";
                            case "TinyCRM.GridDynamicField.DynamicFieldVerifierListItem.Queries.DynamicFieldVerifierGridData":
                                return "FullName, JobTitle";
                            case "TinyCRM.GridDynamicField.DynamicFieldVerifyResultGrid.Queries.DynamicFieldVerifyResultGridData":
                                return "Title, Result, YES, NO";
                            default: return "";
                        }
                    };
                    model.Fields = dynamicFieldsList.Select(x => new DynamicFieldModel
                    {
                        Name = x.Name,
                        Descriptions = getFieldDescriptions.Invoke(x.DataType),
                        ViewHint = x.ViewHint,
                        Order = x.Order,
                        SourceFieldName = x.SourceFieldName,
                        DataType = x.DataType,
                        DynamicDefinedTableSchemaId = x.DynamicDefinedTableSchemaId,
                        DynamicDefinedTableSchema = x.DynamicDefinedTableSchema,
                        AdditionalFilter = x.AdditionalFilter,
                        DisplayName = x.DisplayName,
                        SourceTableName = x.SourceTableName,
                        Display = x.Display,
                        DefaultValue = x.DefaultValue,
                        FieldType = x.FieldType,
                        Id = x.Id,
                        IsReadOnly = x.IsReadOnly,
                        IsRequired = x.IsRequired,
                        Validations = x.Validation.IsNotNullOrEmpty() ?
                            JsonConvert.DeserializeObject<IEnumerable<DynamicValidationModel>>(x.Validation)
                            : new List<DynamicValidationModel>(),
                        BusinessValidations = x.BusinessValidation.IsNotNullOrEmpty() ?
                            JsonConvert.DeserializeObject<IEnumerable<DynamicValidationModel>>(x.BusinessValidation)
                            : new List<DynamicValidationModel>(),
                        AdditionalMetadatas = x.AdditionalMetadata.IsNotNullOrEmpty() ?
                            JsonConvert.DeserializeObject<IEnumerable<DynamicAdditionalMetadataModel>>(x.AdditionalMetadata)
                            : new List<DynamicAdditionalMetadataModel>(),
                        SelectOptions = x.SelectOptions,
                        DynamicFieldSectionId = x.DynamicFieldSectionId,
                        DynamicFieldSectionName = x.DynamicFieldSectionName,
                        FreezeValue = x.FreezeValue,
                        IsExportExcel = x.IsExportExcel,
                        IsExportByConditionBoolean = x.IsExportByConditionBoolean,
                        VersionCode = x.VersionCode,
                        FieldCondition = x.FieldCondition,
                        PaymentType = x.PaymentType,
                        PaymentTypeString = x.PaymentType != null ? QueryExecutor.ExecuteManyAsync(new GetInfoListByTypeQuery { TypeName = "fwd_paymenttype" }).Result.Where(p => p.Id == x.PaymentType).Select(p => p.Name).FirstOrDefault() : ""
                    }).ToList().OrderBy(x => x.Order);
                    model.LinkBusinessSpecific = await QueryExecutor.ExecuteOneAsync(new GetEntityLinkBusinessSpecificListQuery
                    {
                        EntityType = "DynamicForm",
                        ObjectData = null
                    });
                    model.EntityLinkList = (await QueryExecutor.ExecuteManyAsync(new GetEntityLinkListQuery { EntityId = dynamicForm.Id })).ToList();

                }
            }
            else
            {
                model.NumerOfColumn = 1;
                model.FormStyle = FormStyle.Normal;
                ViewBag.StaticFieldGroups = listStaticFieldsInSettings;
            }

            model.DynamicFormBusinessActionList = new List<DynamicFormBusinessAction>();
            // Load business action
            var dynamicFormBusinessActionHandlers = ServiceProvider.GetRequiredService<IEnumerable<IDynamicFormBusinessActionHandler>>().GroupBy(hdl => hdl.GetType().FullName).Select(grp => grp.First()).ToList();
            foreach (var dynamicFormBusinessActionHandler in dynamicFormBusinessActionHandlers)
            {
                DynamicFormBusinessAction dynamicFormBusinessAction = new DynamicFormBusinessAction();
                dynamicFormBusinessAction.Name = dynamicFormBusinessActionHandler.GetType().Name;

                DynamicFormBusinessActionAttribute dynamicFormBusinessActionAttribute = (DynamicFormBusinessActionAttribute)Attribute.GetCustomAttribute(dynamicFormBusinessActionHandler.GetType(), typeof(DynamicFormBusinessActionAttribute));
                if (dynamicFormBusinessActionAttribute != null)
                {
                    if (dynamicFormBusinessActionAttribute.Label.IsNotNullOrEmpty())
                    {
                        dynamicFormBusinessAction.Label = dynamicFormBusinessActionAttribute.Label;
                    }
                    dynamicFormBusinessAction.DisplayOrder = dynamicFormBusinessActionAttribute.DisplayOrder;
                    if (dynamicFormBusinessActionAttribute.CssClassConfig.IsNotNullOrEmpty())
                    {
                        dynamicFormBusinessAction.CssClassConfig = dynamicFormBusinessActionAttribute.CssClassConfig;
                    }
                    if (dynamicFormBusinessActionAttribute.CustomStyleConfig.IsNotNullOrEmpty())
                    {
                        dynamicFormBusinessAction.CustomStyleConfig = dynamicFormBusinessActionAttribute.CustomStyleConfig;
                    }
                    if (dynamicFormBusinessActionAttribute.IconClass.IsNotNullOrEmpty())
                    {
                        dynamicFormBusinessAction.IconClass = dynamicFormBusinessActionAttribute.IconClass;
                    }
                }

                model.DynamicFormBusinessActionList.Add(dynamicFormBusinessAction);
            }



            return View(model);
        }

        [AllowAuthenticated]
        [HttpPost]
        public async Task<IActionResult> CreateModalQuickField(Guid formId, bool isversion)
        {
            var businessSettings = StaticContractFieldList;
            var dynamicForm = await QueryExecutor.ExecuteOneAsync(new GetDynamicFormByIdQuery { Id = formId });
            DynamicFormModel model = new DynamicFormModel();
            var listStaticFieldsInSettings = string.IsNullOrEmpty(businessSettings) ? new List<StaticFieldGroup>()
                : JsonConvert.DeserializeObject<List<StaticFieldGroup>>(businessSettings);
            if (dynamicForm != null)
            {
                var dynamicFields = await QueryExecutor.ExecuteManyAsync(new TinyCRM.DynamicForm.Queries.GetDynamicFieldByFormIdQuery { FormId = formId });
                if (dynamicForm.DisplayDocumentFileId.HasValue)
                {
                    model.DisplayDocument = new FileData { Id = dynamicForm.DisplayDocumentFileId.Value };
                    var displayDocumentVariables = await QueryExecutor.ExecuteManyAsync(new GetAllVariableOfSaleTemplateQuery { FileId = dynamicForm.DisplayDocumentFileId.Value });
                    model.DisplayDocumentVariable = displayDocumentVariables.Where(x => dynamicFields.All(y => y.Name != x));
                }
                ViewBag.StaticFieldGroups = listStaticFieldsInSettings.Select(x => new StaticFieldGroup
                {
                    Name = x.Name,
                    StaticFieldList =
                            x.StaticFieldList.Where(
                                y =>
                                    !dynamicFields.Any(
                                        z =>
                                            z.SourceFieldName == y.SourceFieldName && z.SourceTableName == y.SourceTableName)).ToList(),
                }).ToList();
                model.Id = formId;
            }
            ViewBag.IsVersion = isversion;
            return View("Partials/CreateQuickfield", model);
        }

        [HttpPost]
        public async Task<IActionResult> CreateEdit(DynamicFormModel model)
        {
            if (ModelState.IsValid)
            {
                if (!model.Id.HasValue)
                {
                    model.Id = Guid.NewGuid();
                    model.IsNew = true;
                }
                else
                {
                    model.IsNew = false;
                }

                int i = 0;
                var rootName = model.Name;
                while (_dynamicFormUtility.CheckDuplicateNameDynamicForm(model.Name, model.Id.Value))
                {
                    i++;
                    model.Name = string.Format("{0}_Clone{1}", rootName, i);
                }

                var rootCode = model.Code;
                while (_dynamicFormUtility.CheckDuplicateCodeDynamicForm(model.Code, model.Id.Value))
                {
                    i++;
                    model.Code = string.Format("{0}_{1}", rootCode, i);
                }

                if (!string.IsNullOrEmpty(model.Name))
                {
                    Guid? docFileId = model.DisplayDocument == null ? new Guid?() : model.DisplayDocument.Id;
                    if (model.DisplayDocumentUpload != null)
                    {
                        docFileId = Guid.NewGuid();
                        using (var memoryStream = new MemoryStream())
                        {
                            await model.DisplayDocumentUpload.CopyToAsync(memoryStream);
                            byte[] buffer = memoryStream.ToArray();
                            if (buffer.Length > 0)
                            {
                                await CommandExecutor.ExecuteAsync(new CreateEditFileCommand
                                {
                                    Id = docFileId.Value,
                                    Data = buffer,
                                    Descriptions = "form document",
                                    ReferenceObjectId = null,
                                    ReferenceObjectType = null,
                                    FileName = model.DisplayDocumentUpload.FileName
                                });
                            }
                        }
                    }

                    Guid? excelFileId = model.ExcelData == null ? new Guid?() : model.ExcelData.Id;
                    if (model.ExcelDataUpload != null)
                    {
                        excelFileId = Guid.NewGuid();
                        using (var memoryStream = new MemoryStream())
                        {
                            await model.ExcelDataUpload.CopyToAsync(memoryStream);
                            byte[] buffer = memoryStream.ToArray();
                            if (buffer.Length > 0)
                            {
                                await CommandExecutor.ExecuteAsync(new CreateEditFileCommand
                                {
                                    Id = excelFileId.Value,
                                    Data = buffer,
                                    Descriptions = "form document (data lookup)",
                                    ReferenceObjectId = null,
                                    ReferenceObjectType = null,
                                    FileName = model.ExcelDataUpload.FileName
                                });
                            }
                        }
                    }

                    var dynamicFormCommand = new CreateEditDynamicFormCommand
                    {
                        Id = model.Id.Value,
                        Name = model.Name,
                        Code = model.Code,
                        Formula = model.Formula,
                        IsNew = model.IsNew,
                        DisplayDocumentFileId = docFileId,
                        ExcelDataFileId = excelFileId,
                        FormStyle = model.FormStyle,
                        NumberOfColumn = model.NumerOfColumn,
                        FormOverwrite = model.FormOverwrite,
                        TurnOffWarning = model.TurnOffWarning,
                        SavedIntegratedHandler = model.SavedIntegratedHandler,
                        IsPrivate = model.IsPrivate
                    };

                    await CommandExecutor.ExecuteAsync(dynamicFormCommand);
                    Redirect(Url.Action("CreateEdit", "DynamicForm", new { formId = model.Id }));                    
                }
            }
            return DefaultResult();
        }

        [HttpGet]
        [NoCache]
        [AllowAuthenticated]
        public IActionResult LoadDynamicFormSavedHandlerDropdownList()
        {
            List<SelectListItem> selectListItems = new List<SelectListItem>();

            var dynamicFormSavedHandlers = ServiceProvider.GetRequiredService<IEnumerable<IDynamicFormSavedHandler>>().GroupBy(hdl => hdl.GetType().FullName).Select(grp => grp.First()).ToList();
            foreach (var dynamicFormSavedHandler in dynamicFormSavedHandlers)
            {
                SelectListItem selectListItem = new SelectListItem();
                selectListItem.Value = dynamicFormSavedHandler.GetType().Name;
                selectListItem.Text = dynamicFormSavedHandler.GetType().Name;

                DynamicFormActionAttribute dynamicFormActionAttribute = (DynamicFormActionAttribute)Attribute.GetCustomAttribute(dynamicFormSavedHandler.GetType(), typeof(DynamicFormActionAttribute));
                if (dynamicFormActionAttribute != null)
                {
                    if (dynamicFormActionAttribute.Name.IsNotNullOrEmpty())
                    {
                        selectListItem.Value = dynamicFormActionAttribute.Name;
                    }
                    if (dynamicFormActionAttribute.Description.IsNotNullOrEmpty())
                    {
                        selectListItem.Text = dynamicFormActionAttribute.Description;
                    }
                }

                selectListItems.Add(selectListItem);
            }

            return Json(selectListItems);
        }

        [HttpPost]
        [AllowAuthenticated]
        public IActionResult ExecuteDynamicFormBusinessAction(Guid dynamicFormId, string dynamicFormBusinessActionName)
        {
            string actionLabel = dynamicFormBusinessActionName;

            var dynamicFormBusinessActionHandlers = ServiceProvider.GetRequiredService<IEnumerable<IDynamicFormBusinessActionHandler>>().GroupBy(hdl => hdl.GetType().FullName).Select(grp => grp.First()).ToList();
            foreach (var dynamicFormBusinessActionHandler in dynamicFormBusinessActionHandlers)
            {
                if (dynamicFormBusinessActionName.IsEqualIgnoreCase(dynamicFormBusinessActionHandler.GetType().Name))
                {
                    DynamicFormBusinessActionAttribute dynamicFormBusinessActionAttribute = (DynamicFormBusinessActionAttribute)Attribute.GetCustomAttribute(dynamicFormBusinessActionHandler.GetType(), typeof(DynamicFormBusinessActionAttribute));
                    if (dynamicFormBusinessActionAttribute != null)
                    {
                        if (dynamicFormBusinessActionAttribute.Label.IsNotNullOrEmpty())
                        {
                            actionLabel = dynamicFormBusinessActionAttribute.Label;
                        }
                    }

                    var result = dynamicFormBusinessActionHandler.Handle(dynamicFormId);
                    if (result.InfoMessages.IsNotNullOrEmpty())
                    {
                        Info(result.InfoMessages);
                    }
                    if (result.ErrorMessages.IsNotNullOrEmpty())
                    {
                        Error(result.ErrorMessages);
                    }
                    if (result.ReturnUrl.IsNotNullOrEmpty())
                    {
                        Redirect(Url.Content(result.ReturnUrl));
                    }

                    if (result.FileResult != null)
                    {
                        if (result.FileResult.Bytes != null)
                        {
                            Response.Cookies.Append("DownloadFileExcell", "success");
                            return File(result.FileResult.Bytes, MediaTypeNames.Application.Octet, result.FileResult.FileName);
                        }
                    }
                    return DefaultResult(result);
                }
            }

            Error(T["Không tìm thấy handler cho \"{0}\"", actionLabel]);
            return DefaultResult();
        }

        [HttpPost]
        [AllowAuthenticated]
        public async Task<IActionResult> CloneDynamicForm(Guid dynamicFormId, Guid newId)
        {
            await CommandExecutor.ExecuteAsync(new CloneDynamicFormCommand
            {
                DynamicFormId = dynamicFormId,
                NewDynamicFormId = newId
            });
            return Json(new { Success = true });
        }

        [AllowAuthenticated]
        [HttpGet]
        public async Task<IActionResult> GetDynamicFieldSectionList(Guid dynamicFormId, bool? isShow, Guid? sectionId)
        {
            var dynamicFieldSectionList = (await QueryExecutor.ExecuteManyAsync(new GetDynamicFieldSectionByFormIdQuery { DynamicFormId = dynamicFormId })).ToList();
            if (isShow.HasValue && isShow.Value == true)
            {
                ViewBag.ShowBtnEdit = true;
                ViewBag.SectionId = sectionId.Value;
            }
            return View("Partials/DynamicFieldSectionList", dynamicFieldSectionList);
        }

        [AllowAuthenticated]
        [HttpGet]
        public async Task<IActionResult> CreateEditDynamicFieldSection(Guid? dynamicFieldSectionId, Guid? dynamicFormId, int? viewMode)
        {
            DynamicFieldSectionModel model = new DynamicFieldSectionModel();
            model.DynamicFormId = dynamicFormId;

            if (dynamicFieldSectionId.IsNotNullOrEmpty())
            {
                var dynamicFieldSection = await QueryExecutor.ExecuteOneAsync(new GetDynamicFieldSectionByIdQuery { Id = dynamicFieldSectionId.Value });
                if (dynamicFieldSection != null)
                {
                    model = Mapper.Map<DynamicFieldSectionModel>(dynamicFieldSection);
                }
                model.IsNew = false;
            }
            else
            {
                model.IsNew = true;
            }

            if (viewMode.HasValue && viewMode.Value == 1)
            {
                model.ListDynamicFieldSection = (await QueryExecutor.ExecuteManyAsync(new GetDynamicFieldSectionByFormIdQuery { DynamicFormId = dynamicFormId.Value })).ToList();
                return View("Partials/ModalDynamicFieldSection", model);
            }

            return View("Partials/CreateEditDynamicFieldSection", model);
        }

        [AllowAuthenticated]
        [HttpGet]
        public IActionResult ManageDynamicFieldSections(Guid dynamicFormId)
        {
            ManageDynamicFieldSectionListModel model = new ManageDynamicFieldSectionListModel();
            model.DynamicFormId = dynamicFormId;

            return View("Partials/ManageDynamicFieldSectionListModal", model);
        }

        [AllowAuthenticated]
        [HttpGet]
        public async Task<IActionResult> CreateEditDynamicFieldSectionModel(Guid dynamicFormId, Guid? dynamicFieldSectionId, Guid? parentSectionId)
        {
            CreateEditDynamicFieldSectionModel model = new CreateEditDynamicFieldSectionModel();
            model.DynamicFormId = dynamicFormId;

            DynamicFieldSectionData dynamicFieldSection = null;
            if (dynamicFieldSectionId.IsNotNullOrEmpty())
            {
                dynamicFieldSection = await QueryExecutor.ExecuteOneAsync(new GetDynamicFieldSectionByIdQuery { Id = dynamicFieldSectionId.Value });
                if (dynamicFieldSection != null)
                {
                    model = Mapper.Map<CreateEditDynamicFieldSectionModel>(dynamicFieldSection);
                    if (dynamicFieldSection.ParentSectionId.IsNotNullOrEmpty())
                    {
                        var parentSection = await QueryExecutor.ExecuteOneAsync(new GetDynamicFieldSectionByIdQuery { Id = dynamicFieldSection.ParentSectionId.Value });
                        if (parentSection != null)
                        {
                            model.ParentSectionName = parentSection.SectionName;
                        }
                    }
                }
            }

            if (dynamicFieldSection == null)
            {
                model.Id = Guid.NewGuid();
                model.IsNew = true;
                model.ParentSectionId = parentSectionId;
                var dynamicFieldSectionList = (await QueryExecutor.ExecuteManyAsync(new GetDynamicFieldSectionByFormIdQuery { DynamicFormId = dynamicFormId })).ToList();
                if (parentSectionId.IsNotNullOrEmpty())
                {
                    var childSectionCount = dynamicFieldSectionList.Where(sec => sec.ParentSectionId == parentSectionId).Count();
                    model.DisplayOrder = childSectionCount + 1;
                }
                else
                {
                    var rootSectionCount = dynamicFieldSectionList.Where(sec => sec.ParentSectionId.IsNullOrEmpty()).Count();
                    model.DisplayOrder = rootSectionCount + 1;
                }
            }

            return View("Partials/CreateEditDynamicFieldSectionModal", model);
        }

        [AllowAuthenticated]
        [HttpPost]
        public async Task<IActionResult> CreateEditDynamicFieldSectionModel(CreateEditDynamicFieldSectionModel model)
        {
            if (ModelState.IsValid)
            {
                if (model.DisplayOrder <= 0)
                {
                    ModelState.AddModelError("", T["Thứ tự hiển thị phải > 0."]);
                    return DefaultResult();
                }

                CreateEditDynamicFieldSectionCommand createEditDynamicFieldSectionCommand = Mapper.Map<CreateEditDynamicFieldSectionCommand>(model);
                await CommandExecutor.ExecuteAsync(createEditDynamicFieldSectionCommand);
                Info(T["Vùng thông tin đã được lưu."]);
            }

            return DefaultResult(new { Id = model.Id });
        }

        [AllowAuthenticated]
        [HttpPost]
        public async Task<IActionResult> DeleteDynamicFieldSection(Guid dynamicFieldSectionId)
        {
            try
            {
                await CommandExecutor.ExecuteAsync(new DeleteDynamicFieldSectionCommand { Id = dynamicFieldSectionId });
                Info(T["Vùng thông tin đã được xóa."]);
            }
            catch (Exception ex)
            {
                return Json(new { IsSuccess = false, ErrorMessage = ex.Message });
            }
            return Json(new { IsSuccess = true });
        }

        [AllowAuthenticated]
        [HttpPost]
        public async Task<IActionResult> CreateEditDynamicFieldSection(DynamicFieldSectionModel model)
        {
            if (ModelState.IsValid)
            {
                if (model.DisplayOrder <= 0)
                {
                    ModelState.AddModelError("", T["Thứ tự hiển thị phải > 0."]);
                    return DefaultResult();
                }

                CreateEditDynamicFieldSectionCommand createEditDynamicFieldSectionCommand = Mapper.Map<CreateEditDynamicFieldSectionCommand>(model);
                await CommandExecutor.ExecuteAsync(createEditDynamicFieldSectionCommand);
                Info(T["Nhóm thông tin đã được lưu."]);
            }

            return DefaultResult();
        }

        [AllowAuthenticated]
        [HttpGet]
        public async Task<IActionResult> CreateEditRuleField(Guid dynamicFormId, Guid? dynamicTableColumnId, Guid? dynamicTableId, Guid? dynamicFieldId)
        {
            CreateEditRuleFieldModel model = new CreateEditRuleFieldModel();
            model.DynamicFormId = dynamicFormId;
            if (dynamicTableColumnId != null & dynamicTableId != null)
            {
                var column = (await QueryExecutor.ExecuteManyAsync(new SearchDynamicTableColumnQuery { TableId = dynamicTableId.Value, Id = dynamicTableColumnId, IsShowFull = true })).FirstOrDefault();
                if (column != null)
                {
                    var tableData = JsonConvert.DeserializeObject<TableDataDto>(column.UIFriendlyFormula);
                    model.headerGrid = tableData != null ? tableData.headerGrid : null;
                    model.bodyGrid = tableData != null ? tableData.bodyGrid : null;
                    model.ElseValue = tableData.ElseValue;
                    model.Name = column.Name;
                    model.DisplayName = column.DisplayName;
                    model.Id = column.Id;
                    model.DynamicTableId = column.DynamicDefinedTableSchemaId;
                    model.CreatedDate = column.CreatedDate;
                    model.CreatedBy = column.CreatedBy;
                    model.ColumnOrder = column.ColumnOrder;
                }
            }
            else if (dynamicFieldId.HasValue)
            {
                var field = await QueryExecutor.ExecuteOneAsync(new GetDynamicFieldByIdQuery { Id = dynamicFieldId.Value });
                var tableData = JsonConvert.DeserializeObject<TableDataDto>(field.UIFriendlyFormula);
                model.headerGrid = tableData.headerGrid;
                model.bodyGrid = tableData.bodyGrid;
                model.ElseValue = tableData.ElseValue;
                model.Name = field.Name;
                model.DisplayName = field.DisplayName;
                model.Id = field.Id;
                model.CreatedDate = field.CreatedDate;
                model.CreatedBy = field.CreatedBy;
                model.ColumnOrder = field.Order;
            }
            else
            {
                var dynamicTable = (await QueryExecutor.ExecuteManyAsync(new TinyCRM.DynamicForm.Queries.GetDynamicFieldByFormIdQuery { FormId = dynamicFormId })).Where(x => x.DynamicDefinedTableSchemaId != null).FirstOrDefault();
                if (dynamicTable != null)
                {
                    model.DynamicTableId = dynamicTable.DynamicDefinedTableSchemaId.Value;
                }
            }
            return View("Partials/CreateEditRuleField", model);
        }

        [AllowAuthenticated]
        [HttpGet]
        public async Task<IActionResult> GetDynamicFieldSectionDropdownList(Guid dynamicFormId)
        {
            var dynamicFieldSectionList = (await QueryExecutor.ExecuteManyAsync(new GetDynamicFieldSectionByFormIdQuery { DynamicFormId = dynamicFormId })).ToList();
            return Json(dynamicFieldSectionList.Select(x => new { Value = x.Id, Text = x.SectionName }));
        }

        [AllowAuthenticated]
        [HttpGet]
        public async Task<IActionResult> GetDynamicFieldSectionDropdownListWithLevels(Guid dynamicFormId)
        {
            var dynamicFieldSectionList = (await QueryExecutor.ExecuteManyAsync(new GetDynamicFieldSectionDropdownListQuery { DynamicFormId = dynamicFormId })).ToList();
            return Json(dynamicFieldSectionList.Select(x => new { Value = x.Id, Text = string.Concat(Enumerable.Repeat("-", x.Level * 3)) + " " + x.SectionName }));
        }

        [AllowAuthenticated]
        [HttpGet]
        public async Task<IActionResult> GetDynamicFieldSectionListJson(Guid dynamicFormId)
        {
            var dynamicFieldSectionList = (await QueryExecutor.ExecuteManyAsync(new GetDynamicFieldSectionByFormIdQuery { DynamicFormId = dynamicFormId })).ToList();
            return Json(dynamicFieldSectionList);
        }

        [AllowAuthenticated]
        [HttpPost]
        public async Task<IActionResult> SaveFieldOrders(SaveDynamicFieldOrderModel model)
        {
            if (ModelState.IsValid)
            {
                int countOrder = 1;
                foreach (var item in model.DynamicFieldOrders.OrderBy(x => x.Order))
                {
                    item.Order = countOrder;
                    countOrder++;
                }

                var dynamicFields = await QueryExecutor.ExecuteAsync(new TinyCRM.DynamicForm.Queries.GetDynamicFieldByFormIdQuery { FormId = model.DynamicFormId });

                await CommandExecutor.ExecuteAsync(new SaveDynamicFieldOrdersCommand
                {
                    DynamicFormId = model.DynamicFormId,
                    DynamicFieldOrders = model.DynamicFieldOrders == null ? new List<DynamicFieldOrder>() : (from df in model.DynamicFieldOrders
                                                                                                             select new DynamicFieldOrder
                                                                                                             {
                                                                                                                 DynamicFieldId = df.DynamicFieldId,
                                                                                                                 Order = df.Order,
                                                                                                                 DynamicFieldSectionId = df.DynamicFieldSectionId
                                                                                                             }).ToList()
                });

                // Thay đổi thứ tự các Field
                // => Cần build lại Form Formula
                _dynamicFormUtility.BuildFomularInForm(model.DynamicFormId);
                _dynamicFormUtility.UpdateInjectInForm(model.DynamicFormId);

                if (model.NotReturnMessage == true)
                {
                    return DefaultResult();
                }

                Info(T["Thứ tự các thông đã được lưu."]);
            }
            return DefaultResult();
        }

        [HttpPost]
        public async Task<IActionResult> LoadCreateDynamicLinkedField(Guid formId, DynamicLinkedFieldModel model)
        {
            if (ModelState.IsValid)
            {
                string templateName = string.Empty;
                if (TemplateNameLinkField.IsNotNullOrEmpty())
                {
                    templateName = TemplateNameLinkField;
                }
                var dynamicField = new DynamicFieldModel
                {
                    Name = templateName.IsNotNullOrEmpty() ? templateName.Replace("{0}", model.DisplayName.NonUnicode().Replace(" ", "_")) : string.IsNullOrEmpty(model.Name) ? (model.SourceFieldName + '_' + model.SourceTableName).Replace('.', '_') : model.Name,
                    SourceFieldName = model.SourceFieldName,
                    SourceTableName = model.SourceTableName,
                    DisplayName = model.DisplayName,
                    DataType = model.DataType,
                    DefaultValue = model.DefaultValue,
                    FieldType = FieldType.Linked,
                    DynamicFormId = formId,
                    ViewHint = model.ViewHint,
                    Display = true,
                    IsReadOnly = false,
                    IsRequired = false,
                    Validations = new List<DynamicValidationModel>(),
                    BusinessValidations = new List<DynamicValidationModel>(),
                    TemplateNameLinkField = templateName,
                    VersioningCalcField = model.Isversion
                };
                var fieldList = (await QueryExecutor.ExecuteManyAsync(new TinyCRM.DynamicForm.Queries.GetDynamicFieldByFormIdQuery { FormId = formId })).ToList();
                if (fieldList.Count > 0)
                {
                    var lastFieldOrder = fieldList[fieldList.Count - 1].Order;
                    dynamicField.Order = lastFieldOrder + 1;
                }
                else
                {
                    dynamicField.Order = 1;
                }

                var businessSettings = StaticContractFieldList;
                var listStaticFieldsInSettings = string.IsNullOrEmpty(businessSettings) ? new List<StaticFieldGroup>() : JsonConvert.DeserializeObject<List<StaticFieldGroup>>(businessSettings);
                dynamicField.StaticFieldGroups = listStaticFieldsInSettings.Select(x => new StaticFieldGroup
                {
                    Name = x.Name,
                    StaticFieldList = (from sf in x.StaticFieldList
                                       select new StaticField
                                       {
                                           GroupName = x.Name,
                                           SourceTableName = sf.SourceTableName,
                                           SourceFieldName = sf.SourceFieldName,
                                           DisplayLabel = sf.DisplayLabel,
                                           DataType = sf.DataType,
                                           ViewHint = sf.ViewHint,
                                           DefaultValue = sf.DefaultValue
                                       }).ToList()

                }).ToList();

                return View("Partials/DynamicFieldCreateEdit", dynamicField);
            }
            return DefaultResult();
        }

        [AllowAuthenticated]
        [HttpGet]
        public async Task<IActionResult> LoadSelectGlobalFomular(Guid formId, int dynamicFieldOrder, bool? globalCalcFieldByVersion, bool? useId)
        {
            var dropdownfieldcolor = new List<DynamicFieldModel.LoadColorDropdown>();

            bool usingId = false;
            if (useId.HasValue && useId.Value)
            {
                usingId = true;
            }

            var dynamicFields = await QueryExecutor.ExecuteAsync(new TinyCRM.DynamicForm.Queries.GetDynamicFieldByFormIdQuery { FormId = formId });
            if (globalCalcFieldByVersion.HasValue == false || globalCalcFieldByVersion.Value == false)
            {
                foreach (var item in dynamicFields.Many.Where(x => x.DynamicDefinedTableSchemaId == null).OrderBy(x => x.Order))
                {
                    if (item.ViewHint != "GlobalCalculated" || item.Order < dynamicFieldOrder)
                    {
                        var data = new DynamicFieldModel.LoadColorDropdown { Text = item.DisplayName, Value = (usingId ? item.Id.ToString() : item.Name) };
                        dropdownfieldcolor.Add(data);
                    }
                }
            }
            else
            {
                var repDynamicFieldList = dynamicFields.Many.Where(dfd => dfd.RepresentationDynamicFieldId.IsNotNullOrEmpty())
                                                            .GroupBy(dfd => new { dfd.RepresentationDynamicFieldId })
                                                            .Select(dfd => new { RepresentationDynamicFieldId = dfd.Key, DynamicField = dfd.OrderBy(x => x.Order).FirstOrDefault() });
                foreach (var item in repDynamicFieldList)
                {
                    if (item.DynamicField.ViewHint != "GlobalCalculated" || item.DynamicField.Order < dynamicFieldOrder)
                    {
                        var data = new DynamicFieldModel.LoadColorDropdown { Text = item.DynamicField.DisplayName, Value = (usingId ? item.DynamicField.Id.ToString() : item.DynamicField.Name) };
                        dropdownfieldcolor.Add(data);
                    }
                }
            }
            return Json(dropdownfieldcolor.Select(x => new { Value = x.Value, Text = x.Text }));
        }

        [HttpPost]
        public async Task<IActionResult> LoadCreateEditDynamicField(Guid? fieldId, Guid formId, string name, int? fieldType, bool? versioningCalcField)
        {
            var listexcelformula = new List<DynamicFieldModel.ExCelFormula>();
            var dropdownfieldcolor = new List<DynamicFieldModel.LoadColorDropdown>();
            var groupDropdownFieldColors = new List<DynamicFieldModel.LoadColorDropdown>();
            var dropdowntablecolor = new List<DynamicFieldModel.LoadColorDropdown>();
            var dynamicFields = await QueryExecutor.ExecuteAsync(new TinyCRM.DynamicForm.Queries.GetDynamicFieldByFormIdQuery { FormId = formId });
            int count = 1;
            var usedDynamicFieldList = dynamicFields.Many.Where(x => x.DynamicDefinedTableSchemaId.IsNullOrEmpty() && x.DataType.StartsWith("TinyCRM.") == false).OrderBy(dfd => dfd.Order).ToList();
            Guid representationDynamicFieldId = Guid.Empty;
            foreach (var item in usedDynamicFieldList)
            {
                if (versioningCalcField.HasValue && versioningCalcField.Value)
                {
                    if (item.RepresentationDynamicFieldId.IsNotNullOrEmpty() && item.RepresentationDynamicFieldId != representationDynamicFieldId)
                    {
                        var groupColor = item.Color;
                        if (item.Color == "transparent" || item.Color.IsNullOrEmpty())
                        {
                            groupColor = string.Empty;
                        }
                        string groupDisplayName = T["Nhóm"] + " :: " + item.DisplayName.Replace(string.Format("({0})", item.VersionCode), "").Trim();
                        string groupName = string.Format("[[group_{0}]]", item.Name.Replace(string.Format("_{0}", item.VersionCode.Replace(".", "")), "").Trim());
                        var groupData = new DynamicFieldModel.LoadColorDropdown { Text = groupDisplayName, Value = groupName + "," + groupColor };
                        dropdownfieldcolor.Add(groupData);
                        listexcelformula.Add(new DynamicFieldModel.ExCelFormula { id = count, text = groupDisplayName, value = groupName, description = groupName, color = FwdDynamicFieldGroupColorFormula, icon = FwdDynamicFieldGroupIconFormula, customstyle = FwdDynamicFieldGroupCustomStyleFormula });
                        count++;

                        representationDynamicFieldId = item.RepresentationDynamicFieldId.Value;
                    }
                }

                var color = item.Color;
                if (item.Color == "transparent" || item.Color.IsNullOrEmpty())
                {
                    color = string.Empty;
                }
                var data = new DynamicFieldModel.LoadColorDropdown { Text = item.DisplayName, Value = item.Name + "," + color };
                dropdownfieldcolor.Add(data);
                listexcelformula.Add(new DynamicFieldModel.ExCelFormula { id = count, text = item.DisplayName, value = item.Name, description = item.Name, color = FwdDynamicfieldColorFormula, icon = FwdDynamicfieldIconFormula, customstyle = string.Empty });
                count++;
            }

            Type requestTicketDataType = typeof(RequestTicketData);
            var dynamicTable = dynamicFields.Many.Where(g => g.DynamicDefinedTableSchemaId.IsNotNullOrEmpty()).OrderBy(x => x.Order);
            if (dynamicTable != null)
            {
                foreach (var item in dynamicTable)
                {
                    var columns = (await QueryExecutor.ExecuteManyAsync(new GetDynamicDefinedTableColumnListByTableSchemaQuery { DynamicDefinedTableSchemaId = item.DynamicDefinedTableSchemaId.Value })).ToList();
                    foreach (var column in columns)
                    {
                        if (column.DataType.ToType() != requestTicketDataType)
                        {
                            var data = new DynamicFieldModel.LoadColorDropdown { Text = item.DisplayName + "/" + column.DisplayName, Value = item.Name + "." + column.Name + "," + column.Color };
                            dropdowntablecolor.Add(data);
                            listexcelformula.Add(new DynamicFieldModel.ExCelFormula { id = count, text = item.DisplayName + "/" + column.DisplayName, value = item.Name + "." + column.Name, description = item.Name + "." + column.Name, color = FwdDynamictableColorFormula, icon = FwdDynamictableIconFormula, customstyle = string.Empty });
                            count++;
                        }
                        else
                        {
                            List<DynamicDefinedTableLinkedTicketColumnData> linkedTicketColumnList = (await QueryExecutor.ExecuteManyAsync(new GetDynamicDefinedTableLinkedTicketColumnListByColumnIdQuery { DynamicDefinedTableLinkedColumnId = column.Id })).ToList();
                            foreach (var linkedTicketColumn in linkedTicketColumnList)
                            {
                                var data = new DynamicFieldModel.LoadColorDropdown { Text = item.DisplayName + "/" + linkedTicketColumn.DisplayName, Value = item.Name + "." + linkedTicketColumn.PropertyName + "," + column.Color };
                                dropdowntablecolor.Add(data);
                                listexcelformula.Add(new DynamicFieldModel.ExCelFormula { id = count, text = item.DisplayName + "/" + linkedTicketColumn.DisplayName, value = item.Name + "." + linkedTicketColumn.PropertyName, description = item.Name + "." + linkedTicketColumn.PropertyName, color = FwdDynamictableColorFormula, icon = FwdDynamictableIconFormula, customstyle = string.Empty });
                                count++;
                            }
                        }
                    }
                }
            }

            var dynamicFieldListOfForm = dynamicFields.Many.ToList();
            foreach (var dynamicField in dynamicFieldListOfForm)
            {
                var dataType = dynamicField.DataType.ToType();
                var baseType = dataType.BaseType();

                if (baseType.IsGridData() && !baseType.IsUserDefinedTableGridData())
                {
                    Attribute[] entityDataEditorAttributes = Attribute.GetCustomAttributes(baseType, typeof(GridDataColumnAttribute));
                    if (entityDataEditorAttributes != null && entityDataEditorAttributes.Length > 0)
                    {
                        foreach (var attribute in entityDataEditorAttributes)
                        {
                            GridDataColumnAttribute entityDataEditorAttribute = (GridDataColumnAttribute)attribute;

                            var data = new DynamicFieldModel.LoadColorDropdown
                            {
                                Text = dynamicField.DisplayName + "/" + entityDataEditorAttribute.DisplayName,
                                Value = dynamicField.Name + "." + entityDataEditorAttribute.Name + ",#2d5e18"
                            };
                            dropdowntablecolor.Add(data);

                            listexcelformula.Add(new DynamicFieldModel.ExCelFormula
                            {
                                id = count,
                                text = dynamicField.DisplayName + "/" + entityDataEditorAttribute.DisplayName,
                                value = dynamicField.Name + "." + entityDataEditorAttribute.Name,
                                description = dynamicField.Name + "." + entityDataEditorAttribute.Name,
                                color = FwdDynamictableColorFormula,
                                icon = FwdDynamictableIconFormula,
                                customstyle = string.Empty
                            });
                            count++;
                        }
                    }
                }
            }

            var exFomularcount = 0;
            if (listexcelformula != null)
            {
                exFomularcount = listexcelformula.Count();
            }

            var queryExcellFomular = (await QueryExecutor.ExecuteManyAsync(new GetExcelFormulaListQuery { Count = exFomularcount })).OrderBy(x => x.Order).Select(x => new DynamicFieldModel.ExCelFormula { id = x.Order, text = x.Text, value = x.Value, description = x.Description, color = x.Color, icon = x.Icon }).ToList();
            listexcelformula.AddRange(queryExcellFomular);

            //------- Load Color cho dropdown
            if (fieldId.HasValue || name.IsNotNullOrEmpty())
            {
                var fieldData = new DynamicFieldDefinitionData();
                if (fieldId.HasValue)
                {
                    fieldData = await QueryExecutor.ExecuteOneAsync(new GetDynamicFieldByIdQuery { Id = fieldId.Value });
                }
                else
                {
                    fieldData = await QueryExecutor.ExecuteOneAsync(new GetDynamicFieldByFormIdAndNameQuery { FormId = formId, Name = name });
                }
                if (fieldData != null)
                {
                    var editModel = new DynamicFieldModel
                    {
                        Id = fieldData.Id,
                        Name = fieldData.Name,
                        FieldType = fieldData.FieldType,
                        DataType = fieldData.DataType,
                        ViewHint = fieldData.ViewHint,
                        DynamicDefinedTableSchemaId = fieldData.DynamicDefinedTableSchemaId,
                        SourceFieldName = fieldData.SourceFieldName,
                        SourceTableName = fieldData.SourceTableName,
                        Order = fieldData.Order,
                        DisplayName = fieldData.DisplayName,
                        Display = fieldData.Display,
                        DefaultValue = fieldData.DefaultValue,
                        DynamicFormId = fieldData.DynamicFormId,
                        DynamicFieldSectionId = fieldData.DynamicFieldSectionId,
                        SelectOptions = fieldData.SelectOptions,
                        IsReadOnly = fieldData.IsReadOnly,
                        IsRequired = fieldData.IsRequired,
                        Validations = fieldData.Validation.IsNotNullOrEmpty() ?
                            JsonConvert.DeserializeObject<IEnumerable<DynamicValidationModel>>(fieldData.Validation)
                            : new List<DynamicValidationModel>(),
                        BusinessValidations = fieldData.BusinessValidation.IsNotNullOrEmpty() ?
                            JsonConvert.DeserializeObject<IEnumerable<DynamicValidationModel>>(fieldData.BusinessValidation)
                            : new List<DynamicValidationModel>(),
                        AdditionalMetadatas = fieldData.AdditionalMetadata.IsNotNullOrEmpty() ?
                            JsonConvert.DeserializeObject<IEnumerable<DynamicAdditionalMetadataModel>>(fieldData.AdditionalMetadata)
                            : new List<DynamicAdditionalMetadataModel>(),
                        AdditionalFilter = fieldData.AdditionalFilter,
                        Mapping360FieldName = fieldData.Mapping360FieldName,
                        Mapping360FieldType = fieldData.Mapping360FieldType,
                        Mapping360RowOptions = fieldData.Mapping360RowOptions,
                        UIFriendlyFormula = fieldData.UIFriendlyFormula,
                        Color = fieldData.Color,
                        BackgroundColor = fieldData.BackgroundColor,
                        ColorDynamicFields = dropdownfieldcolor,
                        ColorDynamicTable = dropdowntablecolor,
                        ExtensionExCelFormula = listexcelformula,
                        PaymentType = fieldData.PaymentType,
                        FieldCondition = fieldData.FieldCondition,
                        FreezeValue = fieldData.FreezeValue,
                        IsExportExcel = fieldData.IsExportExcel,
                        IsExportByConditionBoolean = fieldData.IsExportByConditionBoolean,
                        VersionCode = fieldData.VersionCode,
                        RepresentationDynamicFieldId = fieldData.RepresentationDynamicFieldId,
                        IsCheckDuplicate = fieldData.IsCheckDuplicate
                    };
                    editModel.SelectOptionList = editModel.GetSelectOptionList();

                    var businessSettings = StaticContractFieldList;
                    var listStaticFieldsInSettings = string.IsNullOrEmpty(businessSettings) ? new List<StaticFieldGroup>() : JsonConvert.DeserializeObject<List<StaticFieldGroup>>(businessSettings);
                    editModel.StaticFieldGroups = listStaticFieldsInSettings.Select(x => new StaticFieldGroup
                    {
                        Name = x.Name,
                        StaticFieldList = (from sf in x.StaticFieldList
                                           select new StaticField
                                           {
                                               GroupName = x.Name,
                                               SourceTableName = sf.SourceTableName,
                                               SourceFieldName = sf.SourceFieldName,
                                               DisplayLabel = sf.DisplayLabel,
                                               DataType = sf.DataType,
                                               ViewHint = sf.ViewHint,
                                               DefaultValue = sf.DefaultValue
                                           }).ToList()

                    }).ToList();

                    editModel.GlobalCalculatedScriptInfo = new GlobalCalculatedScriptInfo();
                    if (fieldData.GlobalCalculatedScriptJson.IsNotNullOrEmpty())
                    {
                        editModel.GlobalCalculatedScriptInfo = JsonConvert.DeserializeObject<GlobalCalculatedScriptInfo>(fieldData.GlobalCalculatedScriptJson);
                    }

                    editModel.FormDynamicFieldList = (await QueryExecutor.ExecuteManyAsync(new TinyCRM.DynamicForm.Queries.GetDynamicFieldByFormIdQuery { FormId = formId })).ToList();
                    #region configLoadUrl 
                    foreach (var metaData in editModel.AdditionalMetadatas)
                    {
                        if (metaData.Key.ToLower() == "LoadUrl".ToLower() && metaData.ConfiguredURL.IsNotNullOrEmpty())
                        {
                            editModel.SelectedConfigLoadUrl = metaData.ConfiguredURL;
                            string applicationUrl = Configuration.GetValue<string>("application:url");
                            string fullUrl = applicationUrl + metaData.Value;
                            Uri uri = new Uri(fullUrl);
                            string queryString = uri.Query;

                            NameValueCollection queryParameters = HttpUtility.ParseQueryString(queryString);
                            var loadUrlParams = new List<DynamicAdditionalMetadataModel>();
                            if (queryParameters.AllKeys.Count() > 0)
                            {
                                foreach (string key in queryParameters.AllKeys)
                                {
                                    var par = new DynamicAdditionalMetadataModel();
                                    par.Key = key;
                                    par.Value = queryParameters[key];
                                    loadUrlParams.Add(par);
                                    editModel.LoadURLParameters = loadUrlParams;
                                }
                            }

                        }
                    }
                    #endregion
                    return View("Partials/DynamicFieldTabCreatEdit", editModel);
                }
                Error(T["Không tìm thấy trường dữ liệu"]);
            }
            else
            {
                var editModel = new DynamicFieldModel
                {
                    DynamicFormId = formId,
                    FieldType = fieldType.HasValue ? (FieldType)fieldType.Value : FieldType.Input,
                    IsNew = true,
                    Display = true,
                    DataType = "System.String",
                    ColorDynamicFields = dropdownfieldcolor,
                    ColorDynamicTable = dropdowntablecolor,
                    ExtensionExCelFormula = listexcelformula,
                    VersioningCalcField = versioningCalcField.HasValue ? versioningCalcField.Value : false,
                    GlobalCalcFieldByVersion = versioningCalcField.HasValue ? versioningCalcField.Value : false,
                };

                if (fieldType.HasValue && fieldType == 1)
                {
                    editModel.ViewHint = "String";
                }

                //if (fieldType.HasValue && fieldType == 3)
                //{
                //    editModel.ViewHint = "Calculated";
                //}

                if (fieldType.HasValue && fieldType == 5)
                {
                    editModel.ViewHint = "GlobalCalculated";
                }

                if (!string.IsNullOrEmpty(name))
                {
                    editModel.Name = name;
                }

                var fieldList = dynamicFields.Many.OrderBy(dfd => dfd.Order).ToList();
                if (fieldList.Count > 0)
                {
                    var dynamicFieldSectionId = fieldList.Last().DynamicFieldSectionId;
                    editModel.DynamicFieldSectionId = dynamicFieldSectionId;
                    if (fieldList.Count > 0)
                    {
                        var lastFieldOrder = fieldList[fieldList.Count - 1].Order;
                        editModel.Order = lastFieldOrder + 1;
                    }
                    else
                    {
                        editModel.Order = 1;
                    }
                }
                else
                {
                    editModel.Order = 1;
                }

                var businessSettings = StaticContractFieldList;
                var listStaticFieldsInSettings = string.IsNullOrEmpty(businessSettings) ? new List<StaticFieldGroup>() : JsonConvert.DeserializeObject<List<StaticFieldGroup>>(businessSettings);
                editModel.StaticFieldGroups = listStaticFieldsInSettings.Select(x => new StaticFieldGroup
                {
                    Name = x.Name,
                    StaticFieldList = (from sf in x.StaticFieldList
                                       select new StaticField
                                       {
                                           GroupName = x.Name,
                                           SourceTableName = sf.SourceTableName,
                                           SourceFieldName = sf.SourceFieldName,
                                           DisplayLabel = sf.DisplayLabel,
                                           DataType = sf.DataType,
                                           ViewHint = sf.ViewHint,
                                           DefaultValue = sf.DefaultValue
                                       }).ToList()

                }).ToList();

                editModel.GlobalCalculatedScriptInfo = new GlobalCalculatedScriptInfo();
                editModel.FormDynamicFieldList = (await QueryExecutor.ExecuteManyAsync(new TinyCRM.DynamicForm.Queries.GetDynamicFieldByFormIdQuery { FormId = formId })).ToList();

                return View("Partials/DynamicFieldTabCreatEdit", editModel);
            }
            return DefaultResult();
        }

        [HttpPost]
        public async Task<IActionResult> CreateEditDynamicField(DynamicFieldModel model)
        {
            if (!ModelState.IsValid) return DefaultResult();
            if (!model.DynamicFormId.HasValue) return DefaultResult();

            if (model.DataType == "TinyCRM.AppServices.DynamicDefinedTable.DynamicDefinedTableGridData")
            {
                var tableInFormLinkSystemTable = _entitySet.Get<DynamicFieldDefinitionEntity>().FirstOrDefault(x => x.DynamicFormId == new Guid("00000000-1111-2222-3333-************")
                && x.DynamicDefinedTableSchemaId == model.DynamicDefinedTableSchemaId.Value);
                if (tableInFormLinkSystemTable != null)
                {
                    var getConfigTableInSystem = tableInFormLinkSystemTable.DynamicDefinedTableSchemaId;
                    DynamicDefinedTableSchemaData dynamicDefinedTableSchemaData = await QueryExecutor.ExecuteOneAsync(new GetDynamicDefinedTableSchemaQuery
                    {
                        DynamicDefinedTableSchemaId = tableInFormLinkSystemTable.DynamicDefinedTableSchemaId.Value
                    });
                    if (dynamicDefinedTableSchemaData.RowStoredMethod != DynamicDefinedTableStoreMethod.OwnDbTable)
                    {
                        ModelState.AddModelError("", T["Bạn đang dùng tính năng cập nhật đồng bộ dữ liệu từ table trên form về table trên form hệ thống, nhưng kiểu table" +
                            $" không phải là kiểu: bảng dữ liệu riêng. Vui lòng chỉnh sửa kiểu dữ liệu của table: {dynamicDefinedTableSchemaData.Name} thành bảng riêng "]);
                        return DefaultResult();
                    }
                }
            }

            var validPaymentTypeDataType = new List<string> { "System.Int32", "System.Int64", "System.Double" };
            if (model.PaymentType.HasValue && !validPaymentTypeDataType.Any(x => model.DataType.Contains(x)))
            {
                ModelState.AddModelError("", T["PaymentType chỉ hỗ trợ kiểu số."]);
                return DefaultResult();
            }

            if (model.Order <= 0)
            {
                ModelState.AddModelError("", T["Thứ tự hiển thị phải > 0."]);
                return DefaultResult();
            }

            var propertyNameRegex = new Regex(@"^@?[a-zA-Z_]\w*$");
            Match propertyNameMatch = propertyNameRegex.Match(model.Name);
            if (!propertyNameMatch.Success)
            {
                ModelState.AddModelError("", T["Trường thông tin không được bắt đầu bằng chữ số và không chứa các ký tự đặc biệt"]);
                return DefaultResult();
            }

            if (ExcelUtility.ExcelFunctionNameList.Contains(model.Name.ToUpper().Trim()))
            {
                ModelState.AddModelError("", T["\"{0}\" trùng với tên hàm của Excel. Vui lòng đặt lại tên Trường thông tin.", model.Name]);
                return DefaultResult();
            }

            if (model.FieldType == FieldType.GlobalCalculated && model.GlobalCalculatedScriptInfo != null && model.GlobalCalculatedScriptInfo.ExecutedScript.IsNotNullOrEmpty())
            {
                if (model.GlobalCalculatedScriptInfo.DynamicFieldConditions != null && model.GlobalCalculatedScriptInfo.DynamicFieldConditions.Count > 0)
                {
                    bool conditionValid = true;
                    foreach (var condition in model.GlobalCalculatedScriptInfo.DynamicFieldConditions)
                    {
                        if (condition.DynamicFieldName.IsNullOrEmpty())
                        {
                            ModelState.AddModelError("", T["Vui lòng chọn \"Trường thông tin\" cho điều kiện công thức tính."]);
                            conditionValid = false;
                        }
                        if (condition.CompareOperator.IsNullOrEmpty())
                        {
                            ModelState.AddModelError("", T["Vui lòng chọn \"Toán tử\" cho điều kiện công thức tính."]);
                            conditionValid = false;
                        }
                        if (condition.ComparedValue.IsNullOrEmpty())
                        {
                            ModelState.AddModelError("", T["Vui lòng nhập \"Giá trị so sánh\" cho điều kiện công thức tính."]);
                            conditionValid = false;
                        }
                    }
                    if (!conditionValid)
                    {
                        return DefaultResult();
                    }
                }
            }

            if (!model.Id.HasValue)
            {
                model.Id = Guid.NewGuid();
                model.IsNew = true;
            }

            if (model.FieldType == FieldType.Calculated && model.DefaultValue.IsNotNullOrEmpty())
            {
                model.IsReadOnly = true;
            }

            if ((model.FieldType == FieldType.Calculated || model.FieldType == FieldType.SuggestValueCalculated) && model.DefaultValue.IsNotNullOrEmpty())
            {
                var fieldDataType = model.DataType.ToType();
                model.FomularByFieldName = model.DefaultValue;
                var cvfomular_excell = _dynamicFormUtility.GetActualExcelFomula(model.DynamicFormId.Value, model.DefaultValue);
                if (cvfomular_excell.IsNotNullOrEmpty())
                {
                    model.DefaultValue = cvfomular_excell;
                }
            }
            if (model.FieldType == FieldType.GlobalCalculated)
            {
                if (model.GlobalCalculatedScriptInfo.ExecutedScript.IsNullOrEmpty())
                {
                    ModelState.AddModelError("", T["Công thức vùng không được để trống"]);
                    return DefaultResult();
                }

                if (!model.GlobalCalculatedScriptInfo.GlobalFormulaBasedOnDynamicFieldId.HasValue)
                {
                    ModelState.AddModelError("", T["Trường thông tin tính toán không được để trống"]);
                    return DefaultResult();
                }

                model.IsReadOnly = true;
                model.ViewHint = "GlobalCalculated";
            }

            if (model.FieldType == FieldType.Linked)
            {
                if (model.SourceTableName.IsNullOrEmpty() || model.SourceFieldName.IsNullOrEmpty())
                {
                    ModelState.AddModelError("", T["Vui lòng chọn nguồn đối tượng và nguồn thông tin."]);
                    return DefaultResult();
                }
            }

            if (model.FieldType == FieldType.Static)
            {
                model.IsReadOnly = true;
            }

            if (!(await TypeChoices).ContainsKey(model.DataType))
            {
                ModelState.AddModelError("", T["Kiểu dữ liệu không hợp lệ"]);
                return DefaultResult();
            }

            if (model.DataType == "TinyCRM.AppServices.DynamicDefinedTable.DynamicDefinedTableGridData")
            {
                if (model.DynamicDefinedTableSchemaId.IsNullOrEmpty())
                {
                    ModelState.AddModelError("DynamicDefinedTableSchemaId", T["Vui lòng chọn Danh sách đã định nghĩa"]);
                    return DefaultResult();
                }
            }

            string oldDynamicFieldName = model.Name;
            if (!model.IsNew)
            {
                var oldDynamicField = await QueryExecutor.ExecuteOneAsync(new GetDynamicFieldByIdQuery { Id = model.Id.Value });
                if (oldDynamicField.Name != model.Name)
                {
                    oldDynamicFieldName = oldDynamicField.Name;
                }
            }

            var fieldWithSameName = (await QueryExecutor.ExecuteManyAsync(new GetDynamicFieldInFormByNameQuery
            {
                FieldId = model.IsNew ? null : model.Id,
                FormId = model.DynamicFormId.Value,
                Name = model.Name
            })).ToList();

            var fieldWithSameOrder = (await QueryExecutor.ExecuteManyAsync(new GetDynamicFieldInFormByOrderQuery
            {
                FieldId = model.IsNew ? null : model.Id,
                FormId = model.DynamicFormId.Value,
                Order = model.Order
            })).ToList();

            if (fieldWithSameOrder.Count > 0)
            {
                ModelState.AddModelError("", T["Thứ tự bị trùng, vui lòng kiểm tra lại."]);
                return DefaultResult();
            }

            if (model.bodyGrid != null && model.bodyGrid.Count > 0 && model.DynamicFormId.HasValue)
            {
                var tableDataDto = new TableDataDto();
                tableDataDto.headerGrid = model.headerGrid;
                tableDataDto.ElseValue = model.ElseValue;

                tableDataDto.bodyGrid = _dynamicFormAppService.RemoveEmptyRow(model.bodyGrid);
                if (tableDataDto != null && model.headerGrid != null)
                {
                    model.FomularByFieldName = await _dynamicFormAppService.BuildRuleStatement(tableDataDto, model.DynamicFormId.Value, model.DataType);
                    model.DefaultValue = _dynamicFormAppService.BuildExcelFormula(model.FomularByFieldName, model.DynamicFormId.Value, model.DynamicDefinedTableSchemaId);
                    model.UIFriendlyFormula = JsonConvert.SerializeObject(tableDataDto);
                }
            }

            if (fieldWithSameName.Count == 0)
            {
                string globalCalculatedScriptJson = string.Empty;
                // GlobalCalculatedScript
                if (model.FieldType == FieldType.GlobalCalculated && model.GlobalCalculatedScriptInfo != null && model.GlobalCalculatedScriptInfo.ExecutedScript.IsNotNullOrEmpty())
                {
                    globalCalculatedScriptJson = JsonConvert.SerializeObject(model.GlobalCalculatedScriptInfo);
                }
                var dynamicFields = await QueryExecutor.ExecuteManyAsync(new TinyCRM.DynamicForm.Queries.GetDynamicFieldByFormIdQuery { FormId = model.DynamicFormId.Value });
                int dynamicFieldOrder = model.Order;

                if (model.IsNew)
                {
                    if (model.DynamicFieldSectionId.HasValue)
                    {
                        var listDynamicFieldOrder = dynamicFields.Where(x => x.DynamicFieldSectionId == model.DynamicFieldSectionId);
                        if (listDynamicFieldOrder != null && listDynamicFieldOrder.Count() > 0)
                        {
                            dynamicFieldOrder = listDynamicFieldOrder.Max(x => x.Order) + 1;
                        }
                    }
                }

                //Check Công thức được tạo ra có bị vòng lặp hay không
                if (model.FieldType == FieldType.Calculated || model.FieldType == FieldType.GlobalCalculated || model.FieldType == FieldType.SuggestValueCalculated)
                {
                    var fieldsErr = new List<string>();
                    if (model.FomularByFieldName.IsNotNullOrEmpty() && model.FieldType == FieldType.Calculated)
                    {
                        if (!_dynamicFormUtility.ValidateFomulaByCalculatedTree(model.DynamicFormId.Value, model.Name, out fieldsErr, 2, model.FomularByFieldName))
                        {
                            ModelState.AddModelError("", T["Trường dữ liệu: \"{0}\" lặp vô hạn do các trường \"{1}\". Vui lòng sửa lại các công thức", model.Name, string.Join(";", fieldsErr)]);
                            return DefaultResult();
                        }
                    }
                    if (model.FieldType == FieldType.GlobalCalculated)
                    {
                        if (!_dynamicFormUtility.ValidateFomulaByCalculatedTree(model.DynamicFormId.Value, model.Name, out fieldsErr, 2, globalCalculatedScriptJson))
                        {
                            ModelState.AddModelError("", T["Trường dữ liệu: \"{0}\" lặp vô hạn do các trường \"{1}\". Vui lòng sửa lại các công thức", model.Name, string.Join(";", fieldsErr)]);
                            return DefaultResult();
                        }
                    }
                }


                if (model.FomularByFieldName.IsNotNullOrEmpty())
                {
                    string errorMessage = string.Empty;
                    if (!_dynamicFormUtility.ValidateFomula(model.DynamicFormId.Value, model.FomularByFieldName, out errorMessage))
                    {
                        Warn(errorMessage);
                    }
                }

                #region Xử lý loadurl view vào additionalmetadata

                var additionalMetadatasStr = string.Empty;
                var configuredUrl = string.Empty;
                if (model.SelectedConfigLoadUrl.IsNotNullOrEmpty())
                {
                    configuredUrl = model.SelectedConfigLoadUrl;
                    if (model.LoadURLParameters != null && model.LoadURLParameters.Any())
                    {
                        foreach (var param in model.LoadURLParameters)
                        {
                            var oldStr = string.Format("{{{0}}}", param.Key);
                            model.SelectedConfigLoadUrl = model.SelectedConfigLoadUrl.Replace(oldStr, param.Value.IsNotNullOrEmpty() ? param.Value : "");
                        }
                    }
                }
                if (model.AdditionalMetadatas.Any() && model.AdditionalMetadatas.First().Key.IsNotNullOrEmpty())
                {
                    model.AdditionalMetadatas = model.AdditionalMetadatas.Select(item => item.Key.ToLower() == "LoadUrl".ToLower() ? new DynamicAdditionalMetadataModel { Key = item.Key, Value = model.SelectedConfigLoadUrl ?? item.Value, ConfiguredURL = model.SelectedConfigLoadUrl.IsNotNullOrEmpty() ? configuredUrl : null } : item);
                }
                else
                {
                    var additionalMetadatas = new List<DynamicAdditionalMetadataModel>();
                    additionalMetadatas.Add(new DynamicAdditionalMetadataModel { Key = "LoadUrl", Value = model.SelectedConfigLoadUrl, ConfiguredURL = model.SelectedConfigLoadUrl.IsNotNullOrEmpty() ? configuredUrl : null });
                    model.AdditionalMetadatas = additionalMetadatas;
                }
                additionalMetadatasStr = JsonConvert.SerializeObject(model.AdditionalMetadatas);

                #endregion

                if (model.FieldType == FieldType.Linked)
                {
                    if (model.CustomerVersions != null && model.CustomerVersions.Count > 0)
                    {
                        Guid representationDynamicFieldId = Guid.NewGuid();

                        string representationFieldFormula = string.Empty;
                        List<string> orderCustomerVersions = model.CustomerVersions.OrderBy(cv => cv).ToList();
                        foreach (string customerVersion in orderCustomerVersions)
                        {
                            string fomularByFieldName = model.FomularByFieldName, defaultValue = model.DefaultValue, uiFriendlyFormula = model.UIFriendlyFormula;
                            if (model.bodyGrid != null && model.bodyGrid.Count > 0 && model.DynamicFormId.HasValue)
                            {
                                var tableDataDto = new TableDataDto();
                                tableDataDto.headerGrid = model.headerGrid;
                                tableDataDto.ElseValue = model.ElseValue;

                                tableDataDto.bodyGrid = _dynamicFormAppService.RemoveEmptyRow(model.bodyGrid);
                                if (tableDataDto != null && model.headerGrid != null)
                                {
                                    fomularByFieldName = await _dynamicFormAppService.BuildRuleStatement(tableDataDto, model.DynamicFormId.Value, model.DataType);
                                    defaultValue = _dynamicFormAppService.BuildExcelFormula(model.FomularByFieldName, model.DynamicFormId.Value, model.DynamicDefinedTableSchemaId);
                                    uiFriendlyFormula = JsonConvert.SerializeObject(tableDataDto);
                                }
                            }

                            string fieldName = model.Name + "_" + customerVersion.Replace(".", "");
                            string displayName = string.Format("{0} ({1})", model.DisplayName, customerVersion);
                            representationFieldFormula += fieldName + "+";

                            await CommandExecutor.ExecuteAsync(new CreateEditDynamicFieldDefinitionCommand
                            {
                                Id = Guid.NewGuid(),
                                DataType = model.DataType,
                                Name = fieldName,
                                FieldType = model.GetFieldType(),
                                ViewHint = model.ViewHint,
                                IsNew = model.IsNew,
                                DisplayName = displayName,
                                DefaultValue = model.DefaultValue,
                                SourceFieldName = model.SourceFieldName,
                                Display = true,
                                SourceTableName = model.SourceTableName,
                                DynamicFormId = model.DynamicFormId.Value,
                                Order = dynamicFieldOrder,
                                DynamicDefinedTableSchemaId = model.DynamicDefinedTableSchemaId,
                                IsReadOnly = model.IsReadOnly,
                                IsRequired = model.IsRequired,
                                Validation = (model.Validations.Any()
                                    && model.Validations.First().Validation.IsNotNullOrEmpty()) ?
                                    JsonConvert.SerializeObject(model.Validations)
                                    : null,
                                BusinessValidation = (model.BusinessValidations.Any()
                                    && model.BusinessValidations.First().Validation.IsNotNullOrEmpty()) ?
                                    JsonConvert.SerializeObject(model.BusinessValidations)
                                    : null,
                                AdditionalMetadata = additionalMetadatasStr,
                                SelectOptions = model.GetSelectOptions(),
                                AdditionalFilter = model.AdditionalFilter,
                                Mapping360FieldName = model.Mapping360FieldName,
                                Mapping360FieldType = model.Mapping360FieldType,
                                Mapping360RowOptions = model.Mapping360RowOptions,
                                EntityTableName = model.GetEntityTableName(),
                                DynamicFieldSectionId = model.DynamicFieldSectionId,
                                UIFriendlyFormula = model.UIFriendlyFormula,
                                Color = model.Color,
                                BackgroundColor = model.BackgroundColor,
                                FomularByFieldName = model.FomularByFieldName,
                                RepresentationDynamicFieldId = representationDynamicFieldId,
                                PaymentType = model.PaymentType,
                                VersionCode = customerVersion,
                                IsCheckDuplicate = model.IsCheckDuplicate
                            });

                            dynamicFieldOrder++;
                        }

                        // Đổi tên, replace lại các FormularByFieldName trước khi rebuild lại Form
                        if (oldDynamicFieldName != model.Name)
                        {
                            _dynamicFormUtility.RebuildFormulaByFieldName(model.DynamicFormId.Value, oldDynamicFieldName, model.Name);
                        }

                        // Rebuild Formula Form luôn. Vì order trong Form có thể thay đổi
                        // Order thay đổi có thể do:
                        // - Thêm field mới vào 1 Section ở giữ
                        // - Đổi Section cho 1 fields
                        // - ...
                        _dynamicFormUtility.BuildFomularInForm(model.DynamicFormId.Value);
                        _dynamicFormUtility.UpdateInjectInForm(model.DynamicFormId.Value);

                        if (model.IsNew)
                        {
                            Info(T["Trường dữ liệu đã được thêm"]);
                        }
                        else
                        {
                            Info(T["Trường dữ liệu đã được cập nhật"]);
                        }
                        return DefaultResult();
                    }
                }

                await CommandExecutor.ExecuteAsync(new CreateEditDynamicFieldDefinitionCommand
                {
                    Id = model.Id.Value,
                    DataType = model.DataType,
                    Name = model.Name,
                    FieldType = model.GetFieldType(),
                    ViewHint = model.ViewHint,
                    IsNew = model.IsNew,
                    DisplayName = model.DisplayName,
                    DefaultValue = model.DefaultValue,
                    SourceFieldName = model.SourceFieldName,
                    Display = model.Display,
                    SourceTableName = model.SourceTableName,
                    DynamicFormId = model.DynamicFormId.Value,
                    Order = dynamicFieldOrder,
                    DynamicDefinedTableSchemaId = model.DynamicDefinedTableSchemaId,
                    IsReadOnly = model.IsReadOnly,
                    IsRequired = model.IsRequired,
                    Validation = (model.Validations.Any()
                        && model.Validations.First().Validation.IsNotNullOrEmpty()) ?
                        JsonConvert.SerializeObject(model.Validations)
                        : null,
                    BusinessValidation = (model.BusinessValidations.Any()
                        && model.BusinessValidations.First().Validation.IsNotNullOrEmpty()) ?
                        JsonConvert.SerializeObject(model.BusinessValidations)
                        : null,
                    AdditionalMetadata = additionalMetadatasStr,
                    SelectOptions = model.GetSelectOptions(),
                    AdditionalFilter = model.AdditionalFilter,
                    Mapping360FieldName = model.Mapping360FieldName,
                    Mapping360FieldType = model.Mapping360FieldType,
                    Mapping360RowOptions = model.Mapping360RowOptions,
                    EntityTableName = model.GetEntityTableName(),
                    DynamicFieldSectionId = model.DynamicFieldSectionId,
                    UIFriendlyFormula = model.UIFriendlyFormula,
                    Color = model.Color,
                    BackgroundColor = model.BackgroundColor,
                    GlobalCalculatedScriptJson = globalCalculatedScriptJson,
                    FomularByFieldName = model.FomularByFieldName,
                    PaymentType = model.PaymentType,
                    FieldCondition = model.FieldCondition,
                    FreezeValue = model.FreezeValue,
                    IsExportExcel = model.IsExportExcel,
                    IsExportByConditionBoolean = model.IsExportByConditionBoolean,
                    VersionCode = model.VersionCode,
                    RepresentationDynamicFieldId = model.RepresentationDynamicFieldId,
                    IsCheckDuplicate = model.IsCheckDuplicate
                });

                // Đổi tên, replace lại các FormularByFieldName trước khi rebuild lại Form
                if (oldDynamicFieldName != model.Name)
                {
                    _dynamicFormUtility.RebuildFormulaByFieldName(model.DynamicFormId.Value, oldDynamicFieldName, model.Name);
                }

                // Rebuild Formula Form luôn. Vì order trong Form có thể thay đổi
                // Order thay đổi có thể do:
                // - Thêm field mới vào 1 Section ở giữ
                // - Đổi Section cho 1 fields
                // - ...
                _dynamicFormUtility.BuildFomularInForm(model.DynamicFormId.Value);
                _dynamicFormUtility.UpdateInjectInForm(model.DynamicFormId.Value);

                if (model.IsNew)
                {
                    Info(T["Trường dữ liệu đã được thêm"]);
                }
                else
                {
                    Info(T["Trường dữ liệu đã được cập nhật"]);
                }
                return DefaultResult();
            }
            Error(T["Trường dữ liệu đã tồn tại"]);
            return DefaultResult();
        }

        [AllowAuthenticated]
        [HttpPost]
        public async Task<IActionResult> CreateEditVersioningCalcDynamicField(DynamicFieldModel model)
        {
            if (!ModelState.IsValid) return DefaultResult();
            if (!model.DynamicFormId.HasValue) return DefaultResult();

            if (model.Order <= 0)
            {
                ModelState.AddModelError("", T["Thứ tự hiển thị phải > 0."]);
                return DefaultResult();
            }

            var propertyNameRegex = new Regex(@"^@?[a-zA-Z_]\w*$");
            Match propertyNameMatch = propertyNameRegex.Match(model.Name);
            if (!propertyNameMatch.Success)
            {
                ModelState.AddModelError("", T["Trường thông tin không được bắt đầu bằng chữ số và không chứa các ký tự đặc biệt"]);
                return DefaultResult();
            }

            if (ExcelUtility.ExcelFunctionNameList.Contains(model.Name.ToUpper().Trim()))
            {
                ModelState.AddModelError("", T["\"{0}\" trùng với tên hàm của Excel. Vui lòng đặt lại tên Trường thông tin.", model.Name]);
                return DefaultResult();
            }

            if (!model.Id.HasValue)
            {
                model.Id = Guid.NewGuid();
                model.IsNew = true;
            }

            if (model.FieldType == FieldType.Calculated && model.DefaultValue.IsNotNullOrEmpty())
            {
                model.IsReadOnly = true;
                //model.ViewHint = "Calculated";
                model.FomularByFieldName = model.DefaultValue;
                var cvfomular_excell = _dynamicFormUtility.GetActualExcelFomula(model.DynamicFormId.Value, model.DefaultValue);
                if (cvfomular_excell.IsNotNullOrEmpty())
                {
                    model.DefaultValue = cvfomular_excell;
                }
            }

            if (model.FieldType == FieldType.GlobalCalculated && model.GlobalCalculatedScriptInfo != null && model.GlobalCalculatedScriptInfo.ExecutedScript.IsNotNullOrEmpty())
            {
                if (model.GlobalCalculatedScriptInfo.DynamicFieldConditions != null && model.GlobalCalculatedScriptInfo.DynamicFieldConditions.Count > 0)
                {
                    bool conditionValid = true;
                    foreach (var condition in model.GlobalCalculatedScriptInfo.DynamicFieldConditions)
                    {
                        if (condition.DynamicFieldName.IsNullOrEmpty())
                        {
                            ModelState.AddModelError("", T["Vui lòng chọn \"Trường thông tin\" cho điều kiện công thức tính."]);
                            conditionValid = false;
                        }
                        if (condition.CompareOperator.IsNullOrEmpty())
                        {
                            ModelState.AddModelError("", T["Vui lòng chọn \"Toán tử\" cho điều kiện công thức tính."]);
                            conditionValid = false;
                        }
                        if (condition.ComparedValue.IsNullOrEmpty())
                        {
                            ModelState.AddModelError("", T["Vui lòng nhập \"Giá trị so sánh\" cho điều kiện công thức tính."]);
                            conditionValid = false;
                        }
                    }
                    if (!conditionValid)
                    {
                        return DefaultResult();
                    }
                }
            }

            if (!(await TypeChoices).ContainsKey(model.DataType))
            {
                ModelState.AddModelError("", T["Kiểu dữ liệu không hợp lệ"]);
                return DefaultResult();
            }

            string oldDynamicFieldName = model.Name;
            if (!model.IsNew)
            {
                var oldDynamicField = await QueryExecutor.ExecuteOneAsync(new GetDynamicFieldByIdQuery { Id = model.Id.Value });
                if (oldDynamicField.Name != model.Name)
                {
                    oldDynamicFieldName = oldDynamicField.Name;
                }
            }

            var fieldWithSameName = (await QueryExecutor.ExecuteManyAsync(new GetDynamicFieldInFormByNameQuery
            {
                FieldId = model.IsNew ? null : model.Id,
                FormId = model.DynamicFormId.Value,
                Name = model.Name
            })).ToList();

            var fieldWithSameOrder = (await QueryExecutor.ExecuteManyAsync(new GetDynamicFieldInFormByOrderQuery
            {
                FieldId = model.IsNew ? null : model.Id,
                FormId = model.DynamicFormId.Value,
                Order = model.Order
            })).ToList();

            if (fieldWithSameOrder.Count > 0)
            {
                ModelState.AddModelError("", T["Thứ tự bị trùng, vui lòng kiểm tra lại."]);
                return DefaultResult();
            }

            if (fieldWithSameName.Count == 0)
            {
                var dynamicFields = (await QueryExecutor.ExecuteManyAsync(new TinyCRM.DynamicForm.Queries.GetDynamicFieldByFormIdQuery { FormId = model.DynamicFormId.Value }));

                Guid representationDynamicFieldId = Guid.NewGuid();

                string representationFieldFormula = string.Empty;
                int dynamicFieldOrder = model.Order;

                if (model.IsNew)
                {
                    if (model.DynamicFieldSectionId.HasValue)
                    {
                        var listDynamicFieldOrder = dynamicFields.Where(x => x.DynamicFieldSectionId == model.DynamicFieldSectionId);
                        if (listDynamicFieldOrder != null && listDynamicFieldOrder.Count() > 0)
                        {
                            dynamicFieldOrder = listDynamicFieldOrder.Max(x => x.Order) + 1;
                        }
                    }
                }

                if (model.FieldType == FieldType.GlobalCalculated)
                {
                    if (model.GlobalCalculatedScriptInfo.ExecutedScript.IsNullOrEmpty())
                    {
                        ModelState.AddModelError("", T["Công thức vùng không được để trống"]);
                        return DefaultResult();
                    }

                    if (!model.GlobalCalculatedScriptInfo.GlobalFormulaBasedOnDynamicFieldId.HasValue)
                    {
                        ModelState.AddModelError("", T["Trường thông tin tính toán không được để trống"]);
                        return DefaultResult();
                    }

                    string globalCalculatedScriptJson = string.Empty;

                    if (model.GlobalCalcFieldByVersion.HasValue && model.GlobalCalcFieldByVersion.Value)
                    {
                        if (model.GlobalCalculatedScriptInfo.GlobalFormulaBasedOnDynamicFieldId.IsNullOrEmpty())
                        {
                            ModelState.AddModelError("", T["Vui lòng chọn Trường thông tin tính toán."]);
                            return DefaultResult();
                        }

                        var basedOnDynamicField = dynamicFields.Where(dfd => dfd.Id == model.GlobalCalculatedScriptInfo.GlobalFormulaBasedOnDynamicFieldId.Value).SingleOrDefault();
                        var representationDynamicFields = dynamicFields.Where(dfd => dfd.RepresentationDynamicFieldId == basedOnDynamicField.RepresentationDynamicFieldId).OrderBy(dfd => dfd.Order).ToList();
                        foreach (var representationDynamicFieldItem in representationDynamicFields)
                        {
                            string fieldName = model.Name, displayName = model.DisplayName;
                            if (representationDynamicFieldItem.VersionCode.IsNotNullOrEmpty())
                            {
                                fieldName += "_" + representationDynamicFieldItem.VersionCode.Replace(".", "");
                                displayName = string.Format("{0} ({1})", model.DisplayName, representationDynamicFieldItem.VersionCode);
                            }

                            Guid dynamicFieldId = Guid.NewGuid();
                            if (representationDynamicFieldItem.RepresentationDynamicFieldId == representationDynamicFieldItem.Id)
                            {
                                dynamicFieldId = representationDynamicFieldId;
                            }

                            model.GlobalCalculatedScriptInfo.GlobalFormulaBasedOnDynamicFieldId = representationDynamicFieldItem.Id;
                            globalCalculatedScriptJson = JsonConvert.SerializeObject(model.GlobalCalculatedScriptInfo);

                            await CommandExecutor.ExecuteAsync(new CreateEditDynamicFieldDefinitionCommand
                            {
                                Id = dynamicFieldId,
                                DataType = model.DataType,
                                Name = fieldName,
                                FieldType = model.GetFieldType(),
                                ViewHint = model.ViewHint,
                                IsNew = model.IsNew,
                                DisplayName = displayName,
                                DefaultValue = model.DefaultValue,
                                SourceFieldName = model.SourceFieldName,
                                Display = true,
                                SourceTableName = model.SourceTableName,
                                DynamicFormId = model.DynamicFormId.Value,
                                Order = dynamicFieldOrder,
                                DynamicDefinedTableSchemaId = model.DynamicDefinedTableSchemaId,
                                IsReadOnly = model.IsReadOnly,
                                IsRequired = model.IsRequired,
                                Validation = (model.Validations.Any()
                                    && model.Validations.First().Validation.IsNotNullOrEmpty()) ?
                                    JsonConvert.SerializeObject(model.Validations)
                                    : null,
                                BusinessValidation = (model.BusinessValidations.Any()
                                    && model.BusinessValidations.First().Validation.IsNotNullOrEmpty()) ?
                                    JsonConvert.SerializeObject(model.BusinessValidations)
                                    : null,
                                AdditionalMetadata = (model.AdditionalMetadatas.Any()
                                    && model.AdditionalMetadatas.First().Key.IsNotNullOrEmpty()) ?
                                    JsonConvert.SerializeObject(model.AdditionalMetadatas)
                                    : null,
                                SelectOptions = model.GetSelectOptions(),
                                AdditionalFilter = model.AdditionalFilter,
                                Mapping360FieldName = model.Mapping360FieldName,
                                Mapping360FieldType = model.Mapping360FieldType,
                                Mapping360RowOptions = model.Mapping360RowOptions,
                                EntityTableName = model.GetEntityTableName(),
                                DynamicFieldSectionId = model.DynamicFieldSectionId,
                                UIFriendlyFormula = model.UIFriendlyFormula,
                                Color = model.Color,
                                BackgroundColor = model.BackgroundColor,
                                FomularByFieldName = model.FomularByFieldName,
                                RepresentationDynamicFieldId = representationDynamicFieldId,
                                VersionCode = representationDynamicFieldItem.VersionCode,
                                PaymentType = model.PaymentType,
                                GlobalCalculatedScriptJson = globalCalculatedScriptJson,
                                IsCheckDuplicate = model.IsCheckDuplicate
                            });

                            dynamicFieldOrder++;
                        }

                        if (model.IsNew)
                        {
                            Info(T["Trường dữ liệu đã được thêm"]);
                        }
                        else
                        {
                            Info(T["Trường dữ liệu đã được cập nhật"]);
                        }
                        return DefaultResult();
                    }
                }
                else
                {
                    if (model.CustomerVersions != null && model.CustomerVersions.Count > 0)
                    {
                        if (model.bodyGrid != null && model.bodyGrid.Count > 0 && model.DynamicFormId.HasValue)
                        {
                            var tableDataDto = new TableDataDto();
                            tableDataDto.headerGrid = model.headerGrid;
                            tableDataDto.ElseValue = model.ElseValue;

                            tableDataDto.bodyGrid = _dynamicFormAppService.RemoveEmptyRow(model.bodyGrid);
                            if (tableDataDto != null && model.headerGrid != null)
                            {
                                model.FomularByFieldName = await _dynamicFormAppService.BuildRuleStatement(tableDataDto, model.DynamicFormId.Value, model.DataType);
                                model.DefaultValue = _dynamicFormAppService.BuildExcelFormula(model.FomularByFieldName, model.DynamicFormId.Value, model.DynamicDefinedTableSchemaId);
                                model.UIFriendlyFormula = JsonConvert.SerializeObject(tableDataDto);
                            }
                        }

                        List<DynamicFieldDefinitionData> representationDynamicFieldList = new List<DynamicFieldDefinitionData>();
                        var preDynamicFieldList = dynamicFields.Where(dfd => dfd.RepresentationDynamicFieldId.IsNotNullOrEmpty()).OrderBy(dfd => dfd.Order).ToList();
                        foreach (var preDynamicField in preDynamicFieldList)
                        {
                            string groupName = string.Format("[[group_{0}]]", preDynamicField.Name.Replace(string.Format("_{0}", preDynamicField.VersionCode.Replace(".", "")), "").Trim());
                            if (model.FomularByFieldName.Contains(groupName, StringComparison.OrdinalIgnoreCase))
                            {
                                if (!representationDynamicFieldList.Exists(dfd => dfd.RepresentationDynamicFieldId == preDynamicField.RepresentationDynamicFieldId))
                                {
                                    representationDynamicFieldList.Add(preDynamicField);
                                }
                            }
                        }

                        string orginFomularByFieldName = model.FomularByFieldName;
                        string orginUIFriendlyFormula = model.UIFriendlyFormula;

                        List<string> orderCustomerVersions = model.CustomerVersions.OrderBy(cv => cv).ToList();
                        foreach (string customerVersion in orderCustomerVersions)
                        {
                            string fomularByFieldNameItem = orginFomularByFieldName;
                            string uiFriendlyFormulaItem = orginUIFriendlyFormula;

                            foreach (var representationDynamicFieldItem in representationDynamicFieldList)
                            {
                                var preDynamicField = dynamicFields.Where(dfd => dfd.RepresentationDynamicFieldId == representationDynamicFieldItem.RepresentationDynamicFieldId && dfd.VersionCode == customerVersion).FirstOrDefault();
                                if (preDynamicField != null)
                                {
                                    string groupName = string.Format("[[group_{0}]]", representationDynamicFieldItem.Name.Replace(string.Format("_{0}", representationDynamicFieldItem.VersionCode.Replace(".", "")), "").Trim());
                                    if (orginFomularByFieldName.Contains(groupName))
                                    {
                                        fomularByFieldNameItem = fomularByFieldNameItem.Replace(groupName, preDynamicField.Name);
                                        uiFriendlyFormulaItem = uiFriendlyFormulaItem.Replace(groupName, preDynamicField.Name);
                                    }
                                }
                            }

                            string fieldName = model.Name + "_" + customerVersion.Replace(".", "");
                            string displayName = string.Format("{0} ({1})", model.DisplayName, customerVersion);

                            representationFieldFormula += fieldName + "+";

                            await CommandExecutor.ExecuteAsync(new CreateEditDynamicFieldDefinitionCommand
                            {
                                Id = Guid.NewGuid(),
                                DataType = model.DataType,
                                Name = fieldName,
                                FieldType = model.GetFieldType(),
                                ViewHint = model.ViewHint,
                                IsNew = model.IsNew,
                                DisplayName = displayName,
                                DefaultValue = model.DefaultValue,
                                SourceFieldName = model.SourceFieldName,
                                Display = true,
                                SourceTableName = model.SourceTableName,
                                DynamicFormId = model.DynamicFormId.Value,
                                Order = dynamicFieldOrder,
                                DynamicDefinedTableSchemaId = model.DynamicDefinedTableSchemaId,
                                IsReadOnly = model.IsReadOnly,
                                IsRequired = model.IsRequired,
                                Validation = (model.Validations.Any()
                                    && model.Validations.First().Validation.IsNotNullOrEmpty()) ?
                                    JsonConvert.SerializeObject(model.Validations)
                                    : null,
                                BusinessValidation = (model.BusinessValidations.Any()
                                    && model.BusinessValidations.First().Validation.IsNotNullOrEmpty()) ?
                                    JsonConvert.SerializeObject(model.BusinessValidations)
                                    : null,
                                AdditionalMetadata = (model.AdditionalMetadatas.Any()
                                    && model.AdditionalMetadatas.First().Key.IsNotNullOrEmpty()) ?
                                    JsonConvert.SerializeObject(model.AdditionalMetadatas)
                                    : null,
                                SelectOptions = model.GetSelectOptions(),
                                AdditionalFilter = model.AdditionalFilter,
                                Mapping360FieldName = model.Mapping360FieldName,
                                Mapping360FieldType = model.Mapping360FieldType,
                                Mapping360RowOptions = model.Mapping360RowOptions,
                                EntityTableName = model.GetEntityTableName(),
                                DynamicFieldSectionId = model.DynamicFieldSectionId,
                                UIFriendlyFormula = uiFriendlyFormulaItem,
                                Color = model.Color,
                                BackgroundColor = model.BackgroundColor,
                                FomularByFieldName = fomularByFieldNameItem,
                                RepresentationDynamicFieldId = representationDynamicFieldId,
                                PaymentType = model.PaymentType,
                                VersionCode = customerVersion,
                                IsCheckDuplicate = model.IsCheckDuplicate
                            });

                            dynamicFieldOrder++;
                        }

                        // Đổi tên, replace lại các FormularByFieldName trước khi rebuild lại Form
                        if (oldDynamicFieldName != model.Name)
                        {
                            _dynamicFormUtility.RebuildFormulaByFieldName(model.DynamicFormId.Value, oldDynamicFieldName, model.Name);
                        }

                        // Rebuild Formula Form luôn. Vì order trong Form có thể thay đổi
                        // Order thay đổi có thể do:
                        // - Thêm field mới vào 1 Section ở giữ
                        // - Đổi Section cho 1 fields
                        // - ...
                        _dynamicFormUtility.BuildFomularInForm(model.DynamicFormId.Value);
                        _dynamicFormUtility.UpdateInjectInForm(model.DynamicFormId.Value);

                        if (model.IsNew)
                        {
                            Info(T["Trường dữ liệu đã được thêm"]);
                        }
                        else
                        {
                            Info(T["Trường dữ liệu đã được cập nhật"]);
                        }
                        return DefaultResult();
                    }
                    else
                    {
                        ModelState.AddModelError("", T["Vui lòng chọn Versions."]);
                        return DefaultResult();
                    }
                }
            }
            Error(T["Trường dữ liệu đã tồn tại"]);
            return DefaultResult();
        }

        [AllowAuthenticated]
        [HttpPost]
        public async Task<IActionResult> SaveDefaultFieldValues(SaveDefaultFieldValuesModel model)
        {
            var dynamicFieldValues = (await QueryExecutor.ExecuteManyAsync(new GetDynamicFieldValueInfoByFormIdQuery
            {
                DynamicFormId = model.DynamicFormId,
                //Display = true
            })).Where(dfd => dfd.FieldType == FieldType.Static || dfd.FieldType == FieldType.Input || dfd.FieldType == FieldType.Linked).ToList();

            var allFormKeys = HttpContext.Request.Form.Keys.ToList();
            dynamicFieldValues = (from dfv in dynamicFieldValues
                                  join k in allFormKeys on dfv.Name equals k
                                  select dfv).ToList();

            if (dynamicFieldValues.Count > 0)
            {
                Dictionary<string, List<string>> validateErrors;
                if (!dynamicFieldValues.TryMapping(HttpContext.Request.Form.ToNameValueCollection(), HttpContext.Request.Form.Files, "", ServiceProvider, out validateErrors))
                {
                    foreach (var errorItem in validateErrors)
                    {
                        ModelState.AddErrors(validateErrors);
                    }
                    return DefaultResult();
                }

                await dynamicFieldValues.SaveEntityDataFieldsAsync(model.DynamicFormId);

                var dynamicFields = (await QueryExecutor.ExecuteManyAsync(new TinyCRM.DynamicForm.Queries.GetDynamicFieldByFormIdQuery { FormId = model.DynamicFormId })).Where(dfd => dfd.FieldType == FieldType.Static || dfd.FieldType == FieldType.Input || dfd.FieldType == FieldType.Linked).ToList();
                foreach (var dynamicField in dynamicFields)
                {
                    var dynamicFieldValue = dynamicFieldValues.Where(dfv => dfv.FieldId == dynamicField.Id).FirstOrDefault();
                    if (dynamicFieldValue != null)
                    {
                        dynamicField.DefaultValue = dynamicFieldValue.Value;
                    }
                }

                await CommandExecutor.ExecuteAsync(new SaveDefaultFieldValuesCommand
                {
                    DynamicFormId = model.DynamicFormId,
                    DynamicFieldDefinitionList = dynamicFields
                });
                Info(T["Đã lưu các giá trị cố định cho các trường thông tin tĩnh"]);
                return DefaultResult();
            }

            return DefaultResult();
        }

        [HttpPost]
        public async Task<IActionResult> DeleteDynamicField(Guid fieldId, Guid formId)
        {
            if (ModelState.IsValid)
            {
                var field = await QueryExecutor.ExecuteOneAsync(new GetDynamicFieldByIdQuery { Id = fieldId });
                var fieldsErr = new List<string>();
                //var relatedCalculate = QueryExecutor.Execute(new GetRelatedCalculatedFieldInFormQuery { FormId = formId, Name = field.Name }).Many;
                //Sử dụng cây công thức check delete Field
                if (!_dynamicFormUtility.ValidateFomulaByCalculatedTree(formId, field.Name, out fieldsErr, 1, string.Empty))
                {
                    Error(T["Trường dữ liệu đang được dùng trong các trường công thức \"{0}\". Vui lòng sửa lại các công thức trước khi xóa", string.Join(";", fieldsErr)]);
                }
                else
                {
                    await CommandExecutor.ExecuteAsync(new DeleteDynamicFieldDefinitionCommand
                    {
                        Id = fieldId
                    });

                    // Xóa field => Làm sai thứ tự các Field
                    // => Cũng cần build lại Form Formula
                    _dynamicFormUtility.BuildFomularInForm(formId);

                    Info(T["Trường dữ liệu đã được xóa"]);
                }
            }
            return DefaultResult();
        }

        [HttpGet]
        public async Task<IActionResult> LoadDynamicFieldList(Guid formId)
        {
            var dynamicFields = (await QueryExecutor.ExecuteManyAsync(new TinyCRM.DynamicForm.Queries.GetDynamicFieldByFormIdQuery { FormId = formId })).ToList();
            for (int i = 0; i < dynamicFields.Count; i++)
            {
                dynamicFields.ToList()[i].Order = i + 1;
            }
            return View("Partials/DynamicFieldList", dynamicFields.Select(x => new DynamicFieldModel
            {
                Name = x.Name,
                ViewHint = x.ViewHint,
                Order = x.Order,
                SourceFieldName = x.SourceFieldName,
                DataType = x.DataType,
                DynamicDefinedTableSchemaId = x.DynamicDefinedTableSchemaId,
                DynamicDefinedTableSchema = x.DynamicDefinedTableSchema,
                DisplayName = x.DisplayName,
                SourceTableName = x.SourceTableName,
                Display = x.Display,
                DefaultValue = x.DefaultValue,
                SelectOptions = x.SelectOptions,
                FieldType = x.FieldType,
                Id = x.Id,
                DynamicFieldSectionId = x.DynamicFieldSectionId,
                DynamicFieldSectionName = x.DynamicFieldSectionName
            }).ToList());
        }

        [AllowAuthenticated]
        [HttpGet]
        public async Task<IActionResult> LoadColumnFormulaForDynamicTableField(Guid dynamicFieldId)
        {
            var dynamicField = await QueryExecutor.ExecuteOneAsync(new GetDynamicFieldByIdQuery { Id = dynamicFieldId });
            if (dynamicField != null && dynamicField.DynamicDefinedTableSchemaId.IsNotNullOrEmpty())
            {
                Dictionary<string, string> externalTableFormula = new Dictionary<string, string>();
                try
                {
                    externalTableFormula = JsonConvert.DeserializeObject<Dictionary<string, string>>(dynamicField.DefaultValue);
                }
                catch (Exception)
                {
                }

                var columnList = (await QueryExecutor.ExecuteManyAsync(new GetDynamicDefinedTableColumnListByTableSchemaQuery { DynamicDefinedTableSchemaId = dynamicField.DynamicDefinedTableSchemaId.Value })).ToList();
                foreach (var column in columnList)
                {
                    column.DefaultValue = string.Empty;
                    if (externalTableFormula.ContainsKey(column.Name))
                    {
                        column.DefaultValue = externalTableFormula[column.Name];
                    }
                }
                return View("Partials/ColumnFormulaForDynamicTableField", columnList);
            }
            return View("Partials/ColumnFormulaForDynamicTableField", null);
        }

        [AllowAuthenticated]
        [HttpPost]
        public async Task<IActionResult> SaveColumnFormulaForDynamicTableField(SaveColumnFormulaForDynamicTableFieldModel model)
        {
            var dynamicField = await QueryExecutor.ExecuteOneAsync(new GetDynamicFieldByIdQuery { Id = model.DynamicFieldId });
            if (dynamicField != null && dynamicField.DynamicDefinedTableSchemaId.IsNotNullOrEmpty())
            {
                if (model.Columns != null && model.Columns.Count > 0)
                {
                    Dictionary<string, string> externalTableFormula = new Dictionary<string, string>();
                    foreach (var column in model.Columns)
                    {
                        if (column.DefaultValue.IsNotNullOrEmpty())
                        {
                            string defaultValue = column.DefaultValue.Trim();
                            if (defaultValue.IsNotNullOrEmpty())
                            {
                                externalTableFormula.Add(column.Name, defaultValue);
                            }
                        }
                    }
                    if (externalTableFormula.Count > 0)
                    {
                        string jsonFormulaList = JsonConvert.SerializeObject(externalTableFormula);
                        await CommandExecutor.ExecuteAsync(new UpdateDynamicFieldDefinitionDefaultValueCommand
                        {
                            Id = dynamicField.Id,
                            DefaultValue = jsonFormulaList
                        });
                    }
                }
            }
            return DefaultResult();
        }

        [AllowAuthenticated]
        [HttpPost]
        public async Task<IActionResult> DeleteRepresentationDynamicField(Guid representationDynamicFieldId, Guid formId)
        {
            var fieldsErr = new List<string>();
            var dynamicRepresentationFields = await QueryExecutor.ExecuteManyAsync(new GetDynamicRepresentationFieldQuery { RepresentationDynamicFieldId = representationDynamicFieldId });
            foreach (var dynamicField in dynamicRepresentationFields)
            {
                if (!_dynamicFormUtility.ValidateFomulaByCalculatedTree(formId, dynamicField.Name, out fieldsErr, 1, string.Empty))
                {
                    Error(T["Trường dữ liệu đang được dùng trong các trường công thức \"{0}\". Vui lòng sửa lại các công thức trước khi xóa", string.Join(";", fieldsErr)]);
                    return DefaultResult();
                }
            }

            await CommandExecutor.ExecuteAsync(new DeleteRepresentationDynamicFieldCommand { RepresentationDynamicFieldId = representationDynamicFieldId });
            _dynamicFormUtility.BuildFomularInForm(formId);

            Info(T["Trường dữ liệu đã được xóa"]);

            return DefaultResult();
        }

        [AllowAuthenticated]
        [HttpGet]
        public async Task<IActionResult> LoadGlobalCalculatedScriptParams(Guid dynamicFormId, Guid? dynamicFieldId, string executedScript, int dynamicFieldOrder)
        {
            GlobalCalculatedScriptInfo globalCalculatedScriptInfoModel = new GlobalCalculatedScriptInfo();
            globalCalculatedScriptInfoModel.DynamicFormId = dynamicFormId;
            globalCalculatedScriptInfoModel.DynamicFieldOrder = dynamicFieldOrder;

            List<GlobalCalculatedScriptInfo> globalCalculatedScriptInfoList = new List<GlobalCalculatedScriptInfo>();
            string globalCalculatedScriptInfoListString = Configuration.GetValue<string>("globalcalculated.scriptinfo.list");
            if (globalCalculatedScriptInfoListString.IsNotNullOrEmpty())
            {
                globalCalculatedScriptInfoList = JsonConvert.DeserializeObject<List<GlobalCalculatedScriptInfo>>(globalCalculatedScriptInfoListString);
            }

            foreach (var globalCalculatedScriptInfo in globalCalculatedScriptInfoList)
            {
                if (globalCalculatedScriptInfo.ExecutedScript.IsEqualIgnoreCase(executedScript))
                {
                    globalCalculatedScriptInfoModel = globalCalculatedScriptInfo;
                }
            }

            if (dynamicFieldId.IsNotNullOrEmpty())
            {
                var dynamicField = await QueryExecutor.ExecuteOneAsync(new GetDynamicFieldByIdQuery { Id = dynamicFieldId.Value });
                if (dynamicField != null)
                {
                    globalCalculatedScriptInfoModel.DynamicFieldOrder = dynamicField.Order;
                    if (dynamicField.GlobalCalculatedScriptJson.IsNotNullOrEmpty())
                    {
                        try
                        {
                            GlobalCalculatedScriptInfo fieldGlobalCalculatedScriptInfo = new GlobalCalculatedScriptInfo();
                            fieldGlobalCalculatedScriptInfo = JsonConvert.DeserializeObject<GlobalCalculatedScriptInfo>(dynamicField.GlobalCalculatedScriptJson);
                            foreach (var param in globalCalculatedScriptInfoModel.Parameters)
                            {
                                var fieldParam = fieldGlobalCalculatedScriptInfo.Parameters.Where(p => p.Name == param.Name).FirstOrDefault();
                                if (fieldParam != null)
                                {
                                    param.Value = fieldParam.Value;
                                    //param.ViewHint = fieldParam.ViewHint;
                                }
                            }
                        }
                        catch { }
                    }
                    globalCalculatedScriptInfoModel.DynamicFormId = dynamicField.DynamicFormId;
                }
            }
            else
            {
                globalCalculatedScriptInfoModel.DynamicFormId = dynamicFormId;
            }

            return View("Partials/GlobalCalculatedScriptParameters", globalCalculatedScriptInfoModel);
        }

        [HttpGet]
        public async Task<IActionResult> LoadStaticFieldList(Guid formId, bool? isversion, bool? isallfield)
        {
            var businessSettings = StaticContractFieldList;
            var listStaticFieldsInSettings = string.IsNullOrEmpty(businessSettings) ? new List<StaticFieldGroup>()
                : JsonConvert.DeserializeObject<List<StaticFieldGroup>>(businessSettings);
            var dynamicFields = await QueryExecutor.ExecuteManyAsync(new TinyCRM.DynamicForm.Queries.GetDynamicFieldByFormIdQuery { FormId = formId });
            var staticFieldGroup = new List<StaticFieldGroup>();
            if (isallfield.HasValue && isallfield.Value)
            {
                staticFieldGroup = listStaticFieldsInSettings.Select(x => new StaticFieldGroup
                {
                    Name = x.Name,
                    StaticFieldList =
                      x.StaticFieldList.ToList(),
                }).ToList();
            }
            else
            {
                staticFieldGroup = listStaticFieldsInSettings.Select(x => new StaticFieldGroup
                {
                    Name = x.Name,
                    StaticFieldList =
                      x.StaticFieldList.Where(
                       y =>
                          !dynamicFields.Any(
                             z =>
                               z.SourceFieldName == y.SourceFieldName && z.SourceTableName == y.SourceTableName)).ToList(),
                }).ToList();
            }
            var getisversion = false;
            var hideFields = false;
            if (isversion.HasValue)
            {
                getisversion = isversion.Value;
                hideFields = true;
            }
            else
            {
                ViewBag.GetIsVersion = false;
            }
            ViewBag.GetIsVersion = getisversion;
            ViewBag.HideFields = hideFields;
            return View("Partials/StaticFieldList", staticFieldGroup);
        }

        [HttpPost]
        public async Task<IActionResult> LoadDocumentFieldList(Guid formId, IEnumerable<string> name)
        {
            var mappingNameList = name == null ? new List<string>() : name.ToList();
            var dynamicForm = await QueryExecutor.ExecuteOneAsync(new GetDynamicFormByIdQuery { Id = formId });
            if (dynamicForm.DisplayDocumentFileId.HasValue)
            {
                var dynamicFields = await QueryExecutor.ExecuteManyAsync(new TinyCRM.DynamicForm.Queries.GetDynamicFieldByFormIdQuery { FormId = formId });
                var listFields = (await QueryExecutor.ExecuteManyAsync(new GetAllVariableOfSaleTemplateQuery { FileId = dynamicForm.DisplayDocumentFileId.Value }))
                    .Where(x => dynamicFields.All(y => y.Name != x))
                    .Select(x => new DocumentFieldModel
                    {
                        Name = x,
                        IsMapping = mappingNameList.Contains(x),
                    }).ToList();
                return View("Partials/DocumentFieldList", listFields);
            }
            return DefaultResult();
        }

        [HttpGet]
        public async Task<IActionResult> PreviewDataStruct(Guid dynamicFormId)
        {
            var dynamicFieldList = await _dynamicFormAppService.GetDynamicFieldValueInfoByFormId(dynamicFormId, false);
            var data = await QueryExecutor.ExecuteManyAsync(new PreviewDataStructWithDynamicTableQuery
            {
                DynamicFormId = dynamicFormId,
                DynamicFieldValueInfo = dynamicFieldList
            });
            return File(data.ToArray(), MediaTypeNames.Application.Octet, "Template.xls");
        }

        [AllowAuthenticated]
        [HttpPost]
        public async Task<IActionResult> UploadFormula(UploadFormulaModel model)
        {
            if (model.FormulaExcelFile != null && model.FormulaExcelFile.Length > 0)
            {
                using (var memoryStream = new MemoryStream())
                {
                    await model.FormulaExcelFile.CopyToAsync(memoryStream);
                    byte[] buffer = memoryStream.ToArray();

                    SpreadsheetGear.IWorkbookSet workbookSet = SpreadsheetGear.Factory.GetWorkbookSet();
                    SpreadsheetGear.IWorkbook workbook = workbookSet.Workbooks.OpenFromMemory(buffer);

                    var dynamicFieldList = (from df in await QueryExecutor.ExecuteManyAsync(new TinyCRM.DynamicForm.Queries.GetDynamicFieldByFormIdQuery { FormId = model.DynamicFormId })
                                            orderby df.Order
                                            select df).ToList();

                    var dynamicTableFieldList = dynamicFieldList.Where(f => f.DynamicDefinedTableSchemaId.HasValue && f.DynamicDefinedTableSchemaId.Value != Guid.Empty).OrderBy(f => f.Order).ToList();

                    SpreadsheetGear.IWorksheet tempCalculatedWorksheet = workbook.Worksheets["__calc_template__"];
                    if (tempCalculatedWorksheet != null)
                    {
                        SpreadsheetGear.IRange tempCalculatedCells = tempCalculatedWorksheet.Cells;
                        foreach (var dynamicField in dynamicFieldList)
                        {
                            dynamicField.Inject = string.Empty;
                            if (dynamicField.FieldType == FieldType.Calculated)
                            {
                                dynamicField.RequiredDependency = string.Empty;
                            }
                        }

                        foreach (var dynamicField in dynamicFieldList)
                        {
                            var dataType = dynamicField.DataType.ToType();
                            if (!dataType.IsGridData())
                            {
                                if (dynamicField.FieldType == FieldType.Calculated)
                                {
                                    var cell = tempCalculatedCells["B" + dynamicField.Order];
                                    if (cell.Formula.IsNotNullOrEmpty())
                                    {
                                        dynamicField.DefaultValue = cell.Formula;

                                        foreach (var dynamicFieldInject in dynamicFieldList)
                                        {
                                            string injectExcelCellName = "B" + dynamicFieldInject.Order;
                                            if (dynamicField.DefaultValue.Contains(injectExcelCellName))
                                            {
                                                dynamicFieldInject.Inject = dynamicFieldInject.Inject.IsNullOrEmpty() ? dynamicField.Name : dynamicFieldInject.Inject + ";" + dynamicField.Name;
                                                dynamicField.RequiredDependency = dynamicField.RequiredDependency.IsNullOrEmpty() ? dynamicFieldInject.Name : dynamicField.RequiredDependency + ";" + dynamicFieldInject.Name;
                                            }
                                        }
                                    }
                                }
                            }
                            else
                            {
                                if (dataType.IsUserDefinedTableGridData())
                                {
                                    SpreadsheetGear.IWorksheet dynamicTableWorksheet = workbook.Worksheets[dynamicField.Name];
                                    if (dynamicTableWorksheet != null)
                                    {
                                        var columnList = (await QueryExecutor.ExecuteManyAsync(new GetDynamicDefinedTableColumnListByTableSchemaQuery { DynamicDefinedTableSchemaId = dynamicField.DynamicDefinedTableSchemaId.Value })).ToList();

                                        foreach (var column in columnList)
                                        {
                                            column.Inject = string.Empty;
                                            if (column.ColumnType == DynamicDefinedTableColumnType.Calculated)
                                            {
                                                column.RequiredDependency = string.Empty;
                                            }
                                        }

                                        SpreadsheetGear.IRange dynamicTableCells = dynamicTableWorksheet.Cells;
                                        Dictionary<string, string> externalTableFormula = new Dictionary<string, string>();

                                        int columnOrder = 1;
                                        foreach (var column in columnList)
                                        {
                                            if (column.ColumnType == DynamicDefinedTableColumnType.Calculated)
                                            {
                                                string excelColumnName = GetExcelColumnName(columnOrder) + "2";

                                                var cell = dynamicTableCells[excelColumnName];
                                                if (cell.Formula.IsNotNullOrEmpty())
                                                {
                                                    column.UiHint = "Calculated";

                                                    bool isExternalTableFormula = false;
                                                    if (cell.Formula.Contains("__calc_template__!"))
                                                    {
                                                        isExternalTableFormula = true;
                                                    }
                                                    if (isExternalTableFormula == false)
                                                    {
                                                        foreach (var dynamicTableField in dynamicTableFieldList)
                                                        {
                                                            if (cell.Formula.Contains(dynamicTableField.Name + "!"))
                                                            {
                                                                isExternalTableFormula = true;
                                                            }
                                                        }
                                                    }

                                                    if (isExternalTableFormula == false)
                                                    {
                                                        column.DefaultValue = cell.Formula;

                                                        int injectColumnOrder = 1;
                                                        foreach (var columnInject in columnList)
                                                        {
                                                            string injectExcelCellName = GetExcelColumnName(injectColumnOrder) + "2";
                                                            if (column.DefaultValue.Contains(injectExcelCellName))
                                                            {
                                                                columnInject.Inject = columnInject.Inject.IsNullOrEmpty() ? column.Name : columnInject.Inject + ";" + column.Name;
                                                                column.RequiredDependency = column.RequiredDependency.IsNullOrEmpty() ? columnInject.Name : column.RequiredDependency + ";" + columnInject.Name;
                                                            }
                                                            injectColumnOrder++;
                                                        }
                                                    }
                                                    else
                                                    {
                                                        externalTableFormula.Add(column.Name, cell.Formula);
                                                    }
                                                }
                                            }
                                            columnOrder++;
                                        }

                                        if (externalTableFormula.Count > 0)
                                        {
                                            dynamicField.DefaultValue = JsonConvert.SerializeObject(externalTableFormula);
                                        }

                                        await CommandExecutor.ExecuteAsync(new SaveColumnFormulaListCommand { DynamicDefinedTableSchemaId = dynamicField.DynamicDefinedTableSchemaId.Value, DynamicDefinedTableColumnList = columnList });
                                    }
                                }
                            }
                        }

                        await CommandExecutor.ExecuteAsync(new SaveDynamicFieldFormulaListCommand { DynamicFormId = model.DynamicFormId, DynamicFieldDefinitionList = dynamicFieldList });
                        Info(T["Công thức đã được cập nhật"]);
                    }
                }
            }
            else
            {
                Error(T["Vui lòng upload Excel công thức."]);
            }

            return DefaultResult();
        }

        [HttpGet]
        public IActionResult ExportWithTemplate(Guid dynamicFormValueId)
        {
            return DefaultResult();
        }

        [HttpGet]
        [NoCache]
        [AllowAuthenticated]
        public async Task<IActionResult> SearchDropdownList()
        {
            var result = await QueryExecutor.ExecuteAsync(new GetDynamicFormQuery
            {
                Pagination = new Pagination(0, int.MaxValue)
            });
            return Json(result.Many.Select(x => new { Value = x.Id, Text = x.Name }));
        }

        [HttpGet]
        [NoCache]
        [AllowAuthenticated]
        public async Task<IActionResult> GetDynamicDefinedTableSchemaListDropdownList()
        {
            var userDefinedTableList = (await QueryExecutor.ExecuteManyAsync(new GetDynamicDefinedTableSchemaQuery { })).OrderBy(tbl => tbl.DisplayName);
            return Json(userDefinedTableList.Select(x => new { Value = x.Id, Text = x.DisplayName }));
        }

        [HttpPost]
        [AllowAuthenticated]
        public async Task<IActionResult> DeleteDynamicForm(Guid id)
        {
            var command = new DeleteDynamicFormCommand { Id = id };
            await CommandExecutor.ExecuteAsync(command);
            return Json(new { Success = true });
        }

        [HttpPost]
        [AllowAuthenticated]
        public async Task<IActionResult> GetRefObject(Guid id)
        {
            return View("Partials/RefObjectList", await QueryExecutor.ExecuteManyAsync(new GetRefObjectOfDynamicFormQuery { Url = Url, DynamicFormId = id }));
        }

        [HttpPost]
        [AllowAuthenticated]
        public IActionResult GetMapping360ViewOptions()
        {
            var mapping360FieldLists = JsonConvert.DeserializeObject<List<Mapping360DefinitionGroup>>(Mapping360FieldList);
            return Json(mapping360FieldLists);
        }

        [AllowAuthenticated]
        [HttpGet]
        public async Task<IActionResult> GetDataTypeList()
        {
            var typeChoices = await GetTypeChoicesAsync();
            return Json(typeChoices.Select(x => new { Value = x.Key, Text = x.Value }));
        }

        [AllowAuthenticated]
        [HttpGet]
        public IActionResult GetOperatorTypes()
        {
            return Json(OperatorType.Select(x => new { Value = x.Key, Text = x.Value }));
        }

        [AllowAuthenticated]
        [HttpGet]
        public async Task<IActionResult> GetViewHintList(string type = "", FieldType? fieldType = null)
        {
            if (fieldType == FieldType.Calculated)
            {
                return Json(new[]
                {
                    new
                    {
                        Value = FieldType.Calculated.ToString(),
                        Text = FieldType.Calculated.GetText()
                    },
                    new
                    {
                        Value = "CalculatedxQRCode",
                        Text = "Calculated x QRCode"
                    }
                });
            }
            if (fieldType == FieldType.GlobalCalculated)
            {
                return Json(new[]
                {
                    new
                    {
                        Value = FieldType.GlobalCalculated.ToString(),
                        Text = FieldType.GlobalCalculated.GetText()
                    }
                });
            }
            var typeInputChoices = (await TypeInputChoices);
            if (fieldType == FieldType.Static)
            {
                if (!string.IsNullOrEmpty(type))
                {
                    if (typeInputChoices.ContainsKey(type))
                    {
                        var availableViewHint = typeInputChoices[type];
                        return Json(availableViewHint.Select(x => new { Value = "Static_" + x.Item1, Text = x.Item2 }));
                    }
                }
            }
            if (!string.IsNullOrEmpty(type))
            {
                if (typeInputChoices.ContainsKey(type))
                {
                    var availableViewHint = typeInputChoices[type];
                    return Json(availableViewHint.Select(x => new { Value = x.Item1, Text = x.Item2 }));
                }
            }
            return Json(new { });
        }

        private string GetExcelColumnName(int columnNumber)
        {
            int dividend = columnNumber;
            string columnName = String.Empty;
            int modulo;

            while (dividend > 0)
            {
                modulo = (dividend - 1) % 26;
                columnName = Convert.ToChar(65 + modulo).ToString() + columnName;
                dividend = (int)((dividend - modulo) / 26);
            }

            return columnName;
        }

        [AllowAuthenticated]
        [HttpGet]
        public async Task<IActionResult> GetAllDynamicFieldInForm(Guid dynamicFormId, bool includedDynamicTable = true, bool versioningCalcField = false)
        {
            var dynamicFields = (await QueryExecutor.ExecuteManyAsync(new TinyCRM.DynamicForm.Queries.GetDynamicFieldByFormIdQuery { FormId = dynamicFormId })).OrderBy(dfd => dfd.Order).ToList();
            List<DropdownSelect2Item> result = new List<DropdownSelect2Item>();

            Guid representationDynamicFieldId = Guid.Empty;
            foreach (var item in dynamicFields)
            {
                if (versioningCalcField)
                {
                    if (item.RepresentationDynamicFieldId.IsNotNullOrEmpty() && item.RepresentationDynamicFieldId != representationDynamicFieldId)
                    {
                        string groupDisplayName = T["Nhóm"] + " :: " + item.DisplayName.Replace(string.Format("({0})", item.VersionCode), "").Trim();
                        string groupName = string.Format("[[group_{0}]]", item.Name.Replace(string.Format("_{0}", item.VersionCode.Replace(".", "")), "").Trim());

                        result.Add(new DropdownSelect2Item { text = groupDisplayName, id = groupName, DataType = item.DataType });

                        representationDynamicFieldId = item.RepresentationDynamicFieldId.Value;
                    }
                }

                result.Add(new DropdownSelect2Item { text = item.DisplayName, id = item.Name, DataType = item.DataType });
            }
            if (includedDynamicTable)
            {
                var dynamicTable = dynamicFields.Where(x => x.DynamicDefinedTableSchemaId != null);
                if (dynamicTable != null)
                {
                    foreach (var item in dynamicTable)
                    {
                        var columns = (await QueryExecutor.ExecuteManyAsync(new GetDynamicDefinedTableColumnListByTableSchemaQuery { DynamicDefinedTableSchemaId = item.DynamicDefinedTableSchemaId.Value })).ToList();
                        for (int i = 0; i < columns.Count; i++)
                        {
                            result.Add(new DropdownSelect2Item { text = item.DisplayName + "." + columns[i].DisplayName, id = item.Name + "." + columns[i].Name, Color = columns[i].Color, DataType = columns[i].DataType });
                        }
                    }
                }
            }

            var systemDynamicFieldList = (await QueryExecutor.ExecuteManyAsync(new TinyCRM.DynamicForm.Queries.GetDynamicFieldByFormIdQuery { FormId = DynamicFormContants.SystemDynamicFormId })).ToList();
            foreach (var systemDynamicField in systemDynamicFieldList)
            {
                if (systemDynamicField.DynamicDefinedTableSchemaId.IsNullOrEmpty())
                {
                    result.Add(new DropdownSelect2Item { text = T["Trường hệ thống: "] + systemDynamicField.DisplayName, id = "__SysDF__" + systemDynamicField.Name, DataType = systemDynamicField.DataType });
                }
                else
                {
                    if (includedDynamicTable)
                    {
                        var columns = (await QueryExecutor.ExecuteManyAsync(new GetDynamicDefinedTableColumnListByTableSchemaQuery { DynamicDefinedTableSchemaId = systemDynamicField.DynamicDefinedTableSchemaId.Value })).ToList();
                        foreach (var column in columns)
                        {
                            result.Add(new DropdownSelect2Item { text = T["Trường hệ thống: "] + systemDynamicField.DisplayName + "." + column.DisplayName, id = "__SysDF__" + systemDynamicField.Name + "." + column.Name, DataType = column.DataType });
                        }
                    }
                }
            }

            return Json(result);
        }

        [AllowAuthenticated]
        [HttpGet]
        public async Task<IActionResult> InputEditorForDynamicField(Guid dynamicFieldId, string newFieldName)
        {
            List<DynamicFieldValueInfo> dynamicFieldList = new List<DynamicFieldValueInfo>();

            DynamicFieldDefinitionData dynamicField = await QueryExecutor.ExecuteOneAsync(new GetDynamicFieldByIdQuery { Id = dynamicFieldId });
            if (dynamicField != null)
            {
                DynamicFieldValueInfo dynamicFieldValueInfo = DynamicFieldHelper.GetGlobalConditionComparedValueInput(dynamicField, newFieldName, Mapper);

                dynamicFieldList.Add(dynamicFieldValueInfo);
            }

            dynamicFieldList.MapTypeFromExtended();
            object dynamicFormValue = await dynamicFieldList.ConvertToDynamicObjectAsync(ServiceProvider, "DynamicFormModel", "Dynamic");

            return View("Partials/InputEditorForDynamicField", dynamicFormValue);
        }

        [HttpGet]
        [AllowAuthenticated]
        public async Task<IActionResult> GetSelectDynamicField(string type)
        {
            if (ModelState.IsValid)
            {
                var resultList = await QueryExecutor.ExecuteManyAsync(new GetInfoListByTypeQuery { TypeName = type });
                return Json(resultList.Select(x => new DropdownListItem
                {
                    Text = x.Name,
                    Value = x.Name
                }));
            }
            return DefaultResult();
        }

        [HttpGet]
        [AllowAuthenticated]
        public async Task<IActionResult> GetGroupSelectSystemDynamicField(string tablename, string columngroup, string columnid, string columntext)
        {
            if (ModelState.IsValid)
            {
                var tableForSelectGroup = new List<TableForSelectGroup>();
                var dynamicFields = await QueryExecutor.ExecuteManyAsync(new TinyCRM.DynamicForm.Queries.GetDynamicFieldByFormIdQuery { FormId = DynamicFormContants.SystemDynamicFormId });
                var focusTable = dynamicFields.FirstOrDefault(x => x.Name == tablename);
                if (focusTable != null)
                {
                    if (focusTable.ViewHint == "DynamicDefinedTable")
                    {
                        try
                        {
                            var columns = (await QueryExecutor.ExecuteManyAsync(new GetDynamicDefinedTableColumnListByTableSchemaQuery { DynamicDefinedTableSchemaId = focusTable.DynamicDefinedTableSchemaId.Value })).ToList();
                            var group_colId = columns.FirstOrDefault(x => x.Name == columngroup).Id;
                            var id_colId = columns.FirstOrDefault(x => x.Name == columnid).Id;
                            var text_colId = columns.FirstOrDefault(x => x.Name == columntext).Id;

                            var recordDataList_1 = (await QueryExecutor.ExecuteManyAsync(new GetDynamicDefinedTableCellValueListByDynamicFieldValueIdQuery { DynamicFieldValueId = Guid.Parse(focusTable.DefaultValue), DynamicDefinedTableSchemaId = focusTable.DynamicDefinedTableSchemaId.Value })).ToList();
                            var recordDataList_2 = (await QueryExecutor.ExecuteManyAsync(new GetDynamicDefinedTableCellValueListByDynamicFieldValueIdQuery { DynamicFieldValueId = Guid.Parse(focusTable.DefaultValue), DynamicDefinedTableSchemaId = focusTable.DynamicDefinedTableSchemaId.Value })).ToList();
                            foreach (var value_item in recordDataList_1.OrderBy(x => x.RowNumber).Where(x => x.DynamicDefinedTableColumnId == id_colId))
                            {
                                var item_group = recordDataList_2.FirstOrDefault(x => x.DynamicDefinedTableColumnId == group_colId && x.RowNumber == value_item.RowNumber).Value;
                                var item_id = recordDataList_2.FirstOrDefault(x => x.DynamicDefinedTableColumnId == id_colId && x.RowNumber == value_item.RowNumber).Value;
                                var item_text = recordDataList_2.FirstOrDefault(x => x.DynamicDefinedTableColumnId == text_colId && x.RowNumber == value_item.RowNumber).Value;
                                tableForSelectGroup.Add(new TableForSelectGroup { groupname = item_group, value = item_id, text = item_text });
                            }

                            var groupDict = new Dictionary<string, SelectGroupItem>();
                            foreach (var item in tableForSelectGroup)
                            {
                                SelectGroupItem groupItem;
                                if (!groupDict.TryGetValue(item.groupname, out groupItem))
                                {
                                    groupItem = new SelectGroupItem
                                    {
                                        id = item.value, // or any other logic to assign the ID
                                        text = item.groupname,
                                        children = new List<ChildItem>()
                                    };
                                    groupDict[item.groupname] = groupItem;
                                }
                                groupItem.children.Add(new ChildItem
                                {
                                    id = item.value, // assuming value is unique and can be used as ID
                                    text = item.text,
                                    parentid = groupItem.id // this will link the child to the parent group
                                });
                            }
                            var resultList = groupDict.Values.ToList();

                            foreach (var item in resultList)
                            {
                                if (item.children != null && item.children.Count == 0)
                                {
                                    item.children = null;
                                }
                            }
                            foreach (var item in resultList)
                            {
                                if (item.children != null)
                                {
                                    item.id = null;
                                }
                            }

                            var settings = new JsonSerializerSettings();
                            settings.NullValueHandling = NullValueHandling.Ignore;
                            settings.DefaultValueHandling = DefaultValueHandling.Ignore;

                            var result = JsonConvert.SerializeObject(resultList, settings);
                            return Json(result);
                        }
                        catch
                        {
                            Error(string.Format(T["Cấu hình Group Select sai ở các tham số tablename: {0}, columngroup: {1}, columnid: {2}, columntext: {3} ."], tablename, columngroup, columnid, columntext));
                            return DefaultResult();
                        }
                    }
                    else
                    {
                        Error(T["Không phải là table type"]);
                        return DefaultResult();
                    }
                }
                else
                {
                    Error(T["Không tìm thấy tham số"]);
                    return DefaultResult();
                }
            }
            return DefaultResult();
        }

        [HttpGet]
        [AllowAuthenticated]
        public async Task<IActionResult> GetSelectSystemDynamicField(string tablename, string columnvalue, string columntext)
        {
            if (ModelState.IsValid)
            {
                List<DropdownListItem> resultList = new List<DropdownListItem>();
                var dynamicFields = await QueryExecutor.ExecuteManyAsync(new TinyCRM.DynamicForm.Queries.GetDynamicFieldByFormIdQuery { FormId = DynamicFormContants.SystemDynamicFormId });
                var focusTable = dynamicFields.FirstOrDefault(x => x.Name == tablename);
                if (focusTable != null)
                {
                    if (focusTable.ViewHint == "DynamicDefinedTable")
                    {
                        if (focusTable.DefaultValue.IsNullOrEmpty())
                        {
                            Error(string.Format(T["Table: {0}, chưa được lưu giá trị mặc định"], tablename));
                            return DefaultResult();
                        }

                        try
                        {
                            var columns = (await QueryExecutor.ExecuteManyAsync(new GetDynamicDefinedTableColumnListByTableSchemaQuery { DynamicDefinedTableSchemaId = focusTable.DynamicDefinedTableSchemaId.Value })).ToList();
                            var value_colId = columns.FirstOrDefault(x => x.Name == columnvalue).Id;
                            var text_colId = columns.FirstOrDefault(x => x.Name == columntext).Id;
                            var recordDataList_1 = (await QueryExecutor.ExecuteManyAsync(new GetDynamicDefinedTableCellValueListByDynamicFieldValueIdQuery { DynamicFieldValueId = Guid.Parse(focusTable.DefaultValue), DynamicDefinedTableSchemaId = focusTable.DynamicDefinedTableSchemaId.Value })).ToList();
                            var recordDataList_2 = (await QueryExecutor.ExecuteManyAsync(new GetDynamicDefinedTableCellValueListByDynamicFieldValueIdQuery { DynamicFieldValueId = Guid.Parse(focusTable.DefaultValue), DynamicDefinedTableSchemaId = focusTable.DynamicDefinedTableSchemaId.Value })).ToList();
                            foreach (var value_item in recordDataList_1.OrderBy(x => x.RowNumber).Where(x => x.DynamicDefinedTableColumnId == value_colId))
                            {
                                var text_item = recordDataList_2.FirstOrDefault(x => x.DynamicDefinedTableColumnId == text_colId && x.RowNumber == value_item.RowNumber);
                                resultList.Add(new DropdownListItem { Value = string.Format("{0}", value_item.Value), Text = string.Format("{0}", text_item.Value) });
                            }
                        }
                        catch
                        {
                            Error(string.Format(T["Cấu hình Select sai ở các tham số tablename: {0}, columnvalue: {1}, columntext: {2}"], tablename, columnvalue, columntext));
                            return DefaultResult();
                        }
                    }
                    else
                    {
                        Error(string.Format(T["Table: {0}, không phải là kiểu table"], tablename));
                        return DefaultResult();
                    }
                }
                else
                {
                    Error(string.Format(T["Table: {0}, không tồn tại trong hệ thống"], tablename));
                    return DefaultResult();
                }
                return Json(resultList);
            }
            return DefaultResult();
        }


        [HttpGet]
        [AllowAuthenticated]
        public async Task<IActionResult> GetInfoListUserProfile()
        {
            // Default search all: SearchInvalid false, IsCUStation false
            var resultList = await QueryExecutor.ExecuteOneAsync(new SearchUsersQuery { });

            return Json(resultList.Items.Select(x => new DropdownListItem
            {
                Text = x.FullName,
                Value = x.FullName
            }));

        }

        [HttpGet]
        [AllowAuthenticated]
        public async Task<IActionResult> GetInfoListDropdownList(string type)
        {
            if (ModelState.IsValid)
            {
                var resultList = await QueryExecutor.ExecuteManyAsync(new GetInfoListByTypeQuery { TypeName = type });
                return Json(resultList.Select(x => new DropdownListItem
                {
                    Text = x.DisplayName,
                    Value = x.Name
                }));
            }
            return DefaultResult();
        }

        [HttpGet]
        [NoCache]
        [AllowAuthenticated]
        public async Task<IActionResult> GetDynamicFormDropdownList(Guid? ticketId, int type = 0)
        {
            var resultList = (await QueryExecutor.ExecuteManyAsync(new GetDynamicFormByEntityLinkTicketQuery { RequestTicketId = ticketId, Type = type })).Select(x => new
            {
                Id = x.Id,
                Text = string.Format("{0} - {1}", x.Code, x.Name)
            }).ToList();
            return Json(resultList);

        }

        [HttpGet]
        [NoCache]
        [AllowAuthenticated]
        public async Task<IActionResult> LoadCustomerVersions(Guid dynamicFormId)
        {
            if (DynamicFormCustomerVersionFilterStaticDynamicFieldName.IsNotNullOrEmpty())
            {
                var customerVersionListStaticDynamicFieldName = await QueryExecutor.ExecuteOneAsync(new GetDynamicFieldByFormIdAndNameQuery { FormId = dynamicFormId, Name = DynamicFormCustomerVersionFilterStaticDynamicFieldName });
                if (customerVersionListStaticDynamicFieldName != null)
                {
                    if (customerVersionListStaticDynamicFieldName.DefaultValue.IsNotNullOrEmpty())
                    {
                        try
                        {
                            List<string> versionList = JsonConvert.DeserializeObject<List<string>>(customerVersionListStaticDynamicFieldName.DefaultValue);
                            List<DropdownListItem> resultListByStaticField = versionList.OrderBy(vr => vr).Select(vr => new DropdownListItem { Text = vr, Value = vr }).ToList();

                            return Json(resultListByStaticField);
                        }
                        catch { }
                    }
                }
            }

            var nameVersionList = (await QueryExecutor.ExecuteManyAsync(new GetAllCustomerVersionNameQuery { })).ToList();
            List<DropdownListItem> resultList = nameVersionList.Select(nv => new DropdownListItem { Value = nv.NameVersion, Text = nv.NameVersion }).ToList();

            return Json(resultList);
        }

        [HttpGet]
        [NoCache]
        [AllowAuthenticated]
        public async Task<IActionResult> GetSystemDynamicFieldList()
        {
            List<DropdownListItem> resultList = new List<DropdownListItem>();

            var systemDynamicFieldList = (await QueryExecutor.ExecuteManyAsync(new TinyCRM.DynamicForm.Queries.GetDynamicFieldByFormIdQuery { FormId = DynamicFormContants.SystemDynamicFormId })).ToList();
            foreach (var sysDynamicField in systemDynamicFieldList)
            {
                if (sysDynamicField.DynamicDefinedTableSchemaId.IsNullOrEmpty())
                {
                    resultList.Add(new DropdownListItem { Value = sysDynamicField.Name, Text = sysDynamicField.DisplayName });
                }
                else
                {
                    var columns = (await QueryExecutor.ExecuteManyAsync(new GetDynamicDefinedTableColumnListByTableSchemaQuery { DynamicDefinedTableSchemaId = sysDynamicField.DynamicDefinedTableSchemaId.Value })).ToList();
                    foreach (var column in columns)
                    {
                        resultList.Add(new DropdownListItem { Value = string.Format("{0}.{1}", sysDynamicField.Name, column.Name), Text = string.Format("{0}/{1}", sysDynamicField.DisplayName, column.DisplayName) });
                    }
                }
            }

            return Json(resultList);
        }

        [HttpGet]
        [NoCache]
        [AllowAuthenticated]
        public async Task<IActionResult> LoadAllCustomerVersions()
        {
            var nameVersionList = (await QueryExecutor.ExecuteManyAsync(new GetAllCustomerVersionNameQuery { })).ToList();
            List<DropdownListItem> resultList = nameVersionList.Select(nv => new DropdownListItem { Value = nv.NameVersion, Text = nv.NameVersion }).ToList();

            return Json(resultList);
        }

        [HttpGet]
        [AllowAuthenticated]
        public async Task<IActionResult> GetCalculatedFieldsTree(Guid dynamcFormId)
        {
            var result = await QueryExecutor.ExecuteManyAsync(new GetCalculatedFieldsTreeQuery { DynamicFormId = dynamcFormId });
            return Json(result);
        }


        [HttpGet]
        [NoCache]
        [AllowAuthenticated]
        public IActionResult GetLoadURLDefinitions()
        {
            var configStr = Configuration.GetValue<string>("loadurl.definitions");
            List<DropdownListItem> resultList = new List<DropdownListItem>();
            if (configStr.IsNotNullOrEmpty())
            {
                var configList = JsonConvert.DeserializeObject<List<LoadUrlDefinitionModel>>(configStr);
                if (configList != null)
                {
                    resultList = configList.Where(x => x.IsEnable == true).Select(s => new DropdownListItem { Text = s.DisplayName, Value = s.LoadUrl }).ToList();
                }
            }

            return Json(resultList);
        }

        [HttpGet]
        [AllowAuthenticated]
        public IActionResult CheckCalculated(Guid dynamcFormId)
        {
            var contetData = string.Empty;
            Guid[] batchCalculated;
            var myData = _dynamicFormUtility.IsBatchLevelCalculatedTree(dynamcFormId, out batchCalculated);
            if (!myData)
            {
                contetData = "False";
            }
            var batch = string.Join("||", batchCalculated);
            var listNode = _dynamicFormUtility.GetListNodeLevel(dynamcFormId);
            int level = listNode.Max(x => x.level);
            foreach (var item in listNode.OrderByDescending(x => x.level))
            {
                if (item.level == level)
                {
                    contetData = string.Format("{0}{1}|", contetData, item.displayname);
                }
                else
                {
                    level = item.level;
                    contetData = string.Format("{0}<br/>{1}|", contetData, item.displayname);
                }
            }
            contetData = string.Format("{0}<br/><br/>{1}", contetData, batch);
            return Content(contetData, "text/html");
        }

        [HttpGet]
        public async Task<IActionResult> SystemDynamicFormValues(string sysDynamicFieldName, Guid? dynamicSectionId, string config)
        {
            DynamicFormModel model = new DynamicFormModel();
            model.Id = DynamicFormContants.SystemDynamicFormId;
            if (model.Id.HasValue)
            {
                var dynamicForm = await QueryExecutor.ExecuteOneAsync(new GetDynamicFormByIdQuery { Id = model.Id.Value });
                if (dynamicForm != null)
                {
                    model.Id = dynamicForm.Id;
                    model.Name = dynamicForm.Name;
                    model.NumerOfColumn = dynamicForm.NumberOfColumn;
                    model.Code = dynamicForm.Code;
                    model.FormStyle = dynamicForm.FormStyle;
                    model.FormOverwrite = dynamicForm.FormOverwrite;
                    model.TurnOffWarning = dynamicForm.TurnOffWarning;
                    model.SavedIntegratedHandler = dynamicForm.SavedIntegratedHandler;
                    model.DynamicFormValueCount = (await QueryExecutor.ExecuteAsync(new GetDynamicFormValueByFormIdQuery { FormId = dynamicForm.Id })).Total;
                    model.IsPrivate = dynamicForm.IsPrivate;
                    var dynamicFields = await QueryExecutor.ExecuteManyAsync(new TinyCRM.DynamicForm.Queries.GetDynamicFieldByFormIdQuery { FormId = model.Id.Value });

                    for (int i = 0; i < dynamicFields.ToList().Count; i++)
                    {
                        dynamicFields.ToList()[i].Order = i + 1;
                    }

                    if (dynamicForm.DisplayDocumentFileId.HasValue)
                    {
                        model.DisplayDocument = new FileData { Id = dynamicForm.DisplayDocumentFileId.Value };
                        model.DisplayDocumentVariable = (await QueryExecutor.ExecuteManyAsync(new GetAllVariableOfSaleTemplateQuery { FileId = dynamicForm.DisplayDocumentFileId.Value }))
                            .Where(x => dynamicFields.All(y => y.Name != x));
                    }
                    if (dynamicForm.ExcelDataFileId.HasValue)
                    {
                        model.ExcelData = new FileData { Id = dynamicForm.ExcelDataFileId.Value };
                    }
                    var paymentTypeDict = (await QueryExecutor.ExecuteManyAsync(
        new GetInfoListByTypeQuery { TypeName = "fwd_paymenttype" }))
    .ToDictionary(p => p.Id, p => p.Name);

                    model.Fields = dynamicFields.Select(x => new DynamicFieldModel
                    {
                        Name = x.Name,
                        ViewHint = x.ViewHint,
                        Order = x.Order,
                        SourceFieldName = x.SourceFieldName,
                        DataType = x.DataType,
                        DynamicDefinedTableSchemaId = x.DynamicDefinedTableSchemaId,
                        DynamicDefinedTableSchema = x.DynamicDefinedTableSchema,
                        AdditionalFilter = x.AdditionalFilter,
                        DisplayName = x.DisplayName,
                        SourceTableName = x.SourceTableName,
                        Display = x.Display,
                        DefaultValue = x.DefaultValue,
                        FieldType = x.FieldType,
                        Id = x.Id,
                        IsReadOnly = x.IsReadOnly,
                        IsRequired = x.IsRequired,
                        Validations = x.Validation.IsNotNullOrEmpty() ?
                            JsonConvert.DeserializeObject<IEnumerable<DynamicValidationModel>>(x.Validation)
                            : new List<DynamicValidationModel>(),
                        BusinessValidations = x.BusinessValidation.IsNotNullOrEmpty() ?
                            JsonConvert.DeserializeObject<IEnumerable<DynamicValidationModel>>(x.BusinessValidation)
                            : new List<DynamicValidationModel>(),
                        AdditionalMetadatas = x.AdditionalMetadata.IsNotNullOrEmpty() ?
                            JsonConvert.DeserializeObject<IEnumerable<DynamicAdditionalMetadataModel>>(x.AdditionalMetadata)
                            : new List<DynamicAdditionalMetadataModel>(),
                        SelectOptions = x.SelectOptions,
                        DynamicFieldSectionId = x.DynamicFieldSectionId,
                        DynamicFieldSectionName = x.DynamicFieldSectionName,
                        FreezeValue = x.FreezeValue,
                        IsExportExcel = x.IsExportExcel,
                        IsExportByConditionBoolean = x.IsExportByConditionBoolean,
                        VersionCode = x.VersionCode,
                        FieldCondition = x.FieldCondition,
                        PaymentType = x.PaymentType,
                        PaymentTypeString = x.PaymentType != null ? paymentTypeDict.TryGetValue(x.PaymentType.Value, out var name)
                            ? name
                            : string.Empty
                            : string.Empty
                    }).ToList();
                    model.Fields = model.Fields.OrderBy(x => x.Order);
                    model.LinkBusinessSpecific = await QueryExecutor.ExecuteOneAsync(new GetEntityLinkBusinessSpecificListQuery
                    {
                        EntityType = "DynamicForm",
                        ObjectData = null
                    });
                    model.EntityLinkList = (await QueryExecutor.ExecuteManyAsync(new GetEntityLinkListQuery { EntityId = dynamicForm.Id })).ToList();
                }
            }
            var loadFullConfig = false;
            if (dynamicSectionId.IsNotNullOrEmpty() || sysDynamicFieldName.IsNotNullOrEmpty())
            {
                loadFullConfig = true;
            }

            ViewBag.SysDynamicFieldName = sysDynamicFieldName;
            ViewBag.DynamicSectionId = dynamicSectionId;
            ViewBag.LoadFullConfig = loadFullConfig;
            if (dynamicSectionId.IsNotNullOrEmpty())
            {
                var dynamicFieldSection = await QueryExecutor.ExecuteOneAsync(new GetDynamicFieldSectionByIdQuery { Id = dynamicSectionId.Value });
                if (dynamicFieldSection != null)
                {
                    ViewBag.DynamicSectionName = dynamicFieldSection.SectionName;
                }
            }

            ViewBag.ConfigFillter = config;
            return View(model);
        }

        [HttpPost]
        [AllowAuthenticated]
        public async Task<IActionResult> DeleteRowSystemDynamicTable(Guid tableSchemaId, Guid tableId, int rowNumber, Guid? rowId)
        {
            await CommandExecutor.ExecuteAsync(new DeleteDynamicDefinedTableRowCommand
            {
                TableSchemaId = tableSchemaId,
                DynamicFieldValueId = tableId,
                RowNumber = rowNumber,
                RowId = rowId
            });
            return DefaultResult();
        }

        [HttpPost]
        [AllowAnonymous]
        public IActionResult BankQRCodeGenerator(string codeStr)
        {
            var bankList = BankList.IsNotNullOrEmpty() ? JsonConvert.DeserializeObject<List<BankData>>(BankList) : new List<BankData>();
            var bankInfoArr = codeStr.Split('|');
            var bankCode = bankInfoArr[0];
            var bankAccount = bankInfoArr[1];
            var amount = bankInfoArr[2];
            var message = bankInfoArr[3].ToString().RemoveDiacriticChars();
            if (message.Length > 90)
            {
                Error(T["Nội dung chuyển khoản quá dài!."]);
                return Json(new
                {
                    Success = false
                });
            }
            BankData bank = bankList.FirstOrDefault(x => x.Code.ToLower() == bankCode.ToLower());
            if (bank != null)
            {
                StringBuilder part12Builder = new StringBuilder()
                    .Append("00")
                    .Append($"{bank.Bin.Length:D2}")
                    .Append(bank.Bin)
                    .Append("01")
                    .Append($"{bankAccount.Length:D2}")
                    .Append(bankAccount);
                StringBuilder part11Builder = new StringBuilder()
                    .Append("0010A000000727")
                    .Append("01")
                    .Append($"{part12Builder.Length:D2}")
                    .Append(part12Builder)
                    .Append("0208QRIBFTTA");
                StringBuilder part1Builder = new StringBuilder()
                    .Append("38")
                    .Append($"{part11Builder.Length:D2}")
                    .Append(part11Builder);
                StringBuilder part21Builder = new StringBuilder()
                    .Append("08")
                    .Append($"{message.Length:D2}")
                    .Append(message);
                string part2 = new StringBuilder()
                    .Append("5303704")
                    .Append("54")
                    .Append($"{amount.Length:D2}")
                    .Append(amount)
                    .Append("5802VN")
                    .Append("62")
                    .Append($"{part21Builder.Length:D2}")
                    .Append(part21Builder)
                    .ToString();
                StringBuilder builder = new StringBuilder()
                    .Append("000201")
                    .Append("010212")
                    .Append(part1Builder)
                    .Append(part2)
                    .Append("6304");
                string qrcodeContent = builder
                    .Append(builder.ToString().GenerateCheckSum().ToUpper())
                    .ToString();
                QRCodeGenerator qr = new QRCodeGenerator();
                QRCodeData data = qr.CreateQrCode(qrcodeContent, QRCodeGenerator.ECCLevel.L);
                BitmapByteQRCode qrCode = new BitmapByteQRCode(data);
                byte[] qrCodeAsBitmapByteArr = qrCode.GetGraphic(20);

                using (MemoryStream ms = new MemoryStream(qrCodeAsBitmapByteArr))
                {
                    Bitmap bitmap = new Bitmap(ms);
                    bitmap.Save(ms, ImageFormat.Jpeg);
                    byte[] byteImage = ms.ToArray();
                    return Json(new
                    {
                        Success = true,
                        QR_Image = Convert.ToBase64String(byteImage),
                        QR_ImageFormat = "Jpeg",
                    });
                }
            }
            else
            {
                Error(T["Không có thông tin ngân hàng."]);
                return Json(new
                {
                    Success = false
                });
            }
        }



        [HttpGet]
        [NoCache]
        [AllowAuthenticated]
        public IActionResult GetBankList()
        {
            var bankList = BankList.IsNotNullOrEmpty() ? JsonConvert.DeserializeObject<List<BankData>>(BankList) : new List<BankData>();

            return Json(bankList.Select(x => new { Value = x.Code, Text = x.Code + " - " + x.Short_Name }));

        }
    }
}