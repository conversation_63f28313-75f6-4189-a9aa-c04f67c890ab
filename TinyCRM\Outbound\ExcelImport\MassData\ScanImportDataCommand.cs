﻿using AutoMapper;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Threading.Tasks;
using TinyCRM.Customer.Queries;
using TinyCRM.DigitalChannel.Queries;
using Webaby;
using Webaby.Data;
using Webaby.Localization;
using Webaby.Security;

namespace TinyCRM.Outbound.ExcelImport.MassData
{
    public class ScanImportDataCommand : CommandBase
    {
        public Guid ImportSessionId { get; set; }

        public Guid? CampaignId { get; set; }

        public string DedupColumnDefinitions { get; set; }

        public string RequiredColumns { get; set; }

        public List<string> CustomerValidRootLevels { get; set; }
    }
    internal class ScanImportDataCommandHandler : CommandHandlerBase<ScanImportDataCommand>
    {
        IUserService _userService;
        public ScanImportDataCommandHandler(IServiceProvider serviceProvider, IUserService userService) : base(serviceProvider) 
        { 
            _userService = userService; 
        }

        public override async Task ExecuteAsync(ScanImportDataCommand command)
        {
            var cmd = EntitySet.CreateDbCommand();
            cmd.CommandText = "dbo.ImportCustomer_ScanData";
            cmd.CommandType = CommandType.StoredProcedure;
            cmd.AddDataAuthorizedParameters(_userService);
            cmd.Parameters.Add(DbParameterHelper.AddNullableGuid(cmd, "@ImportSessionId", command.ImportSessionId));
            cmd.Parameters.Add(DbParameterHelper.AddNullableGuid(cmd, "@CampaignId", command.CampaignId));

            var requiredColumns = command.RequiredColumns.IsNotNullOrEmpty() ? JsonConvert.DeserializeObject<List<string>>(command.RequiredColumns) : new List<string>();

            cmd.Parameters.Add(DbParameterHelper.NewStringListParameter("@RequiredColumns", requiredColumns));
            cmd.Parameters.Add(DbParameterHelper.NewStringListParameter("@ValidRootLevels", command.CustomerValidRootLevels));

            bool isDedupByDigitalContact = false;
            DataTable dedupDigitalContactListParam = new DataTable("GuidGuid");
            dedupDigitalContactListParam.Columns.Add("Value1", typeof(Guid));
            dedupDigitalContactListParam.Columns.Add("Value2", typeof(Guid));

            var definedDeduplicatedColumns = CustomerDedupDefinition.GetListObjectFromJsonString(command.DedupColumnDefinitions);
            if (definedDeduplicatedColumns != null)
            {
                // Ưu tiên Dedup theo DigitalContact
                // Nếu Không cấu hình Dedup theo DigitalContact thì mới dedup theo các Column của Customer
                if (definedDeduplicatedColumns.DedupDigitalContactType != null && definedDeduplicatedColumns.DedupDigitalContactType.Count > 0)
                {
                    isDedupByDigitalContact = true;

                    var digitalContactTypeList = await QueryExecutor.ExecuteManyAsync(new GetDigitalContactTypeListQuery { });
                    var allDedupColumns = (from dct in digitalContactTypeList
                                           join dedupConfig in definedDeduplicatedColumns.DedupDigitalContactType on dct.Id equals dedupConfig.DigitalContactTypeId
                                           join dedupDct in digitalContactTypeList on dedupConfig.DedupDigitalContactTypeId equals dedupDct.Id
                                           select new
                                           {
                                               DigitalContactTypeId = dedupConfig.DigitalContactTypeId,
                                               ColumnName = "DigitalContact_" + dct.Name,
                                               DedupDigitalContactTypeId = dedupConfig.DedupDigitalContactTypeId,
                                               DedupColumnName = "DigitalContact_" + dedupDct.Name,
                                           }).ToList();

                    DataTable dedupColumnListParam = new DataTable("StringString");
                    dedupColumnListParam.Columns.Add("Value1", typeof(string));
                    dedupColumnListParam.Columns.Add("Value2", typeof(string));

                    foreach (var dedupColumn in allDedupColumns)
                    {
                        dedupColumnListParam.Rows.Add(dedupColumn.ColumnName, dedupColumn.DedupColumnName);
                        dedupDigitalContactListParam.Rows.Add(dedupColumn.DigitalContactTypeId, dedupColumn.DedupDigitalContactTypeId);
                    }

                    var customerDedupParam = new SqlParameter();
                    customerDedupParam.ParameterName = "@CustomerDedupColumns";
                    customerDedupParam.Value = dedupColumnListParam;
                    customerDedupParam.SqlDbType = SqlDbType.Structured;
                    cmd.Parameters.Add(customerDedupParam);

                    var internalDedupParam = new SqlParameter();
                    internalDedupParam.ParameterName = "@InternalDedupColumns";
                    internalDedupParam.Value = dedupColumnListParam;
                    internalDedupParam.SqlDbType = SqlDbType.Structured;
                    cmd.Parameters.Add(internalDedupParam);
                }
                else
                {
                    DataSet dedupColumns = CustomerDedupDefinition.GetFromJsonString(command.DedupColumnDefinitions);
                    var customerDedupParam = new SqlParameter();
                    customerDedupParam.ParameterName = "@CustomerDedupColumns";
                    customerDedupParam.Value = dedupColumns.Tables["DedupDatabase"];
                    customerDedupParam.SqlDbType = SqlDbType.Structured;
                    cmd.Parameters.Add(customerDedupParam);

                    var internalDedupParam = new SqlParameter();
                    internalDedupParam.ParameterName = "@InternalDedupColumns";
                    internalDedupParam.Value = dedupColumns.Tables["DedupInternal"];
                    internalDedupParam.SqlDbType = SqlDbType.Structured;
                    cmd.Parameters.Add(internalDedupParam);
                }
            }

            var dedupDigitalContactParam = new SqlParameter();
            dedupDigitalContactParam.ParameterName = "@DedupDigitalContactList";
            dedupDigitalContactParam.Value = dedupDigitalContactListParam;
            dedupDigitalContactParam.SqlDbType = SqlDbType.Structured;
            cmd.Parameters.Add(dedupDigitalContactParam);

            cmd.Parameters.Add(DbParameterHelper.NewNullableBooleanParameter(cmd, "@DedupByDigitalContact", isDedupByDigitalContact));

            await EntitySet.ExecuteNonQueryAsync(cmd);
        }
    }
}
