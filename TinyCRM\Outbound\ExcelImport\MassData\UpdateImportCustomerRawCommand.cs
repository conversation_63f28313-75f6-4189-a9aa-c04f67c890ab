﻿using System;
using System.Data;
using System.Data.SqlClient;
using System.Threading.Tasks;
using Webaby;
using Webaby.Data;

namespace TinyCRM.Outbound.ExcelImport.MassData
{
    public class UpdateImportCustomerRawCommand : CommandBase
    {
        public Guid ImportSessionId { get; set; }
    }
    internal class UpdateImportCustomerRawCommandHandler : CommandHandlerBase<UpdateImportCustomerRawCommand>
    {
        public UpdateImportCustomerRawCommandHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task ExecuteAsync(UpdateImportCustomerRawCommand command)
        {
            var sqlCommand = EntitySet.CreateDbCommand();
            sqlCommand.CommandType = CommandType.Text;
            sqlCommand.CommandText = @"UPDATE	rw
                                                SET		rw.CustomerId = c.Id
                                                FROM	dbo.ImportCustomerRaw rw
		                                                JOIN dbo.Customer c ON c.Code = rw.Code
                                                WHERE	rw.ImportSessionId = @ImportSessionId
		                                                AND rw.CustomerId IS NULL
		                                                AND c.IsDisabled = 0 AND c.Deleted = 0";
            sqlCommand.Parameters.Add(DbParameterHelper.AddNullableGuid(sqlCommand, "@ImportSessionId", command.ImportSessionId));
            await EntitySet.ExecuteNonQueryAsync(sqlCommand);
        }
    }
}
