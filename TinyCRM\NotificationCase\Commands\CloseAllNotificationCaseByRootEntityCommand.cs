﻿using System;
using System.Data;
using System.Data.Common;
using System.Linq;
using System.Threading.Tasks;
using Webaby;
using Webaby.Data;
using Webaby.Notification;
using Webaby.Security;

namespace TinyCRM.NotificationCase.Commands
{
    public class CloseAllNotificationCaseByRootEntityCommand : CommandBase
    {
        public Guid RootEntityId { get; set; }
    }

    internal class CloseAllNotificationCaseByRootEntityCommandHandler : CommandHandlerBase<CloseAllNotificationCaseByRootEntityCommand>
    {
        public IUserService _userService { get; set; }

        public CloseAllNotificationCaseByRootEntityCommandHandler(IServiceProvider serviceProvider, IUserService userService) : base(serviceProvider)
        {
            _userService = userService;
        }

        public override async Task ExecuteAsync(CloseAllNotificationCaseByRootEntityCommand command)
        {
            var cmd = EntitySet.CreateDbCommand(CommandType.StoredProcedure, "dbo.CloseAllNotificationCases");
            cmd.Parameters.AddRange(new DbParameter[]
            {
                DbParameterHelper.AddNullableGuid(cmd, "@RootEntityId", command.RootEntityId),
                DbParameterHelper.AddNullableGuid(cmd, "@ClosedBy", _userService.GetCurrentUser().Id)
            });

            await EntitySet.ExecuteNonQueryAsync(cmd);
        }
    }
}