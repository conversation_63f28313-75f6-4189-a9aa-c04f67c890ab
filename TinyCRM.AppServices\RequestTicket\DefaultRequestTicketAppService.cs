﻿using AutoMapper;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Poptech.CEP.ClientIntegration.RequestTicket;
using SpreadsheetGear;
using SpreadsheetGear.Data;
using System.Collections.Specialized;
using System.Data;
using System.Data.Common;
using System.Globalization;
using TinyCRM.Access;
using TinyCRM.AppServices.RequestTicket.Dto;
using TinyCRM.AppServices.RequestTicketDynamicModel;
using TinyCRM.AppServices.RequestTicketDynamicModel.Dto;
using TinyCRM.AutomaticTask;
using TinyCRM.AutomaticTask.Command;
using TinyCRM.BusinessResult.Queries;
using TinyCRM.Channel.Queries;
using TinyCRM.Customer;
using TinyCRM.Customer.Commands;
using TinyCRM.Customer.Queries;
using TinyCRM.DynamicDefinedTable;
using TinyCRM.DynamicDefinedTable.Queries;
using TinyCRM.DynamicForm.Command;
using TinyCRM.DynamicForm.Query;
using TinyCRM.Enums;
using TinyCRM.NotificationCase;
using TinyCRM.Outbound.ContactCall.Events;
using TinyCRM.Phase;
using TinyCRM.Phase.Command;
using TinyCRM.Phase.Queries;
using TinyCRM.RequestTicket;
using TinyCRM.RequestTicket.Commands;
using TinyCRM.RequestTicket.Events;
using TinyCRM.RequestTicket.Queries;
using TinyCRM.RequestTicketDynamicModel.Queries;
using TinyCRM.ServiceType;
using TinyCRM.ServiceType.Queries;
using TinyCRM.TaskType.Queries;
using TinyCRM.TbCallback;
using TinyCRM.TbCallback.Commands;
using TinyCRM.Workflow;
using TinyCRM.Workflow.Queries;
using Webaby;
using Webaby.Caching;
using Webaby.Core.Access;
using Webaby.Core.DueTime;
using Webaby.Core.DueTime.Queries;
using Webaby.Core.DynamicForm;
using Webaby.Core.DynamicForm.Queries;
using Webaby.Core.Organization;
using Webaby.Core.Organization.Queries;
using Webaby.Core.Role.Queries;
using Webaby.Core.Sequence;
using Webaby.Core.UserAccount.Queries;
using Webaby.Data;
using Webaby.DynamicModel;
using Webaby.Localization;
using Webaby.Security;
using Webaby.Validation.Email;
using Webaby.Validation.PhoneNumber;
using Webaby.Web;
using TaskStatus = TinyCRM.Enums.TaskStatus;

namespace TinyCRM.AppServices.RequestTicket
{
    [AutoRegisterService(typeof(IRequestTicketAppService), ServiceLifetime.Scoped)]
    public class DefaultRequestTicketAppService : IRequestTicketAppService
    {
        private static Guid _closeRequestTicketPermissionId = Guid.Parse("B9249FB9-7443-4CF1-B2C2-143A05EDA5E8");
        private static Guid _deleteRequestTicketPermissionId = Guid.Parse("CC4F754F-4CBD-4482-9777-2045E3302AE5");

        public string CustomerCodeFormat { get { return Configuration.GetValue<string>("Customer.Code.Format"); } }

        public string RequestTicketCodeFormat { get { return Configuration.GetValue<string>("requestTicket.code.Format"); } }

        public string RequestTicketIsoCodeFormat { get { return Configuration.GetValue<string>("RequestTicket.IsoCode.Format"); } }

        public string SendByEmail { get { return Configuration.GetValue<string>("Send.By.Email"); } }

        string ApplicationUrl { get { return Configuration.GetValue<string>("Application:Url"); } }

        private readonly IWebHostEnvironment _env;

        public IServiceProvider ServiceProvider { get; set; }

        public IRequestTicketServices RequestTicketServices { get; set; }

        public IUserService UserService { get; set; }

        public IBusinessPermissionService BusinessPermissionService { get; set; }

        public ICommandExecutor CommandExecutor { get; set; }

        public IEntitySet EntitySet { get; set; }

        public IRepository Repository { get; set; }

        public IDueTimeUtility DueTimeUtility { get; set; }

        public IQueryExecutor QueryExecutor { get; set; }

        public ILocalTransactionManager LocalTransactionManager { get; set; }

        public ITaskCommand TaskCommand { get; set; }

        public IText T { get; set; }

        public IIsoCodePrefixGenerator IsoCodePrefixGenerator { get; set; }

        public ISequenceUtility SequenceUtility { get; set; }

        public IEventBus EventBus { get; set; }

        public IWebabyHubEventBus WebabyHubEventBus { get; set; }

        public ICacheProvider CacheProvider { get; set; }

        public IOrganizationUtility OrganizationUtility { get; set; }

        public ILogger<DefaultRequestTicketAppService> Logger { get; set; }

        public IConfiguration Configuration { get; set; }

        public IMapper Mapper { get; set; }

        public IRequestTicketDynamicModelAppService RequestTicketDynamicModelAppService { get; set; }

        public IEnumerable<IRequestTicketCreatedHanlder> RequestTicketCreatedHanlders { get; set; }

        public DefaultRequestTicketAppService(
            IServiceProvider serviceProvider,
            IRequestTicketServices requestTicketServices,
            IUserService userService,
            IBusinessPermissionService businessPermissionService,
            ICommandExecutor commandExecutor,
            IEntitySet entitySet,
            IRepository repository,
            IDueTimeUtility dueTimeUtility,
            IQueryExecutor queryExecutor,
            ILocalTransactionManager localTransactionManager,
            ITaskCommand taskCommand,
            IText t,
            IIsoCodePrefixGenerator isoCodePrefixGenerator,
            ISequenceUtility sequenceUtility,
            IEventBus eventBus,
            IWebabyHubEventBus webabyHubEventBus,
            ICacheProvider cacheProvider,
            IOrganizationUtility organizationUtility,
            ILogger<DefaultRequestTicketAppService> logger,
            IConfiguration configuration,
            IMapper mapper,
            IRequestTicketDynamicModelAppService requestTicketDynamicModelAppService,
            IEnumerable<IRequestTicketCreatedHanlder> requestTicketCreatedHanlders,
            IWebHostEnvironment env
            )
        {
            ServiceProvider = serviceProvider;
            RequestTicketServices = requestTicketServices;
            UserService = userService;
            BusinessPermissionService = businessPermissionService;
            CommandExecutor = commandExecutor;
            EntitySet = entitySet;
            Repository = repository;
            DueTimeUtility = dueTimeUtility;
            QueryExecutor = queryExecutor;
            LocalTransactionManager = localTransactionManager;
            TaskCommand = taskCommand;
            T = t;
            IsoCodePrefixGenerator = isoCodePrefixGenerator;
            SequenceUtility = sequenceUtility;
            EventBus = eventBus;
            WebabyHubEventBus = webabyHubEventBus;
            CacheProvider = cacheProvider;
            OrganizationUtility = organizationUtility;
            Logger = logger;
            Configuration = configuration;
            Mapper = mapper;
            RequestTicketDynamicModelAppService = requestTicketDynamicModelAppService;
            RequestTicketCreatedHanlders = requestTicketCreatedHanlders;
            _env = env;
        }

        private async Task<ValidateRequestTicketCustomerResult> ValidateRequestTicketCustomer(RequestTicketCustomer requestTicketCustomer)
        {
            CustomerData customerData = null;
            var customerId = requestTicketCustomer.Id ?? Guid.NewGuid();

            #region Validates

            if (requestTicketCustomer.Email.IsNotNullOrEmpty())
            {
                if (!EmailStringHandle.Validate(requestTicketCustomer.Email.Trim()))
                {
                    return new ValidateRequestTicketCustomerResult
                    {
                        ErrorMessage = T["Thông tin email không hợp lệ."]
                    };
                }
            }

            if (requestTicketCustomer.Id.IsNullOrEmpty())
            {
                if (requestTicketCustomer.Name.IsNullOrEmpty())
                {
                    return new ValidateRequestTicketCustomerResult
                    {
                        ErrorMessage = (T["Vui lòng nhập tên khách hàng."])
                    };
                }

                if (requestTicketCustomer.Type != CustomerType.B2B)
                {
                    // Check field required on Customer
                    if ((string.IsNullOrEmpty(requestTicketCustomer.Email) || requestTicketCustomer.Email.Trim() == string.Empty) &&
                        (string.IsNullOrEmpty(requestTicketCustomer.FacebookId) || requestTicketCustomer.FacebookId.Trim() == string.Empty) &&
                        (string.IsNullOrEmpty(requestTicketCustomer.Phone1) || requestTicketCustomer.Phone1.Trim() == string.Empty) &&
                        (string.IsNullOrEmpty(requestTicketCustomer.Phone2) || requestTicketCustomer.Phone2.Trim() == string.Empty) &&
                        (string.IsNullOrEmpty(requestTicketCustomer.Phone3) || requestTicketCustomer.Phone3.Trim() == string.Empty))
                    {
                        return new ValidateRequestTicketCustomerResult
                        {
                            ErrorMessage = (T["Chưa có thông tin liên lạc của khách hàng. Vui lòng nhập lại."])
                        };
                    }
                }

                #region Phones validate

                CheckPhoneValidResult phone1ValidateResult = PhoneNumberValidator.Input(requestTicketCustomer.Phone1, T, true);
                CheckPhoneValidResult phone2ValidateResult = PhoneNumberValidator.Input(requestTicketCustomer.Phone2, T, true);
                CheckPhoneValidResult phone3ValidateResult = PhoneNumberValidator.Input(requestTicketCustomer.Phone3, T, true);

                if (requestTicketCustomer.Id.IsNullOrEmpty() || (requestTicketCustomer.Id.IsNotNullOrEmpty() && requestTicketCustomer.AllowEdit))
                {
                    // Tạo mới VÃNG LAI hoặc cho phép edit khách hàng CORE
                    // Validate 3 phone                                
                    if (phone1ValidateResult.Type == PhoneType.UNKNOWN && phone1ValidateResult.ValidationMessage.IsNotNullOrEmpty())
                    {
                        return new ValidateRequestTicketCustomerResult
                        {
                            ErrorMessage = T["Phone 1: "] + phone1ValidateResult.ValidationMessage
                        };
                    }

                    if (phone2ValidateResult.Type == PhoneType.UNKNOWN && phone2ValidateResult.ValidationMessage.IsNotNullOrEmpty())
                    {
                        return new ValidateRequestTicketCustomerResult
                        {
                            ErrorMessage = T["Phone 2: "] + phone2ValidateResult.ValidationMessage
                        };
                    }

                    if (phone3ValidateResult.Type == PhoneType.UNKNOWN && phone3ValidateResult.ValidationMessage.IsNotNullOrEmpty())
                    {
                        return new ValidateRequestTicketCustomerResult
                        {
                            ErrorMessage = T["Phone 3: "] + phone3ValidateResult.ValidationMessage
                        };
                    }
                }
                else
                {
                    // Update Phone2, Phone3 ONLY
                    // Validate phone
                    if (phone2ValidateResult.Type == PhoneType.UNKNOWN && phone2ValidateResult.ValidationMessage.IsNotNullOrEmpty())
                    {
                        return new ValidateRequestTicketCustomerResult
                        {
                            ErrorMessage = T["Phone 2: "] + phone2ValidateResult.ValidationMessage
                        };
                    }

                    if (phone3ValidateResult.Type == PhoneType.UNKNOWN && phone3ValidateResult.ValidationMessage.IsNotNullOrEmpty())
                    {
                        return new ValidateRequestTicketCustomerResult
                        {
                            ErrorMessage = T["Phone 3: "] + phone3ValidateResult.ValidationMessage
                        };
                    }
                }

                #endregion
            }
            else
            {
                customerData = await QueryExecutor.ExecuteOneAsync(new GetCustomerByIdQuery { Id = requestTicketCustomer.Id.Value });
                if (customerData != null)
                {
                    CheckPhoneValidResult phone1ValidateResult = PhoneNumberValidator.Input(requestTicketCustomer.Phone1, T, true);
                    CheckPhoneValidResult phone2ValidateResult = PhoneNumberValidator.Input(requestTicketCustomer.Phone2, T, true);
                    CheckPhoneValidResult phone3ValidateResult = PhoneNumberValidator.Input(requestTicketCustomer.Phone3, T, true);

                    if (!customerData.IsBackendCustomer)
                    {
                        if (phone1ValidateResult.Type == PhoneType.UNKNOWN && phone1ValidateResult.ValidationMessage.IsNotNullOrEmpty())
                        {
                            return new ValidateRequestTicketCustomerResult
                            {
                                ErrorMessage = T["Phone 1: "] + phone1ValidateResult.ValidationMessage
                            };
                        }
                    }

                    if (phone2ValidateResult.Type == PhoneType.UNKNOWN && phone2ValidateResult.ValidationMessage.IsNotNullOrEmpty())
                    {
                        return new ValidateRequestTicketCustomerResult
                        {
                            ErrorMessage = T["Phone 2: "] + phone2ValidateResult.ValidationMessage
                        };
                    }

                    if (phone3ValidateResult.Type == PhoneType.UNKNOWN && phone3ValidateResult.ValidationMessage.IsNotNullOrEmpty())
                    {
                        return new ValidateRequestTicketCustomerResult
                        {
                            ErrorMessage = T["Phone 3: "] + phone3ValidateResult.ValidationMessage
                        };
                    }
                }
            }

            // Dedup Customer via Phone1, Phone2, Phone3, Email and FacebookId
            var duplicate = await QueryExecutor.ExecuteAsync(new GetCustomerDuplicateQuery
            {
                Id = customerId,
                Email = requestTicketCustomer.Email,
                FacebookId = requestTicketCustomer.FacebookId,
                Phone1 = requestTicketCustomer.Phone1,
                Phone2 = requestTicketCustomer.Phone2,
                Phone3 = requestTicketCustomer.Phone3,
                Type = (int)requestTicketCustomer.Type
            });

            if (duplicate.Many.Any())
            {
                // Error Message
                var fieldDuplicate = duplicate.Many.Select(x => x.DuplicateFieldName).Distinct().ToList();
                var duplicateFieldDisplayName = new List<string>();
                foreach (var f in fieldDuplicate)
                {
                    duplicateFieldDisplayName.Add(requestTicketCustomer.GetLabelName(f));
                }
                return new ValidateRequestTicketCustomerResult
                {
                    ErrorMessage = T["Khách hàng có {0} đã tồn tại trong hệ thống", string.Join(", ", duplicateFieldDisplayName.ToArray())]
                };
            }

            #endregion

            #region Create/Edit Customer

            if (requestTicketCustomer.Id.IsNullOrEmpty() || (requestTicketCustomer.Id.IsNotNullOrEmpty() && requestTicketCustomer.AllowEdit))
            {
                requestTicketCustomer.Id = customerId;

                string codePrefix = codePrefix = IsoCodePrefixGenerator.GetIsoCodePrefix((int)requestTicketCustomer.Type);

                // Generate Customer Code
                int nextCustomerCodeCount = await SequenceUtility.GetNextValue("CusCode" + codePrefix, SequenceCycleType.Monthly);
                string nextCustomerDigitString = nextCustomerCodeCount.ToString().PadLeft(4, '0');

                if (string.IsNullOrEmpty(requestTicketCustomer.Code))
                {
                    string customerCodeFormat = CustomerCodeFormat;
                    if (customerCodeFormat.IsNullOrEmpty())
                    {
                        customerCodeFormat = "{CodePrefix}{CustomerDigit}{MonthString}{YearString}";
                    }
                    customerCodeFormat = customerCodeFormat.Replace("{CodePrefix}", "{0}");
                    customerCodeFormat = customerCodeFormat.Replace("{CustomerDigit}", "{1}");
                    customerCodeFormat = customerCodeFormat.Replace("{MonthString}", "{2}");
                    customerCodeFormat = customerCodeFormat.Replace("{YearString}", "{3}");

                    string dayString = DateTime.Now.Day.ToString().PadLeft(2, '0');
                    string monthString = DateTime.Now.Month.ToString().PadLeft(2, '0');
                    string yearString = DateTime.Now.Year.ToString().Remove(0, 2);
                    if (RequestTicketCodeFormat.Contains("{YearString:4}"))
                    {
                        yearString = DateTime.Now.Year.ToString();
                    }
                    if (RequestTicketCodeFormat.Contains("{YearString:2}"))
                    {
                        yearString = DateTime.Now.Year.ToString().Remove(0, 2);
                    }

                    requestTicketCustomer.Code = string.Format(customerCodeFormat, codePrefix, nextCustomerDigitString, monthString, yearString);
                }
                CreateEditCustomerCommand createEditCustomerCommand = Mapper.Map<CreateEditCustomerCommand>(requestTicketCustomer);
                await CommandExecutor.ExecuteAsync(createEditCustomerCommand);
            }
            else
            {
                // Update Phone2, Phone3 ONLY
                await CommandExecutor.ExecuteAsync(new UpdateCustomerExtendedFieldsCommand
                {
                    Id = customerId,
                    Phone2 = requestTicketCustomer.Phone2,
                    Phone3 = requestTicketCustomer.Phone3
                });
            }

            #endregion

            CustomerAlternativeAddressData customerAlternative = null;
            if (requestTicketCustomer.CustomerAlternativeAddressId.HasValue)
            {
                customerAlternative = await QueryExecutor.ExecuteOneAsync(new GetCustomerAlternativeAddressByIdQuery(requestTicketCustomer.CustomerAlternativeAddressId.Value));
            }

            customerData = await QueryExecutor.ExecuteOneAsync(new GetCustomerByIdQuery { Id = customerId });

            return new ValidateRequestTicketCustomerResult
            {
                IsSuccess = true,
                CustomerData = customerData,
                CustomerAlternativeAddressData = customerAlternative
            };
        }

        public async Task<string> CreateFirstTasksAsync(Guid requestTicketId)
        {
            var requestTicketData = await QueryExecutor.ExecuteOneAsync(new GetRequestTicketByIdQuery(requestTicketId));
            if (requestTicketData == null)
            {
                return T["Không tìm thấy Phiếu yêu cầu có Id: \"{0}\".", requestTicketId];
            }

            if (requestTicketData.Status == RequestTicketStatus.Done)
            {
                return T["Không thể kích hoạt tạo Tác vụ vì Phiếu yêu cầu đã hoàn thành."];
            }

            var serviceType = await QueryExecutor.ExecuteOneAsync(new GetServiceTypeByIdQuery(requestTicketData.ServiceTypeId));
            string errorMessage = await CreateFirstTasksAsync(requestTicketData, serviceType, requestTicketId, requestTicketData.OwnerId, requestTicketData.PlannedDate);

            return errorMessage;
        }

        private async Task<string> CreateFirstTasksAsync(TinyCRM.RequestTicket.Queries.RequestTicketData requestTicketData, ServiceTypeData serviceType, Guid ticketId, Guid? ticketOwnerId, DateTime? plannedDate)
        {
            string errorMessage = string.Empty;

            // If ServiceType has DefaultOrganizationId Create Firts Task in Workflow
            if (serviceType.DefaultOrganizationId.HasValue)
            {
                try
                {
                    var taskTypeList = await QueryExecutor.ExecuteManyAsync(new GetTaskTypeInWorkflowByServiceTypeIdQuery { ServiceTypeId = serviceType.Id });
                    var defaultAssignUser = await QueryExecutor.ExecuteOneAsync(new GetUserProfileByAccessOrgQuery { CodeAccessId = ConstCodePermissions.OrgUserInCharge, OrganizationId = serviceType.DefaultOrganizationId.Value });
                    if (defaultAssignUser != null && taskTypeList.Any())
                    {
                        var firstTaskType = taskTypeList.First();

                        var createTaskArguments = new CreateTaskArguments
                        {
                            TaskId = Guid.NewGuid(),
                            SMS = false,
                            SendEmail = true,
                            TicketId = ticketId,
                            TaskType = firstTaskType.Id,
                            Description = null,
                            AssignTo = defaultAssignUser.Id,
                            OrgId = serviceType.DefaultOrganizationId,

                            CreatedBy = AutomaticTaskConstants.AutoTaskCreatorUserId,
                            PlannedDate = plannedDate
                        };
                        //using (var trans = LocalTransactionManager.CreateLocalTransaction())
                        {
                            var createTaskResult = await TaskCommand.CreateTask(createTaskArguments, true);
                            if (createTaskResult.IsSuccess)
                            {
                                await TaskCommand.HandleHttpTask(createTaskArguments.TaskId.Value);
                            }
                            else
                            {
                                errorMessage = createTaskResult.ErrorMessage;
                            }
                            //trans.Commit();
                        }
                    }
                }
                catch (Exception ex)
                {
                    Logger.LogError(ex, ex.Message);
                    errorMessage = ex.Message;
                }
            }
            else
            {
                // LUỒNG TỰ ĐỘNG
                if (serviceType.WorkflowId.IsNotNullOrEmpty())
                {
                    try
                    {
                        CreateTasksByAutoNextTasksCommand createTasksByAutoNextTasksCommand = new CreateTasksByAutoNextTasksCommand
                        {
                            TriggeredByTaskDone = false,

                            ServiceTypeId = serviceType.Id,
                            WorkflowId = serviceType.WorkflowId.Value,
                            RequestTicketId = ticketId,
                            RequestTicketCreatorId = UserService.GetCurrentUser().Id,
                            RequestTicketOwnerId = ticketOwnerId,
                            RequestTicketData = requestTicketData,
                        };
                        await CommandExecutor.ExecuteAsync(createTasksByAutoNextTasksCommand);
                        errorMessage = createTasksByAutoNextTasksCommand.ErrorMessage;
                    }
                    catch (Exception ex)
                    {
                        Logger.LogError(ex, ex.Message);
                        errorMessage = ex.Message;
                    }
                }
            }

            return errorMessage;
        }

        public async Task<CreateEditRequestTicketResult> CreateRequestTicketAsync(RequestTicketCreateEditArguments arguments, IEnumerable<InsertProductRequestTicketItem> productRequestTickets, NameValueCollection form, IFormFileCollection files, bool checkExistedRequestTicket = true, bool auditDynamicForm = true, bool ignoreTransaction = false)
        {
            var dynamicModelProperties = RequestTicketCreateEditDynamicModel.GetModelPropertyList(Configuration);

            RequestTicketDynamicModelData requestTicketDynamicModelData = await RequestTicketDynamicModelAppService.GetRequestTicketDynamicModelAsync(new GetRequestTicketDynamicModelArguments { IsNoneCustomerTicket = arguments.IsNoneCustomerTicket, CreatedMode = arguments.IsNew, ServiceTypeId = arguments.ServiceTypeId });
            if (requestTicketDynamicModelData != null && requestTicketDynamicModelData.DynamicModelJson.IsNotNullOrEmpty())
            {
                DynamicCreateEditModel dynamicCreateEditModel = JsonConvert.DeserializeObject<DynamicCreateEditModel>(requestTicketDynamicModelData.DynamicModelJson);
                if (dynamicCreateEditModel != null)
                {
                    List<DynamicCreateEditModelProperty> dynamicCreateEditModelProperties = new List<DynamicCreateEditModelProperty>();
                    foreach (var propertyRowGroup in dynamicCreateEditModel.PropertyRowGroupList)
                    {
                        dynamicCreateEditModelProperties.AddRange(propertyRowGroup.Properties);
                    }

                    foreach (var dynamicModelProperty in dynamicModelProperties)
                    {
                        foreach (var dynamicCreateEditModelProperty in dynamicCreateEditModelProperties)
                        {
                            if (dynamicModelProperty.Name.IsEqualIgnoreCase(dynamicCreateEditModelProperty.PropertyName))
                            {
                                dynamicModelProperty.IsRequired = dynamicCreateEditModelProperty.IsRequired;
                                dynamicModelProperty.DisplayName = dynamicCreateEditModelProperty.Label;
                            }
                        }
                    }
                }
            }

            dynamicModelProperties.MapObjectValueAndAttributes(arguments, arguments.Id);

            Dictionary<string, List<string>> dynamicModelPropertiesValidateErrors;
            if (!dynamicModelProperties.TryMapping(arguments, form, files, "", out dynamicModelPropertiesValidateErrors))
            {
                string errorMessages = string.Join("<br/>", dynamicModelPropertiesValidateErrors.Values.SelectMany(x => x).Select(x => x));
                return new CreateEditRequestTicketResult
                {
                    ErrorMessage = errorMessages,
                    IsSuccess = false
                };
            }

            arguments.Status = RequestTicketStatus.Forward;

            var currentUser = UserService.GetCurrentUser();
            if (!currentUser.IsWebApiUserAccount)
            {
                bool checkPermission = await QueryExecutor.ExecuteOneAsync(new CheckPermissionQuery { ServiceTypeId = arguments.ServiceTypeId, UserId = currentUser.Id });
                if (!checkPermission)
                {
                    return new CreateEditRequestTicketResult
                    {
                        IsSuccess = false,
                        ErrorMessage = T["Bạn không có quyền tạo phiếu cho dịch vụ hiện tại"]
                    };
                }
            }

            if (arguments.Id.HasValue)
            {
                var auditSessionId = Guid.NewGuid();
                if (checkExistedRequestTicket)
                {
                    var requestTicket = await QueryExecutor.ExecuteOneAsync(new GetRequestTicketByIdQuery(arguments.Id.Value));
                    if (requestTicket != null)
                    {
                        return new CreateEditRequestTicketResult
                        {
                            IsSuccess = false,
                            ErrorMessage = T["Phiếu có Id: \"{0}\" đã tồn tại.", arguments.Id.Value]
                        };
                    }
                }

                // Find ServiceType
                var serviceType = await QueryExecutor.ExecuteOneAsync(new GetServiceTypeByIdQuery(arguments.ServiceTypeId));

                // Check ServiceType exists
                if (serviceType != null)
                {
                    List<DynamicFieldValueInfo> dynamicFieldValues = new List<DynamicFieldValueInfo>();
                    if (arguments.DynamicFormId.HasValue)
                    {
                        arguments.DynamicFormValueId = Guid.NewGuid();
                        var ticketProperties = arguments.GetType().GetProperties().Select(x => x.Name).ToArray();
                        dynamicFieldValues = (await QueryExecutor.ExecuteManyAsync(new GetDynamicFieldValueInfoByFormIdQuery
                        {
                            DynamicFormId = arguments.DynamicFormId.Value,
                            //Display = true
                        })).ToList().Where(x => !ticketProperties.Contains(x.Name)).ToList();
                    }

                    if (dynamicFieldValues.Count > 0)
                    {
                        Dictionary<string, List<string>> validateErrors;
                        if (!dynamicFieldValues.TryMapping(form, files, "", ServiceProvider, out validateErrors))
                        {
                            string errorMessages = string.Join(Environment.NewLine, validateErrors.Values.SelectMany(x => x).Select(x => x));
                            return new CreateEditRequestTicketResult
                            {
                                ErrorMessage = errorMessages,
                                IsSuccess = false
                            };
                        }
                        arguments.DynamicFormValueId = dynamicFieldValues.First().ObjectId;

                        // Huy Vo noted
                        // Chỗ này Nhân làm mục đích là để kiểm tra trùng như Book phòng họp,...
                        // Nhưng hiện tại store query hết dữ liệu DynamicFieldValue của một ServiceType lên
                        // => Sẽ rất nặng mỗi khi tạo/edit phiếu, cần xem xét lại
                        // Hoặc đưa config vào ServiceType, ServiceType nào bật lên thì mới check

                        #region Check data từ dynamic field có bị trùng trên toàn bộ hệ thống theo cấu hình - ticket (ticket + tasktypeId)

                        var newDynamicFieldInsert = new List<DynamicFieldValueList>();
                        var newDynamicFieldRangTimeInsert = new List<DynamicFieldValueList>();
                        var fieldsCheckDup = new List<string>();
                        var errList = new List<string>();
                        foreach (var item in dynamicFieldValues.Where(x => x.IsCheckDuplicate))
                        {
                            if (item.ViewHint.ToLower() != "datetimefull_v5")
                            {
                                fieldsCheckDup.Add(item.DisplayName);
                                newDynamicFieldInsert.Add(new DynamicFieldValueList { DynamicFieldId = item.FieldId, Value = item.Value });
                                errList.Add(string.Format("{0}: {1}", item.DisplayName, item.Value));
                            }
                            else newDynamicFieldRangTimeInsert.Add(new DynamicFieldValueList { DynamicFieldId = item.FieldId, Value = item.Value });
                        }
                        var checkCanInsertDynamicFieldValues = await QueryExecutor.ExecuteManyAsync(new CheckDynamicFieldValueDupOnTaskQuery
                        {
                            TicketId = arguments.Id.Value,
                            ServiceTypeId = arguments.ServiceTypeId,
                            DynamicFieldValueData = newDynamicFieldInsert,
                            DynamicFieldValueRangeTimeData = newDynamicFieldRangTimeInsert
                        });
                        //Ý tưởng, trên mỗi dynamicFormValue by ticket(ticket + taskTypeId) sẽ check trùng bộ dynamicField Data có enable check duplicate, 0 là ko trùng, 1 là trùng
                        foreach (var item in checkCanInsertDynamicFieldValues)
                        {
                            var kq = item.Result;
                            var dynamicFormValueId = item.DynamicFormValueId;
                            if (kq == 1)
                            {
                                var checkNewTimeOverlap = item.New_Start;
                                var checkOldTime = item.Old_End;
                                if (dynamicFormValueId != arguments.DynamicFormValueId.Value)
                                {
                                    //Bị duplicate so với đứa khác
                                    //Nếu newStart <= old end --> newStart overlap(có data), nếu ko có Old_End -> ko có check theo rangeTime
                                    if (checkOldTime.HasValue == false)
                                    {
                                        var infoMess = string.Join(",", fieldsCheckDup);
                                        throw new Exception(string.Format("Dữ liệu tại các trường: {0} đã tồn tại, vui lòng nhập lại", infoMess));
                                    }
                                    if (checkNewTimeOverlap.HasValue && checkOldTime.HasValue)
                                    {
                                        //Có check rangeTime và bị overlap
                                        var buidErrMess = string.Join(";", errList);
                                        throw new Exception(string.Format("{0} phải tới: {1} mới kết thúc, vui lòng nhập lại", buidErrMess, checkOldTime.Value.ToString()));
                                    }
                                }
                            }
                            else break;
                        }
                        //Check trùng trên DynamicTable private nằm trên ticket(task)

                        foreach (var table in dynamicFieldValues.Where(x => x.DataType == "TinyCRM.AppServices.DynamicDefinedTable.DynamicDefinedTableGridData" && x.DynamicDefinedTableSchemaId.HasValue))
                        {
                            var dynamicDefinedTable = await QueryExecutor.ExecuteOneAsync(new GetDynamicDefinedTableSchemaQuery { DynamicDefinedTableSchemaId = table.DynamicDefinedTableSchemaId.Value });
                            if (dynamicDefinedTable.RowStoredMethod == DynamicDefinedTableStoreMethod.OwnDbTable)
                            {
                                var listColName = (await QueryExecutor.ExecuteManyAsync(new GetDynamicDefinedTableColumnByIdQuery { DynamicDefinedTableSchemaId = dynamicDefinedTable.Id })).OrderBy(x => x.ColumnOrder).ToList();
                                if (listColName.Where(x => x.IsCheckDupOnDynamicForm).ToList().Count > 1)
                                {
                                    var tempId = Guid.NewGuid();
                                    string querySelect = "SELECT DISTINCT " + string.Join(",", listColName.Select(x => $"t2.{x.Name}"));
                                    querySelect = string.Format("{0} FROM dbo.DynamicDefinedTable_{1} t1 " +
                                        "JOIN dbo.DynamicDefinedTable_{1} t2 ON ", querySelect, dynamicDefinedTable.Name);

                                    var getColumnCheckDup = listColName.Where(x => x.IsCheckDupOnDynamicForm && x.DataType != "System.Nullable`1[System.DateTime]" && x.UiHint != "DateTimeFull_v5").ToList();
                                    //t2 lấy all  trừ [ chính nó (nếu là edit) và temp-new ]
                                    var queryJoin = string.Join(" AND ", getColumnCheckDup.Select(x => $"t1.{x.Name} = t2.{x.Name}")) + string.Format(" AND t2.DynamicFieldValueId <> '{0}'", tempId)
                                        + (table.Id.HasValue ? string.Format(" AND t2.DynamicFieldValueId <> '{0}'", table.Id.Value) : "");

                                    string overLapTimeWhere = "";
                                    var listColStart_End = listColName.Where(x => x.DataType == "System.Nullable`1[System.DateTime]" && x.UiHint == "DateTimeFull_v5").ToList();
                                    var getColStartTime = listColStart_End.ElementAtOrDefault(0);
                                    var getColEndTime = listColStart_End.ElementAtOrDefault(1);
                                    if (getColStartTime != null && getColEndTime != null)
                                    {
                                        overLapTimeWhere = $@"  
                                        AND (  
                                            (t2.{getColStartTime.Name} BETWEEN t1.{getColStartTime.Name} AND t1.{getColEndTime.Name})  
                                            OR (t2.{getColEndTime.Name} BETWEEN t1.{getColStartTime.Name} AND t1.{getColEndTime.Name})  
                                            OR (t1.{getColStartTime.Name} BETWEEN t2.{getColStartTime.Name} AND t2.{getColEndTime.Name})  
                                        )";
                                    }
                                    //t1 chỉ lấy tạm ko lấy chính nó (nếu là edit)
                                    var fullQuery = querySelect + queryJoin + overLapTimeWhere + string.Format("WHERE t1.DynamicFieldValueId = '{0}'", tempId);

                                    var ListItems = new List<DynamicDefinedTableCellValueData>();
                                    var rowCount = form.AllKeys.Count(x => x.Contains(string.Format("{0}.Items[", table.Name)) && x.Contains("].RowNumber"));
                                    for (int rowNumber = 0; rowNumber < rowCount; rowNumber++)
                                    {
                                        var rowItems = new List<DynamicDefinedTableCellValueData>();
                                        var ticketCreateEditArgumentsId = Guid.NewGuid();
                                        var listTypeCOl = listColName.Select(x => x.DataType.ToType()).ToList();

                                        foreach (var column in listColName)
                                        {
                                            var dataType = column.DataType.ToType();

                                            var columnIdString = form[string.Format("{0}.Items[{1}].{2}.Id", table.Name, rowNumber, column.Name)];
                                            string columnValueName = string.Format("{0}.Items[{1}].{2}.Value", table.Name, rowNumber, column.Name);
                                            string columnStringValue = form[columnValueName];

                                            try
                                            {
                                                var typeConverter = dataType.GetTypeConverter(string.IsNullOrEmpty(columnStringValue));
                                                var convertValue = typeConverter.ConvertFromString(null, CultureInfo.CurrentCulture, columnStringValue);
                                                if (convertValue != null)
                                                {
                                                    if (convertValue is DateTime)
                                                    {
                                                        var dateTime = (DateTime)convertValue;
                                                        columnStringValue = dateTime.ToString("yyyy-MM-dd HH:mm:ss.fff");
                                                    }
                                                    else columnStringValue = convertValue.ToString();
                                                }
                                            }
                                            catch { }

                                            var dynamicDefinedTableCellValueData = new DynamicDefinedTableCellValueData
                                            {
                                                Id = columnIdString.IsNotNullOrEmpty() ? new Guid(columnIdString) : Guid.NewGuid(),
                                                DynamicDefinedTableColumnId = column.Id,
                                                Value = columnStringValue,
                                                RowNumber = rowNumber,
                                                DynamicFieldValueId = tempId
                                            };
                                            ListItems.Add(dynamicDefinedTableCellValueData);
                                        }
                                    }
                                    var mappingLists = new List<string[]>
                                {
                                    new[] {"Id", "Id"},
                                    new[] { "DynamicDefinedTableSchemaId", "DynamicDefinedTableSchemaId"},
                                    new[] { "DynamicFieldValueId", "DynamicFieldValueId"},
                                    new[] { "RowNumber", "RowNumber"},
                                };
                                    DataTable tbOwnTable = new DataTable();
                                    tbOwnTable.Columns.Add("Id", typeof(Guid));
                                    tbOwnTable.Columns.Add("DynamicDefinedTableSchemaId", typeof(Guid));
                                    tbOwnTable.Columns.Add("DynamicFieldValueId", typeof(Guid));
                                    tbOwnTable.Columns.Add("RowNumber", typeof(int));
                                    foreach (var column in listColName)
                                    {
                                        mappingLists.Add(new[] { column.Name, column.Name.ToUpper() });
                                        var columnDataType = typeof(string);
                                        if (column.DataType.Contains("Int32", StringComparison.OrdinalIgnoreCase)) columnDataType = typeof(int);
                                        else if (column.DataType.Contains("Int64", StringComparison.OrdinalIgnoreCase)) columnDataType = typeof(long);
                                        else if (column.DataType.Contains("Double", StringComparison.OrdinalIgnoreCase)) columnDataType = typeof(double);
                                        else if (column.DataType.Contains("Boolean", StringComparison.OrdinalIgnoreCase)) columnDataType = typeof(bool);
                                        else if (column.DataType.Contains("DateTime", StringComparison.OrdinalIgnoreCase)) columnDataType = typeof(DateTime);
                                        tbOwnTable.Columns.Add(column.Name, columnDataType);
                                    }
                                    var rowList = ListItems.GroupBy(x => x.RowNumber).Select(x => new { RowNumber = x.Key, CellValues = x.ToList() });
                                    foreach (var rowValues in rowList)
                                    {
                                        DataRow newRow = tbOwnTable.NewRow();
                                        newRow["Id"] = Guid.NewGuid();
                                        newRow["DynamicDefinedTableSchemaId"] = table.DynamicDefinedTableSchemaId;
                                        newRow["DynamicFieldValueId"] = tempId;
                                        newRow["RowNumber"] = 0 + rowValues.RowNumber;
                                        foreach (var column in listColName)
                                        {
                                            string cellValue = null;
                                            var cellItem = rowValues.CellValues.Where(cell => cell.DynamicDefinedTableColumnId == column.Id).FirstOrDefault();
                                            if (cellItem != null) cellValue = cellItem.Value;
                                            var columnDataType = typeof(string);
                                            if (column.DataType.Contains("Int32", StringComparison.OrdinalIgnoreCase)) columnDataType = typeof(int);
                                            else if (column.DataType.Contains("Int64", StringComparison.OrdinalIgnoreCase)) columnDataType = typeof(long);
                                            else if (column.DataType.Contains("Double", StringComparison.OrdinalIgnoreCase)) columnDataType = typeof(double);
                                            else if (column.DataType.Contains("Boolean", StringComparison.OrdinalIgnoreCase))
                                            {
                                                columnDataType = typeof(bool);
                                            }
                                            else if (column.DataType.Contains("DateTime", StringComparison.OrdinalIgnoreCase))
                                            {
                                                columnDataType = typeof(DateTime);
                                            }

                                            var typeConverter = columnDataType.GetTypeConverter(string.IsNullOrEmpty(cellValue));
                                            if (cellValue.IsNullOrEmpty()) newRow[column.Name] = DBNull.Value;
                                            else newRow[column.Name] = typeConverter.ConvertFromString(null, CultureInfo.CurrentCulture, cellValue);
                                        }
                                        tbOwnTable.Rows.Add(newRow);
                                    }
                                    Repository.BulkInsertAll(tbOwnTable, string.Format("[dbo].[DynamicDefinedTable_{0}]", dynamicDefinedTable.Name), mappingLists);
                                    var checkRecordNotValidDynamicTable = await QueryExecutor.ExecuteOneAsync(new CheckDynamicFieldValueDupOnDataTableTaskQuery
                                    {
                                        DynamicTableName = dynamicDefinedTable.Name,
                                        DynamicFieldValueIdTemp = tempId,
                                        FullQuery = fullQuery
                                    });

                                    var infoNotValid = new List<string>();
                                    foreach (DataRow item in checkRecordNotValidDynamicTable.Rows)
                                    {
                                        var infoRecord = "";
                                        foreach (var col in listColName.Where(x => x.IsCheckDupOnDynamicForm && x.DataType != "System.Nullable`1[System.DateTime]" && x.UiHint != "DateTimeFull_v5").ToList())
                                        {
                                            if (col.DataType == "TinyCRM.UserAccount.UserProfileEntityData")
                                            {
                                                var rowData = item[col.Name].ToString();
                                                var user = EntitySet.Get<ApplicationUser>(new Guid(rowData));
                                                infoRecord = infoRecord.IsNullOrEmpty() ? user.FullName : " - " + user.FullName;
                                            }
                                            else infoRecord = infoRecord.IsNullOrEmpty() ? item[col.Name].ToString() : " - " + item[col.Name].ToString();
                                        }
                                        infoNotValid.Add(infoRecord);
                                    }
                                    if (infoNotValid.Count > 0)
                                    {
                                        var joinInfo = string.Join(", ", infoNotValid);
                                        var messAlert = string.Format(T["{0} đã bận. Vui lòng chọn lại"], joinInfo);
                                        return new CreateEditRequestTicketResult
                                        {
                                            ErrorMessage = messAlert,
                                            IsSuccess = false
                                        };
                                    }
                                }
                            }
                        }

                        #endregion
                    }

                    #region Validate và lấy các thông tin của Customer

                    Guid customerId = Guid.Empty;
                    string customerPhone1 = string.Empty, customerPhone2 = string.Empty, customerPhone3 = string.Empty, customerEmail = string.Empty;

                    Guid? provinceId = null;

                    // Get Customer Code, Request Ticket Code and IsoCode prefix based on Customer Type
                    string codePrefix = string.Empty;

                    ValidateRequestTicketCustomerResult validateCustomerResult = null;

                    if (!arguments.IsNoneCustomerTicket)
                    {
                        if (arguments.Customer != null)
                        {
                            customerId = arguments.Customer.Id ?? Guid.NewGuid();

                            customerPhone1 = arguments.Customer.Phone1;
                            customerPhone2 = arguments.Customer.Phone2;
                            customerPhone3 = arguments.Customer.Phone3;
                            customerEmail = arguments.Customer.Email;

                            codePrefix = IsoCodePrefixGenerator.GetIsoCodePrefix((int)arguments.Customer.Type);
                        }

                        if (arguments.Customer.AllowEdit)
                        {
                            validateCustomerResult = await ValidateRequestTicketCustomer(arguments.Customer);
                            if (!validateCustomerResult.IsSuccess)
                            {
                                return new CreateEditRequestTicketResult
                                {
                                    ErrorMessage = validateCustomerResult.ErrorMessage,
                                    IsSuccess = false
                                };
                            }

                            if (validateCustomerResult.CustomerData != null)
                            {
                                customerId = validateCustomerResult.CustomerData.Id;

                                customerPhone1 = validateCustomerResult.CustomerData.Phone1;
                                customerPhone2 = validateCustomerResult.CustomerData.Phone2;
                                customerPhone3 = validateCustomerResult.CustomerData.Phone3;
                                customerEmail = validateCustomerResult.CustomerData.Email;

                                provinceId = validateCustomerResult.CustomerData.ProvinceId;
                            }

                            if (validateCustomerResult.CustomerAlternativeAddressData != null)
                            {
                                provinceId = validateCustomerResult.CustomerAlternativeAddressData.ProvinceId;
                            }
                        }
                    }

                    #endregion

                    // Kiểm tra MultiRequestTicketMode của ServiceType
                    // Xem có bị giới hạn tạo phiếu cho cùng một khách hàng hay không
                    if (serviceType.MultiRequestTicketMode != MultiRequestTicketMode.Default)
                    {
                        if (!arguments.IsNoneCustomerTicket && customerId != Guid.Empty)
                        {
                            bool includeDeleted = false;
                            if (serviceType.MultiRequestTicketMode == MultiRequestTicketMode.NotDupAll)
                            {
                                includeDeleted = true;
                            }
                            int customerRequestTicketCount = await CountCustomerRequestTicketsAsync(serviceType.Id, customerId, includeDeleted);
                            if (customerRequestTicketCount > 0)
                            {
                                return new CreateEditRequestTicketResult
                                {
                                    ErrorMessage = T["Không thể tạo trùng phiếu cho Khách hàng với Loại dịch vụ đã chọn."],
                                    IsSuccess = false
                                };
                            }
                        }
                    }

                    string dayString = DateTime.Now.Day.ToString().PadLeft(2, '0');
                    string monthString = DateTime.Now.Month.ToString().PadLeft(2, '0');
                    string yearString = DateTime.Now.Year.ToString().Remove(0, 2);
                    if (RequestTicketCodeFormat.Contains("{YearString:4}"))
                    {
                        yearString = DateTime.Now.Year.ToString();
                    }
                    if (RequestTicketCodeFormat.Contains("{YearString:2}"))
                    {
                        yearString = DateTime.Now.Year.ToString().Remove(0, 2);
                    }

                    // Generate Request Ticket Code and IsoCode
                    int nextRequestCodeCount = await SequenceUtility.GetNextValue("TiqCode" + codePrefix, SequenceCycleType.Monthly);

                    string nextRequestDigitString = nextRequestCodeCount.ToString().PadLeft(4, '0');


                    string requestTicketCodeFormat = RequestTicketCodeFormat;
                    if (serviceType.TicketMode == TicketCodeMode.Default || serviceType.TicketMode == null || serviceType.TicketMode == TicketCodeMode.Auto && arguments.Code.IsNullOrEmpty())
                    {
                        if (requestTicketCodeFormat.IsNullOrEmpty())
                        {
                            requestTicketCodeFormat = "{CodePrefix}{RequestDigit}{MonthString}{YearString}";
                        }
                        requestTicketCodeFormat = requestTicketCodeFormat.Replace("{CodePrefix}", "{0}");
                        requestTicketCodeFormat = requestTicketCodeFormat.Replace("{RequestDigit}", "{1}");
                        requestTicketCodeFormat = requestTicketCodeFormat.Replace("{MonthString}", "{2}");
                        requestTicketCodeFormat = requestTicketCodeFormat.Replace("{YearString}", "{3}");
                        requestTicketCodeFormat = requestTicketCodeFormat.Replace("{YearString:2}", "{3}");
                        requestTicketCodeFormat = requestTicketCodeFormat.Replace("{YearString:4}", "{3}");
                        requestTicketCodeFormat = requestTicketCodeFormat.Replace("{DayString}", "{4}");

                        arguments.Code = string.Format(requestTicketCodeFormat, codePrefix, nextRequestDigitString, monthString, yearString, dayString);
                    }

                    if (serviceType.TicketMode == TicketCodeMode.InputCode || serviceType.TicketMode == TicketCodeMode.Auto && arguments.Code.IsNotNullOrEmpty())
                    {
                        if (arguments.Code.IsNullOrEmpty())
                        {
                            return new CreateEditRequestTicketResult
                            {
                                IsSuccess = false,
                                ErrorMessage = T["Nhập mã số phiếu"]
                            };
                        }
                        var requestTicketData = await QueryExecutor.ExecuteManyAsync(new GetRequestTicketByCodeQuery { Code = arguments.Code });
                        if (requestTicketData.Count() > 0)
                        {
                            return new CreateEditRequestTicketResult
                            {
                                IsSuccess = false,
                                ErrorMessage = T["Đã tồn tại mã số phiếu này"]
                            };
                        }
                    }

                    if (serviceType.TicketMode == TicketCodeMode.TemplateCode)
                    {
                        if (serviceType.TemplateTicketCode.IsNullOrEmpty())
                        {
                            return new CreateEditRequestTicketResult
                            {
                                IsSuccess = false,
                                ErrorMessage = T["ServiceType có Id: \"{0}\" không có template tạo mã phiếu", arguments.ServiceTypeId]
                            };
                        }

                        string templateTicketCode= serviceType.TemplateTicketCode;
                        string getSequence = string.Empty;
                        try
                        {
                            var cvstring = templateTicketCode.Substring(templateTicketCode.IndexOf("{{"));
                            getSequence = cvstring;
                        } catch
                        {
                            return new CreateEditRequestTicketResult
                            {
                                IsSuccess = false,
                                ErrorMessage = T["Template tạo mã phiếu của ServiceType có Id: \"{0}\" không có sequence", arguments.ServiceTypeId]
                            };
                        }
                        int? sequenceLength = null;
                        string sequenceType = string.Empty;
                        var CycleType = SequenceCycleType.None;
                        var replaceSequnce = getSequence.Substring(0, getSequence.IndexOf("}}") + 2);
                        var nameSequnce = replaceSequnce.Replace("{{", null); 
                        nameSequnce = nameSequnce.Replace("}}", null);
                        if (nameSequnce.Contains(":"))
                        {
                            var arrValue = nameSequnce.Split(':');
                            nameSequnce = arrValue[0];
                            var configstring = arrValue[1];
                            if (configstring.Contains("$"))
                            {
                                var checkstring = configstring.Substring(0, configstring.IndexOf("$"));
                                if (checkstring.IsNotNullOrEmpty())
                                {
                                    sequenceLength = Int32.Parse(checkstring);
                                }
                                sequenceType = configstring.Substring(configstring.IndexOf("$") + 1).ToLower();
                            }
                            else
                            {
                                if (configstring.IsNotNullOrEmpty())
                                {
                                    sequenceLength = Int32.Parse(configstring);
                                }
                            }
                        }
                        if (sequenceType == "daily") CycleType = SequenceCycleType.Daily;
                        if (sequenceType == "minute") CycleType = SequenceCycleType.Minutely;
                        if (sequenceType == "hour") CycleType = SequenceCycleType.Hourly;                        
                        if (sequenceType == "month") CycleType = SequenceCycleType.Monthly;
                        if (sequenceType == "year") CycleType = SequenceCycleType.Yearly;

                        int nextRequestSequenceCount = await SequenceUtility.GetNextValue(nameSequnce, CycleType);
                        string nextRequestSequenceString;
                        if (sequenceLength.HasValue)
                        {
                            nextRequestSequenceString = nextRequestSequenceCount.ToString().PadLeft(sequenceLength.Value, '0');
                        } else
                        {
                            nextRequestSequenceString = nextRequestSequenceCount.ToString();
                        }
                        
                        templateTicketCode = templateTicketCode.Replace("{DayString}", dayString);
                        templateTicketCode = templateTicketCode.Replace("{MonthString}", monthString);
                        templateTicketCode = templateTicketCode.Replace("{YearString:4}", DateTime.Now.Year.ToString());
                        templateTicketCode = templateTicketCode.Replace("{YearString:2}", DateTime.Now.Year.ToString().Remove(0, 2));                        
                        templateTicketCode = templateTicketCode.Replace(replaceSequnce, nextRequestSequenceString);

                        arguments.Code = templateTicketCode;
                        var requestTicketData = await QueryExecutor.ExecuteManyAsync(new GetRequestTicketByCodeQuery { Code = arguments.Code });
                        if (requestTicketData.Count() > 0)
                        {
                            return new CreateEditRequestTicketResult
                            {
                                IsSuccess = false,
                                ErrorMessage = T["Mã phiếu theo template: \"{1}\" đã tồn tại. Đề nghị đổi template tạo mã phiếu trong ServiceType có Id: \"{0}\"", arguments.ServiceTypeId, arguments.Code]
                            };
                        }
                    }

                    if (arguments.UsingIsoCode)
                    {
                        yearString = DateTime.Now.Year.ToString().Remove(0, 2);
                        if (RequestTicketIsoCodeFormat.Contains("{YearString:4}"))
                        {
                            yearString = DateTime.Now.Year.ToString();
                        }
                        if (RequestTicketIsoCodeFormat.Contains("{YearString:2}"))
                        {
                            yearString = DateTime.Now.Year.ToString().Remove(0, 2);
                        }

                        string requestTicketIsoCodeFormat = RequestTicketIsoCodeFormat;
                        if (requestTicketIsoCodeFormat.IsNullOrEmpty())
                        {
                            requestTicketIsoCodeFormat = "{IsoRequestDigit}/{MonthString}/DVKH.{CodePrefix}/{YearString}";
                        }
                        requestTicketIsoCodeFormat = requestTicketIsoCodeFormat.Replace("{CodePrefix}", "{2}");
                        requestTicketIsoCodeFormat = requestTicketIsoCodeFormat.Replace("{IsoRequestDigit}", "{0}");
                        requestTicketIsoCodeFormat = requestTicketIsoCodeFormat.Replace("{MonthString}", "{1}");
                        requestTicketIsoCodeFormat = requestTicketIsoCodeFormat.Replace("{YearString}", "{3}");
                        requestTicketIsoCodeFormat = requestTicketIsoCodeFormat.Replace("{YearString:2}", "{3}");
                        requestTicketIsoCodeFormat = requestTicketIsoCodeFormat.Replace("{YearString:4}", "{3}");
                        requestTicketIsoCodeFormat = requestTicketIsoCodeFormat.Replace("{DayString}", "{4}");

                        int nextIsoCodeCount = await SequenceUtility.GetNextValue("TiqIsoCode" + codePrefix, SequenceCycleType.Monthly);

                        string nextIsoDigitString = nextIsoCodeCount.ToString().PadLeft(4, '0');
                        arguments.IsoCode = string.Format(requestTicketIsoCodeFormat, nextIsoDigitString, monthString, codePrefix, yearString, dayString);
                    }

                    // Process DueTime
                    DateTime? processSoonDueDate = null;
                    if (serviceType.ProcessDueTimeId.IsNotNullOrEmpty())
                    {
                        var processDueTimeData = await QueryExecutor.ExecuteOneAsync(new GetDueTimeByIdQuery { Id = serviceType.ProcessDueTimeId.Value });
                        arguments.ProcessDueTimeId = processDueTimeData.CurrentDueTimeReferenceId;
                        arguments.ProcessDueDate = await DueTimeUtility.GetDueTimeAsync(serviceType.ProcessDueTimeId.Value, DateTime.Now);
                        processSoonDueDate = await DueTimeUtility.GetProcessSoonDueTimeAsync(serviceType.ProcessDueTimeId.Value, DateTime.Now);
                    }

                    // Accept DueTime
                    if (serviceType.AcceptDueTimeId.IsNotNullOrEmpty())
                    {
                        var acceptDueTimeData = await QueryExecutor.ExecuteOneAsync(new GetDueTimeByIdQuery { Id = serviceType.AcceptDueTimeId.Value });
                        arguments.AcceptDueTimeId = acceptDueTimeData.CurrentDueTimeReferenceId;
                        arguments.AcceptDueDate = await DueTimeUtility.GetDueTimeAsync(serviceType.AcceptDueTimeId.Value, DateTime.Now);
                    }

                    // Nếu phiếu chưa có Người phụ trách xử lý
                    // Nếu Loại dịch vụ có Phòng ban chuyên phụ trách phiếu khi không có Owner
                    // Tìm User có phân quyền "Đại diện phòng ban nhận xử lý phiếu" để chuyển xử lý phiếu
                    if (arguments.OwnerId.IsNullOrEmpty() && serviceType.TicketOwnerDefaultOrganizationId.IsNotNullOrEmpty())
                    {
                        var defaultTicketOwnerUser = await QueryExecutor.ExecuteOneAsync(new GetUserProfileByAccessOrgQuery { CodeAccessId = ConstCodePermissions.OrgTicketOwnerDelegate, OrganizationId = serviceType.TicketOwnerDefaultOrganizationId.Value });
                        if (defaultTicketOwnerUser != null)
                        {
                            arguments.OwnerId = defaultTicketOwnerUser.Id;
                            arguments.Status = RequestTicketStatus.Forward;
                        }
                    }

                    CreateEditRequestTicketResult createEditRequestTicketResult = new CreateEditRequestTicketResult
                    {
                        IsSuccess = true,
                        WarningMessage = new List<string>(),
                        RequestTicketCode = arguments.Code,
                        SuccessMessage = T["Phiếu đã được tạo thành công."],
                        ReturnUrl = string.Format("{0}/RequestTicket/Edit?RequestTicketId={1}", ApplicationUrl.Trim('/'), arguments.Id.Value),
                    };

                    // Create Request Ticket
                    var insertRequestTicketCommand = new InsertRequestTicketCommand
                    {
                        Id = arguments.Id.Value,
                        CustomerId = customerId,
                        CustomerAlternativeAddressId = arguments.Customer != null ? arguments.Customer.CustomerAlternativeAddressId : null,
                        Type = arguments.IsNoneCustomerTicket ? 0 : (int)arguments.Customer.Type,
                        SourceChannel = arguments.SourceChannel,
                        Level1Id = serviceType.Level1Id,
                        Level2Id = serviceType.Level2Id,
                        Level3Id = serviceType.Level3Id,
                        Level4Id = serviceType.Level4Id,
                        ServiceTypeId = arguments.ServiceTypeId,
                        Notes = arguments.Notes,
                        OwnerId = arguments.OwnerId == Guid.Empty ? (Guid?)null : arguments.OwnerId,
                        Status = arguments.Status,
                        Code = arguments.Code,
                        IsoCode = arguments.IsoCode,
                        OpenTicketDate = arguments.OpenTicketDate,
                        ProcessDueDate = arguments.ProcessDueDate,
                        AcceptDueDate = arguments.AcceptDueDate,
                        ProcessDueTimeId = arguments.ProcessDueTimeId,
                        AcceptDueTimeId = arguments.AcceptDueTimeId,
                        BehaviorClassifications = arguments.BehaviorClassifications,
                        DelegatedTicket = arguments.DelegatedTicket,
                        TicketDelegatedRelationship = arguments.DelegatedRelationship,
                        DelegatedOtherRelationship = arguments.DelegatedOtherRelationship,

                        RpName = arguments.RpName,
                        RpPhone = arguments.RpPhone,
                        RpEmail = arguments.RpEmail,

                        Treatment = arguments.Treatment,
                        DifficultyDegree = arguments.DifficultyDegree,

                        CustomerPhone1 = customerPhone1,
                        CustomerPhone2 = customerPhone2,
                        CustomerPhone3 = customerPhone3,

                        CustomerEmail = customerEmail,
                        ProvinceId = provinceId,

                        ProcessSoonDueDate = processSoonDueDate,
                        DynamicFormValueId = dynamicFieldValues.Count > 0 ? arguments.DynamicFormValueId : (Guid?)null,
                        ContextToken = arguments.ContextToken,
                        IsNoneCustomerTicket = arguments.IsNoneCustomerTicket,
                        AuditSessionId = auditSessionId,

                        PlannedDate = arguments.PlannedDate,
                        PartId = arguments.PartId,
                        BudgetId = arguments.BudgetId,
                        DepartmentId = arguments.DepartmentId,
                        ProspectAssignmentId = arguments.ProspectAssignmentId,
                        ProspectId = arguments.ProspectId,
                        CampaignId = arguments.CampaignId,
                        WorkflowId = serviceType.WorkflowId,
                        ObjectApproveType = arguments.ObjectApproveType,
                        ObjectApproveId = arguments.ObjectApproveId,
                        PlannedDate_Begin = arguments.PlannedDate_Begin,
                        PlannedDate_End = arguments.PlannedDate_End
                    };

                    if (arguments.OwnerId.IsNotNullOrEmpty())
                    {
                        var ownedOrganizationData = await QueryExecutor.ExecuteOneAsync(new GetOrganizationByUserIdQuery(arguments.OwnerId.Value));
                        if (ownedOrganizationData != null)
                        {
                            insertRequestTicketCommand.OwnedByOrganizationId = ownedOrganizationData.Id;
                        }
                    }

                    var createdOrganizationData = await QueryExecutor.ExecuteOneAsync(new GetOrganizationByUserIdQuery(UserService.GetCurrentUser().Id));
                    if (createdOrganizationData != null)
                    {
                        insertRequestTicketCommand.CreatedByOrgId = createdOrganizationData.Id;
                    }

                    if (ignoreTransaction)
                    {
                        await CommandExecutor.ExecuteAsync(insertRequestTicketCommand);
                        if (dynamicFieldValues.Count > 0)
                        {
                            foreach (var i in dynamicFieldValues)
                            {
                                if (!i.Id.HasValue) i.Id = Guid.NewGuid();
                            }
                            await dynamicFieldValues.SaveAsync(insertRequestTicketCommand.Id, "dbo.RequestTicket", auditSessionId, auditDynamicForm, CommandExecutor);
                        }
                    }
                    else
                    {
                        using (var trans = LocalTransactionManager.CreateTransaction())
                        {
                            try
                            {
                                await CommandExecutor.ExecuteAsync(insertRequestTicketCommand);
                                if (dynamicFieldValues.Count > 0)
                                {
                                    foreach (var i in dynamicFieldValues)
                                    {
                                        if (!i.Id.HasValue) i.Id = Guid.NewGuid();
                                    }
                                    var savedDynamicFieldValues = dynamicFieldValues.Where(dfv => dfv.FieldType != FieldType.Static).ToList();
                                    await savedDynamicFieldValues.SaveAsync(insertRequestTicketCommand.Id, "dbo.RequestTicket", auditSessionId, auditDynamicForm, CommandExecutor);
                                }
                                trans.Commit();
                            }
                            catch (Exception ex)
                            {
                                trans.Rollback();
                                return new CreateEditRequestTicketResult
                                {
                                    ErrorMessage = ex.Message,
                                    IsSuccess = false
                                };
                            }
                        }
                    }

                    if (arguments.IsFromAPI)
                    {
                        if (dynamicFieldValues.Any(dfv => dfv.FieldType == FieldType.Calculated || dfv.Inject.IsNotNullOrEmpty()))
                        {
                            await CommandExecutor.ExecuteAsync(new ReCalculateDynamicFormValueCommand { DynamicFormValueId = arguments.DynamicFormValueId.Value });
                        }
                    }

                    var ticketInfo = await QueryExecutor.ExecuteOneAsync(new GetRequestTicketByIdQuery(arguments.Id.Value));

                    if (!arguments.IgnoreCreateFirstTask)
                    {
                        try
                        {
                            // Create new Phase when Request Ticket is created
                            await Repository.SaveAsync(new PhaseEntity
                            {
                                TicketId = arguments.Id.Value,
                                Order = 1,
                                Status = PhaseStatus.InProcess
                            });

                            // Tạo "các" TÁC VỤ đầu tiên của phiếu
                            string errorMessage = await CreateFirstTasksAsync(ticketInfo, serviceType, arguments.Id.Value, arguments.OwnerId, arguments.PlannedDate);
                            if (errorMessage.IsNotNullOrEmpty())
                            {
                                createEditRequestTicketResult.WarningMessage.Add(errorMessage);
                            }
                        }
                        catch (Exception ex)
                        {
                            Logger.LogError(ex, ex.Message);
                            createEditRequestTicketResult.WarningMessage.Add(T["Lỗi tạo tác vụ tự động khi tạo phiếu: {0}", ex.Message]);

                            CreateAutoNextTaskErrorLogCommand createAutoNextTaskErrorLogCommand = new CreateAutoNextTaskErrorLogCommand();
                            createAutoNextTaskErrorLogCommand.Id = Guid.NewGuid();
                            createAutoNextTaskErrorLogCommand.RequestTicketId = arguments.Id.Value;
                            createAutoNextTaskErrorLogCommand.ErrorMessage = T["Lỗi tạo tác vụ tự động khi tạo phiếu: {0}", ex.Message];
                            await CommandExecutor.ExecuteAsync(createAutoNextTaskErrorLogCommand);
                        }
                    }

                    try
                    {
                        var customer_Id = insertRequestTicketCommand.CustomerId;
                        var customer_Email = insertRequestTicketCommand.CustomerEmail;
                        var customer_Phone = insertRequestTicketCommand.CustomerPhone1.IsNullOrEmpty() ? (insertRequestTicketCommand.CustomerPhone2.IsNullOrEmpty() ? insertRequestTicketCommand.CustomerPhone3 : insertRequestTicketCommand.CustomerPhone2) : insertRequestTicketCommand.CustomerPhone1;
                        if (arguments.Customer != null)
                        {                           
                            if (!arguments.Customer.AllowEdit)
                            {
                                customer_Id = arguments.Customer.Id ?? Guid.Empty;
                                var c = EntitySet.Get<CustomerEntity>(customer_Id);
                                if (c != null)
                                {
                                    customer_Email = c.Email;
                                    customer_Phone = c.Phone1.IsNotNullOrEmpty() ? c.Phone1 : c.Phone2.IsNotNullOrEmpty() ? c.Phone2 : c.Phone3;
                                }
                            }
                        }
                        EventBus.Publish(new RequestTicketCreatedEvent
                        {
                            RequestTicketId = insertRequestTicketCommand.Id,
                            ServiceTypeId = insertRequestTicketCommand.ServiceTypeId,
                            CustomerId = customer_Id,
                            CreatedBy = UserService.GetCurrentUser().Id,
                            OwnerId = insertRequestTicketCommand.OwnerId,
                            OwnedByOrganizationId = insertRequestTicketCommand.OwnedByOrganizationId,
                            CreatedDate = DateTime.Now,
                            PhoneNumber = customer_Phone,
                            Email = customer_Email,
                            DynamicFormValueId = insertRequestTicketCommand.DynamicFormValueId.HasValue ? insertRequestTicketCommand.DynamicFormValueId.Value : Guid.Empty
                        });

                        Dictionary<string, Poptech.CEP.ClientIntegration.DynamicFieldValueData> dynamicFormValueDictionary = new Dictionary<string, Poptech.CEP.ClientIntegration.DynamicFieldValueData>();
                        dynamicFieldValues.ForEach(x => dynamicFormValueDictionary.Add(x.Name, new Poptech.CEP.ClientIntegration.DynamicFieldValueData
                        {
                            Id = x.Id ?? Guid.Empty,
                            Name = x.Name,
                            Value = x.Value
                        }));

                        if (RequestTicketCreatedHanlders != null && RequestTicketCreatedHanlders.Any())
                        {
                            RequestTicketCreatedHanlders.ToList().ForEach(x =>
                             {
                                 try
                                 {
                                     x.Handle(Mapper.Map<Poptech.CEP.ClientIntegration.RequestTicket.RequestTicketData>(insertRequestTicketCommand), dynamicFormValueDictionary);
                                 }
                                 catch (Exception) { }
                                 finally { }
                             });
                        }

                        // Create ProductRequestTicket
                        if (productRequestTickets != null && productRequestTickets.Any())
                        {
                            foreach (var item in productRequestTickets)
                            {
                                item.RequestTicketId = arguments.Id.Value;
                                var inserProductRequestTicketCommand = new InsertProductRequestTicketCommand
                                {
                                    Id = item.Id,
                                    ProductId = item.ProductId,
                                    RequestTicketId = item.RequestTicketId,
                                    ServiceCategoryId = item.ServiceCategoryId,
                                    FactoryId = item.FactoryId,
                                    DueDate = item.DueDate,
                                    AffectedQuantity = item.AffectedQuantity,
                                    BoughtQuantity = item.BoughtQuantity,
                                    LeftQuantity = item.LeftQuantity,
                                    AffectedProductUsed = item.AffectedProductUsed == UsedStatus.Used,
                                    BoughtFromStore = item.BoughtFromStore,
                                    MethodOfStorageAtHome = item.MethodOfStorageAtHome,
                                    MethodOfStorageInStore = item.MethodOfStorageInStore,
                                    MethodOfUsingProduct = item.MethodOfUsingProduct,
                                    TargetUserOfProduct = item.TargetUserOfProduct,
                                    DeliverDate = item.DeliverDate,
                                    DeliverCode = item.DeliverCode,
                                    ProductionLine = item.ProductionLine,
                                };
                                await CommandExecutor.ExecuteAsync(inserProductRequestTicketCommand);
                            }
                        }

                        #region Callback

                        if (arguments.TbCallbackData != null && arguments.TbCallbackData.CallbackDate.HasValue)
                        {
                            await CommandExecutor.ExecuteAsync(new CreateEditTbCallbackCommand
                            {
                                Id = Guid.NewGuid(),
                                CallbackDate = arguments.TbCallbackData.CallbackDate.Value,
                                NextReminderTime = arguments.TbCallbackData.CallbackDate.Value,
                                Notes = arguments.TbCallbackData.Notes,
                                Status = TbCallbackSatus.Scheduled,
                                ReferenceObjectId = arguments.Id.Value
                            });
                        }

                        #endregion

                        if (arguments.OwnerId.IsNullOrEmpty())
                        {
                            await ServiceProvider.GetRequiredKeyedService<INotificationCaseService>("noowner.ticket.created").CreateAsync(ticketInfo);
                            await ServiceProvider.GetRequiredKeyedService<INotificationCaseService>("noowner.ticket.accept.duetime").CreateAsync(ticketInfo);
                            await ServiceProvider.GetRequiredKeyedService<INotificationCaseService>("noowner.ticket.process.duetime").CreateAsync(ticketInfo);
                        }
                        else
                        {
                            // Notification for Owner
                            await ServiceProvider.GetRequiredKeyedService<INotificationCaseService>("ticket.assigned.notifytoowner").CreateAsync(ticketInfo);
                            await ServiceProvider.GetRequiredKeyedService<INotificationCaseService>("ticket.accept.duetime.notifytoowner").CreateAsync(ticketInfo);

                            // Notification for Creator
                            await ServiceProvider.GetRequiredKeyedService<INotificationCaseService>("ticket.accept.duetime.notifytocreator").CreateAsync(ticketInfo);

                            await ServiceProvider.GetRequiredKeyedService<INotificationCaseService>("ticket.soonprocess.duetime.notifytoowner").CreateAsync(ticketInfo);
                            await ServiceProvider.GetRequiredKeyedService<INotificationCaseService>("ticket.process.duetime.notifytoowner").CreateAsync(ticketInfo);
                        }

                        createEditRequestTicketResult.NeedToCreateTaskWarning = false;
                        if (serviceType.WarningTaskCreatingWhenCreateTicket)
                        {
                            var taskTypeCount = (await QueryExecutor.ExecuteManyAsync(new GetTaskTypeInWorkflowByServiceTypeIdQuery { ServiceTypeId = arguments.ServiceTypeId })).Count();
                            if (taskTypeCount > 0)
                            {
                                if (arguments.Id.HasValue)
                                {
                                    var taskCount = (await QueryExecutor.ExecuteManyAsync(new GetTaskByRequestTicketIdQuery(arguments.Id.Value))).Count();
                                    if (taskCount == 0)
                                    {
                                        createEditRequestTicketResult.NeedToCreateTaskWarning = true;
                                    }
                                }
                            }
                        }

                        // Link RequestTicket & CALL
                        if (arguments.ibUCID.IsNotNullOrEmpty())
                        {
                            var id = Guid.NewGuid();
                            await CommandExecutor.ExecuteAsync(new InsertRequestTicketCallCommand
                            {
                                Id = id,
                                RequestTicketId = arguments.Id.Value,
                                UCID = arguments.ibUCID
                            });
                            EventBus.Publish(new ContactCallSubmittedEvent { ContactCallId = id, InteractionId = id });
                        }

                        // Link RequestTicket & Webchat
                        if (arguments.RequestTicketInteraction != null)
                        {
                            if (arguments.RequestTicketInteraction.InteractionType == RequestTicketInteractionType.WebChat && arguments.RequestTicketInteraction.WorkItemID.IsNotNullOrEmpty())
                            {
                                await CommandExecutor.ExecuteAsync(new InsertRequestTicketWebchatCommand
                                {
                                    RequestTicketId = arguments.Id.Value,
                                    WorkItemID = arguments.RequestTicketInteraction.WorkItemID.Value
                                });
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        Logger.LogError(ex, ex.Message);
                        createEditRequestTicketResult.WarningMessage.Add(T["Lỗi sau khi tạo phiếu: {0}", ex.Message]);
                    }

                    return createEditRequestTicketResult;
                }
                else
                {
                    return new CreateEditRequestTicketResult
                    {
                        ErrorMessage = T["Không tìm thấy loại dịch vụ"],
                        IsSuccess = false
                    };
                }
            }
            else
            {
                return new CreateEditRequestTicketResult
                {
                    ErrorMessage = T["Thông tin phiếu không hợp lệ"],
                    IsSuccess = false
                };
            }
        }

        public async Task<CreateEditRequestTicketResult> CreateRequestTicketAsync(Guid customerId, Guid serviceTypeId, Guid ownerId, Guid currentUserId, List<DynamicFieldValueInfo> dynamicFieldList, bool syncLinkedField = true, bool checkExistedRequestTicket = true, bool auditDynamicForm = true)
        {
            CreateEditRequestTicketResult createRequestTicketResult = new CreateEditRequestTicketResult();

            NameValueCollection formValues = new NameValueCollection();

            RequestTicketCreateEditArguments requestTicketCreateEditArguments = new RequestTicketCreateEditArguments();
            requestTicketCreateEditArguments.IsNew = true;
            requestTicketCreateEditArguments.Id = Guid.NewGuid();

            requestTicketCreateEditArguments.ServiceTypeId = serviceTypeId;
            requestTicketCreateEditArguments.OwnerId = ownerId;

            if (dynamicFieldList == null)
            {
                var serviceType = await QueryExecutor.ExecuteOneAsync(new GetServiceTypeByIdQuery(serviceTypeId));
                if (serviceType.DynamicFormId.IsNotNullOrEmpty())
                {
                    dynamicFieldList = (await QueryExecutor.ExecuteManyAsync(new GetDynamicFieldValueInfoByFormIdQuery { DynamicFormId = serviceType.DynamicFormId.Value, Display = true })).ToList();
                }
            }
            var firstDynamicField = dynamicFieldList.FirstOrDefault();
            if (firstDynamicField != null)
            {
                requestTicketCreateEditArguments.DynamicFormId = firstDynamicField.FormId;
            }

            requestTicketCreateEditArguments.Customer = new RequestTicketCustomer();
            requestTicketCreateEditArguments.Customer.Id = customerId;
            requestTicketCreateEditArguments.Customer.AllowEdit = false;

            requestTicketCreateEditArguments.Status = RequestTicketStatus.Forward;
            if (ownerId == currentUserId)
            {
                requestTicketCreateEditArguments.Status = RequestTicketStatus.InProgress;
            }

            if (dynamicFieldList.Count > 0)
            {
                foreach (var field in dynamicFieldList)
                {
                    if (field.DynamicDefinedTableSchemaId.IsNotNullOrEmpty())
                    {
                        formValues.Add(string.Format("{0}.DynamicDefinedTableSchemaId", field.Name), field.DynamicDefinedTableSchemaId.Value.ToString());
                    }
                }

                if (syncLinkedField)
                {
                    int linkedFieldCount = dynamicFieldList.Where(dfd => dfd.FieldType == FieldType.Linked).Count();
                    if (linkedFieldCount > 0)
                    {
                        List<Guid> keys = new List<Guid>();
                        keys.Add(currentUserId);
                        keys.Add(customerId);

                        var userProfiles = await QueryExecutor.ExecuteOneAsync(new GetUserProfileByIdQuery(currentUserId));
                        if (userProfiles.OrganizationId.HasValue) keys.Add(userProfiles.OrganizationId.Value);
                        var role = await QueryExecutor.ExecuteOneAsync(new GetRoleByUserIdQuery { UserId = currentUserId });
                        if (role != null) keys.Add(role.Id);

                        var linkedDynamicFieldList = (await QueryExecutor.ExecuteManyAsync(new GetSourceValueOfDynamicFieldQuery() { DynamicFieldValueInfo = dynamicFieldList, Keys = keys })).ToList();
                        foreach (var linkedDynamicField in linkedDynamicFieldList)
                        {
                            if (linkedDynamicField.FieldType == FieldType.Linked)
                            {
                                formValues.Add(linkedDynamicField.Name, linkedDynamicField.Value);
                            }
                        }
                    }
                }
            }

            try
            {
                createRequestTicketResult = await CreateRequestTicketAsync(requestTicketCreateEditArguments, new List<InsertProductRequestTicketItem>(), formValues, null, checkExistedRequestTicket, auditDynamicForm);
            }
            catch (Exception ex)
            {
                createRequestTicketResult.IsSuccess = false;
                createRequestTicketResult.ErrorMessage = ex.Message;
            }

            return createRequestTicketResult;
        }

        public async Task<bool> CheckUpdateDefinedTableAsync(Guid serviceTypeId, Guid ticketId)
        {
            var isEdit = true;
            var serviceType = await QueryExecutor.ExecuteOneAsync(new GetServiceTypeByIdQuery(serviceTypeId));
            if (serviceType.WorkflowId.IsNotNullOrEmpty())
            {
                var workflow = await QueryExecutor.ExecuteOneAsync(new GetWorkflowByIdQuery { Id = serviceType.WorkflowId.Value });
                if (workflow != null && workflow.ProcessType == WorkflowProcessType.Approval)
                {
                    bool editableApprovalTicket = false;

                    // Chỉ có người ĐANG phụ trách Task "Soạn thảo" mới được phép cập nhật phiếu
                    var phaseList = await QueryExecutor.ExecuteManyAsync(new GetPhaseDataQuery { TicketId = ticketId });
                    foreach (var phaseItem in phaseList)
                    {
                        foreach (var taskItem in phaseItem.TaskList)
                        {
                            if (taskItem.IsDraftingStepTaskType && taskItem.AssignedTo == UserService.GetCurrentUser().Id && taskItem.Status != TaskStatus.Done)
                            {
                                editableApprovalTicket = true;
                                break;
                            }
                        }
                        if (editableApprovalTicket)
                        {
                            break;
                        }
                    }

                    if (!editableApprovalTicket)
                    {
                        isEdit = false; 
                    }
                }
            }
            return isEdit;
        }

        public async Task<CreateEditRequestTicketResult> EditRequestTicketAsync(RequestTicketCreateEditArguments arguments, IEnumerable<InsertProductRequestTicketItem> productRequestTickets, NameValueCollection form, IFormFileCollection files)
        {
            var dynamicModelProperties = RequestTicketCreateEditDynamicModel.GetModelPropertyList(Configuration);

            RequestTicketDynamicModelData requestTicketDynamicModelData = await RequestTicketDynamicModelAppService.GetRequestTicketDynamicModelAsync(new GetRequestTicketDynamicModelArguments { IsNoneCustomerTicket = arguments.IsNoneCustomerTicket, CreatedMode = arguments.IsNew, ServiceTypeId = arguments.ServiceTypeId });
            if (requestTicketDynamicModelData != null && requestTicketDynamicModelData.DynamicModelJson.IsNotNullOrEmpty())
            {
                DynamicCreateEditModel dynamicCreateEditModel = JsonConvert.DeserializeObject<DynamicCreateEditModel>(requestTicketDynamicModelData.DynamicModelJson);
                if (dynamicCreateEditModel != null)
                {
                    List<DynamicCreateEditModelProperty> dynamicCreateEditModelProperties = new List<DynamicCreateEditModelProperty>();
                    foreach (var propertyRowGroup in dynamicCreateEditModel.PropertyRowGroupList)
                    {
                        dynamicCreateEditModelProperties.AddRange(propertyRowGroup.Properties);
                    }

                    foreach (var dynamicModelProperty in dynamicModelProperties)
                    {
                        foreach (var dynamicCreateEditModelProperty in dynamicCreateEditModelProperties)
                        {
                            if (dynamicModelProperty.Name.IsEqualIgnoreCase(dynamicCreateEditModelProperty.PropertyName))
                            {
                                dynamicModelProperty.IsRequired = dynamicCreateEditModelProperty.IsRequired;
                                dynamicModelProperty.DisplayName = dynamicCreateEditModelProperty.Label;
                            }
                        }
                    }
                }
            }

            dynamicModelProperties.MapObjectValueAndAttributes(arguments, arguments.Id);

            Dictionary<string, List<string>> dynamicModelPropertiesValidateErrors;
            if (!dynamicModelProperties.TryMapping(arguments, form, files, "", out dynamicModelPropertiesValidateErrors))
            {
                string errorMessages = string.Join("<br/>", dynamicModelPropertiesValidateErrors.Values.SelectMany(x => x).Select(x => x));
                return new CreateEditRequestTicketResult
                {
                    ErrorMessage = errorMessages,
                    IsSuccess = false
                };
            }

            var auditSessionId = Guid.NewGuid();

            // Find ServiceType
            var serviceType = await QueryExecutor.ExecuteOneAsync(new GetServiceTypeByIdQuery(arguments.ServiceTypeId));
            if (serviceType != null)
            {
                TinyCRM.RequestTicket.Queries.RequestTicketData oldRequestTicketData = null;
                var requestTicketData = await QueryExecutor.ExecuteOneAsync(new GetRequestTicketByIdQuery(arguments.Id.Value));
                oldRequestTicketData = requestTicketData;
                if (requestTicketData.Status == RequestTicketStatus.Done)
                {
                    return new CreateEditRequestTicketResult
                    {
                        ErrorMessage = T["Phiếu yêu cầu đã hoàn thành không thể chỉnh sửa"],
                        IsSuccess = false
                    };
                }

                #region Phiếu thuộc Workflow Trình duyệt?
                
                if (serviceType.WorkflowId.IsNotNullOrEmpty())
                {
                    var workflow = await QueryExecutor.ExecuteOneAsync(new GetWorkflowByIdQuery { Id = serviceType.WorkflowId.Value });
                    if (workflow != null && workflow.ProcessType == WorkflowProcessType.Approval)
                    {
                        Guid currentUserId = UserService.GetCurrentUser().Id;

                        // Chỉ có người ĐANG phụ trách Task "Soạn thảo" mới được phép cập nhật phiếu
                        var phaseList = await QueryExecutor.ExecuteManyAsync(new GetPhaseDataQuery { TicketId = requestTicketData.Id });

                        // Kiểm tra Quy trình Phê duyệt đã được kích hoạt (Có tác vụ được tạo) hay chưa?
                        // Nếu chưa, chỉ cho phép người tạo phiếu, hoặc người phụ trách phiếu mới được phép edit phiếu
                        bool hasTask = phaseList.Where(p => p.TaskList.Count() > 0).Any();
                        if (!hasTask)
                        {
                            if (currentUserId != requestTicketData.CreatedBy && currentUserId != requestTicketData.OwnerId)
                            {
                                return new CreateEditRequestTicketResult
                                {
                                    ErrorMessage = T["Bạn không có phân quyền cập nhật phiếu yêu cầu này. Vui lòng liên hệ quản trị để biết thêm thông tin."],
                                    IsSuccess = false
                                };
                            }
                        }
                        else
                        {
                            bool editableApprovalTicket = false;

                            // Chỉ có người ĐANG phụ trách Task "Soạn thảo" mới được phép cập nhật phiếu
                            foreach (var phaseItem in phaseList)
                            {
                                foreach (var taskItem in phaseItem.TaskList)
                                {
                                    if (taskItem.IsDraftingStepTaskType && taskItem.AssignedTo == currentUserId && taskItem.Status != TaskStatus.Done)
                                    {
                                        editableApprovalTicket = true;
                                        break;
                                    }
                                }
                                if (editableApprovalTicket)
                                {
                                    break;
                                }
                            }

                            if (!editableApprovalTicket)
                            {
                                return new CreateEditRequestTicketResult
                                {
                                    ErrorMessage = T["Không thể cập nhật nội dung phiếu đang trình duyệt. Phiếu chỉ có thể cập nhật bởi người phụ trách ở các bước soạn thảo."],
                                    IsSuccess = false
                                };
                            }
                        }
                    }
                }

                #endregion

                var currentUser = UserService.GetCurrentUser();
                if (!currentUser.IsWebApiUserAccount)
                {
                    bool checkEditRequestTicketPermission = await CheckEditPermissionAsync(currentUser.Id, arguments.Id.Value);
                    if (checkEditRequestTicketPermission == false)
                    {
                        return new CreateEditRequestTicketResult
                        {
                            ErrorMessage = T["Bạn không có phân quyền cập nhật phiếu yêu cầu này."],
                            IsSuccess = false
                        };
                    }
                }

                Dictionary<Guid, string> oldDynamicFieldValues = new Dictionary<Guid, string>();
                Dictionary<Guid, string> newDynamicFieldValues = new Dictionary<Guid, string>();

                List<DynamicFieldValueInfo> dynamicFieldValues = new List<DynamicFieldValueInfo>();
                var requestTicketProperties = arguments.GetType().GetProperties().Select(x => x.Name).ToArray();
                if (arguments.DynamicFormValueId.Equals(requestTicketData.DynamicFormValueId))
                {
                    if (requestTicketData.DynamicFormValueId.HasValue)
                    {
                        dynamicFieldValues = (await QueryExecutor.ExecuteManyAsync(new GetDynamicFieldValueInfoQuery
                        {
                            DynamicFormValueId = requestTicketData.DynamicFormValueId.Value
                        })).Where(x => !requestTicketProperties.Contains(x.Name)).ToList();

                        oldDynamicFieldValues = dynamicFieldValues.ToDictionary(k => k.FieldId, v => v.Value);
                    }
                }
                else
                {
                    if (arguments.DynamicFormId.HasValue)
                    {
                        dynamicFieldValues = (await QueryExecutor.ExecuteManyAsync(new GetDynamicFieldValueInfoByFormIdQuery
                        {
                            DynamicFormId = arguments.DynamicFormId.Value,
                            Display = true
                        })).Where(x => !requestTicketProperties.Contains(x.Name)).ToList();
                        if (dynamicFieldValues.Count > 0)
                        {
                            arguments.DynamicFormValueId = dynamicFieldValues.First().ObjectId;
                        }
                    }
                }
                if (dynamicFieldValues.Count > 0)
                {                    
                    if (dynamicFieldValues.Count > form.Count)
                    {
                        var formDict = form.ToDictionary();
                        foreach (var item in dynamicFieldValues)
                        {
                            if (form[item.Name] == null)
                            {
                                Type fieldType = item.DataType.ToType();
                                if (fieldType.IsUserDefinedTableGridData())
                                {
                                    form.Add(item.Name + ".Id", item.Value);
                                }
                                else
                                {
                                    form.Add(item.Name, item.Value);
                                }
                            }
                        }
                    }
                    Dictionary<string, List<string>> validateErrors;
                    if (!dynamicFieldValues.TryMapping(form, files, "", ServiceProvider, out validateErrors))
                    {
                        string errorMessages = string.Join("<br/>", validateErrors.Values.SelectMany(x => x).Select(x => x));
                        return new CreateEditRequestTicketResult
                        {
                            ErrorMessage = errorMessages,
                            IsSuccess = false
                        };
                    }

                    #region Check data từ dynamic field có bị trùng trên toàn bộ hệ thống theo cấu hình - ticket (ticket + tasktypeId)

                    var newDynamicFieldInsert = new List<DynamicFieldValueList>();
                    var newDynamicFieldRangTimeInsert = new List<DynamicFieldValueList>();
                    var fieldsCheckDup = new List<string>();
                    var errList = new List<string>();
                    foreach (var item in dynamicFieldValues.Where(x => x.IsCheckDuplicate))
                    {
                        if (item.ViewHint.ToLower() != "datetimefull_v5")
                        {
                            fieldsCheckDup.Add(item.DisplayName);
                            newDynamicFieldInsert.Add(new DynamicFieldValueList { DynamicFieldId = item.FieldId, Value = item.Value });
                            errList.Add(string.Format("{0}: {1}", item.DisplayName, item.Value));
                        }
                        else newDynamicFieldRangTimeInsert.Add(new DynamicFieldValueList { DynamicFieldId = item.FieldId, Value = item.Value });
                    }
                    var checkCanInsertDynamicFieldValues = await QueryExecutor.ExecuteManyAsync(new CheckDynamicFieldValueDupOnTaskQuery
                    {
                        TicketId = arguments.Id.Value,
                        ServiceTypeId = arguments.ServiceTypeId,
                        DynamicFieldValueData = newDynamicFieldInsert,
                        DynamicFieldValueRangeTimeData = newDynamicFieldRangTimeInsert
                    });
                    //Ý tưởng, trên mỗi dynamicFormValue by ticket(ticket + taskTypeId) sẽ check trùng bộ dynamicField Data có enable check duplicate, 0 là ko trùng, 1 là trùng
                    foreach (var item in checkCanInsertDynamicFieldValues)
                    {
                        var kq = item.Result;
                        var dynamicFormValueId = item.DynamicFormValueId;
                        if (kq == 1)
                        {
                            var checkNewTimeOverlap = item.New_Start;
                            var checkOldTime = item.Old_End;
                            if (dynamicFormValueId != arguments.DynamicFormValueId.Value)
                            {
                                //Bị duplicate so với đứa khác
                                //Nếu newStart <= old end --> newStart overlap(có data), nếu ko có Old_End -> ko có check theo rangeTime
                                if (checkOldTime.HasValue == false)
                                {
                                    var infoMess = string.Join(",", fieldsCheckDup);
                                    return new CreateEditRequestTicketResult
                                    {
                                        ErrorMessage = string.Format(T["Dữ liệu tại các trường: {0} đã tồn tại, vui lòng nhập lại"], infoMess),
                                        IsSuccess = false
                                    };
                                }
                                if (checkNewTimeOverlap.HasValue && checkOldTime.HasValue)
                                {
                                    //Có check rangeTime và bị overlap
                                    var buidErrMess = string.Join(";", errList);
                                    return new CreateEditRequestTicketResult
                                    {
                                        ErrorMessage = string.Format(T["{0} phải tới: {1} mới kết thúc, vui lòng nhập lại"], buidErrMess, item.Old_End.Value.ToString()),
                                        IsSuccess = false
                                    };
                                }
                            }
                        }
                        else break;
                    }
                    //Check trùng trên DynamicTable private nằm trên ticket(task)

                    foreach (var table in dynamicFieldValues.Where(x => x.DataType == "TinyCRM.AppServices.DynamicDefinedTable.DynamicDefinedTableGridData" && x.DynamicDefinedTableSchemaId.HasValue))
                    {
                        var dynamicDefinedTable = await QueryExecutor.ExecuteOneAsync(new GetDynamicDefinedTableSchemaQuery { DynamicDefinedTableSchemaId = table.DynamicDefinedTableSchemaId.Value });
                        if (dynamicDefinedTable.RowStoredMethod == DynamicDefinedTableStoreMethod.OwnDbTable)
                        {
                            var listColName = (await QueryExecutor.ExecuteManyAsync(new GetDynamicDefinedTableColumnByIdQuery { DynamicDefinedTableSchemaId = dynamicDefinedTable.Id })).OrderBy(x => x.ColumnOrder).ToList();
                            if (listColName.Where(x => x.IsCheckDupOnDynamicForm).ToList().Count > 1)
                            {
                                var tempId = Guid.NewGuid();
                                string querySelect = "SELECT DISTINCT " + string.Join(",", listColName.Select(x => $"t2.{x.Name}"));
                                querySelect = string.Format("{0} FROM dbo.DynamicDefinedTable_{1} t1 " +
                                    "JOIN dbo.DynamicDefinedTable_{1} t2 ON ", querySelect, dynamicDefinedTable.Name);

                                var getColumnCheckDup = listColName.Where(x => x.IsCheckDupOnDynamicForm && x.DataType != "System.Nullable`1[System.DateTime]" && x.UiHint != "DateTimeFull_v5").ToList();
                                //t2 lấy all  trừ [ chính nó (nếu là edit) và temp-new ]
                                var queryJoin = string.Join(" AND ", getColumnCheckDup.Select(x => $"t1.{x.Name} = t2.{x.Name}")) + string.Format(" AND t2.DynamicFieldValueId <> '{0}'", tempId)
                                    + (table.Id.HasValue ? string.Format(" AND t2.DynamicFieldValueId <> '{0}'", table.Id.Value) : "");

                                string overLapTimeWhere = "";
                                var listColStart_End = listColName.Where(x => x.DataType == "System.Nullable`1[System.DateTime]" && x.UiHint == "DateTimeFull_v5").ToList();
                                var getColStartTime = listColStart_End.ElementAtOrDefault(0);
                                var getColEndTime = listColStart_End.ElementAtOrDefault(1);
                                if (getColStartTime != null && getColEndTime != null)
                                {
                                    overLapTimeWhere = $@"  
                                        AND (  
                                            (t2.{getColStartTime.Name} BETWEEN t1.{getColStartTime.Name} AND t1.{getColEndTime.Name})  
                                            OR (t2.{getColEndTime.Name} BETWEEN t1.{getColStartTime.Name} AND t1.{getColEndTime.Name})  
                                            OR (t1.{getColStartTime.Name} BETWEEN t2.{getColStartTime.Name} AND t2.{getColEndTime.Name})  
                                        )";
                                }
                                //t1 chỉ lấy tạm ko lấy chính nó (nếu là edit)
                                var fullQuery = querySelect + queryJoin + overLapTimeWhere + string.Format("WHERE t1.DynamicFieldValueId = '{0}'", tempId);

                                var ListItems = new List<DynamicDefinedTableCellValueData>();
                                var rowCount = form.AllKeys.Count(x => x.Contains(string.Format("{0}.Items[", table.Name)) && x.Contains("].RowNumber"));
                                for (int rowNumber = 0; rowNumber < rowCount; rowNumber++)
                                {
                                    var rowItems = new List<DynamicDefinedTableCellValueData>();
                                    var ticketCreateEditArgumentsId = Guid.NewGuid();
                                    var listTypeCOl = listColName.Select(x => x.DataType.ToType()).ToList();

                                    foreach (var column in listColName)
                                    {
                                        var dataType = column.DataType.ToType();

                                        var columnIdString = form[string.Format("{0}.Items[{1}].{2}.Id", table.Name, rowNumber, column.Name)];
                                        string columnValueName = string.Format("{0}.Items[{1}].{2}.Value", table.Name, rowNumber, column.Name);
                                        string columnStringValue = form[columnValueName];

                                        try
                                        {
                                            var typeConverter = dataType.GetTypeConverter(string.IsNullOrEmpty(columnStringValue));
                                            var convertValue = typeConverter.ConvertFromString(null, CultureInfo.CurrentCulture, columnStringValue);
                                            if (convertValue != null)
                                            {
                                                if (convertValue is DateTime)
                                                {
                                                    var dateTime = (DateTime)convertValue;
                                                    columnStringValue = dateTime.ToString("yyyy-MM-dd HH:mm:ss.fff");
                                                }
                                                else columnStringValue = convertValue.ToString();
                                            }
                                        }
                                        catch { }

                                        var dynamicDefinedTableCellValueData = new DynamicDefinedTableCellValueData
                                        {
                                            Id = columnIdString.IsNotNullOrEmpty() ? new Guid(columnIdString) : Guid.NewGuid(),
                                            DynamicDefinedTableColumnId = column.Id,
                                            Value = columnStringValue,
                                            RowNumber = rowNumber,
                                            DynamicFieldValueId = tempId
                                        };
                                        ListItems.Add(dynamicDefinedTableCellValueData);
                                    }
                                }
                                var mappingLists = new List<string[]>
                                {
                                    new[] {"Id", "Id"},
                                    new[] { "DynamicDefinedTableSchemaId", "DynamicDefinedTableSchemaId"},
                                    new[] { "DynamicFieldValueId", "DynamicFieldValueId"},
                                    new[] { "RowNumber", "RowNumber"},
                                };
                                DataTable tbOwnTable = new DataTable();
                                tbOwnTable.Columns.Add("Id", typeof(Guid));
                                tbOwnTable.Columns.Add("DynamicDefinedTableSchemaId", typeof(Guid));
                                tbOwnTable.Columns.Add("DynamicFieldValueId", typeof(Guid));
                                tbOwnTable.Columns.Add("RowNumber", typeof(int));
                                foreach (var column in listColName)
                                {
                                    mappingLists.Add(new[] { column.Name, column.Name.ToUpper() });
                                    var columnDataType = typeof(string);
                                    if (column.DataType.Contains("Int32", StringComparison.OrdinalIgnoreCase)) columnDataType = typeof(int);
                                    else if (column.DataType.Contains("Int64", StringComparison.OrdinalIgnoreCase)) columnDataType = typeof(long);
                                    else if (column.DataType.Contains("Double", StringComparison.OrdinalIgnoreCase)) columnDataType = typeof(double);
                                    else if (column.DataType.Contains("Boolean", StringComparison.OrdinalIgnoreCase)) columnDataType = typeof(bool);
                                    else if (column.DataType.Contains("DateTime", StringComparison.OrdinalIgnoreCase)) columnDataType = typeof(DateTime);
                                    tbOwnTable.Columns.Add(column.Name, columnDataType);
                                }
                                var rowList = ListItems.GroupBy(x => x.RowNumber).Select(x => new { RowNumber = x.Key, CellValues = x.ToList() });
                                foreach (var rowValues in rowList)
                                {
                                    DataRow newRow = tbOwnTable.NewRow();
                                    newRow["Id"] = Guid.NewGuid();
                                    newRow["DynamicDefinedTableSchemaId"] = table.DynamicDefinedTableSchemaId;
                                    newRow["DynamicFieldValueId"] = tempId;
                                    newRow["RowNumber"] = 0 + rowValues.RowNumber;
                                    foreach (var column in listColName)
                                    {
                                        string cellValue = null;
                                        var cellItem = rowValues.CellValues.Where(cell => cell.DynamicDefinedTableColumnId == column.Id).FirstOrDefault();
                                        if (cellItem != null) cellValue = cellItem.Value;
                                        var columnDataType = typeof(string);
                                        if (column.DataType.Contains("Int32", StringComparison.OrdinalIgnoreCase)) columnDataType = typeof(int);
                                        else if (column.DataType.Contains("Int64", StringComparison.OrdinalIgnoreCase)) columnDataType = typeof(long);
                                        else if (column.DataType.Contains("Double", StringComparison.OrdinalIgnoreCase)) columnDataType = typeof(double);
                                        else if (column.DataType.Contains("Boolean", StringComparison.OrdinalIgnoreCase))
                                        {
                                            columnDataType = typeof(bool);
                                        }
                                        else if (column.DataType.Contains("DateTime", StringComparison.OrdinalIgnoreCase))
                                        {
                                            columnDataType = typeof(DateTime);
                                        }

                                        var typeConverter = columnDataType.GetTypeConverter(string.IsNullOrEmpty(cellValue));
                                        if (cellValue.IsNullOrEmpty()) newRow[column.Name] = DBNull.Value;
                                        else newRow[column.Name] = typeConverter.ConvertFromString(null, CultureInfo.CurrentCulture, cellValue);
                                    }
                                    tbOwnTable.Rows.Add(newRow);
                                }
                                Repository.BulkInsertAll(tbOwnTable, string.Format("[dbo].[DynamicDefinedTable_{0}]", dynamicDefinedTable.Name), mappingLists);
                                var checkRecordNotValidDynamicTable = await QueryExecutor.ExecuteOneAsync(new CheckDynamicFieldValueDupOnDataTableTaskQuery
                                {
                                    DynamicTableName = dynamicDefinedTable.Name,
                                    DynamicFieldValueIdTemp = tempId,
                                    FullQuery = fullQuery
                                });

                                var infoNotValid = new List<string>();
                                foreach (DataRow item in checkRecordNotValidDynamicTable.Rows)
                                {
                                    var infoRecord = "";
                                    foreach (var col in listColName.Where(x => x.IsCheckDupOnDynamicForm && x.DataType != "System.Nullable`1[System.DateTime]" && x.UiHint != "DateTimeFull_v5").ToList())
                                    {
                                        if (col.DataType == "TinyCRM.UserAccount.UserProfileEntityData")
                                        {
                                            var rowData = item[col.Name].ToString();
                                            var user = EntitySet.Get<ApplicationUser>(new Guid(rowData));
                                            infoRecord = infoRecord.IsNullOrEmpty() ? user.FullName : " - " + user.FullName;
                                        }
                                        else infoRecord = infoRecord.IsNullOrEmpty() ? item[col.Name].ToString() : " - " + item[col.Name].ToString();
                                    }
                                    infoNotValid.Add(infoRecord);
                                }
                                if (infoNotValid.Count > 0)
                                {
                                    var joinInfo = string.Join(", ", infoNotValid);
                                    var messAlert = string.Format(T["{0} đã bận. Vui lòng chọn lại"], joinInfo);
                                    return new CreateEditRequestTicketResult
                                    {
                                        ErrorMessage = messAlert,
                                        IsSuccess = false
                                    };
                                }
                            }
                        }
                    }

                    #endregion
                }

                Guid customerId = Guid.Empty;
                Guid? provinceId = null;

                if (!arguments.IsNoneCustomerTicket)
                {
                    ValidateRequestTicketCustomerResult validateCustomerResult = null;
                    validateCustomerResult = await ValidateRequestTicketCustomer(arguments.Customer);
                    if (!validateCustomerResult.IsSuccess)
                    {
                        return new CreateEditRequestTicketResult
                        {
                            ErrorMessage = validateCustomerResult.ErrorMessage,
                            IsSuccess = false
                        };
                    }

                    if (validateCustomerResult.CustomerData != null)
                    {
                        customerId = validateCustomerResult.CustomerData.Id;
                        provinceId = validateCustomerResult.CustomerData.ProvinceId;
                    }
                    if (validateCustomerResult.CustomerAlternativeAddressData != null)
                    {
                        provinceId = validateCustomerResult.CustomerAlternativeAddressData.ProvinceId;
                    }
                }

                // Set Status
                // Nếu Phiếu chưa có người phụ trách và Giờ mới giao người phụ trách
                if (!requestTicketData.OwnerId.HasValue && arguments.OwnerId.HasValue && requestTicketData.Status == RequestTicketStatus.New)
                {
                    arguments.Status = RequestTicketStatus.Forward;
                }
                else
                {
                    // Nếu đổi người xử lý phiếu
                    // => Trạng thái trở về "Chuyển xử lý"
                    if (requestTicketData.OwnerId != arguments.OwnerId)
                    {
                        arguments.Status = RequestTicketStatus.Forward;
                    }
                }

                arguments.AcceptDueTimeId = requestTicketData.AcceptDueTimeId;
                arguments.AcceptDueDate = requestTicketData.AcceptDueDate;

                arguments.ProcessDueDate = requestTicketData.ProcessDueDate;

                bool updateIsoCode = string.IsNullOrEmpty(arguments.IsoCode) && arguments.UsingIsoCode;

                if (updateIsoCode)
                {
                    string codePrefix = string.Empty;
                    if (!arguments.IsNoneCustomerTicket)
                    {
                        codePrefix = IsoCodePrefixGenerator.GetIsoCodePrefix((int)arguments.Customer.Type);
                    }

                    string dayString = DateTime.Now.Day.ToString().PadLeft(2, '0');
                    string monthString = DateTime.Now.Month.ToString().PadLeft(2, '0');
                    string yearString = DateTime.Now.Year.ToString().Remove(0, 2);
                    if (RequestTicketIsoCodeFormat.Contains("{YearString:4}"))
                    {
                        yearString = DateTime.Now.Year.ToString();
                    }
                    if (RequestTicketIsoCodeFormat.Contains("{YearString:2}"))
                    {
                        yearString = DateTime.Now.Year.ToString().Remove(0, 2);
                    }

                    int nextIsoCodeCount = await SequenceUtility.GetNextValue("TiqIsoCode" + codePrefix, SequenceCycleType.Monthly);

                    string nextIsoDigitString = nextIsoCodeCount.ToString().PadLeft(4, '0');

                    string requestTicketIsoCodeFormat = RequestTicketIsoCodeFormat;
                    if (requestTicketIsoCodeFormat.IsNullOrEmpty())
                    {
                        requestTicketIsoCodeFormat = "{IsoRequestDigit}/{MonthString}/DVKH.{CodePrefix}/{YearString}";
                    }
                    requestTicketIsoCodeFormat = requestTicketIsoCodeFormat.Replace("{CodePrefix}", "{2}");
                    requestTicketIsoCodeFormat = requestTicketIsoCodeFormat.Replace("{IsoRequestDigit}", "{0}");
                    requestTicketIsoCodeFormat = requestTicketIsoCodeFormat.Replace("{MonthString}", "{1}");
                    requestTicketIsoCodeFormat = requestTicketIsoCodeFormat.Replace("{YearString}", "{3}");
                    requestTicketIsoCodeFormat = requestTicketIsoCodeFormat.Replace("{YearString:2}", "{3}");
                    requestTicketIsoCodeFormat = requestTicketIsoCodeFormat.Replace("{YearString:4}", "{3}");
                    requestTicketIsoCodeFormat = requestTicketIsoCodeFormat.Replace("{DayString}", "{4}");

                    arguments.IsoCode = string.Format(requestTicketIsoCodeFormat, nextIsoDigitString, monthString, codePrefix, yearString, dayString);
                }

                // Update RequestTicket Command
                //===============================================================================================================
                var updateRequestTicketCommand = new UpdateRequestTicketCommand();

                // Mặc định, LẤY LẠI tất cả Property của RequestTicket
                requestTicketData.TryMappProperties(updateRequestTicketCommand, requestTicketData.GetType().GetProperties().Select(pr => pr.Name).AsEnumerable());

                // Chỉ update Field nào được Submit từ Form, Field nào KHÔNG SUBMIT THÌ KHÔNG OVERWRITE
                arguments.TryMappProperties(updateRequestTicketCommand, form.AllKeys.AsEnumerable());

                // Fix map các Field cố định
                updateRequestTicketCommand.Id = arguments.Id.Value;
                updateRequestTicketCommand.CustomerId = customerId;
                updateRequestTicketCommand.CustomerAlternativeAddressId = arguments.Customer != null ? arguments.Customer.CustomerAlternativeAddressId : null;
                updateRequestTicketCommand.ProvinceId = provinceId;
                updateRequestTicketCommand.Type = arguments.IsNoneCustomerTicket ? 0 : (int)arguments.Customer.Type;
                updateRequestTicketCommand.ServiceTypeId = serviceType.Id;
                updateRequestTicketCommand.Level1Id = serviceType.Level1Id;
                updateRequestTicketCommand.Level2Id = serviceType.Level2Id;
                updateRequestTicketCommand.Level3Id = serviceType.Level3Id;
                updateRequestTicketCommand.Level4Id = serviceType.Level4Id;
                updateRequestTicketCommand.DynamicFormValueId = dynamicFieldValues.Count > 0 ? arguments.DynamicFormValueId : (Guid?)null;
                updateRequestTicketCommand.AuditSessionId = auditSessionId;
                updateRequestTicketCommand.Notes = arguments.Notes;
                updateRequestTicketCommand.OldStatus = requestTicketData.Status;
                updateRequestTicketCommand.ObjectApproveId = arguments.ObjectApproveId;

                // Đổi loại dịch vụ => Đổi SLA
                if (requestTicketData.ServiceTypeId != serviceType.Id)
                {
                    if (serviceType.ProcessDueTimeId.IsNotNullOrEmpty())
                    {
                        var processDueTimeData = await QueryExecutor.ExecuteOneAsync(new GetDueTimeByIdQuery() { Id = serviceType.ProcessDueTimeId.Value });
                        updateRequestTicketCommand.ProcessDueDate = await DueTimeUtility.GetDueTimeAsync(serviceType.ProcessDueTimeId.Value, requestTicketData.CreatedDate);
                        updateRequestTicketCommand.ProcessSoonDueDate = await DueTimeUtility.GetProcessSoonDueTimeAsync(serviceType.ProcessDueTimeId.Value, requestTicketData.CreatedDate);
                        updateRequestTicketCommand.ProcessDueTimeId = processDueTimeData.CurrentDueTimeReferenceId;
                    }
                }

                // Đổi loại dịch vụ,
                // Hoặc đổi người phụ trách phiếu
                // => Đổi AcceptDueTime
                if (requestTicketData.ServiceTypeId != serviceType.Id
                    || requestTicketData.OwnerId != arguments.OwnerId)
                {
                    // Accept DueTime
                    if (serviceType.AcceptDueTimeId.IsNotNullOrEmpty())
                    {
                        var acceptDueTimeData = await QueryExecutor.ExecuteOneAsync(new GetDueTimeByIdQuery() { Id = serviceType.AcceptDueTimeId.Value });
                        updateRequestTicketCommand.AcceptDueDate = await DueTimeUtility.GetDueTimeAsync(serviceType.AcceptDueTimeId.Value, requestTicketData.CreatedDate);
                        updateRequestTicketCommand.AcceptDueTimeId = acceptDueTimeData.CurrentDueTimeReferenceId;
                    }
                }

                if (arguments.Status == RequestTicketStatus.Done)
                {
                    var tasks = await QueryExecutor.ExecuteAsync(new GetRequestTicketNotDoneTaskQuery(requestTicketData.Id));
                    if (tasks.Total == 0)
                    {
                        if (requestTicketData.AcceptedTicketDate == null)
                        {
                            updateRequestTicketCommand.AcceptedTicketDate = DateTime.Now;
                        }

                        updateRequestTicketCommand.FinishedTicketDate = DateTime.Now;
                        if (requestTicketData.SoonerProcessDueMinutes == null && requestTicketData.ProcessDueDate.HasValue)
                        {
                            updateRequestTicketCommand.SoonerProcessDueMinutes = (int)requestTicketData.ProcessDueDate.Value.Subtract(DateTime.Now).TotalMinutes;
                        }

                        updateRequestTicketCommand.Status = RequestTicketStatus.Done;

                        await ServiceProvider.GetRequiredKeyedService<INotificationCaseService>("ticket.finished.notifytocreator").CreateAsync(requestTicketData);

                        if (requestTicketData.OwnerId.IsNullOrEmpty())
                        {
                            await ServiceProvider.GetRequiredKeyedService<INotificationCaseService>("noowner.ticket.created").CloseAsync(requestTicketData.Id);
                            await ServiceProvider.GetRequiredKeyedService<INotificationCaseService>("noowner.ticket.accept.duetime").CloseAsync(requestTicketData.Id);
                            await ServiceProvider.GetRequiredKeyedService<INotificationCaseService>("noowner.ticket.process.duetime").CloseAsync(requestTicketData.Id);
                        }

                        await ServiceProvider.GetRequiredKeyedService<INotificationCaseService>("ticket.assigned.notifytoowner").CloseAsync(requestTicketData.Id);
                        await ServiceProvider.GetRequiredKeyedService<INotificationCaseService>("ticket.accept.duetime.notifytoowner").CloseAsync(requestTicketData.Id);
                        await ServiceProvider.GetRequiredKeyedService<INotificationCaseService>("ticket.accept.duetime.notifytocreator").CloseAsync(requestTicketData.Id);
                        await ServiceProvider.GetRequiredKeyedService<INotificationCaseService>("ticket.soonprocess.duetime.notifytoowner").CloseAsync(requestTicketData.Id);
                        await ServiceProvider.GetRequiredKeyedService<INotificationCaseService>("ticket.process.duetime.notifytoowner").CloseAsync(requestTicketData.Id);

                        // Gửi mail, SMS đến Khách hàng
                        await ServiceProvider.GetRequiredKeyedService<INotificationCaseService>("ticket.finished.notifytocustomer").CreateAsync(requestTicketData);

                        EventBus.Publish(new RequestTicketClosedEvent
                        {
                            RequestTicketId = requestTicketData.Id,
                            TicketBusinessResultId = arguments.TicketBusinessResultId,
                            ServiceTypeId = requestTicketData.ServiceTypeId
                        });
                    }
                    else
                    {
                        return new CreateEditRequestTicketResult
                        {
                            ErrorMessage = T["Không thể kết thúc phiếu yêu cầu khi có tác vụ chưa được xác nhận hoàn thành"],
                            IsSuccess = false
                        };
                    }

                }

                if (arguments.OwnerId.HasValue)
                {
                    var organizationData = await QueryExecutor.ExecuteOneAsync(new GetOrganizationByUserIdQuery(arguments.OwnerId.Value));
                    if (organizationData != null)
                    {
                        updateRequestTicketCommand.OwnedByOrganizationId = organizationData.Id;
                    }
                }

                if (updateIsoCode)
                {
                    updateRequestTicketCommand.IsoCode = arguments.IsoCode;
                }

                await CommandExecutor.ExecuteAsync(updateRequestTicketCommand);

                if (dynamicFieldValues.Count > 0)
                {
                    var savedDynamicFieldValues = dynamicFieldValues.Where(dfv => dfv.FieldType != FieldType.Static);
                    await savedDynamicFieldValues.SaveAsync(updateRequestTicketCommand.Id, "dbo.RequestTicket", auditSessionId, true, CommandExecutor);
                }

                if (arguments.IsFromAPI)
                {
                    if (dynamicFieldValues.Any(dfv => dfv.FieldType == FieldType.Calculated || dfv.Inject.IsNotNullOrEmpty()))
                    {
                        await CommandExecutor.ExecuteAsync(new ReCalculateDynamicFormValueCommand { DynamicFormValueId = updateRequestTicketCommand.DynamicFormValueId.Value });
                    }
                }

                newDynamicFieldValues = dynamicFieldValues.ToDictionary(k => k.FieldId, v => v.Value);
                EventBus.Publish(new RequestTicketUpdatedEvent
                {
                    RequestTicketId = requestTicketData.Id,

                    OldRequestTicketData = oldRequestTicketData,
                    NewRequestTicketData = requestTicketData,

                    OldDynamicFieldValues = oldDynamicFieldValues,
                    NewDynamicFieldValues = newDynamicFieldValues
                });

                #region Callback

                if (arguments.TbCallbackData != null && arguments.TbCallbackData.CallbackDate.HasValue)
                {
                    // Chỉ tạo Callback khi chưa có Callback cho Phiếu.
                    if (arguments.TbCallbackData.Id.IsNullOrEmpty())
                    {
                        await CommandExecutor.ExecuteAsync(new CreateEditTbCallbackCommand
                        {
                            Id = Guid.NewGuid(),
                            CallbackDate = arguments.TbCallbackData.CallbackDate.Value,
                            NextReminderTime = arguments.TbCallbackData.CallbackDate.Value,
                            Notes = arguments.TbCallbackData.Notes,
                            Status = TbCallbackSatus.Scheduled,
                            ReferenceObjectId = arguments.Id.Value
                        });
                    }
                }

                #endregion

                if (requestTicketData.Status != RequestTicketStatus.Done)
                {
                    // Assign phiếu ngoài hệ thống cho 1 Owner
                    if (requestTicketData.OwnerId.IsNullOrEmpty() && arguments.OwnerId.IsNotNullOrEmpty())
                    {
                        // Tạo "các" TÁC VỤ đầu tiên của phiếu
                        //CreateFirstTasks(serviceType, requestTicketData.Id, arguments.OwnerId, DateTime.Now);
                    }

                    if (requestTicketData.OwnerId != arguments.OwnerId)
                    {
                        // Assign phiếu ngoài hệ thống cho 1 Owner
                        if (requestTicketData.OwnerId.IsNullOrEmpty() && arguments.OwnerId.IsNotNullOrEmpty())
                        {
                            await ServiceProvider.GetRequiredKeyedService<INotificationCaseService>("noowner.ticket.created").CloseAsync(requestTicketData.Id);
                            await ServiceProvider.GetRequiredKeyedService<INotificationCaseService>("noowner.ticket.accept.duetime").CloseAsync(requestTicketData.Id);
                            await ServiceProvider.GetRequiredKeyedService<INotificationCaseService>("noowner.ticket.process.duetime").CloseAsync(requestTicketData.Id);
                        }

                        // Close old notification
                        await ServiceProvider.GetRequiredKeyedService<INotificationCaseService>("ticket.assigned.notifytoowner").CloseAsync(requestTicketData.Id);
                        await ServiceProvider.GetRequiredKeyedService<INotificationCaseService>("ticket.accept.duetime.notifytoowner").CloseAsync(requestTicketData.Id);
                        await ServiceProvider.GetRequiredKeyedService<INotificationCaseService>("ticket.accept.duetime.notifytocreator").CloseAsync(requestTicketData.Id);
                        await ServiceProvider.GetRequiredKeyedService<INotificationCaseService>("ticket.soonprocess.duetime.notifytoowner").CloseAsync(requestTicketData.Id);
                        await ServiceProvider.GetRequiredKeyedService<INotificationCaseService>("ticket.process.duetime.notifytoowner").CloseAsync(requestTicketData.Id);

                        // New Notification
                        await ServiceProvider.GetRequiredKeyedService<INotificationCaseService>("ticket.assigned.notifytoowner").CreateWithIdAsync(requestTicketData.Id);
                        await ServiceProvider.GetRequiredKeyedService<INotificationCaseService>("ticket.accept.duetime.notifytoowner").CreateWithIdAsync(requestTicketData.Id);
                        await ServiceProvider.GetRequiredKeyedService<INotificationCaseService>("ticket.accept.duetime.notifytocreator").CreateWithIdAsync(requestTicketData.Id);

                        await ServiceProvider.GetRequiredKeyedService<INotificationCaseService>("ticket.soonprocess.duetime.notifytoowner").CreateWithIdAsync(requestTicketData.Id);
                        await ServiceProvider.GetRequiredKeyedService<INotificationCaseService>("ticket.process.duetime.notifytoowner").CreateWithIdAsync(requestTicketData.Id);

                        // Nếu có Callback, mà đổi TicketOwner, thì đóng và tạo Noti Case mới
                        if (arguments.TbCallbackData != null && arguments.TbCallbackData.Id.IsNotNullOrEmpty() && arguments.TbCallbackData.Status == TbCallbackSatus.Scheduled)
                        {
                            await ServiceProvider.GetRequiredKeyedService<INotificationCaseService>("tbcallback.createedit.ticketowner").CloseAsync(arguments.TbCallbackData.Id.Value);
                            await ServiceProvider.GetRequiredKeyedService<INotificationCaseService>("tbcallback.createedit.ticketowner").CreateWithIdAsync(arguments.TbCallbackData.Id.Value);
                        }
                    }
                    else if (requestTicketData.ServiceTypeId != serviceType.Id)
                    {
                        // Close old notification
                        await ServiceProvider.GetRequiredKeyedService<INotificationCaseService>("ticket.accept.duetime.notifytoowner").CloseAsync(requestTicketData.Id);
                        await ServiceProvider.GetRequiredKeyedService<INotificationCaseService>("ticket.accept.duetime.notifytocreator").CloseAsync(requestTicketData.Id);
                        await ServiceProvider.GetRequiredKeyedService<INotificationCaseService>("ticket.soonprocess.duetime.notifytoowner").CloseAsync(requestTicketData.Id);
                        await ServiceProvider.GetRequiredKeyedService<INotificationCaseService>("ticket.process.duetime.notifytoowner").CloseAsync(requestTicketData.Id);

                        // New Notification
                        // Chỉ noti nếu CHƯA TIẾP NHẬN phiếu
                        if (requestTicketData.AcceptedTicketDate.HasValue == false)
                        {
                            await ServiceProvider.GetRequiredKeyedService<INotificationCaseService>("ticket.accept.duetime.notifytoowner").CreateWithIdAsync(requestTicketData.Id);
                            await ServiceProvider.GetRequiredKeyedService<INotificationCaseService>("ticket.accept.duetime.notifytocreator").CreateWithIdAsync(requestTicketData.Id);
                        }

                        await ServiceProvider.GetRequiredKeyedService<INotificationCaseService>("ticket.soonprocess.duetime.notifytoowner").CreateWithIdAsync(requestTicketData.Id);
                        await ServiceProvider.GetRequiredKeyedService<INotificationCaseService>("ticket.process.duetime.notifytoowner").CreateWithIdAsync(requestTicketData.Id);
                    }
                    else if (serviceType.AllowManualSLA == true)
                    {
                        var updateSoon = false;
                        if (oldRequestTicketData.PlannedDate_Begin != arguments.PlannedDate_Begin && !requestTicketData.AcceptedTicketDate.HasValue)
                        {
                            await ServiceProvider.GetRequiredKeyedService<INotificationCaseService>("ticket.accept.duetime.notifytoowner").CloseAsync(requestTicketData.Id);
                            await ServiceProvider.GetRequiredKeyedService<INotificationCaseService>("ticket.accept.duetime.notifytoowner").CreateWithIdAsync(requestTicketData.Id);

                            await ServiceProvider.GetRequiredKeyedService<INotificationCaseService>("ticket.accept.duetime.notifytocreator").CloseAsync(requestTicketData.Id);
                            await ServiceProvider.GetRequiredKeyedService<INotificationCaseService>("ticket.accept.duetime.notifytocreator").CreateWithIdAsync(requestTicketData.Id);
                            updateSoon = true;
                        }
                        if (oldRequestTicketData.PlannedDate_End != arguments.PlannedDate_End)
                        {
                            await ServiceProvider.GetRequiredKeyedService<INotificationCaseService>("ticket.process.duetime.notifytoowner").CloseAsync(requestTicketData.Id);
                            await ServiceProvider.GetRequiredKeyedService<INotificationCaseService>("ticket.process.duetime.notifytoowner").CreateWithIdAsync(requestTicketData.Id);
                            updateSoon = true;
                        }
                        if (updateSoon)
                        {
                            await ServiceProvider.GetRequiredKeyedService<INotificationCaseService>("ticket.soonprocess.duetime.notifytoowner").CloseAsync(requestTicketData.Id);
                            await ServiceProvider.GetRequiredKeyedService<INotificationCaseService>("ticket.soonprocess.duetime.notifytoowner").CreateWithIdAsync(requestTicketData.Id);
                        }
                    }
                }

                // Link RequestTicket & CALL
                if (arguments.ibUCID.IsNotNullOrEmpty())
                {
                    await CommandExecutor.ExecuteAsync(new InsertRequestTicketCallCommand
                    {
                        RequestTicketId = arguments.Id.Value,
                        UCID = arguments.ibUCID
                    });
                }

                // Link RequestTicket & Webchat
                if (arguments.RequestTicketInteraction != null)
                {
                    if (arguments.RequestTicketInteraction.InteractionType == RequestTicketInteractionType.WebChat && arguments.RequestTicketInteraction.WorkItemID.IsNotNullOrEmpty())
                    {
                        await CommandExecutor.ExecuteAsync(new InsertRequestTicketWebchatCommand
                        {
                            RequestTicketId = arguments.Id.Value,
                            WorkItemID = arguments.RequestTicketInteraction.WorkItemID.Value
                        });
                    }
                }

                CreateEditRequestTicketResult executionResult = new CreateEditRequestTicketResult();
                executionResult.IsSuccess = true;

                if (!requestTicketData.OwnerId.HasValue && requestTicketData.Status == RequestTicketStatus.New &&
                    arguments.OwnerId.HasValue)
                {
                    var ownerProfile = await QueryExecutor.ExecuteOneAsync(new GetUserProfileByIdQuery(arguments.OwnerId.Value));
                    executionResult.SuccessMessage = T["Phiếu yêu cầu đã được lưu chỉnh sửa và chuyển tiếp cho {0}", ownerProfile.FullName];
                }
                else
                {
                    executionResult.SuccessMessage = T["Phiếu yêu cầu đã được cập nhật"];
                }
                executionResult.RequestTicketCode = arguments.Code;
                executionResult.ReturnUrl = string.Format("{0}/RequestTicket/Edit?RequestTicketId={1}", ApplicationUrl.Trim('/'), arguments.Id);

                return executionResult;
            }

            return new CreateEditRequestTicketResult
            {
                ErrorMessage = T["Không tìm thấy Loại dịch vụ hoặc Loại dịch vụ đã bị xóa."],
                IsSuccess = false
            };
        }

        public async Task<FinishRequestTicketResult> FinishRequestTicketAsync(FinishRequestTicketArguments arguments, bool ignoreBusinessPermission = false, bool forcedFinish = false)
        {
            var requestTicketData = await QueryExecutor.ExecuteOneAsync(new GetRequestTicketByIdQuery(arguments.RequestTicketId));
            if (requestTicketData != null && requestTicketData.Status != RequestTicketStatus.Done)
            {
                try
                {
                    if (!ignoreBusinessPermission)
                    {
                        bool checkCloseRequestTicketPermission = (await BusinessPermissionService.GetBusinessPermissionsAsync(UserService.GetCurrentUser().Id)).Any(bp => bp.Id == _closeRequestTicketPermissionId);
                        if (checkCloseRequestTicketPermission == false)
                        {
                            return new FinishRequestTicketResult { IsSuccess = false, ErrorMessage = T["Bạn không thể Kết thúc phiếu yêu cầu này do thiếu Phân quyền."] };
                        }
                    }

                    Guid closedBy = UserService.GetCurrentUser().Id;
                    if (arguments.ModifiedBy.IsNotNullOrEmpty())
                    {
                        closedBy = arguments.ModifiedBy.Value;
                    }
                    // Đóng tất cả các Tác vụ "Todo"
                    await CommandExecutor.ExecuteAsync(new CloseAllTodoTaskStatusCommand { RequestTicketId = arguments.RequestTicketId, ClosedBy = closedBy });

                    bool validToFinish = true;
                    if (!forcedFinish)
                    {
                        var tasks = await QueryExecutor.ExecuteAsync(new GetRequestTicketNotDoneTaskQuery(arguments.RequestTicketId));
                        if (tasks.Total > 0)
                        {
                            validToFinish = false;
                        }
                    }

                    if (validToFinish)
                    {
                        var updateStatusRequestTicketCommand = new UpdateRequestTicketStatusCommand
                        {
                            Id = arguments.RequestTicketId,
                            Status = RequestTicketStatus.Done,
                            SourceChannel = arguments.SourceChannel,
                            TicketBusinessResultId = arguments.BusinessResultId,
                            ModifiedBy = arguments.ModifiedBy
                        };
                        await CommandExecutor.ExecuteAsync(updateStatusRequestTicketCommand);

                        return new FinishRequestTicketResult { IsSuccess = true, SuccessMessage = T["Đã kết thúc phiếu yêu cầu {0}", requestTicketData.Code] };
                    }
                    else
                    {
                        return new FinishRequestTicketResult { IsSuccess = false, ErrorMessage = T["Không thể kết thúc phiếu yêu cầu khi có tác vụ chưa được xác nhận hoàn thành"] };
                    }
                }
                catch (Exception ex)
                {
                    return new FinishRequestTicketResult { IsSuccess = false, ErrorMessage = ex.Message };
                }
            }
            return new FinishRequestTicketResult { IsSuccess = false, ErrorMessage = T["Không tìm phiếu phiếu có Id: {0}", arguments.RequestTicketId] };
        }

        public async Task<DeleteRequestTicketResultDto> DeleteRequestTicketAsync(Guid requestTicketId, bool ignoreBusinessPermission = false, bool forcedDelete = false)
        {
            Guid currentUserId = UserService.GetCurrentUser().Id;

            var requestTicketData = await QueryExecutor.ExecuteOneAsync(new GetRequestTicketByIdQuery(requestTicketId));
            if (requestTicketData != null)
            {
                try
                {
                    if (!ignoreBusinessPermission)
                    {
                        bool checkCloseRequestTicketPermission = (await BusinessPermissionService.GetBusinessPermissionsAsync(currentUserId)).Any(bp => bp.Id == _deleteRequestTicketPermissionId);
                        if (checkCloseRequestTicketPermission == false)
                        {
                            return new DeleteRequestTicketResultDto { IsSuccess = false, ErrorMessage = T["Bạn không thể xóa phiếu yêu cầu này do thiếu Phân quyền."] };
                        }
                    }

                    bool validToDelete = true;
                    List<TaskEntity> notDoneTasks = new List<TaskEntity>();
                    if (!forcedDelete)
                    {
                        notDoneTasks = (await QueryExecutor.ExecuteManyAsync(new GetRequestTicketNotDoneTaskQuery(requestTicketId))).ToList();
                        if (notDoneTasks.Count > 0)
                        {
                            validToDelete = false;
                        }
                    }

                    if (validToDelete)
                    {
                        foreach (var taskEntity in notDoneTasks)
                        {
                            await CommandExecutor.ExecuteAsync(new CloseTaskCommand { TaskId = taskEntity.Id, ClosedBy = currentUserId });
                        }
                        await CommandExecutor.ExecuteAsync(new DeleteRequestTicketCommand { Id = requestTicketId });

                        return new DeleteRequestTicketResultDto { IsSuccess = true, SuccessMessage = T["Đã xóa phiếu yêu cầu {0}", requestTicketData.Code] };
                    }
                    else
                    {
                        return new DeleteRequestTicketResultDto { IsSuccess = false, ErrorMessage = T["Không thể xóa phiếu yêu cầu khi có tác vụ chưa được xác nhận hoàn thành"] };
                    }
                }
                catch (Exception ex)
                {
                    return new DeleteRequestTicketResultDto { IsSuccess = false, ErrorMessage = ex.Message };
                }
            }
            return new DeleteRequestTicketResultDto { IsSuccess = false, ErrorMessage = T["Không tìm phiếu phiếu có Id: {0}", requestTicketId] };
        }

        public async Task<bool> IsRequiredBusinessResultAsync(RequestTicketStatus Status, Guid ServiceTypeId, Guid? TicketBusinessResultId)
        {
            var isRequired = false;
            if(Status == RequestTicketStatus.Done)
            {
                var listBusinessResult = (await QueryExecutor.ExecuteManyAsync(new GetBusinessResultByServiceTypeId { ServiceTypeId = ServiceTypeId })).ToList();
                if (listBusinessResult != null && listBusinessResult.Count > 0)
                {
                    if (TicketBusinessResultId.HasValue == false)
                    {
                        isRequired = true;
                    }
                }
            }
            return isRequired;
        }

        public async Task<SearchRequestTicketResult> SearchRequestTicketAsync(SearchRequestTicketArguments arguments, int pageIndex, int pageSize, bool includeDynamicForm = false)
        {
            SearchRequestTicketResult searchRequestTicketResult = new SearchRequestTicketResult();

            SearchRequestTicketNABQuery searchRequestTicketQuery = Mapper.Map<SearchRequestTicketNABQuery>(arguments);
            searchRequestTicketQuery.PageIndex = pageIndex;
            searchRequestTicketQuery.PageSize = pageSize;
            var ticketList = await QueryExecutor.ExecuteManyAsync(searchRequestTicketQuery);

            searchRequestTicketResult.RequestTicketList = Mapper.Map<List<SearchRequestTicketResultItem>>(ticketList.ToList());
            searchRequestTicketResult.TotalCount = ticketList.Count() == 0 ? 0 : ticketList.FirstOrDefault().TotalCount;

            if (includeDynamicForm)
            {
                List<Guid> dynamicFormIds = searchRequestTicketResult.RequestTicketList.Where(rt => rt.DynamicFormValueId.HasValue).Select(rt => rt.DynamicFormValueId.Value).ToList();
                if (dynamicFormIds.Count > 0)
                {
                    var dynamicFieldValueList = (await QueryExecutor.ExecuteManyAsync(new GetDynamicFieldValueInfoByDynamicFormValueIdListQuery { DynamicFormValueIdList = dynamicFormIds, Display = true, IncludeDeleted = false })).ToList();
                    for (int i = 0; i < searchRequestTicketResult.RequestTicketList.Count; i++)
                    {
                        if (searchRequestTicketResult.RequestTicketList[i].DynamicFormValueId.IsNotNullOrEmpty())
                        {
                            searchRequestTicketResult.RequestTicketList[i].DynamicFieldList = (from dfr in dynamicFieldValueList
                                                                                               where dfr.DynamicFormValueId == searchRequestTicketResult.RequestTicketList[i].DynamicFormValueId.Value
                                                                                               orderby dfr.Order
                                                                                               select dfr).ToList();
                        }
                    }
                }
            }

            return searchRequestTicketResult;
        }

        public async Task<DataSet> SearchRequestTicketAsync(SearchRequestTicketArguments arguments, int pageIndex, int pageSize)
        {
            SearchRequestTicketNABQuery searchRequestTicketQuery = Mapper.Map<SearchRequestTicketNABQuery>(arguments);
            searchRequestTicketQuery.PageIndex = pageIndex;
            searchRequestTicketQuery.PageSize = pageSize;

            return await searchRequestTicketQuery.ExecuteDataSetQuery(Configuration, EntitySet);
        }

        public async Task<byte[]> ExportSearchRequestTicketResultToImportAsync(SearchRequestTicketArguments arguments, string templateName, bool? isDisplay)
        {
            SearchRequestTicketNABQuery searchQuery = Mapper.Map<SearchRequestTicketNABQuery>(arguments);

            string selectedColumns = string.Empty;
            string whereCondition = "";

            List<TicketTaskExportImportColumnMapping> allColumnMappingList = new List<TicketTaskExportImportColumnMapping>();
            if (templateName.IsNullOrEmpty())
            {
                templateName = "Views/RequestTicket/ExportDefs/ExportSearchRequestTicketResult-ImportColumnMapping.txt";
            }
            string columnMappingListJson = System.IO.File.ReadAllText(Path.Combine(_env.ContentRootPath, templateName));
            allColumnMappingList = JsonConvert.DeserializeObject<List<TicketTaskExportImportColumnMapping>>(columnMappingListJson).OrderBy(x => x.ColumnOrder).ToList();

            int ownerIdColumnIndex = 0, channelColumnIndex = 0, difficultyDegreeColumnIndex = 0, requestTicketStatusColumnIndex = 0, ticketBusinessResultColumnIndex = 0;
            int level1ColumnIndex = 0, level2ColumnIndex = 0, level3ColumnIndex = 0, level4ColumnIndex = 0;

            #region Các Properties + DynamicFields của Phiếu

            int tempColumnIndex = 1;
            List<TicketTaskExportImportColumnMapping> ticketColumnMappingList = allColumnMappingList.Where(col => col.IsTicketColumn == true).OrderBy(col => col.ColumnOrder).ToList();
            int countLevelServiceType = 0;
            foreach (var columnMapping in ticketColumnMappingList)
            {
                if (!selectedColumns.IsNullOrEmpty())
                {
                    selectedColumns += ", ";
                }
                selectedColumns += string.Format("{0} [{1}]", columnMapping.ColumnName, columnMapping.ColumnAlias);

                if (columnMapping.ColumnName.IsEqualIgnoreCase("OwnerUser"))
                {
                    ownerIdColumnIndex = tempColumnIndex;
                }
                else if (columnMapping.ColumnName.IsEqualIgnoreCase("Channel"))
                {
                    channelColumnIndex = tempColumnIndex;
                }
                else if (columnMapping.ColumnName.IsEqualIgnoreCase("DifficultyDegree"))
                {
                    difficultyDegreeColumnIndex = tempColumnIndex;
                }
                else if (columnMapping.ColumnName.IsEqualIgnoreCase("RequestTicketStatus"))
                {
                    requestTicketStatusColumnIndex = tempColumnIndex;
                }
                else if (columnMapping.ColumnName.IsEqualIgnoreCase("TicketBusinessResult"))
                {
                    ticketBusinessResultColumnIndex = tempColumnIndex;
                }
                else if (columnMapping.ColumnName.IsEqualIgnoreCase("Level1"))
                {
                    countLevelServiceType++;
                    level1ColumnIndex = tempColumnIndex;
                }
                else if (columnMapping.ColumnName.IsEqualIgnoreCase("Level2"))
                {
                    countLevelServiceType++;
                    level2ColumnIndex = tempColumnIndex;
                }
                else if (columnMapping.ColumnName.IsEqualIgnoreCase("Level3"))
                {
                    countLevelServiceType++;
                    level3ColumnIndex = tempColumnIndex;
                }
                else if (columnMapping.ColumnName.IsEqualIgnoreCase("Level4"))
                {
                    countLevelServiceType++;
                    level4ColumnIndex = tempColumnIndex;
                }
                tempColumnIndex++;
            }
            string exeptionJoins = string.Empty;

            string ticketDynamicFieldSelect = string.Empty;
            List<string> distinctTicketDynamicFieldList = new List<string>();
            List<DynamicFieldListItem> ticketDynamicFieldList = new List<DynamicFieldListItem>();

            // Lấy tất cả Dynamic Field trong tất cả Service Type được Search
            if (searchQuery.Level1Id.IsNotNullOrEmpty())
            {
                ticketDynamicFieldList = (await QueryExecutor.ExecuteManyAsync(new GetDynamicFieldListByServiceTypesQuery
                {
                    Level1Id = searchQuery.Level1Id,
                    Level2Id = searchQuery.Level2Id,
                    Level3Id = searchQuery.Level3Id,
                    Level4Id = searchQuery.Level4Id,
                    ServiceTypeId = searchQuery.ServiceTypeId,
                    Display = isDisplay
                })).ToList();

                var fileDataType = typeof(Webaby.Core.File.Queries.FileData);
                var orgDataType = typeof(Webaby.Core.Organization.Queries.OrganizationData);                
                foreach (var dynamicColumn in ticketDynamicFieldList.Where(x=>x.IsExportExcel == true))
                {
                    if (dynamicColumn.IsExportByConditionBoolean && (dynamicColumn.DataType == "System.Nullable`1[System.Int64]" || dynamicColumn.DataType == "System.Nullable`1[System.Boolean]" || dynamicColumn.DataType == "System.Nullable`1[System.Int32]"))
                    {
                        if (whereCondition == "")
                        {
                            whereCondition = string.Format("Where [{0}] <> 0", dynamicColumn.DynamicFieldName);
                        } else
                        {
                            whereCondition = string.Format("{0} AND [{1}] <> 0", whereCondition, dynamicColumn.DynamicFieldName);
                        }                        
                    }
                    string sqlDynamicFieldName = string.Format("[{0}]", dynamicColumn.DynamicFieldName);
                    if (!distinctTicketDynamicFieldList.Exists(dfd => dfd.IsEqualIgnoreCase(sqlDynamicFieldName)))
                    {
                        Type dynamicType = dynamicColumn.DataType.ToType();
                        bool isFileData = dynamicType == fileDataType;
                        bool isMultiFileData = false;
                        if (!isFileData)
                        {
                            if (dynamicType.IsGenericType)
                            {
                                isMultiFileData = dynamicType.GetGenericArguments().Single() == fileDataType;
                            }
                        }

                        if (!dynamicType.IsUserDefinedTableGridData())
                        {
                            if (!selectedColumns.IsNullOrEmpty())
                            {
                                selectedColumns += ", ";
                            }
                        }

                        if (dynamicType == orgDataType)
                        {
                            selectedColumns += string.Format("'(' + [{0}TicketOrg].Code + ')-' + [{0}TicketOrg].Name [{1}]", dynamicColumn.DynamicFieldName, dynamicColumn.DisplayName);
                            exeptionJoins += string.Format(" LEFT JOIN dbo.Organization [{0}TicketOrg] WITH(NOLOCK) ON [{0}TicketOrg].Id = dbo.ConvertToGuid(mainResult.{0}, '00000000-0000-0000-0000-000000000000') ", dynamicColumn.DynamicFieldName);
                        }
                        else if (isFileData)
                        {
                            selectedColumns += string.Format("[{0}TicketFile].FileName [{1}]", dynamicColumn.DynamicFieldName, dynamicColumn.DisplayName);
                            exeptionJoins += string.Format(" LEFT JOIN dbo.[File] [{0}TicketFile] WITH(NOLOCK) ON [{0}TicketFile].Id = dbo.ConvertToGuid(mainResult.{0}, '00000000-0000-0000-0000-000000000000') ", dynamicColumn.DynamicFieldName);
                        }
                        else if (isMultiFileData)
                        {
                            selectedColumns += string.Format("[dbo].[GetDynamicMultiFileNames](mainResult.[{0}]) [{1}] ", dynamicColumn.DynamicFieldName, dynamicColumn.DisplayName);
                        }
                        else if (!dynamicType.IsUserDefinedTableGridData())
                        {
                            selectedColumns += string.Format("[{0}] [{1}] ", dynamicColumn.DynamicFieldName, dynamicColumn.DisplayName);
                        }

                        if (!dynamicType.IsUserDefinedTableGridData())
                        {
                            distinctTicketDynamicFieldList.Add(sqlDynamicFieldName);
                        }
                    }
                }

                ticketDynamicFieldSelect = string.Join(", ", distinctTicketDynamicFieldList);
            }

            #endregion

            var exportSearchRequestTicketResults = RequestTicketServices.ExportSearchRequestTicketDynamicResult(searchQuery, selectedColumns, ticketDynamicFieldSelect, exeptionJoins, false, whereCondition);

            SpreadsheetGear.IWorkbook workbook = SpreadsheetGear.Factory.GetWorkbook();
            SpreadsheetGear.IWorksheet worksheet = workbook.Worksheets["Sheet1"];
            worksheet.Name = "RequestTicket";
            SpreadsheetGear.IRange range = worksheet.Cells["A1"];
            range.CopyFromDataTable(exportSearchRequestTicketResults.Tables[0], SetDataFlags.AllText);

            #region Style Export Excel

            SpreadsheetGear.IWorksheet requestTicketWorksheet = workbook.Worksheets[0];
            SpreadsheetGear.IRange requestTicketCells = requestTicketWorksheet.Cells;

            string[] requiredColumns = { "channel", "difficultydegree" };

            int totalColumnCount = selectedColumns.Split(',').Count();
            for (int i = 1; i <= totalColumnCount; i++)
            {
                string columnExcelName = GetExcelColumnName(i);

                requestTicketCells[columnExcelName + "1"].RowHeight = 21;
                requestTicketCells[columnExcelName + "1"].ColumnWidth = 16;
                requestTicketCells[columnExcelName + "1"].VerticalAlignment = SpreadsheetGear.VAlign.Center;
                requestTicketCells[columnExcelName + "1"].Font.Bold = true;
                requestTicketCells[columnExcelName + "1"].Font.Color = SpreadsheetGear.Drawing.Color.FromArgb(0, 32, 96);
                requestTicketCells[columnExcelName + "1"].Interior.Color = SpreadsheetGear.Drawing.Color.FromArgb(198, 217, 241);
            }

            string[] selectedColumnList = selectedColumns.Split(',');
            int columnIndex = 1;
            foreach (var selectedColumn in selectedColumnList)
            {
                string columnExcelName = GetExcelColumnName(columnIndex);

                string[] selectedColumnParts = selectedColumn.Split('[');
                string columnName = selectedColumnParts[0].Trim().ToLower();
                if (requiredColumns.Contains(columnName))
                {
                    requestTicketCells[columnExcelName + "1"].Font.Italic = true;

                    int labelLength = requestTicketCells[columnExcelName + "1"].Text.Length;
                    requestTicketCells[columnExcelName + "1"].Value = requestTicketCells[columnExcelName + "1"].Text + " (*)";
                    ICharacters characters = requestTicketCells[columnExcelName + "1"].GetCharacters(labelLength, 4);
                    characters.Font.Bold = false;
                    characters.Font.Italic = true;
                    characters.Font.Size = 10;
                }
                columnIndex++;
            }

            int startTicketDynamicColumnIndex = ticketColumnMappingList.Count + 1;
            int endTicketDynamicColumnIndex = startTicketDynamicColumnIndex + distinctTicketDynamicFieldList.Count - 1;

            if (countLevelServiceType >= 2)
            {
                requestTicketCells["1:1"].Insert();

                requestTicketCells["A1:D1"].RowHeight = 18;
                requestTicketCells["A1:D1"].VerticalAlignment = VAlign.Center;
                requestTicketCells["A1:D1"].HorizontalAlignment = HAlign.Center;
                requestTicketCells["A1:D1"].Font.Bold = true;
                requestTicketCells["A1:D1"].Font.Italic = true;
                requestTicketCells["A1:D1"].Font.Color = SpreadsheetGear.Drawing.Color.FromArgb(0, 32, 96);
                requestTicketCells["A1:D1"].Interior.Color = SpreadsheetGear.Drawing.Color.FromArgb(198, 217, 241);
                requestTicketCells["A1:D1"].Merge();

                requestTicketCells["A1"].Value = T["Loại dịch vụ"];
                int labelLengthTemp = requestTicketCells["A1"].Text.Length;
                requestTicketCells["A1"].Value = requestTicketCells["A1"].Text + " (**)";
                ICharacters tempCharacters = requestTicketCells["A1"].GetCharacters(labelLengthTemp, 5);
                tempCharacters.Font.Bold = false;
                tempCharacters.Font.Italic = true;
                tempCharacters.Font.Size = 10;

                for (int i = 5; i <= endTicketDynamicColumnIndex; i++)
                {
                    string columnExcelName = GetExcelColumnName(i);
                    requestTicketCells[string.Format("{0}1:{0}2", columnExcelName)].Merge();
                }

                for (int i = startTicketDynamicColumnIndex; i <= endTicketDynamicColumnIndex; i++)
                {
                    string columnExcelName = GetExcelColumnName(i);
                    requestTicketCells[string.Format("{0}1:{0}2", columnExcelName)].Font.Color = SpreadsheetGear.Drawing.Color.FromArgb(0, 32, 96);
                    requestTicketCells[string.Format("{0}1:{0}2", columnExcelName)].Interior.Color = SpreadsheetGear.Drawing.Color.FromArgb(142, 180, 227);
                }

                string endColumnExcelName = GetExcelColumnName(endTicketDynamicColumnIndex);
                requestTicketCells[string.Format("A1:{0}2", endColumnExcelName)].Borders.Weight = BorderWeight.Thin;
                requestTicketCells[string.Format("A1:{0}2", endColumnExcelName)].Borders.Color = SpreadsheetGear.Drawing.Color.FromArgb(0, 0, 0);
            } else
            {
                for (int i = startTicketDynamicColumnIndex; i <= endTicketDynamicColumnIndex; i++)
                {
                    string columnExcelName = GetExcelColumnName(i);
                    requestTicketCells[string.Format("{0}1:{0}1", columnExcelName)].Font.Color = SpreadsheetGear.Drawing.Color.FromArgb(0, 32, 96);
                    requestTicketCells[string.Format("{0}1:{0}1", columnExcelName)].Interior.Color = SpreadsheetGear.Drawing.Color.FromArgb(142, 180, 227);
                }

                string endColumnExcelName = GetExcelColumnName(endTicketDynamicColumnIndex);
                requestTicketCells[string.Format("A1:{0}1", endColumnExcelName)].Borders.Weight = BorderWeight.Thin;
                requestTicketCells[string.Format("A1:{0}1", endColumnExcelName)].Borders.Color = SpreadsheetGear.Drawing.Color.FromArgb(0, 0, 0);
            }

            #endregion

            #region Hướng dẫn Sheet

            SpreadsheetGear.IWorksheet userGuideWorksheet = workbook.Worksheets.Add();
            userGuideWorksheet.Name = T["User Guides"];

            userGuideWorksheet.Cells["B2"].ColumnWidth = 160;
            userGuideWorksheet.Cells["B2"].RowHeight = 500;
            userGuideWorksheet.Cells["B2"].VerticalAlignment = SpreadsheetGear.VAlign.Top;
            userGuideWorksheet.Cells["B2"].HorizontalAlignment = HAlign.Left;

            string userGuideRangeCacheKey = "ImportRequestTicket-UserGuide";
            IRange userGuideRange = null;
            if (!CacheProvider.IsSet(userGuideRangeCacheKey))
            {
                SpreadsheetGear.IWorkbook guideWorkbook = SpreadsheetGear.Factory.GetWorkbook(Path.Combine(_env.ContentRootPath, "Views/RequestTicket/Templates/UserGuides/ImportRequestTicket-UserGuide.xlsx"));
                userGuideRange = guideWorkbook.Worksheets[0].Cells["A1"];
                CacheProvider.Set(userGuideRangeCacheKey, userGuideRange, 7 * 24 * 60);
            }
            else
            {
                userGuideRange = CacheProvider.Get(userGuideRangeCacheKey) as IRange;
            }
            userGuideRange.Copy(userGuideWorksheet.Cells["B2"]);

            #endregion

            #region Levels Sheet

            var serviceTypeList = (await QueryExecutor.ExecuteManyAsync(new GetServiceTypeDataByLevelQuery { Level1Id = searchQuery.Level1Id, Level2Id = searchQuery.Level2Id, Level3Id = searchQuery.Level3Id, Level4Id = searchQuery.Level4Id })).ToList();

            SpreadsheetGear.IWorksheet serviceTypeWorksheet = workbook.Worksheets.Add();
            serviceTypeWorksheet.Name = "Service Types";
            serviceTypeWorksheet.MoveAfter(userGuideWorksheet);

            // Level 1
            serviceTypeWorksheet.Cells["A1"].ColumnWidth = 32;
            serviceTypeWorksheet.Cells["A1"].Value = T["Level 1"];
            serviceTypeWorksheet.Cells["A1"].RowHeight = 21;
            serviceTypeWorksheet.Cells["A1"].VerticalAlignment = SpreadsheetGear.VAlign.Center;
            serviceTypeWorksheet.Cells["A1"].Font.Bold = true;
            serviceTypeWorksheet.Cells["A1"].Font.Color = SpreadsheetGear.Drawing.Color.FromArgb(54, 163, 247);
            serviceTypeWorksheet.Cells["A1"].Interior.Color = SpreadsheetGear.Drawing.Color.FromArgb(234, 234, 234);

            // Level 2
            serviceTypeWorksheet.Cells["B1"].ColumnWidth = 32;
            serviceTypeWorksheet.Cells["B1"].Value = T["Level 2"];
            serviceTypeWorksheet.Cells["B1"].RowHeight = 21;
            serviceTypeWorksheet.Cells["B1"].VerticalAlignment = SpreadsheetGear.VAlign.Center;
            serviceTypeWorksheet.Cells["B1"].Font.Bold = true;
            serviceTypeWorksheet.Cells["B1"].Font.Color = SpreadsheetGear.Drawing.Color.FromArgb(54, 163, 247);
            serviceTypeWorksheet.Cells["B1"].Interior.Color = SpreadsheetGear.Drawing.Color.FromArgb(234, 234, 234);

            // Level 3
            serviceTypeWorksheet.Cells["C1"].ColumnWidth = 32;
            serviceTypeWorksheet.Cells["C1"].Value = T["Level 3"];
            serviceTypeWorksheet.Cells["C1"].RowHeight = 21;
            serviceTypeWorksheet.Cells["C1"].VerticalAlignment = SpreadsheetGear.VAlign.Center;
            serviceTypeWorksheet.Cells["C1"].Font.Bold = true;
            serviceTypeWorksheet.Cells["C1"].Font.Color = SpreadsheetGear.Drawing.Color.FromArgb(54, 163, 247);
            serviceTypeWorksheet.Cells["C1"].Interior.Color = SpreadsheetGear.Drawing.Color.FromArgb(234, 234, 234);

            // Level 4
            serviceTypeWorksheet.Cells["D1"].ColumnWidth = 32;
            serviceTypeWorksheet.Cells["D1"].Value = T["Level 4"];
            serviceTypeWorksheet.Cells["D1"].RowHeight = 21;
            serviceTypeWorksheet.Cells["D1"].VerticalAlignment = SpreadsheetGear.VAlign.Center;
            serviceTypeWorksheet.Cells["D1"].Font.Bold = true;
            serviceTypeWorksheet.Cells["D1"].Font.Color = SpreadsheetGear.Drawing.Color.FromArgb(54, 163, 247);
            serviceTypeWorksheet.Cells["D1"].Interior.Color = SpreadsheetGear.Drawing.Color.FromArgb(234, 234, 234);

            int rowIndex = 2;
            foreach (var serviceType in serviceTypeList)
            {
                serviceTypeWorksheet.Cells["A" + rowIndex.ToString()].Value = serviceType.Level1Name;
                serviceTypeWorksheet.Cells["B" + rowIndex.ToString()].Value = serviceType.Level2Name;
                serviceTypeWorksheet.Cells["C" + rowIndex.ToString()].Value = serviceType.Level3Name;
                serviceTypeWorksheet.Cells["D" + rowIndex.ToString()].Value = serviceType.Level4Name;
                rowIndex++;
            }

            #region Distinct Levels

            SpreadsheetGear.IWorksheet levelWorksheet = workbook.Worksheets.Add();
            levelWorksheet.Name = "Levels";

            // Level 1
            levelWorksheet.Cells["A1"].ColumnWidth = 32;
            levelWorksheet.Cells["A1"].Value = T["Level 1"];
            levelWorksheet.Cells["A1"].RowHeight = 21;
            levelWorksheet.Cells["A1"].VerticalAlignment = SpreadsheetGear.VAlign.Center;
            levelWorksheet.Cells["A1"].Font.Bold = true;
            levelWorksheet.Cells["A1"].Font.Color = SpreadsheetGear.Drawing.Color.FromArgb(54, 163, 247);
            levelWorksheet.Cells["A1"].Interior.Color = SpreadsheetGear.Drawing.Color.FromArgb(234, 234, 234);

            levelWorksheet.Cells["B1"].ColumnWidth = 5;

            // Level 2
            levelWorksheet.Cells["C1"].ColumnWidth = 32;
            levelWorksheet.Cells["C1"].Value = T["Level 2"];
            levelWorksheet.Cells["C1"].RowHeight = 21;
            levelWorksheet.Cells["C1"].VerticalAlignment = SpreadsheetGear.VAlign.Center;
            levelWorksheet.Cells["C1"].Font.Bold = true;
            levelWorksheet.Cells["C1"].Font.Color = SpreadsheetGear.Drawing.Color.FromArgb(54, 163, 247);
            levelWorksheet.Cells["C1"].Interior.Color = SpreadsheetGear.Drawing.Color.FromArgb(234, 234, 234);

            levelWorksheet.Cells["D1"].ColumnWidth = 5;

            // Level 3
            levelWorksheet.Cells["E1"].ColumnWidth = 32;
            levelWorksheet.Cells["E1"].Value = T["Level 3"];
            levelWorksheet.Cells["E1"].RowHeight = 21;
            levelWorksheet.Cells["E1"].VerticalAlignment = SpreadsheetGear.VAlign.Center;
            levelWorksheet.Cells["E1"].Font.Bold = true;
            levelWorksheet.Cells["E1"].Font.Color = SpreadsheetGear.Drawing.Color.FromArgb(54, 163, 247);
            levelWorksheet.Cells["E1"].Interior.Color = SpreadsheetGear.Drawing.Color.FromArgb(234, 234, 234);

            levelWorksheet.Cells["F1"].ColumnWidth = 5;

            // Level 4
            levelWorksheet.Cells["G1"].ColumnWidth = 32;
            levelWorksheet.Cells["G1"].Value = T["Level 4"];
            levelWorksheet.Cells["G1"].RowHeight = 21;
            levelWorksheet.Cells["G1"].VerticalAlignment = SpreadsheetGear.VAlign.Center;
            levelWorksheet.Cells["G1"].Font.Bold = true;
            levelWorksheet.Cells["G1"].Font.Color = SpreadsheetGear.Drawing.Color.FromArgb(54, 163, 247);
            levelWorksheet.Cells["G1"].Interior.Color = SpreadsheetGear.Drawing.Color.FromArgb(234, 234, 234);

            List<string> level1Ids = serviceTypeList.Select(st => st.Level1Name).Distinct().ToList();
            rowIndex = 2;
            foreach (var level1Id in level1Ids)
            {
                levelWorksheet.Cells["A" + rowIndex.ToString()].Value = level1Id;
                rowIndex++;
            }
            if (level1ColumnIndex > 0)
            {
                string level1ColumnName = GetExcelColumnName(level1ColumnIndex);
                requestTicketWorksheet.Cells[string.Format("{0}2:{0}6500", level1ColumnName)].Validation.Add(ValidationType.List, ValidationAlertStyle.Warning, ValidationOperator.Default, string.Format("=Levels!$A$2:$A${0}", rowIndex - 1), "");
            }

            List<string> level2Ids = serviceTypeList.Where(st => st.Level2Id.HasValue).Select(st => st.Level2Name).Distinct().ToList();
            rowIndex = 2;
            foreach (var level2Id in level2Ids)
            {
                levelWorksheet.Cells["C" + rowIndex.ToString()].Value = level2Id;
                rowIndex++;
            }
            if (level2ColumnIndex > 0)
            {
                string level2ColumnName = GetExcelColumnName(level2ColumnIndex);
                requestTicketWorksheet.Cells[string.Format("{0}2:{0}6500", level2ColumnName)].Validation.Add(ValidationType.List, ValidationAlertStyle.Warning, ValidationOperator.Default, string.Format("=Levels!$C$2:$C${0}", rowIndex - 1), "");
            }

            List<string> level3Ids = serviceTypeList.Where(st => st.Level3Id.HasValue).Select(st => st.Level3Name).Distinct().ToList();
            rowIndex = 2;
            foreach (var level3Id in level3Ids)
            {
                levelWorksheet.Cells["E" + rowIndex.ToString()].Value = level3Id;
                rowIndex++;
            }
            if (level3ColumnIndex > 0)
            {
                string level3ColumnName = GetExcelColumnName(level3ColumnIndex);
                requestTicketWorksheet.Cells[string.Format("{0}2:{0}6500", level3ColumnName)].Validation.Add(ValidationType.List, ValidationAlertStyle.Warning, ValidationOperator.Default, string.Format("=Levels!$E$2:$E${0}", rowIndex - 1), "");
            }

            List<string> level4Ids = serviceTypeList.Where(st => st.Level4Id.HasValue).Select(st => st.Level4Name).Distinct().ToList();
            rowIndex = 2;
            foreach (var level4Id in level4Ids)
            {
                levelWorksheet.Cells["G" + rowIndex.ToString()].Value = level4Id;
                rowIndex++;
            }
            if (level4ColumnIndex > 0)
            {
                string level4ColumnName = GetExcelColumnName(level4ColumnIndex);
                requestTicketWorksheet.Cells[string.Format("{0}2:{0}6500", level4ColumnName)].Validation.Add(ValidationType.List, ValidationAlertStyle.Warning, ValidationOperator.Default, string.Format("=Levels!$G$2:$G${0}", rowIndex - 1), "");
            }

            levelWorksheet.Visible = SheetVisibility.VeryHidden;

            #endregion

            #endregion

            #region References Sheet

            SpreadsheetGear.IWorksheet referenceWorksheet = workbook.Worksheets.Add();
            referenceWorksheet.Name = "References";

            // 1. RequestTicket Owner
            referenceWorksheet.Cells["A1"].ColumnWidth = 32;
            referenceWorksheet.Cells["A1"].Value = T["Danh sách người phụ trách"];
            referenceWorksheet.Cells["A1"].RowHeight = 21;
            referenceWorksheet.Cells["A1"].VerticalAlignment = SpreadsheetGear.VAlign.Center;
            referenceWorksheet.Cells["A1"].Font.Bold = true;
            referenceWorksheet.Cells["A1"].Font.Color = SpreadsheetGear.Drawing.Color.FromArgb(54, 163, 247);
            referenceWorksheet.Cells["A1"].Interior.Color = SpreadsheetGear.Drawing.Color.FromArgb(234, 234, 234);

            var ownerUserProfiles = (await QueryExecutor.ExecuteManyAsync(new GetOwnerListQuery() { IsApproved = true })).ToList();
            rowIndex = 2;
            foreach (var ownerUserProfile in ownerUserProfiles)
            {
                referenceWorksheet.Cells["A" + rowIndex.ToString()].Value = string.Format("({0})-{1}", ownerUserProfile.UserName, ownerUserProfile.FullName);
                rowIndex++;
            }

            referenceWorksheet.Cells["B1"].ColumnWidth = 5;

            // 2. Channel
            referenceWorksheet.Cells["C1"].ColumnWidth = 32;
            referenceWorksheet.Cells["C1"].Value = T["Danh sách Kênh tiếp nhận"];
            referenceWorksheet.Cells["C1"].RowHeight = 21;
            referenceWorksheet.Cells["C1"].VerticalAlignment = SpreadsheetGear.VAlign.Center;
            referenceWorksheet.Cells["C1"].Font.Bold = true;
            referenceWorksheet.Cells["C1"].Font.Color = SpreadsheetGear.Drawing.Color.FromArgb(54, 163, 247);
            referenceWorksheet.Cells["C1"].Interior.Color = SpreadsheetGear.Drawing.Color.FromArgb(234, 234, 234);

            var channelList = await QueryExecutor.ExecuteManyAsync(new GetChannelListQuery() { IsDisabled = false });
            rowIndex = 2;
            foreach (var channel in channelList)
            {
                referenceWorksheet.Cells["C" + rowIndex.ToString()].Value = channel.Name;
                rowIndex++;
            }

            if (channelColumnIndex > 0)
            {
                string channelColumnName = GetExcelColumnName(channelColumnIndex);
                referenceWorksheet.Cells["C1"].Value = requestTicketCells[string.Format("{0}1", channelColumnName)].Text;
                try
                {
                    requestTicketWorksheet.Cells[string.Format("{0}3:{0}6500", channelColumnName)].Validation.Delete();
                    requestTicketWorksheet.Cells[string.Format("{0}3:{0}6500", channelColumnName)].Validation.Add(ValidationType.List, ValidationAlertStyle.Warning, ValidationOperator.Default, string.Format("=References!$C$2:$C${0}", rowIndex - 1), "");
                }
                catch { }
            }

            referenceWorksheet.Cells["D1"].ColumnWidth = 5;

            // 3. Difficulty
            referenceWorksheet.Cells["E1"].ColumnWidth = 32;
            referenceWorksheet.Cells["E1"].Value = T["Danh sách Độ khẩn"];
            referenceWorksheet.Cells["E1"].RowHeight = 21;
            referenceWorksheet.Cells["E1"].VerticalAlignment = SpreadsheetGear.VAlign.Center;
            referenceWorksheet.Cells["E1"].Font.Bold = true;
            referenceWorksheet.Cells["E1"].Font.Color = SpreadsheetGear.Drawing.Color.FromArgb(54, 163, 247);
            referenceWorksheet.Cells["E1"].Interior.Color = SpreadsheetGear.Drawing.Color.FromArgb(234, 234, 234);

            var difficultyDegreeEnumValues = System.Enum.GetValues(typeof(Difficulty));
            rowIndex = 2;
            foreach (var difficultyDegree in difficultyDegreeEnumValues)
            {
                referenceWorksheet.Cells["E" + rowIndex.ToString()].Value = difficultyDegree.GetDescription();
                rowIndex++;
            }

            if (difficultyDegreeColumnIndex > 0)
            {
                string difficultyDegreeColumnName = GetExcelColumnName(difficultyDegreeColumnIndex);
                referenceWorksheet.Cells["E1"].Value = requestTicketCells[string.Format("{0}1", difficultyDegreeColumnName)].Text;
                try
                {
                    requestTicketWorksheet.Cells[string.Format("{0}3:{0}6500", difficultyDegreeColumnName)].Validation.Delete();
                    requestTicketWorksheet.Cells[string.Format("{0}3:{0}6500", difficultyDegreeColumnName)].Validation.Add(ValidationType.List, ValidationAlertStyle.Warning, ValidationOperator.Default, string.Format("=References!$E$2:$E${0}", rowIndex - 1), "");
                }
                catch { }
            }

            referenceWorksheet.Cells["F1"].ColumnWidth = 5;

            // 4. RequestTicketStatus
            referenceWorksheet.Cells["G1"].ColumnWidth = 32;
            referenceWorksheet.Cells["G1"].Value = T["Danh sách Trạng thái phiếu"];
            referenceWorksheet.Cells["G1"].RowHeight = 21;
            referenceWorksheet.Cells["G1"].VerticalAlignment = SpreadsheetGear.VAlign.Center;
            referenceWorksheet.Cells["G1"].Font.Bold = true;
            referenceWorksheet.Cells["G1"].Font.Color = SpreadsheetGear.Drawing.Color.FromArgb(54, 163, 247);
            referenceWorksheet.Cells["G1"].Interior.Color = SpreadsheetGear.Drawing.Color.FromArgb(234, 234, 234);

            var requestTicketStatusEnumValues = System.Enum.GetValues(typeof(RequestTicketStatus));
            rowIndex = 2;
            foreach (var requestTicketStatus in requestTicketStatusEnumValues)
            {
                referenceWorksheet.Cells["G" + rowIndex.ToString()].Value = requestTicketStatus.GetDescription();
                rowIndex++;
            }

            if (requestTicketStatusColumnIndex > 0)
            {
                string requestTicketStatusColumnName = GetExcelColumnName(requestTicketStatusColumnIndex);
                referenceWorksheet.Cells["G1"].Value = requestTicketCells[string.Format("{0}1", requestTicketStatusColumnName)].Text;
                try
                {
                    requestTicketWorksheet.Cells[string.Format("{0}3:{0}6500", requestTicketStatusColumnName)].Validation.Delete();
                    requestTicketWorksheet.Cells[string.Format("{0}3:{0}6500", requestTicketStatusColumnName)].Validation.Add(ValidationType.List, ValidationAlertStyle.Warning, ValidationOperator.Default, string.Format("=References!$G$2:$G${0}", rowIndex - 1), "");
                }
                catch { }
            }

            referenceWorksheet.Cells["H1"].ColumnWidth = 5;

            // 5. TicketBusinessResult
            referenceWorksheet.Cells["I1"].ColumnWidth = 32;
            referenceWorksheet.Cells["I1"].Value = T["Danh sách Kết quả phiếu"];
            referenceWorksheet.Cells["I1"].RowHeight = 21;
            referenceWorksheet.Cells["I1"].VerticalAlignment = SpreadsheetGear.VAlign.Center;
            referenceWorksheet.Cells["I1"].Font.Bold = true;
            referenceWorksheet.Cells["I1"].Font.Color = SpreadsheetGear.Drawing.Color.FromArgb(54, 163, 247);
            referenceWorksheet.Cells["I1"].Interior.Color = SpreadsheetGear.Drawing.Color.FromArgb(234, 234, 234);

            var businessResultList = (await QueryExecutor.ExecuteManyAsync(new GetBusinessResultListQuery { })).ToList();
            rowIndex = 2;
            foreach (var businessResult in businessResultList)
            {
                referenceWorksheet.Cells["I" + rowIndex.ToString()].Value = businessResult.Name;
                rowIndex++;
            }

            if (ticketBusinessResultColumnIndex > 0)
            {
                string ticketBusinessResultColumnName = GetExcelColumnName(ticketBusinessResultColumnIndex);
                referenceWorksheet.Cells["I1"].Value = requestTicketCells[string.Format("{0}1", ticketBusinessResultColumnName)].Text;
                try
                {
                    requestTicketWorksheet.Cells[string.Format("{0}3:{0}6500", ticketBusinessResultColumnName)].Validation.Delete();
                    requestTicketWorksheet.Cells[string.Format("{0}3:{0}6500", ticketBusinessResultColumnName)].Validation.Add(ValidationType.List, ValidationAlertStyle.Warning, ValidationOperator.Default, string.Format("=References!$I$2:$I${0}", rowIndex - 1), "");
                }
                catch { }
            }

            referenceWorksheet.Cells["J1"].ColumnWidth = 5;

            // 6. Danh sách phòng ban
            referenceWorksheet.Cells["K1"].ColumnWidth = 64;
            referenceWorksheet.Cells["K1"].Value = T["Danh sách phòng ban"];
            referenceWorksheet.Cells["K1"].RowHeight = 21;
            referenceWorksheet.Cells["K1"].VerticalAlignment = SpreadsheetGear.VAlign.Center;
            referenceWorksheet.Cells["K1"].Font.Bold = true;
            referenceWorksheet.Cells["K1"].Font.Color = SpreadsheetGear.Drawing.Color.FromArgb(54, 163, 247);
            referenceWorksheet.Cells["K1"].Interior.Color = SpreadsheetGear.Drawing.Color.FromArgb(234, 234, 234);

            Dictionary<int, SpreadsheetGear.Drawing.Color> orgTreeColors = new Dictionary<int, SpreadsheetGear.Drawing.Color>();
            orgTreeColors.Add(1, SpreadsheetGear.Drawing.Color.FromArgb(191, 191, 191));
            orgTreeColors.Add(2, SpreadsheetGear.Drawing.Color.FromArgb(217, 217, 217));
            orgTreeColors.Add(3, SpreadsheetGear.Drawing.Color.FromArgb(242, 242, 242));

            var allOrgList = (await OrganizationUtility.GetAllOrganizationListWithParentChildOrderListAsync()).ToList();
            rowIndex = 2;
            foreach (var org in allOrgList)
            {
                string spaces = string.Empty;
                for (int i = 1; i < org.NodeLevel; i++)
                {
                    spaces += "    ";
                }

                referenceWorksheet.Cells["K" + rowIndex.ToString()].Value = string.Format("{0}({1})-{2}", spaces, org.Code, org.Name);
                if (orgTreeColors.ContainsKey(org.NodeLevel))
                {
                    referenceWorksheet.Cells["K" + rowIndex.ToString()].Interior.Color = orgTreeColors[org.NodeLevel];
                }
                rowIndex++;
            }

            int ticketDynamicFieldIndex = 0;

            // Các DynamicField SelectBox
            List<string> duplicatedDynamicFields = new List<string>();
            int startExcelColumnIndex = 12;
            ticketDynamicFieldIndex = 0;
            foreach (DynamicFieldListItem dynamicColumn in ticketDynamicFieldList)
            {
                if (dynamicColumn.ViewHint.IsEqualIgnoreCase("SelectField") && dynamicColumn.SelectOptions.IsNotNullOrEmpty())
                {
                    if (!duplicatedDynamicFields.Exists(t => t == dynamicColumn.DynamicFieldName))
                    {
                        string blankColumnName = GetExcelColumnName(startExcelColumnIndex);
                        referenceWorksheet.Cells[blankColumnName + "1"].ColumnWidth = 5;

                        string dynamicFieldColumnExcelName = GetExcelColumnName(startExcelColumnIndex + 1);

                        referenceWorksheet.Cells[dynamicFieldColumnExcelName + "1"].ColumnWidth = 32;
                        referenceWorksheet.Cells[dynamicFieldColumnExcelName + "1"].Value = dynamicColumn.DisplayName;
                        referenceWorksheet.Cells[dynamicFieldColumnExcelName + "1"].RowHeight = 21;
                        referenceWorksheet.Cells[dynamicFieldColumnExcelName + "1"].VerticalAlignment = SpreadsheetGear.VAlign.Center;
                        referenceWorksheet.Cells[dynamicFieldColumnExcelName + "1"].Font.Bold = true;
                        referenceWorksheet.Cells[dynamicFieldColumnExcelName + "1"].Font.Color = SpreadsheetGear.Drawing.Color.FromArgb(54, 163, 247);
                        referenceWorksheet.Cells[dynamicFieldColumnExcelName + "1"].Interior.Color = SpreadsheetGear.Drawing.Color.FromArgb(234, 234, 234);

                        List<string> selectOptions = new List<string>();
                        try
                        {
                            selectOptions = JsonConvert.DeserializeObject<List<string>>(dynamicColumn.SelectOptions);
                        }
                        catch (Exception) { }

                        rowIndex = 2;
                        foreach (var selectOption in selectOptions)
                        {
                            if (selectOption.IsNotNullOrEmpty())
                            {
                                referenceWorksheet.Cells[dynamicFieldColumnExcelName + rowIndex.ToString()].Value = selectOption;
                                rowIndex++;
                            }
                        }

                        string ticketSelectDynamicFieldColumnName = GetExcelColumnName(startTicketDynamicColumnIndex + ticketDynamicFieldIndex);
                        if (!requestTicketWorksheet.Cells[string.Format("{0}2:{0}6500", ticketSelectDynamicFieldColumnName)].ValidationDefined)
                        {
                            requestTicketWorksheet.Cells[string.Format("{0}2:{0}6500", ticketSelectDynamicFieldColumnName)].Validation.Add(ValidationType.List, ValidationAlertStyle.Warning, ValidationOperator.Default, string.Format("=References!${1}$2:${1}${0}", rowIndex - 1, dynamicFieldColumnExcelName), "");
                        }

                        duplicatedDynamicFields.Add(dynamicColumn.DynamicFieldName);
                        startExcelColumnIndex += 2;
                    }
                }
                ticketDynamicFieldIndex++;
            }

            #endregion

            #region ReferencesSearch Sheet

            SpreadsheetGear.IWorksheet referenceSearchWorksheet = workbook.Worksheets.Add();
            referenceSearchWorksheet.Name = "ReferencesSearch";
            referenceSearchWorksheet.Visible = SheetVisibility.Hidden;

            referenceSearchWorksheet.Cells["A1"].ColumnWidth = 5;

            // 1. RequestTicket Owner
            referenceSearchWorksheet.Cells["B1"].ColumnWidth = 42;
            referenceSearchWorksheet.Cells["B1"].Value = T["Danh sách người phụ trách"];
            referenceSearchWorksheet.Cells["B1"].RowHeight = 21;
            referenceSearchWorksheet.Cells["B1"].VerticalAlignment = SpreadsheetGear.VAlign.Center;
            referenceSearchWorksheet.Cells["B1"].Font.Bold = true;
            referenceSearchWorksheet.Cells["B1"].Font.Color = SpreadsheetGear.Drawing.Color.FromArgb(54, 163, 247);
            referenceSearchWorksheet.Cells["B1"].Interior.Color = SpreadsheetGear.Drawing.Color.FromArgb(234, 234, 234);

            rowIndex = 2;
            foreach (var ownerUserProfile in ownerUserProfiles)
            {
                referenceSearchWorksheet.Cells["B" + rowIndex.ToString()].Value = string.Format("({0})-{1}", ownerUserProfile.UserName, ownerUserProfile.FullName);
                referenceSearchWorksheet.Cells[String.Format("A{0}", rowIndex)].Formula = string.Format("=IF(ISNUMBER(SEARCH(INDIRECT(CELL(\"address\")),B{0})),MAX($A$1:A{1})+1,0)", rowIndex, rowIndex - 1);
                referenceSearchWorksheet.Cells[String.Format("C{0}", rowIndex)].Formula = string.Format("=IFERROR(VLOOKUP(ROWS($C$1:C{0}),$A$2:$B${1},2,FALSE),\"\")", rowIndex - 1, ownerUserProfiles.Count);
                rowIndex++;
            }

            if (ownerIdColumnIndex > 0)
            {
                string ticketOwnerColumnName = GetExcelColumnName(ownerIdColumnIndex);
                referenceSearchWorksheet.Cells["B1"].Value = requestTicketCells[string.Format("{0}1", ticketOwnerColumnName)].Text;
                requestTicketWorksheet.Cells[string.Format("{0}2:{0}5000", ticketOwnerColumnName)].Validation.Add(ValidationType.List, ValidationAlertStyle.Warning, ValidationOperator.Default, "=OFFSET(ReferencesSearch!$C$2,0,0,COUNTIF(ReferencesSearch!$C:$C,\"?*\"),1)", "");
                requestTicketWorksheet.Cells[string.Format("{0}2:{0}5000", ticketOwnerColumnName)].Validation.ShowError = false;
            }

            referenceSearchWorksheet.Cells["C1"].ColumnWidth = 10;
            referenceSearchWorksheet.Cells["D1"].ColumnWidth = 5;

            // 2. Danh sách phòng ban
            referenceSearchWorksheet.Cells["E1"].ColumnWidth = 64;
            referenceSearchWorksheet.Cells["E1"].Value = T["Danh sách phòng ban"];
            referenceSearchWorksheet.Cells["E1"].RowHeight = 21;
            referenceSearchWorksheet.Cells["E1"].VerticalAlignment = SpreadsheetGear.VAlign.Center;
            referenceSearchWorksheet.Cells["E1"].Font.Bold = true;
            referenceSearchWorksheet.Cells["E1"].Font.Color = SpreadsheetGear.Drawing.Color.FromArgb(54, 163, 247);
            referenceSearchWorksheet.Cells["E1"].Interior.Color = SpreadsheetGear.Drawing.Color.FromArgb(234, 234, 234);

            rowIndex = 2;
            foreach (var org in allOrgList)
            {
                string spaces = string.Empty;
                for (int i = 1; i < org.NodeLevel; i++)
                {
                    spaces += "    ";
                }

                referenceSearchWorksheet.Cells["E" + rowIndex.ToString()].Value = string.Format("{0}({1})-{2}", spaces, org.Code, org.Name);
                if (orgTreeColors.ContainsKey(org.NodeLevel))
                {
                    referenceSearchWorksheet.Cells["E" + rowIndex.ToString()].Interior.Color = orgTreeColors[org.NodeLevel];
                }

                referenceSearchWorksheet.Cells[String.Format("D{0}", rowIndex)].Formula = string.Format("=IF(ISNUMBER(SEARCH(INDIRECT(CELL(\"address\")),E{0})),MAX($D$1:D{1})+1,0)", rowIndex, rowIndex - 1);
                referenceSearchWorksheet.Cells[String.Format("F{0}", rowIndex)].Formula = string.Format("=IFERROR(VLOOKUP(ROWS($F$1:F{0}),$D$2:$E${1},2,FALSE),\"\")", rowIndex - 1, allOrgList.Count);

                rowIndex++;
            }

            var dynamicOrgDataType = typeof(Webaby.Core.Organization.Queries.OrganizationData);
            ticketDynamicFieldIndex = 0;
            foreach (DynamicFieldListItem dynamicColumn in ticketDynamicFieldList)
            {
                Type dynamicType = dynamicColumn.DataType.ToType();
                if (dynamicType == dynamicOrgDataType)
                {
                    string ticketOrgDynamicFieldColumnName = GetExcelColumnName(startTicketDynamicColumnIndex + ticketDynamicFieldIndex);

                    requestTicketWorksheet.Cells[string.Format("{0}2:{0}5000", ticketOrgDynamicFieldColumnName)].Validation.Add(ValidationType.List, ValidationAlertStyle.Warning, ValidationOperator.Default, "=OFFSET(ReferencesSearch!$F$2,0,0,COUNTIF(ReferencesSearch!$F:$F,\"?*\"),1)", "");
                    requestTicketWorksheet.Cells[string.Format("{0}2:{0}5000", ticketOrgDynamicFieldColumnName)].Validation.ShowError = false;
                }
                ticketDynamicFieldIndex++;
            }

            #endregion

            WebabyEvent webabyEvent = new WebabyEvent();
            webabyEvent.Name = "ExportSearchRequestTicketResultToImportCompleted";

            WebabyHubEventBus.PublishEvent(webabyEvent, UserService.GetCurrentUser().Id);

            byte[] bytes = null;
            using (var ms = new MemoryStream())
            {
                workbook.Worksheets[0].Select();
                workbook.SaveToStream(ms, SpreadsheetGear.FileFormat.OpenXMLWorkbook);
                bytes = ms.ToArray();
            }

            return bytes;
        }

        public async Task<byte[]> ExportSearchRequestTicketResultByDynamicFieldListAsync(SearchRequestTicketArguments arguments, string templateName, bool? isDisplay)
        {
            SearchRequestTicketNABQuery searchQuery = Mapper.Map<SearchRequestTicketNABQuery>(arguments);

            string selectedColumns = string.Empty;
            string whereCondition = "";

            List<TicketTaskExportImportColumnMapping> allColumnMappingList = new List<TicketTaskExportImportColumnMapping>();
            if (templateName.IsNullOrEmpty())
            {
                templateName = "Views/RequestTicket/ExportDefs/ExportSearchRequestTicketResult-ImportColumnMapping.txt";
            }
            string columnMappingListJson = System.IO.File.ReadAllText(Path.Combine(_env.ContentRootPath, templateName));
            allColumnMappingList = JsonConvert.DeserializeObject<List<TicketTaskExportImportColumnMapping>>(columnMappingListJson).OrderBy(x => x.ColumnOrder).ToList();

            int ownerIdColumnIndex = 0, channelColumnIndex = 0, difficultyDegreeColumnIndex = 0, requestTicketStatusColumnIndex = 0, ticketBusinessResultColumnIndex = 0;
            int level1ColumnIndex = 0, level2ColumnIndex = 0, level3ColumnIndex = 0, level4ColumnIndex = 0;

            #region Các Properties + DynamicFields của Phiếu

            int tempColumnIndex = 1;
            List<TicketTaskExportImportColumnMapping> ticketColumnMappingList = allColumnMappingList.Where(col => col.IsTicketColumn == true).OrderBy(col => col.ColumnOrder).ToList();
            int countLevelServiceType = 0;
            foreach (var columnMapping in ticketColumnMappingList)
            {
                if (!selectedColumns.IsNullOrEmpty())
                {
                    selectedColumns += ", ";
                }
                selectedColumns += string.Format("{0} [{1}]", columnMapping.ColumnName, columnMapping.ColumnAlias);

                if (columnMapping.ColumnName.IsEqualIgnoreCase("OwnerUser"))
                {
                    ownerIdColumnIndex = tempColumnIndex;
                }
                else if (columnMapping.ColumnName.IsEqualIgnoreCase("Channel"))
                {
                    channelColumnIndex = tempColumnIndex;
                }
                else if (columnMapping.ColumnName.IsEqualIgnoreCase("DifficultyDegree"))
                {
                    difficultyDegreeColumnIndex = tempColumnIndex;
                }
                else if (columnMapping.ColumnName.IsEqualIgnoreCase("RequestTicketStatus"))
                {
                    requestTicketStatusColumnIndex = tempColumnIndex;
                }
                else if (columnMapping.ColumnName.IsEqualIgnoreCase("TicketBusinessResult"))
                {
                    ticketBusinessResultColumnIndex = tempColumnIndex;
                }
                else if (columnMapping.ColumnName.IsEqualIgnoreCase("Level1"))
                {
                    countLevelServiceType++;
                    level1ColumnIndex = tempColumnIndex;
                }
                else if (columnMapping.ColumnName.IsEqualIgnoreCase("Level2"))
                {
                    countLevelServiceType++;
                    level2ColumnIndex = tempColumnIndex;
                }
                else if (columnMapping.ColumnName.IsEqualIgnoreCase("Level3"))
                {
                    countLevelServiceType++;
                    level3ColumnIndex = tempColumnIndex;
                }
                else if (columnMapping.ColumnName.IsEqualIgnoreCase("Level4"))
                {
                    countLevelServiceType++;
                    level4ColumnIndex = tempColumnIndex;
                }
                tempColumnIndex++;
            }
            string exeptionJoins = string.Empty;

            List<DynamicFieldListItem> distinctTicketDynamicFieldList = new List<DynamicFieldListItem>();

            // Lấy tất cả Dynamic Field trong tất cả Service Type được Search
            if (searchQuery.Level1Id.IsNotNullOrEmpty())
            {
                var ticketDynamicFieldList = (await QueryExecutor.ExecuteManyAsync(new GetDynamicFieldListByServiceTypesQuery
                {
                    Level1Id = searchQuery.Level1Id,
                    Level2Id = searchQuery.Level2Id,
                    Level3Id = searchQuery.Level3Id,
                    Level4Id = searchQuery.Level4Id,
                    ServiceTypeId = searchQuery.ServiceTypeId,
                    Display = isDisplay
                })).Where(dfd => dfd.IsExportExcel).ToList();

                var fileDataType = typeof(Webaby.Core.File.Queries.FileData);
                var orgDataType = typeof(Webaby.Core.Organization.Queries.OrganizationData);

                foreach (var dynamicColumn in ticketDynamicFieldList)
                {
                    if (dynamicColumn.IsExportByConditionBoolean && (dynamicColumn.DataType == "System.Nullable`1[System.Int64]" || dynamicColumn.DataType == "System.Nullable`1[System.Boolean]" || dynamicColumn.DataType == "System.Nullable`1[System.Int32]"))
                    {
                        if (whereCondition == "")
                        {
                            whereCondition = string.Format("Where [{0}] <> 0", dynamicColumn.DynamicFieldName);
                        }
                        else
                        {
                            whereCondition = string.Format("{0} AND [{1}] <> 0", whereCondition, dynamicColumn.DynamicFieldName);
                        }
                    }

                    if (!distinctTicketDynamicFieldList.Exists(dfd => dfd.DynamicFieldId == dynamicColumn.DynamicFieldId))
                    {
                        Type dynamicType = dynamicColumn.DataType.ToType();
                        bool isFileData = dynamicType == fileDataType;
                        bool isMultiFileData = false;
                        if (!isFileData)
                        {
                            if (dynamicType.IsGenericType)
                            {
                                isMultiFileData = dynamicType.GetGenericArguments().Single() == fileDataType;
                            }
                        }

                        if (!dynamicType.IsUserDefinedTableGridData())
                        {
                            if (!selectedColumns.IsNullOrEmpty())
                            {
                                selectedColumns += ", ";
                            }
                        }

                        if (dynamicType == orgDataType)
                        {
                            selectedColumns += string.Format("'(' + [{0}TicketOrg].Code + ')-' + [{0}TicketOrg].Name [{1}]", dynamicColumn.DynamicFieldName, dynamicColumn.DisplayName);
                            exeptionJoins += string.Format(" LEFT JOIN dbo.Organization [{0}TicketOrg] WITH(NOLOCK) ON [{0}TicketOrg].Id = dbo.ConvertToGuid(mainResult.{0}, '00000000-0000-0000-0000-000000000000') ", dynamicColumn.DynamicFieldName);
                        }
                        else if (isFileData)
                        {
                            selectedColumns += string.Format("[{0}TicketFile].FileName [{1}]", dynamicColumn.DynamicFieldName, dynamicColumn.DisplayName);
                            exeptionJoins += string.Format(" LEFT JOIN dbo.[File] [{0}TicketFile] WITH(NOLOCK) ON [{0}TicketFile].Id = dbo.ConvertToGuid(mainResult.{0}, '00000000-0000-0000-0000-000000000000') ", dynamicColumn.DynamicFieldName);
                        }
                        else if (isMultiFileData)
                        {
                            selectedColumns += string.Format("[dbo].[GetDynamicMultiFileNames](mainResult.[{0}]) [{1}] ", dynamicColumn.DynamicFieldName, dynamicColumn.DisplayName);
                        }
                        else if (!dynamicType.IsUserDefinedTableGridData())
                        {
                            selectedColumns += string.Format("[{0}] [{1}] ", dynamicColumn.DynamicFieldName, dynamicColumn.DisplayName);
                        }

                        if (!dynamicType.IsUserDefinedTableGridData())
                        {
                            distinctTicketDynamicFieldList.Add(dynamicColumn);
                        }
                    }
                }
            }

            #endregion

            var exportSearchRequestTicketResults = RequestTicketServices.ExportSearchRequestTicketDynamicResult(searchQuery, selectedColumns, distinctTicketDynamicFieldList, exeptionJoins, false, whereCondition);

            SpreadsheetGear.IWorkbook workbook = SpreadsheetGear.Factory.GetWorkbook();
            SpreadsheetGear.IWorksheet worksheet = workbook.Worksheets["Sheet1"];
            worksheet.Name = "RequestTicket";
            SpreadsheetGear.IRange range = worksheet.Cells["A1"];
            range.CopyFromDataTable(exportSearchRequestTicketResults.Tables[0], SetDataFlags.AllText);

            #region Style Export Excel

            SpreadsheetGear.IWorksheet requestTicketWorksheet = workbook.Worksheets[0];
            SpreadsheetGear.IRange requestTicketCells = requestTicketWorksheet.Cells;

            string[] requiredColumns = { "channel", "difficultydegree" };

            int totalColumnCount = selectedColumns.Split(',').Count();
            for (int i = 1; i <= totalColumnCount; i++)
            {
                string columnExcelName = GetExcelColumnName(i);

                requestTicketCells[columnExcelName + "1"].RowHeight = 21;
                requestTicketCells[columnExcelName + "1"].ColumnWidth = 16;
                requestTicketCells[columnExcelName + "1"].VerticalAlignment = SpreadsheetGear.VAlign.Center;
                requestTicketCells[columnExcelName + "1"].Font.Bold = true;
                requestTicketCells[columnExcelName + "1"].Font.Color = SpreadsheetGear.Drawing.Color.FromArgb(0, 32, 96);
                requestTicketCells[columnExcelName + "1"].Interior.Color = SpreadsheetGear.Drawing.Color.FromArgb(198, 217, 241);
            }

            string[] selectedColumnList = selectedColumns.Split(',');
            int columnIndex = 1;
            foreach (var selectedColumn in selectedColumnList)
            {
                string columnExcelName = GetExcelColumnName(columnIndex);

                string[] selectedColumnParts = selectedColumn.Split('[');
                string columnName = selectedColumnParts[0].Trim().ToLower();
                if (requiredColumns.Contains(columnName))
                {
                    requestTicketCells[columnExcelName + "1"].Font.Italic = true;

                    int labelLength = requestTicketCells[columnExcelName + "1"].Text.Length;
                    requestTicketCells[columnExcelName + "1"].Value = requestTicketCells[columnExcelName + "1"].Text + " (*)";
                    ICharacters characters = requestTicketCells[columnExcelName + "1"].GetCharacters(labelLength, 4);
                    characters.Font.Bold = false;
                    characters.Font.Italic = true;
                    characters.Font.Size = 10;
                }
                columnIndex++;
            }

            int startTicketDynamicColumnIndex = ticketColumnMappingList.Count + 1;
            int endTicketDynamicColumnIndex = startTicketDynamicColumnIndex + distinctTicketDynamicFieldList.Count - 1;

            if (countLevelServiceType >= 2)
            {
                requestTicketCells["1:1"].Insert();

                requestTicketCells["A1:D1"].RowHeight = 18;
                requestTicketCells["A1:D1"].VerticalAlignment = VAlign.Center;
                requestTicketCells["A1:D1"].HorizontalAlignment = HAlign.Center;
                requestTicketCells["A1:D1"].Font.Bold = true;
                requestTicketCells["A1:D1"].Font.Italic = true;
                requestTicketCells["A1:D1"].Font.Color = SpreadsheetGear.Drawing.Color.FromArgb(0, 32, 96);
                requestTicketCells["A1:D1"].Interior.Color = SpreadsheetGear.Drawing.Color.FromArgb(198, 217, 241);
                requestTicketCells["A1:D1"].Merge();

                requestTicketCells["A1"].Value = T["Loại dịch vụ"];
                int labelLengthTemp = requestTicketCells["A1"].Text.Length;
                requestTicketCells["A1"].Value = requestTicketCells["A1"].Text + " (**)";
                ICharacters tempCharacters = requestTicketCells["A1"].GetCharacters(labelLengthTemp, 5);
                tempCharacters.Font.Bold = false;
                tempCharacters.Font.Italic = true;
                tempCharacters.Font.Size = 10;

                for (int i = 5; i <= endTicketDynamicColumnIndex; i++)
                {
                    string columnExcelName = GetExcelColumnName(i);
                    requestTicketCells[string.Format("{0}1:{0}2", columnExcelName)].Merge();
                }

                for (int i = startTicketDynamicColumnIndex; i <= endTicketDynamicColumnIndex; i++)
                {
                    string columnExcelName = GetExcelColumnName(i);
                    requestTicketCells[string.Format("{0}1:{0}2", columnExcelName)].Font.Color = SpreadsheetGear.Drawing.Color.FromArgb(0, 32, 96);
                    requestTicketCells[string.Format("{0}1:{0}2", columnExcelName)].Interior.Color = SpreadsheetGear.Drawing.Color.FromArgb(142, 180, 227);
                }

                string endColumnExcelName = GetExcelColumnName(endTicketDynamicColumnIndex);
                requestTicketCells[string.Format("A1:{0}2", endColumnExcelName)].Borders.Weight = BorderWeight.Thin;
                requestTicketCells[string.Format("A1:{0}2", endColumnExcelName)].Borders.Color = SpreadsheetGear.Drawing.Color.FromArgb(0, 0, 0);
            }
            else
            {
                for (int i = startTicketDynamicColumnIndex; i <= endTicketDynamicColumnIndex; i++)
                {
                    string columnExcelName = GetExcelColumnName(i);
                    requestTicketCells[string.Format("{0}1:{0}1", columnExcelName)].Font.Color = SpreadsheetGear.Drawing.Color.FromArgb(0, 32, 96);
                    requestTicketCells[string.Format("{0}1:{0}1", columnExcelName)].Interior.Color = SpreadsheetGear.Drawing.Color.FromArgb(142, 180, 227);
                }

                string endColumnExcelName = GetExcelColumnName(endTicketDynamicColumnIndex);
                requestTicketCells[string.Format("A1:{0}1", endColumnExcelName)].Borders.Weight = BorderWeight.Thin;
                requestTicketCells[string.Format("A1:{0}1", endColumnExcelName)].Borders.Color = SpreadsheetGear.Drawing.Color.FromArgb(0, 0, 0);
            }

            #endregion

            #region Hướng dẫn Sheet

            SpreadsheetGear.IWorksheet userGuideWorksheet = workbook.Worksheets.Add();
            userGuideWorksheet.Name = T["User Guides"];

            userGuideWorksheet.Cells["B2"].ColumnWidth = 160;
            userGuideWorksheet.Cells["B2"].RowHeight = 500;
            userGuideWorksheet.Cells["B2"].VerticalAlignment = SpreadsheetGear.VAlign.Top;
            userGuideWorksheet.Cells["B2"].HorizontalAlignment = HAlign.Left;

            string userGuideRangeCacheKey = "ImportRequestTicket-UserGuide";
            IRange userGuideRange = null;
            if (!CacheProvider.IsSet(userGuideRangeCacheKey))
            {
                SpreadsheetGear.IWorkbook guideWorkbook = SpreadsheetGear.Factory.GetWorkbook(Path.Combine(_env.ContentRootPath, "Views/RequestTicket/Templates/UserGuides/ImportRequestTicket-UserGuide.xlsx"));
                userGuideRange = guideWorkbook.Worksheets[0].Cells["A1"];
                CacheProvider.Set(userGuideRangeCacheKey, userGuideRange, 7 * 24 * 60);
            }
            else
            {
                userGuideRange = CacheProvider.Get(userGuideRangeCacheKey) as IRange;
            }
            userGuideRange.Copy(userGuideWorksheet.Cells["B2"]);

            #endregion

            #region Levels Sheet

            var serviceTypeList = (await QueryExecutor.ExecuteManyAsync(new GetServiceTypeDataByLevelQuery { Level1Id = searchQuery.Level1Id, Level2Id = searchQuery.Level2Id, Level3Id = searchQuery.Level3Id, Level4Id = searchQuery.Level4Id })).ToList();

            SpreadsheetGear.IWorksheet serviceTypeWorksheet = workbook.Worksheets.Add();
            serviceTypeWorksheet.Name = "Service Types";
            serviceTypeWorksheet.MoveAfter(userGuideWorksheet);

            // Level 1
            serviceTypeWorksheet.Cells["A1"].ColumnWidth = 32;
            serviceTypeWorksheet.Cells["A1"].Value = T["Level 1"];
            serviceTypeWorksheet.Cells["A1"].RowHeight = 21;
            serviceTypeWorksheet.Cells["A1"].VerticalAlignment = SpreadsheetGear.VAlign.Center;
            serviceTypeWorksheet.Cells["A1"].Font.Bold = true;
            serviceTypeWorksheet.Cells["A1"].Font.Color = SpreadsheetGear.Drawing.Color.FromArgb(54, 163, 247);
            serviceTypeWorksheet.Cells["A1"].Interior.Color = SpreadsheetGear.Drawing.Color.FromArgb(234, 234, 234);

            // Level 2
            serviceTypeWorksheet.Cells["B1"].ColumnWidth = 32;
            serviceTypeWorksheet.Cells["B1"].Value = T["Level 2"];
            serviceTypeWorksheet.Cells["B1"].RowHeight = 21;
            serviceTypeWorksheet.Cells["B1"].VerticalAlignment = SpreadsheetGear.VAlign.Center;
            serviceTypeWorksheet.Cells["B1"].Font.Bold = true;
            serviceTypeWorksheet.Cells["B1"].Font.Color = SpreadsheetGear.Drawing.Color.FromArgb(54, 163, 247);
            serviceTypeWorksheet.Cells["B1"].Interior.Color = SpreadsheetGear.Drawing.Color.FromArgb(234, 234, 234);

            // Level 3
            serviceTypeWorksheet.Cells["C1"].ColumnWidth = 32;
            serviceTypeWorksheet.Cells["C1"].Value = T["Level 3"];
            serviceTypeWorksheet.Cells["C1"].RowHeight = 21;
            serviceTypeWorksheet.Cells["C1"].VerticalAlignment = SpreadsheetGear.VAlign.Center;
            serviceTypeWorksheet.Cells["C1"].Font.Bold = true;
            serviceTypeWorksheet.Cells["C1"].Font.Color = SpreadsheetGear.Drawing.Color.FromArgb(54, 163, 247);
            serviceTypeWorksheet.Cells["C1"].Interior.Color = SpreadsheetGear.Drawing.Color.FromArgb(234, 234, 234);

            // Level 4
            serviceTypeWorksheet.Cells["D1"].ColumnWidth = 32;
            serviceTypeWorksheet.Cells["D1"].Value = T["Level 4"];
            serviceTypeWorksheet.Cells["D1"].RowHeight = 21;
            serviceTypeWorksheet.Cells["D1"].VerticalAlignment = SpreadsheetGear.VAlign.Center;
            serviceTypeWorksheet.Cells["D1"].Font.Bold = true;
            serviceTypeWorksheet.Cells["D1"].Font.Color = SpreadsheetGear.Drawing.Color.FromArgb(54, 163, 247);
            serviceTypeWorksheet.Cells["D1"].Interior.Color = SpreadsheetGear.Drawing.Color.FromArgb(234, 234, 234);

            int rowIndex = 2;
            foreach (var serviceType in serviceTypeList)
            {
                serviceTypeWorksheet.Cells["A" + rowIndex.ToString()].Value = serviceType.Level1Name;
                serviceTypeWorksheet.Cells["B" + rowIndex.ToString()].Value = serviceType.Level2Name;
                serviceTypeWorksheet.Cells["C" + rowIndex.ToString()].Value = serviceType.Level3Name;
                serviceTypeWorksheet.Cells["D" + rowIndex.ToString()].Value = serviceType.Level4Name;
                rowIndex++;
            }

            #region Distinct Levels

            SpreadsheetGear.IWorksheet levelWorksheet = workbook.Worksheets.Add();
            levelWorksheet.Name = "Levels";

            // Level 1
            levelWorksheet.Cells["A1"].ColumnWidth = 32;
            levelWorksheet.Cells["A1"].Value = T["Level 1"];
            levelWorksheet.Cells["A1"].RowHeight = 21;
            levelWorksheet.Cells["A1"].VerticalAlignment = SpreadsheetGear.VAlign.Center;
            levelWorksheet.Cells["A1"].Font.Bold = true;
            levelWorksheet.Cells["A1"].Font.Color = SpreadsheetGear.Drawing.Color.FromArgb(54, 163, 247);
            levelWorksheet.Cells["A1"].Interior.Color = SpreadsheetGear.Drawing.Color.FromArgb(234, 234, 234);

            levelWorksheet.Cells["B1"].ColumnWidth = 5;

            // Level 2
            levelWorksheet.Cells["C1"].ColumnWidth = 32;
            levelWorksheet.Cells["C1"].Value = T["Level 2"];
            levelWorksheet.Cells["C1"].RowHeight = 21;
            levelWorksheet.Cells["C1"].VerticalAlignment = SpreadsheetGear.VAlign.Center;
            levelWorksheet.Cells["C1"].Font.Bold = true;
            levelWorksheet.Cells["C1"].Font.Color = SpreadsheetGear.Drawing.Color.FromArgb(54, 163, 247);
            levelWorksheet.Cells["C1"].Interior.Color = SpreadsheetGear.Drawing.Color.FromArgb(234, 234, 234);

            levelWorksheet.Cells["D1"].ColumnWidth = 5;

            // Level 3
            levelWorksheet.Cells["E1"].ColumnWidth = 32;
            levelWorksheet.Cells["E1"].Value = T["Level 3"];
            levelWorksheet.Cells["E1"].RowHeight = 21;
            levelWorksheet.Cells["E1"].VerticalAlignment = SpreadsheetGear.VAlign.Center;
            levelWorksheet.Cells["E1"].Font.Bold = true;
            levelWorksheet.Cells["E1"].Font.Color = SpreadsheetGear.Drawing.Color.FromArgb(54, 163, 247);
            levelWorksheet.Cells["E1"].Interior.Color = SpreadsheetGear.Drawing.Color.FromArgb(234, 234, 234);

            levelWorksheet.Cells["F1"].ColumnWidth = 5;

            // Level 4
            levelWorksheet.Cells["G1"].ColumnWidth = 32;
            levelWorksheet.Cells["G1"].Value = T["Level 4"];
            levelWorksheet.Cells["G1"].RowHeight = 21;
            levelWorksheet.Cells["G1"].VerticalAlignment = SpreadsheetGear.VAlign.Center;
            levelWorksheet.Cells["G1"].Font.Bold = true;
            levelWorksheet.Cells["G1"].Font.Color = SpreadsheetGear.Drawing.Color.FromArgb(54, 163, 247);
            levelWorksheet.Cells["G1"].Interior.Color = SpreadsheetGear.Drawing.Color.FromArgb(234, 234, 234);

            List<string> level1Ids = serviceTypeList.Select(st => st.Level1Name).Distinct().ToList();
            rowIndex = 2;
            foreach (var level1Id in level1Ids)
            {
                levelWorksheet.Cells["A" + rowIndex.ToString()].Value = level1Id;
                rowIndex++;
            }
            if (level1ColumnIndex > 0)
            {
                string level1ColumnName = GetExcelColumnName(level1ColumnIndex);
                requestTicketWorksheet.Cells[string.Format("{0}2:{0}6500", level1ColumnName)].Validation.Add(ValidationType.List, ValidationAlertStyle.Warning, ValidationOperator.Default, string.Format("=Levels!$A$2:$A${0}", rowIndex - 1), "");
            }

            List<string> level2Ids = serviceTypeList.Where(st => st.Level2Id.HasValue).Select(st => st.Level2Name).Distinct().ToList();
            rowIndex = 2;
            foreach (var level2Id in level2Ids)
            {
                levelWorksheet.Cells["C" + rowIndex.ToString()].Value = level2Id;
                rowIndex++;
            }
            if (level2ColumnIndex > 0)
            {
                string level2ColumnName = GetExcelColumnName(level2ColumnIndex);
                requestTicketWorksheet.Cells[string.Format("{0}2:{0}6500", level2ColumnName)].Validation.Add(ValidationType.List, ValidationAlertStyle.Warning, ValidationOperator.Default, string.Format("=Levels!$C$2:$C${0}", rowIndex - 1), "");
            }

            List<string> level3Ids = serviceTypeList.Where(st => st.Level3Id.HasValue).Select(st => st.Level3Name).Distinct().ToList();
            rowIndex = 2;
            foreach (var level3Id in level3Ids)
            {
                levelWorksheet.Cells["E" + rowIndex.ToString()].Value = level3Id;
                rowIndex++;
            }
            if (level3ColumnIndex > 0)
            {
                string level3ColumnName = GetExcelColumnName(level3ColumnIndex);
                requestTicketWorksheet.Cells[string.Format("{0}2:{0}6500", level3ColumnName)].Validation.Add(ValidationType.List, ValidationAlertStyle.Warning, ValidationOperator.Default, string.Format("=Levels!$E$2:$E${0}", rowIndex - 1), "");
            }

            List<string> level4Ids = serviceTypeList.Where(st => st.Level4Id.HasValue).Select(st => st.Level4Name).Distinct().ToList();
            rowIndex = 2;
            foreach (var level4Id in level4Ids)
            {
                levelWorksheet.Cells["G" + rowIndex.ToString()].Value = level4Id;
                rowIndex++;
            }
            if (level4ColumnIndex > 0)
            {
                string level4ColumnName = GetExcelColumnName(level4ColumnIndex);
                requestTicketWorksheet.Cells[string.Format("{0}2:{0}6500", level4ColumnName)].Validation.Add(ValidationType.List, ValidationAlertStyle.Warning, ValidationOperator.Default, string.Format("=Levels!$G$2:$G${0}", rowIndex - 1), "");
            }

            levelWorksheet.Visible = SheetVisibility.VeryHidden;

            #endregion

            #endregion

            #region References Sheet

            SpreadsheetGear.IWorksheet referenceWorksheet = workbook.Worksheets.Add();
            referenceWorksheet.Name = "References";

            // 1. RequestTicket Owner
            referenceWorksheet.Cells["A1"].ColumnWidth = 32;
            referenceWorksheet.Cells["A1"].Value = T["Danh sách người phụ trách"];
            referenceWorksheet.Cells["A1"].RowHeight = 21;
            referenceWorksheet.Cells["A1"].VerticalAlignment = SpreadsheetGear.VAlign.Center;
            referenceWorksheet.Cells["A1"].Font.Bold = true;
            referenceWorksheet.Cells["A1"].Font.Color = SpreadsheetGear.Drawing.Color.FromArgb(54, 163, 247);
            referenceWorksheet.Cells["A1"].Interior.Color = SpreadsheetGear.Drawing.Color.FromArgb(234, 234, 234);

            var ownerUserProfiles = (await QueryExecutor.ExecuteManyAsync(new GetOwnerListQuery() { IsApproved = true })).ToList();
            rowIndex = 2;
            foreach (var ownerUserProfile in ownerUserProfiles)
            {
                referenceWorksheet.Cells["A" + rowIndex.ToString()].Value = string.Format("({0})-{1}", ownerUserProfile.UserName, ownerUserProfile.FullName);
                rowIndex++;
            }

            referenceWorksheet.Cells["B1"].ColumnWidth = 5;

            // 2. Channel
            referenceWorksheet.Cells["C1"].ColumnWidth = 32;
            referenceWorksheet.Cells["C1"].Value = T["Danh sách Kênh tiếp nhận"];
            referenceWorksheet.Cells["C1"].RowHeight = 21;
            referenceWorksheet.Cells["C1"].VerticalAlignment = SpreadsheetGear.VAlign.Center;
            referenceWorksheet.Cells["C1"].Font.Bold = true;
            referenceWorksheet.Cells["C1"].Font.Color = SpreadsheetGear.Drawing.Color.FromArgb(54, 163, 247);
            referenceWorksheet.Cells["C1"].Interior.Color = SpreadsheetGear.Drawing.Color.FromArgb(234, 234, 234);

            var channelList = (await QueryExecutor.ExecuteManyAsync(new GetChannelListQuery() { IsDisabled = false })).ToList();
            rowIndex = 2;
            foreach (var channel in channelList)
            {
                referenceWorksheet.Cells["C" + rowIndex.ToString()].Value = channel.Name;
                rowIndex++;
            }

            if (channelColumnIndex > 0)
            {
                string channelColumnName = GetExcelColumnName(channelColumnIndex);
                referenceWorksheet.Cells["C1"].Value = requestTicketCells[string.Format("{0}1", channelColumnName)].Text;
                try
                {
                    requestTicketWorksheet.Cells[string.Format("{0}3:{0}6500", channelColumnName)].Validation.Delete();
                    requestTicketWorksheet.Cells[string.Format("{0}3:{0}6500", channelColumnName)].Validation.Add(ValidationType.List, ValidationAlertStyle.Warning, ValidationOperator.Default, string.Format("=References!$C$2:$C${0}", rowIndex - 1), "");
                }
                catch { }
            }

            referenceWorksheet.Cells["D1"].ColumnWidth = 5;

            // 3. Difficulty
            referenceWorksheet.Cells["E1"].ColumnWidth = 32;
            referenceWorksheet.Cells["E1"].Value = T["Danh sách Độ khẩn"];
            referenceWorksheet.Cells["E1"].RowHeight = 21;
            referenceWorksheet.Cells["E1"].VerticalAlignment = SpreadsheetGear.VAlign.Center;
            referenceWorksheet.Cells["E1"].Font.Bold = true;
            referenceWorksheet.Cells["E1"].Font.Color = SpreadsheetGear.Drawing.Color.FromArgb(54, 163, 247);
            referenceWorksheet.Cells["E1"].Interior.Color = SpreadsheetGear.Drawing.Color.FromArgb(234, 234, 234);

            var difficultyDegreeEnumValues = System.Enum.GetValues(typeof(Difficulty));
            rowIndex = 2;
            foreach (var difficultyDegree in difficultyDegreeEnumValues)
            {
                referenceWorksheet.Cells["E" + rowIndex.ToString()].Value = difficultyDegree.GetDescription();
                rowIndex++;
            }

            if (difficultyDegreeColumnIndex > 0)
            {
                string difficultyDegreeColumnName = GetExcelColumnName(difficultyDegreeColumnIndex);
                referenceWorksheet.Cells["E1"].Value = requestTicketCells[string.Format("{0}1", difficultyDegreeColumnName)].Text;
                try
                {
                    requestTicketWorksheet.Cells[string.Format("{0}3:{0}6500", difficultyDegreeColumnName)].Validation.Delete();
                    requestTicketWorksheet.Cells[string.Format("{0}3:{0}6500", difficultyDegreeColumnName)].Validation.Add(ValidationType.List, ValidationAlertStyle.Warning, ValidationOperator.Default, string.Format("=References!$E$2:$E${0}", rowIndex - 1), "");
                }
                catch { }
            }

            referenceWorksheet.Cells["F1"].ColumnWidth = 5;

            // 4. RequestTicketStatus
            referenceWorksheet.Cells["G1"].ColumnWidth = 32;
            referenceWorksheet.Cells["G1"].Value = T["Danh sách Trạng thái phiếu"];
            referenceWorksheet.Cells["G1"].RowHeight = 21;
            referenceWorksheet.Cells["G1"].VerticalAlignment = SpreadsheetGear.VAlign.Center;
            referenceWorksheet.Cells["G1"].Font.Bold = true;
            referenceWorksheet.Cells["G1"].Font.Color = SpreadsheetGear.Drawing.Color.FromArgb(54, 163, 247);
            referenceWorksheet.Cells["G1"].Interior.Color = SpreadsheetGear.Drawing.Color.FromArgb(234, 234, 234);

            var requestTicketStatusEnumValues = System.Enum.GetValues(typeof(RequestTicketStatus));
            rowIndex = 2;
            foreach (var requestTicketStatus in requestTicketStatusEnumValues)
            {
                referenceWorksheet.Cells["G" + rowIndex.ToString()].Value = requestTicketStatus.GetDescription();
                rowIndex++;
            }

            if (requestTicketStatusColumnIndex > 0)
            {
                string requestTicketStatusColumnName = GetExcelColumnName(requestTicketStatusColumnIndex);
                referenceWorksheet.Cells["G1"].Value = requestTicketCells[string.Format("{0}1", requestTicketStatusColumnName)].Text;
                try
                {
                    requestTicketWorksheet.Cells[string.Format("{0}3:{0}6500", requestTicketStatusColumnName)].Validation.Delete();
                    requestTicketWorksheet.Cells[string.Format("{0}3:{0}6500", requestTicketStatusColumnName)].Validation.Add(ValidationType.List, ValidationAlertStyle.Warning, ValidationOperator.Default, string.Format("=References!$G$2:$G${0}", rowIndex - 1), "");
                }
                catch { }
            }

            referenceWorksheet.Cells["H1"].ColumnWidth = 5;

            // 5. TicketBusinessResult
            referenceWorksheet.Cells["I1"].ColumnWidth = 32;
            referenceWorksheet.Cells["I1"].Value = T["Danh sách Kết quả phiếu"];
            referenceWorksheet.Cells["I1"].RowHeight = 21;
            referenceWorksheet.Cells["I1"].VerticalAlignment = SpreadsheetGear.VAlign.Center;
            referenceWorksheet.Cells["I1"].Font.Bold = true;
            referenceWorksheet.Cells["I1"].Font.Color = SpreadsheetGear.Drawing.Color.FromArgb(54, 163, 247);
            referenceWorksheet.Cells["I1"].Interior.Color = SpreadsheetGear.Drawing.Color.FromArgb(234, 234, 234);

            var businessResultList = (await QueryExecutor.ExecuteManyAsync(new GetBusinessResultListQuery { })).ToList();
            rowIndex = 2;
            foreach (var businessResult in businessResultList)
            {
                referenceWorksheet.Cells["I" + rowIndex.ToString()].Value = businessResult.Name;
                rowIndex++;
            }

            if (ticketBusinessResultColumnIndex > 0)
            {
                string ticketBusinessResultColumnName = GetExcelColumnName(ticketBusinessResultColumnIndex);
                referenceWorksheet.Cells["I1"].Value = requestTicketCells[string.Format("{0}1", ticketBusinessResultColumnName)].Text;
                try
                {
                    requestTicketWorksheet.Cells[string.Format("{0}3:{0}6500", ticketBusinessResultColumnName)].Validation.Delete();
                    requestTicketWorksheet.Cells[string.Format("{0}3:{0}6500", ticketBusinessResultColumnName)].Validation.Add(ValidationType.List, ValidationAlertStyle.Warning, ValidationOperator.Default, string.Format("=References!$I$2:$I${0}", rowIndex - 1), "");
                }
                catch { }
            }

            referenceWorksheet.Cells["J1"].ColumnWidth = 5;

            // 6. Danh sách phòng ban
            referenceWorksheet.Cells["K1"].ColumnWidth = 64;
            referenceWorksheet.Cells["K1"].Value = T["Danh sách phòng ban"];
            referenceWorksheet.Cells["K1"].RowHeight = 21;
            referenceWorksheet.Cells["K1"].VerticalAlignment = SpreadsheetGear.VAlign.Center;
            referenceWorksheet.Cells["K1"].Font.Bold = true;
            referenceWorksheet.Cells["K1"].Font.Color = SpreadsheetGear.Drawing.Color.FromArgb(54, 163, 247);
            referenceWorksheet.Cells["K1"].Interior.Color = SpreadsheetGear.Drawing.Color.FromArgb(234, 234, 234);

            Dictionary<int, SpreadsheetGear.Drawing.Color> orgTreeColors = new Dictionary<int, SpreadsheetGear.Drawing.Color>();
            orgTreeColors.Add(1, SpreadsheetGear.Drawing.Color.FromArgb(191, 191, 191));
            orgTreeColors.Add(2, SpreadsheetGear.Drawing.Color.FromArgb(217, 217, 217));
            orgTreeColors.Add(3, SpreadsheetGear.Drawing.Color.FromArgb(242, 242, 242));

            var allOrgList = (await OrganizationUtility.GetAllOrganizationListWithParentChildOrderListAsync()).ToList();
            rowIndex = 2;
            foreach (var org in allOrgList)
            {
                string spaces = string.Empty;
                for (int i = 1; i < org.NodeLevel; i++)
                {
                    spaces += "    ";
                }

                referenceWorksheet.Cells["K" + rowIndex.ToString()].Value = string.Format("{0}({1})-{2}", spaces, org.Code, org.Name);
                if (orgTreeColors.ContainsKey(org.NodeLevel))
                {
                    referenceWorksheet.Cells["K" + rowIndex.ToString()].Interior.Color = orgTreeColors[org.NodeLevel];
                }
                rowIndex++;
            }

            int ticketDynamicFieldIndex = 0;

            // Các DynamicField SelectBox
            List<string> duplicatedDynamicFields = new List<string>();
            int startExcelColumnIndex = 12;
            ticketDynamicFieldIndex = 0;
            foreach (DynamicFieldListItem dynamicColumn in distinctTicketDynamicFieldList)
            {
                if (dynamicColumn.ViewHint.IsEqualIgnoreCase("SelectField") && dynamicColumn.SelectOptions.IsNotNullOrEmpty())
                {
                    if (!duplicatedDynamicFields.Exists(t => t == dynamicColumn.DynamicFieldName))
                    {
                        string blankColumnName = GetExcelColumnName(startExcelColumnIndex);
                        referenceWorksheet.Cells[blankColumnName + "1"].ColumnWidth = 5;

                        string dynamicFieldColumnExcelName = GetExcelColumnName(startExcelColumnIndex + 1);

                        referenceWorksheet.Cells[dynamicFieldColumnExcelName + "1"].ColumnWidth = 32;
                        referenceWorksheet.Cells[dynamicFieldColumnExcelName + "1"].Value = dynamicColumn.DisplayName;
                        referenceWorksheet.Cells[dynamicFieldColumnExcelName + "1"].RowHeight = 21;
                        referenceWorksheet.Cells[dynamicFieldColumnExcelName + "1"].VerticalAlignment = SpreadsheetGear.VAlign.Center;
                        referenceWorksheet.Cells[dynamicFieldColumnExcelName + "1"].Font.Bold = true;
                        referenceWorksheet.Cells[dynamicFieldColumnExcelName + "1"].Font.Color = SpreadsheetGear.Drawing.Color.FromArgb(54, 163, 247);
                        referenceWorksheet.Cells[dynamicFieldColumnExcelName + "1"].Interior.Color = SpreadsheetGear.Drawing.Color.FromArgb(234, 234, 234);

                        List<string> selectOptions = new List<string>();
                        try
                        {
                            selectOptions = JsonConvert.DeserializeObject<List<string>>(dynamicColumn.SelectOptions);
                        }
                        catch (Exception) { }

                        rowIndex = 2;
                        foreach (var selectOption in selectOptions)
                        {
                            if (selectOption.IsNotNullOrEmpty())
                            {
                                referenceWorksheet.Cells[dynamicFieldColumnExcelName + rowIndex.ToString()].Value = selectOption;
                                rowIndex++;
                            }
                        }

                        string ticketSelectDynamicFieldColumnName = GetExcelColumnName(startTicketDynamicColumnIndex + ticketDynamicFieldIndex);
                        if (!requestTicketWorksheet.Cells[string.Format("{0}2:{0}6500", ticketSelectDynamicFieldColumnName)].ValidationDefined)
                        {
                            requestTicketWorksheet.Cells[string.Format("{0}2:{0}6500", ticketSelectDynamicFieldColumnName)].Validation.Add(ValidationType.List, ValidationAlertStyle.Warning, ValidationOperator.Default, string.Format("=References!${1}$2:${1}${0}", rowIndex - 1, dynamicFieldColumnExcelName), "");
                        }

                        duplicatedDynamicFields.Add(dynamicColumn.DynamicFieldName);
                        startExcelColumnIndex += 2;
                    }
                }
                ticketDynamicFieldIndex++;
            }

            #endregion

            #region ReferencesSearch Sheet

            SpreadsheetGear.IWorksheet referenceSearchWorksheet = workbook.Worksheets.Add();
            referenceSearchWorksheet.Name = "ReferencesSearch";
            referenceSearchWorksheet.Visible = SheetVisibility.Hidden;

            referenceSearchWorksheet.Cells["A1"].ColumnWidth = 5;

            // 1. RequestTicket Owner
            referenceSearchWorksheet.Cells["B1"].ColumnWidth = 42;
            referenceSearchWorksheet.Cells["B1"].Value = T["Danh sách người phụ trách"];
            referenceSearchWorksheet.Cells["B1"].RowHeight = 21;
            referenceSearchWorksheet.Cells["B1"].VerticalAlignment = SpreadsheetGear.VAlign.Center;
            referenceSearchWorksheet.Cells["B1"].Font.Bold = true;
            referenceSearchWorksheet.Cells["B1"].Font.Color = SpreadsheetGear.Drawing.Color.FromArgb(54, 163, 247);
            referenceSearchWorksheet.Cells["B1"].Interior.Color = SpreadsheetGear.Drawing.Color.FromArgb(234, 234, 234);

            rowIndex = 2;
            foreach (var ownerUserProfile in ownerUserProfiles)
            {
                referenceSearchWorksheet.Cells["B" + rowIndex.ToString()].Value = string.Format("({0})-{1}", ownerUserProfile.UserName, ownerUserProfile.FullName);
                referenceSearchWorksheet.Cells[String.Format("A{0}", rowIndex)].Formula = string.Format("=IF(ISNUMBER(SEARCH(INDIRECT(CELL(\"address\")),B{0})),MAX($A$1:A{1})+1,0)", rowIndex, rowIndex - 1);
                referenceSearchWorksheet.Cells[String.Format("C{0}", rowIndex)].Formula = string.Format("=IFERROR(VLOOKUP(ROWS($C$1:C{0}),$A$2:$B${1},2,FALSE),\"\")", rowIndex - 1, ownerUserProfiles.Count);
                rowIndex++;
            }

            if (ownerIdColumnIndex > 0)
            {
                string ticketOwnerColumnName = GetExcelColumnName(ownerIdColumnIndex);
                referenceSearchWorksheet.Cells["B1"].Value = requestTicketCells[string.Format("{0}1", ticketOwnerColumnName)].Text;
                requestTicketWorksheet.Cells[string.Format("{0}2:{0}5000", ticketOwnerColumnName)].Validation.Add(ValidationType.List, ValidationAlertStyle.Warning, ValidationOperator.Default, "=OFFSET(ReferencesSearch!$C$2,0,0,COUNTIF(ReferencesSearch!$C:$C,\"?*\"),1)", "");
                requestTicketWorksheet.Cells[string.Format("{0}2:{0}5000", ticketOwnerColumnName)].Validation.ShowError = false;
            }

            referenceSearchWorksheet.Cells["C1"].ColumnWidth = 10;
            referenceSearchWorksheet.Cells["D1"].ColumnWidth = 5;

            // 2. Danh sách phòng ban
            referenceSearchWorksheet.Cells["E1"].ColumnWidth = 64;
            referenceSearchWorksheet.Cells["E1"].Value = T["Danh sách phòng ban"];
            referenceSearchWorksheet.Cells["E1"].RowHeight = 21;
            referenceSearchWorksheet.Cells["E1"].VerticalAlignment = SpreadsheetGear.VAlign.Center;
            referenceSearchWorksheet.Cells["E1"].Font.Bold = true;
            referenceSearchWorksheet.Cells["E1"].Font.Color = SpreadsheetGear.Drawing.Color.FromArgb(54, 163, 247);
            referenceSearchWorksheet.Cells["E1"].Interior.Color = SpreadsheetGear.Drawing.Color.FromArgb(234, 234, 234);

            rowIndex = 2;
            foreach (var org in allOrgList)
            {
                string spaces = string.Empty;
                for (int i = 1; i < org.NodeLevel; i++)
                {
                    spaces += "    ";
                }

                referenceSearchWorksheet.Cells["E" + rowIndex.ToString()].Value = string.Format("{0}({1})-{2}", spaces, org.Code, org.Name);
                if (orgTreeColors.ContainsKey(org.NodeLevel))
                {
                    referenceSearchWorksheet.Cells["E" + rowIndex.ToString()].Interior.Color = orgTreeColors[org.NodeLevel];
                }

                referenceSearchWorksheet.Cells[String.Format("D{0}", rowIndex)].Formula = string.Format("=IF(ISNUMBER(SEARCH(INDIRECT(CELL(\"address\")),E{0})),MAX($D$1:D{1})+1,0)", rowIndex, rowIndex - 1);
                referenceSearchWorksheet.Cells[String.Format("F{0}", rowIndex)].Formula = string.Format("=IFERROR(VLOOKUP(ROWS($F$1:F{0}),$D$2:$E${1},2,FALSE),\"\")", rowIndex - 1, allOrgList.Count);

                rowIndex++;
            }

            var dynamicOrgDataType = typeof(Webaby.Core.Organization.Queries.OrganizationData);
            ticketDynamicFieldIndex = 0;
            foreach (DynamicFieldListItem dynamicColumn in distinctTicketDynamicFieldList)
            {
                Type dynamicType = dynamicColumn.DataType.ToType();
                if (dynamicType == dynamicOrgDataType)
                {
                    string ticketOrgDynamicFieldColumnName = GetExcelColumnName(startTicketDynamicColumnIndex + ticketDynamicFieldIndex);

                    requestTicketWorksheet.Cells[string.Format("{0}2:{0}5000", ticketOrgDynamicFieldColumnName)].Validation.Add(ValidationType.List, ValidationAlertStyle.Warning, ValidationOperator.Default, "=OFFSET(ReferencesSearch!$F$2,0,0,COUNTIF(ReferencesSearch!$F:$F,\"?*\"),1)", "");
                    requestTicketWorksheet.Cells[string.Format("{0}2:{0}5000", ticketOrgDynamicFieldColumnName)].Validation.ShowError = false;
                }
                ticketDynamicFieldIndex++;
            }

            #endregion

            byte[] bytes = null;
            using (var ms = new MemoryStream())
            {
                workbook.Worksheets[0].Select();
                workbook.SaveToStream(ms, SpreadsheetGear.FileFormat.OpenXMLWorkbook);
                bytes = ms.ToArray();
            }

            return bytes;
        }

        public async Task<bool> CheckEditPermissionAsync(Guid userId, Guid requestTicketId)
        {
            DbCommand cmd = EntitySet.CreateDbCommand();
            cmd.Parameters.AddRange(new[]
            {
                DbParameterHelper.AddNullableGuid(cmd, "@UserId", userId),
                DbParameterHelper.AddNullableGuid(cmd, "@RequestTicketId", requestTicketId)
            });
            cmd.CommandText = "dbo.CheckEditRequestTicketPermission";
            cmd.CommandType = System.Data.CommandType.StoredProcedure;
            var requestTicketIds = (await EntitySet.ExecuteReadCommandAsync<Guid>(cmd)).ToList();

            if (requestTicketIds.Count > 0)
            {
                return true;
            }
            return false;
        }

        public async Task<int> CountCustomerRequestTicketsAsync(Guid serviceTypeId, Guid customerId, bool includeDeleted)
        {
            var customerRequestTicketList = await QueryExecutor.ExecuteManyAsync(new GetRequestTicketByCustomerAndServiceTypeQuery { ServiceTypeId = serviceTypeId, CustomerId = customerId, IncludeDeleted = includeDeleted });
            return customerRequestTicketList.Count();
        }

        private string GetExcelColumnName(int columnNumber)
        {
            int dividend = columnNumber;
            string columnName = String.Empty;
            int modulo;

            while (dividend > 0)
            {
                modulo = (dividend - 1) % 26;
                columnName = Convert.ToChar(65 + modulo).ToString() + columnName;
                dividend = (int)((dividend - modulo) / 26);
            }

            return columnName;
        }

        public async Task<DataSet> SearchRequestTicketResultHasDisplayAsync(SearchRequestTicketArguments arguments, bool isDisplay)
        {
            SearchRequestTicketNABQuery searchQuery = Mapper.Map<SearchRequestTicketNABQuery>(arguments);

            string selectedColumns = string.Empty;

            List<TicketTaskExportImportColumnMapping> allColumnMappingList = new List<TicketTaskExportImportColumnMapping>();
            string columnMappingListJson = System.IO.File.ReadAllText(Path.Combine(_env.ContentRootPath, "Views/RequestTicket/ExportDefs/ExportSearchRequestTicketResult-ImportColumnMapping-FWD.txt"));
            allColumnMappingList = JsonConvert.DeserializeObject<List<TicketTaskExportImportColumnMapping>>(columnMappingListJson).OrderBy(x => x.ColumnOrder).ToList();

            int ownerIdColumnIndex = 0, channelColumnIndex = 0, difficultyDegreeColumnIndex = 0, requestTicketStatusColumnIndex = 0, ticketBusinessResultColumnIndex = 0;
            int level1ColumnIndex = 0, level2ColumnIndex = 0, level3ColumnIndex = 0, level4ColumnIndex = 0;

            #region Các Properties + DynamicFields của Phiếu

            int tempColumnIndex = 1;
            List<TicketTaskExportImportColumnMapping> ticketColumnMappingList = allColumnMappingList.Where(col => col.IsTicketColumn == true).OrderBy(col => col.ColumnOrder).ToList();
            foreach (var columnMapping in ticketColumnMappingList)
            {
                if (!selectedColumns.IsNullOrEmpty())
                {
                    selectedColumns += ", ";
                }
                selectedColumns += string.Format("{0} [{1}]", columnMapping.ColumnName, columnMapping.ColumnAlias);

                if (columnMapping.ColumnName.IsEqualIgnoreCase("OwnerUser"))
                {
                    ownerIdColumnIndex = tempColumnIndex;
                }
                else if (columnMapping.ColumnName.IsEqualIgnoreCase("Channel"))
                {
                    channelColumnIndex = tempColumnIndex;
                }
                else if (columnMapping.ColumnName.IsEqualIgnoreCase("DifficultyDegree"))
                {
                    difficultyDegreeColumnIndex = tempColumnIndex;
                }
                else if (columnMapping.ColumnName.IsEqualIgnoreCase("RequestTicketStatus"))
                {
                    requestTicketStatusColumnIndex = tempColumnIndex;
                }
                else if (columnMapping.ColumnName.IsEqualIgnoreCase("TicketBusinessResult"))
                {
                    ticketBusinessResultColumnIndex = tempColumnIndex;
                }
                else if (columnMapping.ColumnName.IsEqualIgnoreCase("Level1"))
                {
                    level1ColumnIndex = tempColumnIndex;
                }
                else if (columnMapping.ColumnName.IsEqualIgnoreCase("Level2"))
                {
                    level2ColumnIndex = tempColumnIndex;
                }
                else if (columnMapping.ColumnName.IsEqualIgnoreCase("Level3"))
                {
                    level3ColumnIndex = tempColumnIndex;
                }
                else if (columnMapping.ColumnName.IsEqualIgnoreCase("Level4"))
                {
                    level4ColumnIndex = tempColumnIndex;
                }
                tempColumnIndex++;
            }

            string exeptionJoins = string.Empty;

            string ticketDynamicFieldSelect = string.Empty;
            List<string> distinctTicketDynamicFieldList = new List<string>();
            List<DynamicFieldListItem> ticketDynamicFieldList = new List<DynamicFieldListItem>();

            // Lấy tất cả Dynamic Field trong tất cả Service Type được Search
            if (searchQuery.Level1Id.IsNotNullOrEmpty())
            {
                ticketDynamicFieldList = (await QueryExecutor.ExecuteManyAsync(new GetDynamicFieldListByServiceTypesQuery
                {
                    Level1Id = searchQuery.Level1Id,
                    Level2Id = searchQuery.Level2Id,
                    Level3Id = searchQuery.Level3Id,
                    Level4Id = searchQuery.Level4Id,
                    ServiceTypeId = searchQuery.ServiceTypeId,
                    Display = isDisplay
                })).ToList();

                var fileDataType = typeof(Webaby.Core.File.Queries.FileData);
                var orgDataType = typeof(Webaby.Core.Organization.Queries.OrganizationData);

                foreach (var dynamicColumn in ticketDynamicFieldList)
                {
                    string sqlDynamicFieldName = string.Format("[{0}]", dynamicColumn.DynamicFieldName);
                    if (!distinctTicketDynamicFieldList.Exists(dfd => dfd.IsEqualIgnoreCase(sqlDynamicFieldName)))
                    {
                        Type dynamicType = dynamicColumn.DataType.ToType();
                        bool isFileData = dynamicType == fileDataType;
                        bool isMultiFileData = false;
                        if (!isFileData)
                        {
                            if (dynamicType.IsGenericType)
                            {
                                isMultiFileData = dynamicType.GetGenericArguments().Single() == fileDataType;
                            }
                        }

                        if (!dynamicType.IsUserDefinedTableGridData())
                        {
                            if (!selectedColumns.IsNullOrEmpty())
                            {
                                selectedColumns += ", ";
                            }
                        }

                        if (dynamicType == orgDataType)
                        {
                            selectedColumns += string.Format("'(' + [{0}TicketOrg].Code + ')-' + [{0}TicketOrg].Name [{1}]", dynamicColumn.DynamicFieldName, dynamicColumn.DisplayName);
                            exeptionJoins += string.Format(" LEFT JOIN dbo.Organization [{0}TicketOrg] WITH(NOLOCK) ON [{0}TicketOrg].Id = dbo.ConvertToGuid(mainResult.{0}, '00000000-0000-0000-0000-000000000000') ", dynamicColumn.DynamicFieldName);
                        }
                        else if (isFileData)
                        {
                            selectedColumns += string.Format("[{0}TicketFile].FileName [{1}]", dynamicColumn.DynamicFieldName, dynamicColumn.DisplayName);
                            exeptionJoins += string.Format(" LEFT JOIN dbo.[File] [{0}TicketFile] WITH(NOLOCK) ON [{0}TicketFile].Id = dbo.ConvertToGuid(mainResult.{0}, '00000000-0000-0000-0000-000000000000') ", dynamicColumn.DynamicFieldName);
                        }
                        else if (isMultiFileData)
                        {
                            selectedColumns += string.Format("[dbo].[GetDynamicMultiFileNames](mainResult.[{0}]) [{1}] ", dynamicColumn.DynamicFieldName, dynamicColumn.DisplayName);
                        }
                        else if (!dynamicType.IsUserDefinedTableGridData())
                        {
                            selectedColumns += string.Format("[{0}] [{1}] ", dynamicColumn.DynamicFieldName, dynamicColumn.DisplayName);
                        }

                        if (!dynamicType.IsUserDefinedTableGridData())
                        {
                            distinctTicketDynamicFieldList.Add(sqlDynamicFieldName);
                        }
                    }
                }
                ticketDynamicFieldSelect = string.Join(", ", distinctTicketDynamicFieldList);
            }

            #endregion

            var exportSearchRequestTicketResults = RequestTicketServices.ExportSearchRequestTicketDynamicResult(searchQuery, selectedColumns, ticketDynamicFieldSelect, exeptionJoins, false, string.Empty);
            return exportSearchRequestTicketResults;
        }

        public async Task BatchCreateRequestTicketListAsync(Guid serviceTypeId, Guid ownerId, Guid currentUserId, List<Guid> customerIdList, bool trackAuditChange, bool triggerNoticase, bool triggerPhase)
        {
            var serviceType = await QueryExecutor.ExecuteOneAsync(new GetServiceTypeByIdQuery(serviceTypeId));
            if (serviceType != null)
            {
                // Accept DueTime
                DateTime? acceptDueDate = null;
                if (serviceType.AcceptDueTimeId.IsNotNullOrEmpty())
                {
                    acceptDueDate = await DueTimeUtility.GetDueTimeAsync(serviceType.AcceptDueTimeId.Value, DateTime.Now);
                }

                // Process DueTime
                DateTime? processDueDate = null, processSoonDueDate = null;
                if (serviceType.ProcessDueTimeId.IsNotNullOrEmpty())
                {
                    processSoonDueDate = await DueTimeUtility.GetProcessSoonDueTimeAsync(serviceType.ProcessDueTimeId.Value, DateTime.Now);
                    processDueDate = await DueTimeUtility.GetDueTimeAsync(serviceType.ProcessDueTimeId.Value, DateTime.Now);
                }

                string requestTicketCodeFormat = RequestTicketCodeFormat;
                if (serviceType.TicketMode == TicketCodeMode.TemplateCode)
                {
                    if (serviceType.TemplateTicketCode.IsNotNullOrEmpty())
                    {
                        requestTicketCodeFormat = serviceType.TemplateTicketCode;
                    }
                }

                List<DynamicFieldValueInfo> dynamicFieldInfoList = new List<DynamicFieldValueInfo>();
                if (serviceType.DynamicFormId.IsNotNullOrEmpty())
                {
                    dynamicFieldInfoList = (await QueryExecutor.ExecuteManyAsync(new GetDynamicFieldValueInfoByFormIdQuery { DynamicFormId = serviceType.DynamicFormId.Value })).ToList();

                    // Default DynamicFormValue
                    NameValueCollection formValues = new NameValueCollection();
                    if (dynamicFieldInfoList.Count > 0)
                    {
                        foreach (var field in dynamicFieldInfoList)
                        {
                            if (field.DynamicDefinedTableSchemaId.IsNotNullOrEmpty())
                            {
                                formValues.Add(string.Format("{0}.DynamicDefinedTableSchemaId", field.Name), field.DynamicDefinedTableSchemaId.Value.ToString());
                            }
                        }
                    }

                    Dictionary<string, List<string>> validateErrors;
                    if (!dynamicFieldInfoList.TryMapping(formValues, null, "", ServiceProvider, out validateErrors))
                    {
                        string errorMessages = string.Join("<br/>", validateErrors.Values.SelectMany(x => x).Select(x => x));
                    }

                    foreach (var dynamicFieldValue in dynamicFieldInfoList)
                    {
                        var dataType = dynamicFieldValue.DataType.ToType();
                        var isCollection = dataType.IsCollection();
                        var baseType = dataType.BaseType();
                        if (baseType.IsEntityData())
                        {
                            if (dataType.IsGridData() || dataType.IsUserDefinedTableGridData())
                            {
                                if (dynamicFieldValue.Id.IsNullOrEmpty())
                                {
                                    dynamicFieldValue.Id = Guid.NewGuid();
                                }
                                dynamicFieldValue.Value = dynamicFieldValue.Id.Value.ToString();
                            }
                        }
                    }
                }

                await CommandExecutor.ExecuteAsync(new BatchCreateRequestTicketListCommand 
                {
                    ServiceTypeId = serviceType.Id,
                    OwnerId = ownerId,
                    ExecuteUserId = currentUserId,
                    Status = RequestTicketStatus.Forward,
                    DifficultyDegree = Difficulty.Normal,
                    ProcessDueDate = processDueDate,
                    ProcessSoonDueDate = processSoonDueDate,
                    AcceptDueDate = acceptDueDate,
                    TemplateTicketCode = requestTicketCodeFormat,
                    CodePrefix = String.Empty,
                    CustomerIdList = customerIdList,
                    DynamicFieldValues = dynamicFieldInfoList.Where(dfd => dfd.FieldType != FieldType.Static).ToList(),
                    TrackAuditChange = trackAuditChange,
                    TriggerNoticase = triggerNoticase,
                    TriggerPhase = triggerPhase
                });
            }
        }
    }

    public class TicketTaskExportImportColumnMapping
    {
        public int ColumnOrder { get; set; }

        public string ColumnName { get; set; }

        public string ColumnAlias { get; set; }

        public string ImportColumnMapping { get; set; }

        public bool IsTicketColumn { get; set; }
    }
}