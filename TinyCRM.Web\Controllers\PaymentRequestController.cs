﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using Microsoft.EntityFrameworkCore;
using AutoMapper;
using Webaby.Security.Authorize;
using Newtonsoft.Json;
using System.Data;
using TinyCRM.AppServices.RequestTicket;
using TinyCRM.DynamicForm.Command;
using TinyCRM.Endorsement.Queries;
using TinyCRM.InfoList.Queries;
using TinyCRM.PaymentRequest.Commands;
using TinyCRM.PaymentRequest.Queries;
using TinyCRM.RequestTicket.Queries;
using TinyCRM.Web.Models.PaymentRequest;
using Webaby.Excel;
using Webaby.Web;
using Webaby.Web.Attributes;
using Webaby.Core.File.Commands;

namespace TinyCRM.Web.Controllers
{
    [AllowAuthenticated]
    public class PaymentRequestController : WebabyControllerBase
    {
        private readonly IRequestTicketAppService _requestTicketAppService;
        private readonly IWebHostEnvironment _env;

        public PaymentRequestController(IServiceProvider serviceProvider, IRequestTicketAppService requestTicketAppService) : base(serviceProvider)
        {
            _env = serviceProvider.GetRequiredService<IWebHostEnvironment>();
            _requestTicketAppService = requestTicketAppService;
        }

        [GridDynamicFieldValidationAttribute]
        public async Task<IActionResult> AddExpenseItemsToPaymentRequest(AddExpenseItemsToPaymentRequestModel model)
        {
            if (ModelState.IsValid)
            {
                var requestTicket = await QueryExecutor.ExecuteOneAsync(new GetRequestTicketByDynamicFormValueIdQuery { DynamicFormValueId = model.DynamicFormValueId });

                var checkUpdate =await _requestTicketAppService.CheckUpdateDefinedTableAsync(requestTicket.ServiceTypeId, requestTicket.Id);
                if (!checkUpdate)
                {
                    Error(T["Không thể cập nhật nội dung phiếu đang trình duyệt. Phiếu chỉ có thể cập nhật bởi người phụ trách ở các bước soạn thảo."]);
                    return DefaultResult();
                }

                await CommandExecutor.ExecuteAsync(new AddExpenseItemsToPaymentRequestCommand
                {
                    ContestIdList = JsonConvert.DeserializeObject<List<AddExpenseItemsToPaymentRequestCommand.SelectedEndorseContest>>(model.SelectedEndorseContestString),
                    PaymentType = model.PaymentType,
                    PaymentId = model.PaymentId,
                    RequestTicketId = requestTicket.Id
                });
                await CommandExecutor.ExecuteAsync(new ReCalculateDynamicFormValueCommand { DynamicFormValueId = model.DynamicFormValueId });
                Info(T["Đã thêm thành công."]);
            }
            return DefaultResult();
        }

        [GridDynamicFieldValidationAttribute]
        public async Task<IActionResult> RemoveExpenseItem(Guid paymentId, Guid dynamicFormValueId, Guid ticketId)
        {
            var requestTicket = await QueryExecutor.ExecuteOneAsync(new GetRequestTicketByIdQuery(ticketId));
            var checkUpdate = await _requestTicketAppService.CheckUpdateDefinedTableAsync(requestTicket.ServiceTypeId, requestTicket.Id);
            if (!checkUpdate)
            {
                Error(T["Không thể cập nhật nội dung phiếu đang trình duyệt. Phiếu chỉ có thể cập nhật bởi người phụ trách ở các bước soạn thảo."]);
                return DefaultResult();
            }

            if (ModelState.IsValid)
            {
                await CommandExecutor.ExecuteAsync(new RemoveExpenseItemsFromPaymentRequestCommand
                {
                    PaymentId = paymentId,
                    RequestTicketId = ticketId
                });
                await CommandExecutor.ExecuteAsync(new ReCalculateDynamicFormValueCommand { DynamicFormValueId = dynamicFormValueId });

                Info(T["Đã xoá thành công."]);
            }
            return DefaultResult();
        }

        [HttpGet]
        public IActionResult LoadSearchEndorsement()
        {
            EndorsementSearchModel model = new EndorsementSearchModel();
            model.DateRangeCreated = new DateRange();
            model.PageSize = 20;
            return View("Partials/SearchEndorsement", model);
        }

        [HttpPost]
        public async Task<IActionResult> Search(EndorsementSearchModel model)
        {
            if (model.DateRangeCreated == null)
            {
                model.DateRangeCreated = new DateRange();
            }
            if (ModelState.IsValid)
            {
                var data = (await QueryExecutor.ExecuteManyAsync(new SearchEndorsementForPaymentQuery
                {
                    EndorsementCode = model.EndorsementCode,
                    EndorsementName = model.EndorsementName,
                    PaymentTypeId = model.PaymentTypeId,
                    FromDate = model.DateRangeCreated.DateFrom,
                    ToDate = model.DateRangeCreated.DateTo,
                    PageIndex = model.PageIndex,
                    PageSize = model.PageSize
                })).ToList();

                return View("Partials/EndorsementList", new EndorsementSearchResultModel
                {
                    Total = data.Count > 0 ? data[0].TotalCount : 0,
                    Items = data,
                    PageIndex = model.PageIndex,
                    PageSize = model.PageSize
                });
            }
            return DefaultResult();
        }

        public async Task<IActionResult> GetEndorsementSummaryGroupByContest(string ticketId, string paymentType)
        {
            if (ModelState.IsValid)
            {
                var data = (await QueryExecutor.ExecuteManyAsync(new GetEndorsementSummaryGroupByContestForPaymentQuery { RequesTicketId = new Guid(ticketId), PaymentTypeId = new Guid(paymentType) })).ToList();

                return View("Partials/ContestOfEndorsementList", data);
            }
            return DefaultResult();
        }

        public async Task<IActionResult> LoadPaymentItemDetailByContest(Guid paymentId, Guid contestId)
        {
            if (ModelState.IsValid)
            {
                var data = (await QueryExecutor.ExecuteManyAsync(new GetPaymentRequestItemListByValueGroupIdQuery { ValueGroupId = paymentId, ContestId = contestId })).ToList();

                return View("Partials/PaymentItemList", data);
            }
            return DefaultResult();
        }

        [AllowAuthenticated]
        public async Task<IActionResult> GetPaymentTypeDropdownList()
        {
            var resultList = await QueryExecutor.ExecuteManyAsync(new GetInfoListByTypeQuery { TypeName = "fwd_paymenttype" });
            return Json(resultList.Select(x => new { Value = x.Id, Text = x.Name }));
        }

        [AllowAuthenticated]
        [GridDynamicFieldValidationAttribute]
        [HttpPost]
        public async Task<IActionResult> UploadExpenseItem(ImportPaymentRequestModel model)
        {
            if (ModelState.IsValid)
            {
                var requestTicket = await QueryExecutor.ExecuteOneAsync(new GetRequestTicketByDynamicFormValueIdQuery { DynamicFormValueId = model.DynamicFormValueId });
                var checkUpdate =await _requestTicketAppService.CheckUpdateDefinedTableAsync(requestTicket.ServiceTypeId, requestTicket.Id);
                if (!checkUpdate)
                {
                    Error(T["Không thể cập nhật nội dung phiếu đang trình duyệt. Phiếu chỉ có thể cập nhật bởi người phụ trách ở các bước soạn thảo."]);
                    return DefaultResult();
                }

                var memStream = new MemoryStream();

                await model.FileUpload.CopyToAsync(memStream);

                var reader = new Webaby.Excel.ExcelReader(memStream, model.EndorsementCode, false);
                if (reader.SheetNameExists())
                {
                    var rawExcelData = reader.GetDataTable();
                    if (rawExcelData.Rows.Count > 0)
                    {
                        string fieldName = rawExcelData.Columns[rawExcelData.Columns.Count - 1].ColumnName.Trim();
                        var fileId = Guid.NewGuid();
                        var importSessionId = Guid.NewGuid();
                        await CommandExecutor.ExecuteAsync(new CreateEditFileCommand
                        {
                            Id = fileId,
                            Data = memStream.ToArray(),
                            Descriptions = "ImportAdjustment",
                            ReferenceObjectId = importSessionId,
                            ReferenceObjectType = "fwd.ExpenseAdjustmentImportSession",
                            FileName = model.FileUpload.FileName
                        });
                        var createImportSessionCommand = new CreatePaymentRequestImportSessionCommand
                        {
                            Id = importSessionId,
                            FileName = model.FileUpload.FileName,
                            FileId = fileId,
                            PaymentId = model.PaymentId
                        };
                        await CommandExecutor.ExecuteAsync(createImportSessionCommand);

                        var bulkInsertPaymentRequestRawCommand = new BulkInsertImportRawPaymentRequestCommand
                        {
                            ImportSessionId = createImportSessionCommand.Id,
                            RawData = rawExcelData
                        };
                        await CommandExecutor.ExecuteAsync(bulkInsertPaymentRequestRawCommand);

                        await CommandExecutor.ExecuteAsync(new ScanPaymentRequestImportCommand { ImportSessionId = createImportSessionCommand.Id, EndorsementCode = model.EndorsementCode, PaymentType = model.PaymentType });

                        ImportPaymentRequestResultModel importEndorsementResultModel = new ImportPaymentRequestResultModel();
                        importEndorsementResultModel.PaymentId = model.PaymentId;
                        importEndorsementResultModel.PaymentRequestImportSessionId = createImportSessionCommand.Id;
                        importEndorsementResultModel.PaymentRequestDynamicFieldName = model.PaymentRequestDynamicFieldName;
                        importEndorsementResultModel.RequestTicketId = requestTicket.Id;
                        importEndorsementResultModel.Results = (await QueryExecutor.ExecuteManyAsync(new GetScanPaymentRequestImportResultQuery { ImportSessionId = createImportSessionCommand.Id })).OrderBy(ei => ei.ErrorCode).ToList();

                        return View("Partials/ScanPaymentRequestImportResult", importEndorsementResultModel);
                    }
                    else
                    {
                        Error(T["Excel không có dữ liệu."]);
                        return DefaultResult();
                    }
                }
                else
                {
                    Error(T[String.Format("Các mục trong file import không thuộc Endorsement {0} đã chọn", model.EndorsementCode)]);
                    return DefaultResult();
                }
            }
            return DefaultResult();
        }

        [AllowAuthenticated]
        [HttpPost]
        public async Task<IActionResult> ProccessUploadPaymentRequest(ImportPaymentRequestResultModel model)
        {
            await CommandExecutor.ExecuteAsync(new ProcessPaymentRequestImportCommand { ImportSessionId = model.PaymentRequestImportSessionId, PaymentId = model.PaymentId, RequestTicketId = model.RequestTicketId });

            Info(T["Đã import Expense Item list thành công."]);
            return DefaultResult();
        }

        [AllowAuthenticated]
        [HttpPost]
        public async Task<IActionResult> DownloadImportPaymentRequestTemplate(string endorsementCode, Guid paymentType)
        {
            var result = (await QueryExecutor.ExecuteManyAsync(new GetEndorsementItemsForPaymentQuery { EndorsementCode = endorsementCode, PaymentType = paymentType })).ToList();
            DataTable dt = new DataTable();
            dt.Columns.Add(new DataColumn("ContestName", typeof(string)));
            dt.Columns.Add(new DataColumn("ContestCode", typeof(string)));
            dt.Columns.Add(new DataColumn("AgencyCode", typeof(string)));
            dt.Columns.Add(new DataColumn("AgencyName", typeof(string)));
            dt.Columns.Add(new DataColumn("FieldName", typeof(string)));
            dt.Columns.Add(new DataColumn("PaymentType", typeof(string)));
            dt = result.Select(x => dt.LoadDataRow(new object[] { x.ContestName, x.ContestCode, x.AgencyCode, x.AgencyName, x.FieldName, x.PaymentTypeName }, false)).CopyToDataTable();

            String file = Path.Combine(_env.ContentRootPath,"~/Templates/ImportPaymentRequestTemplate.xlsx");
            try
            {
                ExcelWriter excelWriter = new ExcelWriter(dt);
                return File(excelWriter.ToExcel(endorsementCode), "application/excel", "ImportPaymentRequestTemplate.xlsx");
            }
            catch (Exception ex)
            {
                Error(ex.Message);
            }

            return DefaultResult();
        }
    }
}