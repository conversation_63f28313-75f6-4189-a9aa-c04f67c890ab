﻿<Project Sdk="Microsoft.NET.Sdk">
	<PropertyGroup Label="Globals">
		<SccProjectName>SAK</SccProjectName>
		<SccProvider>SAK</SccProvider>
		<SccAuxPath>SAK</SccAuxPath>
		<SccLocalPath>SAK</SccLocalPath>
	</PropertyGroup>
	<PropertyGroup>
		<TargetFramework>net9.0</TargetFramework>
		<ImplicitUsings>enable</ImplicitUsings>
		<Nullable>disable</Nullable>
	</PropertyGroup>
	<ItemGroup>
		<Compile Remove="**\*.cs" />
		<Compile Remove="Appeal\**" />
		<Compile Remove="DetailPayment\**" />
		<Compile Remove="EastSpring\**" />
		<Compile Remove="Endorsement\**" />
		<Compile Remove="FlightRoute\**" />
		<Compile Remove="GanttProject\Command\**" />
		<Compile Remove="GanttProject\Queries\**" />
		<Compile Remove="NotificationCase\NotificationCaseServices\**" />
		<Compile Remove="obj\**" />
		<Compile Remove="Organization\**" />
		<Compile Remove="Outbound\Campaign\Commands\**" />
		<Compile Remove="Outbound\Campaign\Events\**" />
		<Compile Remove="Outbound\Campaign\Tasks\**" />
		<Compile Remove="Sms\Events\**" />
		<Compile Remove="TripRoute\**" />
	</ItemGroup>
	<ItemGroup>
		<EmbeddedResource Remove="Appeal\**" />
		<EmbeddedResource Remove="DetailPayment\**" />
		<EmbeddedResource Remove="EastSpring\**" />
		<EmbeddedResource Remove="Endorsement\**" />
		<EmbeddedResource Remove="FlightRoute\**" />
		<EmbeddedResource Remove="GanttProject\Command\**" />
		<EmbeddedResource Remove="GanttProject\Queries\**" />
		<EmbeddedResource Remove="NotificationCase\NotificationCaseServices\**" />
		<EmbeddedResource Remove="obj\**" />
		<EmbeddedResource Remove="Organization\**" />
		<EmbeddedResource Remove="Outbound\Campaign\Commands\**" />
		<EmbeddedResource Remove="Outbound\Campaign\Events\**" />
		<EmbeddedResource Remove="Outbound\Campaign\Tasks\**" />
		<EmbeddedResource Remove="Sms\Events\**" />
		<EmbeddedResource Remove="TicketHotButton\Commands\**" />
		<EmbeddedResource Remove="TripRoute\**" />
		<EmbeddedResource Remove="UserAccount\ActiveDirectory\**" />
		<None Remove="Appeal\**" />
		<None Remove="DetailPayment\**" />
		<None Remove="EastSpring\**" />
		<None Remove="Endorsement\**" />
		<None Remove="FlightRoute\**" />
		<None Remove="GanttProject\Command\**" />
		<None Remove="GanttProject\Queries\**" />
		<None Remove="NotificationCase\NotificationCaseServices\**" />
		<None Remove="obj\**" />
		<None Remove="Organization\**" />
		<None Remove="Outbound\Campaign\Commands\**" />
		<None Remove="Outbound\Campaign\Events\**" />
		<None Remove="Outbound\Campaign\Tasks\**" />
		<None Remove="Sms\Events\**" />
		<None Remove="TicketHotButton\Commands\**" />
		<None Remove="TripRoute\**" />
		<None Remove="UserAccount\ActiveDirectory\**" />
	</ItemGroup>
	<ItemGroup>
		<Compile Include="Access\GetUserProfileByAccessIdQuery.cs" />
		<Compile Include="Access\GetUserProfileByAccessOrgQuery.cs" />
		<Compile Include="AutomaticTask\Command\CreateAutoNextTaskErrorLogCommand.cs" />
		<Compile Include="AutomaticTask\Command\CreateEditAutoNextTaskCommand.cs" />
		<Compile Include="AutomaticTask\Command\CreateEditAutoNextTaskListCommand.cs" />
		<Compile Include="AutomaticTask\Command\CreateEditUserPathSelectorCommand.cs" />
		<Compile Include="AutomaticTask\Command\DeleteAutoNextTaskCommand.cs" />
		<Compile Include="AutomaticTask\Command\DeleteAutoNextTaskErrorLogCommand.cs" />
		<Compile Include="AutomaticTask\Command\DeleteRequestTicketFirstTaskAutoNextTaskErrorLogCommand.cs" />
		<Compile Include="AutomaticTask\Command\DeleteUserPathSelectorCommand.cs" />
		<Compile Include="AutomaticTask\Command\ParseAutoNextTaskConditionCommand.cs" />
		<Compile Include="AutomaticTask\Command\ParseAutoNextTaskUserPathSelectorCommand.cs" />
		<Compile Include="AutomaticTask\Queries\AutoNextTaskData.cs" />
		<Compile Include="AutomaticTask\Queries\AutoNextTaskInfo.cs" />
		<Compile Include="AutomaticTask\Queries\ExecuteEvaluateAutoNextTaskConditionQuery.cs" />
		<Compile Include="AutomaticTask\Queries\GetAllAutoConditionsQuery.cs" />
		<Compile Include="AutomaticTask\Queries\GetAllDynamicFieldConditionsQuery.cs" />
		<Compile Include="AutomaticTask\Queries\GetAllUserSelectorPathQuery.cs" />
		<Compile Include="AutomaticTask\Queries\GetAutoNextTaskByIdQuery.cs" />
		<Compile Include="AutomaticTask\Queries\GetAutoNextTaskByReferenceObjectIdQuery.cs" />
		<Compile Include="AutomaticTask\Queries\GetAutoNextTaskByWorkflowAndTaskTypeQuery.cs" />
		<Compile Include="AutomaticTask\Queries\GetAutoNextTaskErrorLogListByRequestTicketIdQuery.cs" />
		<Compile Include="AutomaticTask\Queries\GetAutoNextTaskInfoByWorkflowAndTaskTypeQuery.cs" />
		<Compile Include="AutomaticTask\Queries\GetIdsFromSelectorPathQuery.cs" />
		<Compile Include="AutomaticTask\Queries\GetOriginOrgIdQuery.cs" />
		<Compile Include="AutomaticTask\Queries\GetRequestTicketFirstTaskAutoNextTaskErrorLisQuery.cs" />
		<Compile Include="AutomaticTask\Queries\GetUserAssignmentByRoutingQuery.cs" />
		<Compile Include="AutomaticTask\Queries\GetUserListByUserSelectorPathQuery.cs" />
		<Compile Include="AutomaticTask\Queries\GetUserSelectorPathByIdQuery.cs" />
		<Compile Include="Behavior\Commands\CreateEditBehaviorCommand.cs" />
		<Compile Include="Behavior\Commands\DeleteBehaviorCommand.cs" />
		<Compile Include="Behavior\Queries\GetBehaviorByIdQuery.cs" />
		<Compile Include="Behavior\Queries\GetBehaviorListQuery.cs" />
		<Compile Include="Behavior\Queries\SearchBehaviorQuery.cs" />
		<Compile Include="Building\Commands\CreateEditBuildingCommand.cs" />
		<Compile Include="Building\Queries\GetBuildingByIdQuery.cs" />
		<Compile Include="Building\Queries\SearchBuildingQuery.cs" />
		<Compile Include="BusinessPermission\Commands\SaveBusinessPermissionDeletedInfoListCommand.cs" />
		<Compile Include="BusinessPermission\Queries\GetBusinessPermissionsByParentIdQuery.cs" />
		<Compile Include="BusinessPermission\Queries\GetRoleBusinessPermissionListQuery.cs" />
		<Compile Include="BusinessPermission\Queries\GetUserProfileByBusinessPermissionIdQuery.cs" />
		<Compile Include="BusinessResult\Commands\AddBusinessResultCommand.cs" />
		<Compile Include="BusinessResult\Commands\AddTaskTypeBusinessResultCommand.cs" />
		<Compile Include="BusinessResult\Commands\AddTaskTypeLv2BusinessResultCommand.cs" />
		<Compile Include="BusinessResult\Commands\AddWorkflowBusinessResultCommand.cs" />
		<Compile Include="BusinessResult\Commands\CreateEditBusinessResultCommand.cs" />
		<Compile Include="BusinessResult\Commands\DeleteBusinessResultParentOrChildCommand.cs" />
		<Compile Include="BusinessResult\Commands\DeleteTaskTypeAndChildBusinessResultCommand.cs" />
		<Compile Include="BusinessResult\Commands\DeleteTaskTypeBusinessResultCommand.cs" />
		<Compile Include="BusinessResult\Commands\DeleteWorkflowBusinessResultCommand.cs" />
		<Compile Include="BusinessResult\Commands\EditColorBusinessResultCommand.cs" />
		<Compile Include="BusinessResult\Queries\GetAllLv2BusinessResultByParentIdQuery.cs" />
		<Compile Include="BusinessResult\Queries\GetBusinessResultByIdQuery.cs" />
		<Compile Include="BusinessResult\Queries\GetBusinessResultListHierarchyQuery.cs" />
		<Compile Include="BusinessResult\Queries\GetBusinessResultListNotInTaskTypeQuery.cs" />
		<Compile Include="BusinessResult\Queries\GetBusinessResultListNotInWorkflowQuery.cs" />
		<Compile Include="BusinessResult\Queries\GetBusinessResultListQuery.cs" />
		<Compile Include="BusinessResult\Queries\GetDefaultTaskTypeBusinessResultListQuery.cs" />
		<Compile Include="BusinessResult\Queries\GetHiddenQuery.cs" />
		<Compile Include="BusinessResult\Queries\GetListDependOnBusinessResultQuery.cs" />
		<Compile Include="BusinessResult\Queries\GetRequestTicketBusinessResultListHierarchyQuery.cs" />
		<Compile Include="BusinessResult\Queries\GetServiceTypeLv2BusinessResultListQuery.cs" />
		<Compile Include="BusinessResult\Queries\GetTaskTypeBusinessResultListQuery.cs" />
		<Compile Include="BusinessResult\Queries\GetTaskTypeOrBusinessResultListQuery .cs" />
		<Compile Include="BusinessResult\Queries\GetWorkflowBusinessResultListQuery.cs" />
		<Compile Include="Callback\Commands\UpdateCallbackSettingCommand.cs" />
		<Compile Include="Callback\Commands\UpdateCallbackStatusResultCommand.cs" />
		<Compile Include="Callback\Queries\CallbackData.cs" />
		<Compile Include="Callback\Queries\CallbackDetailItem.cs" />
		<Compile Include="Callback\Queries\CallbackItem.cs" />
		<Compile Include="Callback\Queries\CallbackSettingListQuery.cs" />
		<Compile Include="Callback\Queries\CallbackSettingsData.cs" />
		<Compile Include="Callback\Queries\GetCallbackByIdQuery.cs" />
		<Compile Include="Callback\Queries\GetCallbackDetailsByCallbackID.cs" />
		<Compile Include="Callback\Queries\GetCallbackSettingByIdQuery.cs" />
		<Compile Include="Callback\Queries\GetReportSummaryCallback.cs" />
		<Compile Include="Callback\Queries\SearchCallbackDetailsQuery.cs" />
		<Compile Include="Callback\Queries\SearchCallbackListQuery.cs" />
		<Compile Include="Campaign\Commands\AddCustomerIntoCampaignByDynamicFieldDefinitionNameCommand.cs" />
		<Compile Include="Campaign\Commands\AddWorkTicketsToCampaignCommand.cs" />
		<Compile Include="Campaign\Commands\CreateCampaignWorkerCommand.cs" />
		<Compile Include="Campaign\Commands\CreateEditCampaignAssignmentCommand.cs" />
		<Compile Include="Campaign\Commands\CreateEditCampaignCommand.cs" />
		<Compile Include="Campaign\Commands\CreateEditCampaignWorkCommand.cs" />
		<Compile Include="Campaign\Commands\CreateSurveyFeedbackFromCampaignTicketAssignWorkCommand.cs" />
		<Compile Include="Campaign\Commands\DeleteCampaignCommand.cs" />
		<Compile Include="Campaign\Commands\DeleteCampaignWorkCommand.cs" />
		<Compile Include="Campaign\Commands\DeleteCampaignWorkerCommand.cs" />
		<Compile Include="Campaign\Commands\FinishCampaignAssignmentCommand.cs" />
		<Compile Include="Campaign\Commands\RegainTicketAssignmentCommand.cs" />
		<Compile Include="Campaign\Commands\UpdateCampaignAssignmentResultCommand.cs" />
		<Compile Include="Campaign\Queries\AgentCampaignListItem.cs" />
		<Compile Include="Campaign\Queries\AgentCampaignTicketAssginmentListItem.cs" />
		<Compile Include="Campaign\Queries\CampaignAssignmentSumaryInfo.cs" />
		<Compile Include="Campaign\Queries\CampaignData.cs" />
		<Compile Include="Campaign\Queries\CampaignInfo.cs" />
		<Compile Include="Campaign\Queries\GetAgentCampaignAssignmentSumaryInfoQuery.cs" />
		<Compile Include="Campaign\Queries\GetAgentCampaignListQuery.cs" />
		<Compile Include="Campaign\Queries\GetCampaignAssignmentByIdQuery.cs" />
		<Compile Include="Campaign\Queries\GetCampaignAssignmentByWorkIdQuery.cs" />
		<Compile Include="Campaign\Queries\GetCampaignAssignmentForWorkerByQuery.cs" />
		<Compile Include="Campaign\Queries\GetCampaignByFeedbackIdQuery.cs" />
		<Compile Include="Campaign\Queries\GetCampaignByIdQuery.cs" />
		<Compile Include="Campaign\Queries\GetCampaignFileFromEntityLinkQuery.cs" />
		<Compile Include="Campaign\Queries\GetCampaignInfoByIdQuery.cs" />
		<Compile Include="Campaign\Queries\GetCampaignWorkByCurrentAssginementIdQuery.cs" />
		<Compile Include="Campaign\Queries\GetCampaignWorkByIdQuery.cs" />
		<Compile Include="Campaign\Queries\GetCampaignWorkCustomerQuery.cs" />
		<Compile Include="Campaign\Queries\GetCampaignWorkerByIdQuery.cs" />
		<Compile Include="Campaign\Queries\GetCampaignWorkerListQuery.cs" />
		<Compile Include="Campaign\Queries\GetCampaignWorkerQuery.cs" />
		<Compile Include="Campaign\Queries\GetCampaignWorkSummaryQuery.cs" />
		<Compile Include="Campaign\Queries\GetCampaignWorkTicketQuery.cs" />
		<Compile Include="Campaign\Queries\GetFilesFromEntityLinkWithFieldEditFilesQuery.cs" />
		<Compile Include="Campaign\Queries\GetSurveyFeedbackFromCampaignWorkQuery.cs" />
		<Compile Include="Campaign\Queries\GetUserWorkerQuery.cs" />
		<Compile Include="Campaign\Queries\SearchAgentCampaignTicketAssginmentListQuery.cs" />
		<Compile Include="Campaign\Queries\SearchCampaignByQuery.cs" />
		<Compile Include="Campaign\Queries\SearchTicketAssignmentListQuery.cs" />
		<Compile Include="Channel\Commands\CreateEditChannelCommand.cs" />
		<Compile Include="Channel\Queries\ChannelData.cs" />
		<Compile Include="Channel\Queries\GetChannelByIdQuery.cs" />
		<Compile Include="Channel\Queries\SearchChannelQuery.cs" />
		<Compile Include="ConfigureTicket\UpdateConfigTicketCommand.cs" />
		<Compile Include="ContentTemplate\Command\CreateEditAlternativeNotiChannelContentTemplateCommand.cs" />
		<Compile Include="ContentTemplate\Command\CreateEditAutoCompleteContentTemplateCommand.cs" />
		<Compile Include="ContentTemplate\Command\CreateEditContentTemplateCommand.cs" />
		<Compile Include="ContentTemplate\Command\DeleteAlternativeNotiChannelContentTemplateCommand.cs" />
		<Compile Include="ContentTemplate\Command\DeleteAutoCompleteContentTemplateCommand.cs" />
		<Compile Include="ContentTemplate\Command\DeleteContentTemplateCommand.cs" />
		<Compile Include="ContentTemplate\Command\ParseContentCommand.cs" />
		<Compile Include="ContentTemplate\Command\ParseMonthlyPartFeeContentCommand.cs" />
		<Compile Include="ContentTemplate\Command\ParseMonthlyPartFeeReceiptContentCommand.cs" />
		<Compile Include="ContentTemplate\Command\ParseStaticContentCommand.cs" />
		<Compile Include="ContentTemplate\Command\SaveAlternativeNotiChannelContentTemplateOrdersCommand.cs" />
		<Compile Include="ContentTemplate\PhaseListData.cs" />
		<Compile Include="ContentTemplate\Queries\AlternativeNotiChannelContentTemplateData.cs" />
		<Compile Include="ContentTemplate\Queries\AlternativeNotiChannelContentTemplateItem.cs" />
		<Compile Include="ContentTemplate\Queries\ContentTemplateData.cs" />
		<Compile Include="ContentTemplate\Queries\GetAlternativeContentTemplateNotificationChanelSettingQuery.cs" />
		<Compile Include="ContentTemplate\Queries\GetAlternativeNotiChannelContentTemplateByIdQuery.cs" />
		<Compile Include="ContentTemplate\Queries\GetAlternativeNotiChannelContentTemplateByNotificationChannelSettingIdQuery.cs" />
		<Compile Include="ContentTemplate\Queries\GetAutoCompleteContentTemplateByIdQuery.cs" />
		<Compile Include="ContentTemplate\Queries\GetContentTemplateByTaskIdQuery.cs" />
		<Compile Include="ContentTemplate\Queries\GetDynamicFormContentTemplateLinkEntityParamsQuery.cs" />
		<Compile Include="ContentTemplate\Queries\SearchAutoCompleteContentTemplateByTypeQuery.cs" />
		<Compile Include="ContentTemplate\Queries\SearchAutoCompleteContentTemplateQuery.cs" />
		<Compile Include="ContentTemplate\Queries\SearchContentTemplateQuery.cs" />
		<Compile Include="Customer\Queries\GetCustomerByIdQuery.cs" />
		<Compile Include="DigitalChannel\Queries\DigitalChannelData.cs" />
		<Compile Include="DigitalChannel\Queries\GetDigitalChannelByChannelCodeQuery.cs" />
		<Compile Include="DigitalChannel\Queries\GetDigitalChannelByChannelTypeQuery.cs" />
		<Compile Include="DigitalChannel\Queries\GetDigitalChannelByCodeQuery.cs" />
		<Compile Include="DigitalChannel\Queries\GetDigitalChannelByIdQuery.cs" />
		<Compile Include="DigitalChannel\Queries\GetDigitalChannelListQuery.cs" />
		<Compile Include="DigitalChannel\Queries\GetDigitalContactTypeByIdQuery.cs" />
		<Compile Include="DigitalChannel\Queries\GetDigitalContactTypeListByCampaignQuery.cs" />
		<Compile Include="DigitalChannel\Queries\GetDigitalContactTypeListQuery.cs" />
		<Compile Include="DigitalChannel\Queries\GetDigitalServiceConfigurationQuery.cs" />
		<Compile Include="DigitalContact\Commands\AddAnonymousDigitalContactListToCampaignCommand.cs" />
		<Compile Include="DigitalContact\Commands\AddCustomerDigitalContactToCampaignCommand.cs" />
		<Compile Include="DigitalContact\Commands\AddDigitalContactsToCampaignCommand.cs" />
		<Compile Include="DigitalContact\Commands\CreateEditDigitalContactCommand.cs" />
		<Compile Include="DigitalContact\Commands\CreateLogLinkDigitalContactCommand.cs" />
		<Compile Include="DigitalContact\Commands\MergeCustomerAndUpdateLinkDigitalContactCommand.cs" />
		<Compile Include="DigitalContact\Commands\SetDigitalContactAnonymousByContactTypeCommand.cs" />
		<Compile Include="DigitalContact\Commands\UpdateTokenDigitalContactCommand.cs" />
		<Compile Include="DigitalContact\Queries\GetDigitalContactByCusIdQuery.cs" />
		<Compile Include="DigitalContact\Queries\GetDigitalContactByIdQuery.cs" />
		<Compile Include="DigitalContact\Queries\GetDigitalContactByUserIdAndContactTypeQuery.cs" />
		<Compile Include="DigitalContact\Queries\GetDigitalContactByUserIdListQuery.cs" />
		<Compile Include="DigitalContact\Queries\GetDigitalContactByUserIdQuery.cs" />
		<Compile Include="DigitalContact\Queries\GetDigitalContactInfoByCusIdOrDigitalIdQuery.cs" />
		<Compile Include="DigitalContact\Queries\GetDigitalResultInCampaignQuery.cs" />
		<Compile Include="DigitalContact\Queries\GetHistoryCustomerLinkDigitalContactByCustomerIdQuery.cs" />
		<Compile Include="DigitalContact\Queries\SearchAnonymousDigitalContactListToAddToCampaignQuery.cs" />
		<Compile Include="DigitalMessageTemplate\Command\CreateEditMessageTemplateCommand.cs" />
		<Compile Include="DigitalMessageTemplate\Command\DeleteChannelMessageTemplateCommand.cs" />
		<Compile Include="DigitalMessageTemplate\Command\DeleteMessageTemplateCommand.cs" />
		<Compile Include="DigitalMessageTemplate\Queries\GetDigitalTemplateListQuery.cs" />
		<Compile Include="DigitalMessageTemplate\Queries\GetMessageTemplateByIdQuery.cs" />
		<Compile Include="DigitalMessageTemplate\Queries\SearchDigitalMessageTemplateQuery.cs" />
		<Compile Include="DigitalMessage\Commands\UpdateDigitalMessageCommand.cs" />
		<Compile Include="DigitalMessage\Commands\UpdateSendMessageResultCommand.cs" />
		<Compile Include="DigitalMessage\Events\DigitalMessageStatusChangedEvent.cs" />
		<Compile Include="DigitalMessage\Queries\GetDigitalDeliverMessageByIdQuery.cs" />
		<Compile Include="DigitalMessage\Queries\GetDigitalDeliverMessagesHistoryQuery.cs" />
		<Compile Include="DigitalMessage\Queries\GetDigitalDeliverMessagesQuery.cs" />
		<Compile Include="DigitalPushCode\Commands\CreateEditPushCodeCommand.cs" />
		<Compile Include="DigitalPushCode\Commands\CreateEditPushCodeOnChannelCommand.cs" />
		<Compile Include="DigitalPushCode\Commands\CreateEditPushCodeOnChannelItemCommand.cs" />
		<Compile Include="DigitalPushCode\Commands\DeleteDigitalPushCodeCommand.cs" />
		<Compile Include="DigitalPushCode\Commands\DeletePushCodeOnChannelItemCommand.cs" />
		<Compile Include="DigitalPushCode\Queries\GetDigitalPushCodeByCodeNameQuery.cs" />
		<Compile Include="DigitalPushCode\Queries\GetDigitalPushCodeByIdQuery.cs" />
		<Compile Include="DigitalPushCode\Queries\GetDigitalPushCodeListQuery.cs" />
		<Compile Include="DigitalPushCode\Queries\GetDigitalPushCodeOnChannelByIdQuery.cs" />
		<Compile Include="DigitalPushCode\Queries\GetDigitalPushCodeOnChannelInfoListQuery.cs" />
		<Compile Include="DigitalPushCode\Queries\GetDigitalPushCodeOnChannelListQuery.cs" />
		<Compile Include="DynamicDefinedTable\Commands\CreateEditDynamicDefinedTableCellValueListCommand.cs" />
		<Compile Include="DynamicDefinedTable\Commands\CreateLinkedTicketColumnByDynamicTableColumnCommand.cs" />
		<Compile Include="DynamicDefinedTable\Commands\DeleteDynamicDefinedTableCellValuesCommand.cs" />
		<Compile Include="DynamicDefinedTable\Commands\DeleteDynamicDefinedTableRowCommand.cs" />
		<Compile Include="DynamicDefinedTable\Commands\EditLinkedTicketColumnByDynamicTableColumnCommand.cs" />
		<Compile Include="DynamicDefinedTable\Commands\SaveColumnFormulaListCommand.cs" />
		<Compile Include="DynamicDefinedTable\Commands\SaveDynamicDefinedTableColumnInjectsCommand.cs" />
		<Compile Include="DynamicDefinedTable\Commands\SyncOwnDbTableColumnForDynamicDefinedTableSchemaCommand.cs" />
		<Compile Include="DynamicDefinedTable\Commands\UpdateEntityLinkByLinkedTicketColumnCommand.cs" />
		<Compile Include="DynamicDefinedTable\Queries\DynamicDefinedTableCellValueData.cs" />
		<Compile Include="DynamicDefinedTable\Queries\DynamicDefinedTableColumnData.cs" />
		<Compile Include="DynamicDefinedTable\Queries\DynamicDefinedTableFileCellValue.cs" />
		<Compile Include="DynamicDefinedTable\Queries\GetDynamicDefinedTableCellValueListByDynamicFieldValueIdQuery.cs" />
		<Compile Include="DynamicDefinedTable\Queries\GetDynamicDefinedTableCellValuesQuery.cs" />
		<Compile Include="DynamicDefinedTable\Queries\GetDynamicDefinedTableColumnByIdQuery.cs" />
		<Compile Include="DynamicDefinedTable\Queries\GetDynamicDefinedTableColumnListByTableSchemaQuery.cs" />
		<Compile Include="DynamicDefinedTable\Queries\GetDynamicDefinedTableColumnOnFormListByTableSchemaQuery.cs" />
		<Compile Include="DynamicDefinedTable\Queries\GetDynamicDefinedTableLinkedTicketColumnListByColumnIdQuery.cs" />
		<Compile Include="DynamicDefinedTable\Queries\GetDynamicDefinedTableLinkedTicketColumnListByIdQuery.cs" />
		<Compile Include="DynamicDefinedTable\Queries\GetDynamicDefinedTableSchemaQuery.cs" />
		<Compile Include="DynamicForm\Command\DeleteDynamicFormCommand.cs" />
		<Compile Include="DynamicForm\Command\ReCalculateDynamicFormValueCommand.cs" />
		<Compile Include="DynamicForm\Queries\CheckDynamicFieldValueDupOnTaskQuery.cs" />
		<Compile Include="DynamicForm\Queries\GetAllDynamicFieldDefinitionInfoQuery.cs" />
		<Compile Include="DynamicForm\Queries\GetAllDynamicFormByLevelQuery.cs" />
		<Compile Include="DynamicForm\Queries\GetAllDynamicFormByServiceTypeIdQuery.cs" />
		<Compile Include="DynamicForm\Queries\GetDynamicFieldByFormIdAndNameQuery.cs" />
		<Compile Include="DynamicForm\Queries\GetRefObjectOfDynamicFormQuery.cs" />
		<Compile Include="DynamicTable\CreateEditDynamicTableColumnCommand.cs" />
		<Compile Include="DynamicTable\CreateEditDynamicTableColumnKPICommand.cs" />
		<Compile Include="DynamicTable\CreateEditDynamicTableColumnOnDynamicFormCommand.cs" />
		<Compile Include="DynamicTable\CreateEditDynamicTableCommand.cs" />
		<Compile Include="DynamicTable\DeleteDynamicTableColumnCommand.cs" />
		<Compile Include="DynamicTable\DeleteDynamicTableCommand.cs" />
		<Compile Include="DynamicTable\GetTableColumnHideOnDynamicFormQuery.cs" />
		<Compile Include="DynamicTable\GetTableColumnOnDynamicFormQuery.cs" />
		<Compile Include="DynamicTable\SearchDynamicTableColumnQuery.cs" />
		<Compile Include="DynamicTable\SearchDynamicTableQuery.cs" />
		<Compile Include="ECommerce\Commands\CreateECommerceUploadedFileCommand.cs" />
		<Compile Include="EntityLink\Commands\AssignEntityLinkCommand.cs" />
		<Compile Include="EntityLink\Commands\CreateEditEntityLinkCommand.cs" />
		<Compile Include="EntityLink\Commands\CreateOrRefreshLinkTicketCommand.cs" />
		<Compile Include="EntityLink\Commands\DeleteEntityLinkCommand.cs" />
		<Compile Include="EntityLink\Queries\GetEntityLinkBusinessSpecificByNameQuery.cs" />
		<Compile Include="EntityLink\Queries\GetEntityLinkBusinessSpecificListQuery.cs" />
		<Compile Include="EntityLink\Queries\GetEntityLinkBusinessSpecificQuery.cs" />
		<Compile Include="EntityLink\Queries\GetEntityLinkListQuery.cs" />
		<Compile Include="EntityLink\Queries\GetTicketForAddToEntityLinkQuery.cs" />
		<Compile Include="Enums\AssignmentStatus.cs" />
		<Compile Include="Enums\Behavior.cs" />
		<Compile Include="Enums\CampaignStatus.cs" />
		<Compile Include="Enums\CampaignType.cs" />
		<Compile Include="Enums\Channel.cs" />
		<Compile Include="Enums\CheckStatus.cs" />
		<Compile Include="Enums\CustomerType.cs" />
		<Compile Include="Enums\Difficulty.cs" />
		<Compile Include="Enums\DigitalMessageResultCode.cs" />
		<Compile Include="Enums\DigitalMessageStatus.cs" />
		<Compile Include="Enums\ExchangeDataCompareOptions.cs" />
		<Compile Include="Enums\ExchangeSourceType.cs" />
		<Compile Include="Enums\Gender.cs" />
		<Compile Include="Enums\GeolocationType.cs" />
		<Compile Include="Enums\OrganizationType.cs" />
		<Compile Include="Enums\PartEnum.cs" />
		<Compile Include="Enums\PrimaryCustomer.cs" />
		<Compile Include="Enums\RequestTicketStatus.cs" />
		<Compile Include="Enums\RetrievalStatus.cs" />
		<Compile Include="Enums\SchedulerType.cs" />
		<Compile Include="Enums\ServiceCategoryType.cs" />
		<Compile Include="Enums\TaskEnum.cs" />
		<Compile Include="Enums\TaskTypeEnum.cs" />
		<Compile Include="Enums\UsedStatus.cs" />
		<Compile Include="Enums\WorkMode.cs" />
		<Compile Include="EquipmentSystem\Commands\DoImportSystemsCommand.cs" />
		<Compile Include="EquipmentSystem\Commands\ImportStagingSystemsCommand.cs" />
		<Compile Include="EquipmentSystem\Commands\ScanImportSystemsCommand.cs" />
		<Compile Include="ExpenseItem\Commands\BulkInsertExpenseItemCommand.cs" />
		<Compile Include="ExpenseItem\Commands\CommitContestExpenseCommand.cs" />
		<Compile Include="ExpenseItem\Commands\UpdateExpenseItemsCommand.cs" />
		<Compile Include="ExpenseItem\CommitContestExpenseResult.cs" />
		<Compile Include="ExpenseItem\ExpenseItemData.cs" />
		<Compile Include="ExpenseItem\Queries\GetExpenseItemByContestQuery.cs" />
		<Compile Include="ExternalApiEndpoint\Commands\CreateEditExternalApiEndpointCommand.cs" />
		<Compile Include="ExternalApiEndpoint\Queries\GetExternalApiEndpointByIdQuery.cs" />
		<Compile Include="ExternalApiEndpoint\Queries\GetExternalApiEndpointListQuery.cs" />
		<Compile Include="FeeCategoryPart\Commands\CleanupImportFeeCategoryPartsCommand.cs" />
		<Compile Include="FeeCategoryPart\Commands\CreateEditFeeCategoryPartCommand.cs" />
		<Compile Include="FeeCategoryPart\Commands\DeleteFeeCategoryPartCommand.cs" />
		<Compile Include="FeeCategoryPart\Commands\DoImportFeeCategoryPartsCommand.cs" />
		<Compile Include="FeeCategoryPart\Commands\ImportStagingFeeCategoryPartsCommand.cs" />
		<Compile Include="FeeCategoryPart\Commands\ScanImportFeeCategoryPartsCommand.cs" />
		<Compile Include="FeeCategoryPart\Queries\FeeCategoryPartListByMonthlyQuery.cs" />
		<Compile Include="FeeCategoryPart\Queries\GetFeeCategoryPartByIdQuery.cs" />
		<Compile Include="FeeCategoryPart\Queries\GetFeeCategoryPartInfoListQuery.cs" />
		<Compile Include="FeeCategory\Commands\CreateEditFeeCategoryCommand.cs" />
		<Compile Include="FeeCategory\Commands\CreateEditFeeCategoryFormulaCommand.cs" />
		<Compile Include="FeeCategory\Commands\CreateFeeCategoryTaxCommand.cs" />
		<Compile Include="FeeCategory\Commands\DeleteFeeCategoryCommand.cs" />
		<Compile Include="FeeCategory\Commands\DeleteFeeCategoryFormulaCommand.cs" />
		<Compile Include="FeeCategory\Commands\DeleteFeeCategoryTaxCommand.cs" />
		<Compile Include="FeeCategory\Queries\GetFeeCategoryByIdQuery.cs" />
		<Compile Include="FeeCategory\Queries\GetFeeCategoryFormulaListQuery.cs" />
		<Compile Include="FeeCategory\Queries\GetFeeCategoryListQuery.cs" />
		<Compile Include="FeeCategory\Queries\GetFeeCategoryTaxInfoListQuery.cs" />
		<Compile Include="FinishRequestTicketByImport\Commands\BatchUpdateSoonerProcessMinutesCommand.cs" />
		<Compile Include="FinishRequestTicketByImport\Commands\BulkInsertDoneRequestTicketStagingCommand.cs" />
		<Compile Include="FinishRequestTicketByImport\Commands\CreateEditFinishRequestTicketByImportCommand.cs" />
		<Compile Include="FinishRequestTicketByImport\Commands\ProcessDoneRequestTicketImportSessionCommand.cs" />
		<Compile Include="FinishRequestTicketByImport\Commands\ScanDoneRequestTicketImportSessionCommand.cs" />
		<Compile Include="FinishRequestTicketByImport\Queries\GetDoneRequestTicketImportSessionByIdQuery.cs" />
		<Compile Include="FinishRequestTicketByImport\Queries\GetDoneRequestTicketImportSessionResultDetailQuery.cs" />
		<Compile Include="FinishRequestTicketByImport\Queries\GetDoneRequestTicketListImportSessionQuery.cs" />
		<Compile Include="FinishRequestTicketByImport\Queries\ScanDoneRequestTicketImportSessionResultQuery.cs" />
		<Compile Include="GanttProject\Queries\SearchGanttProjectQuery.cs" />
		<Compile Include="Geolocation\Queries\GetAllGeolocationQuery.cs" />
		<Compile Include="Geolocation\Queries\GetGeolocationByIdQuery.cs" />
		<Compile Include="Geolocation\Queries\GetGeolocationByParentIdQuery.cs" />
		<Compile Include="Geolocation\Queries\GetGeolocationByTypeQuery.cs" />
		<Compile Include="GridDynamicField\DynamicFieldFinishStepConentGrid\Commands\CreateEditDynamicFieldFinishStepConentListItemCommand.cs" />
		<Compile Include="GridDynamicField\DynamicFieldFinishStepConentGrid\Queries\DynamicFieldFinishStepConentListItemData.cs" />
		<Compile Include="GridDynamicField\DynamicFieldFinishStepConentGrid\Queries\GetDynamicFieldFinishStepConentListItemDataByValueGroupIdQuery.cs" />
		<Compile Include="GridDynamicField\DynamicFieldKnowledgeGrid\Commands\CreateEditDynamicFieldKnowledgeListItemCommand.cs" />
		<Compile Include="GridDynamicField\DynamicFieldKnowledgeGrid\Queries\GetDynamicFieldKnowledgeListItemDataByValueGroupIdQuery.cs" />
		<Compile Include="GridDynamicField\DynamicFieldVerifierListItem\Commands\CreateEditDynamicFieldVerifierListItemCommand.cs" />
		<Compile Include="GridDynamicField\DynamicFieldVerifierListItem\Queries\GetDynamicFieldVerifierListItemDataByValueGroupIdQuery.cs" />
		<Compile Include="GridDynamicField\DynamicFieldVerifyResultGrid\Commands\CreateEditDynamicFieldVerifyResultListItemCommand.cs" />
		<Compile Include="GridDynamicField\DynamicFieldVerifyResultGrid\Queries\GetDynamicFieldVerifyResultListItemDataByValueGroupIdQuery.cs" />
		<Compile Include="Image\Commands\DeleteImageCommand.cs" />
		<Compile Include="Image\Commands\InsertImageCommand.cs" />
		<Compile Include="Image\Commands\InsertListImageCommand.cs" />
		<Compile Include="Image\Queries\GetImageByIdQuery.cs" />
		<Compile Include="Image\Queries\GetImageInfoByIdQuery.cs" />
		<Compile Include="Image\Queries\ImageData.cs" />
		<Compile Include="ImportB2BCustomersSession\Commands\CleanupImportB2BCustomersCommand.cs" />
		<Compile Include="ImportB2BCustomersSession\Commands\CreateEditImportB2BCustomersSessionCommand.cs" />
		<Compile Include="ImportB2BCustomersSession\Commands\DoImportB2BCustomersCommand.cs" />
		<Compile Include="ImportB2BCustomersSession\Commands\GeolocationScanImportB2BCustomersCommand.cs" />
		<Compile Include="ImportB2BCustomersSession\Commands\ScanImportB2BClassificationsCommand.cs" />
		<Compile Include="ImportB2BCustomersSession\Commands\ScanImportB2BCustomersCommand.cs" />
		<Compile Include="ImportB2BCustomersSession\Queries\GetImportB2BCustomersResultsQuery.cs" />
		<Compile Include="ImportB2BCustomersSession\Queries\GetImportB2BDataStatisticsQuery.cs" />
		<Compile Include="ImportCustomerSession\Commands\CreateEditImportCustomerSessionCommand.cs" />
		<Compile Include="ImportCustomerSession\Commands\InsertImportStagingCustomersCommand.cs" />
		<Compile Include="ImportCustomerSession\Commands\ProcessImportStagingCustomerCommand.cs" />
		<Compile Include="ImportRequestTicket\Commands\CancelImportRequestTicketSessionCommand.cs" />
		<Compile Include="ImportRequestTicket\Commands\CreateEditImportRequestTicketSessionCommand.cs" />
		<Compile Include="ImportRequestTicket\Commands\InsertImportRequestTicketDataItemListCommand.cs" />
		<Compile Include="ImportRequestTicket\Queries\GetImportRequestTicketByIdQuery.cs" />
		<Compile Include="ImportRequestTicket\Queries\GetImportRequestTicketDataItemListQuery.cs" />
		<Compile Include="ImportRequestTicket\Queries\GetImportRequestTicketHistoriesQuery.cs" />
		<Compile Include="ImportSOSession\Commands\CleanUpImportSOCommand.cs" />
		<Compile Include="ImportSOSession\Commands\CreateEditImportSOSessionCommand.cs" />
		<Compile Include="ImportSOSession\Commands\DoImportSOCommand.cs" />
		<Compile Include="ImportSOSession\Commands\InsertStagingSOCommand.cs" />
		<Compile Include="ImportSOSession\Commands\ScanImportSOCommand.cs" />
		<Compile Include="ImportSOSession\Commands\ScanImportSODistributorCommand.cs" />
		<Compile Include="ImportSOSession\Commands\ScanImportSOProductCommand.cs" />
		<Compile Include="ImportSOSession\Commands\ScanImportSORequestTicketCommand.cs" />
		<Compile Include="ImportSOSession\Queries\GetImportSODataStatisticsQuery.cs" />
		<Compile Include="ImportSOSession\Queries\GetImportSOResultsQuery.cs" />
		<Compile Include="ImportSOSession\Queries\GetImportSOSessionDateTimeRangeQuery.cs" />
		<Compile Include="ImportTask\Commands\CancelImportTaskSessionCommand.cs" />
		<Compile Include="ImportTask\Commands\CreateEditImportTaskSessionCommand.cs" />
		<Compile Include="ImportTask\Commands\InsertImportTaskDataItemListCommand.cs" />
		<Compile Include="ImportTask\Queries\GetImportTaskByIdQuery.cs" />
		<Compile Include="ImportTask\Queries\GetImportTaskDataItemListQuery.cs" />
		<Compile Include="ImportTask\Queries\GetImportTaskListHistoriesQuery.cs" />
		<Compile Include="InfoList\Commands\CreateEditInfoListCommand.cs" />
		<Compile Include="InfoList\Queries\GetInfoListByIdQuery.cs" />
		<Compile Include="InfoList\Queries\GetInfoListByTypeQuery.cs" />
		<Compile Include="KnowledgeBase\Queries\GetAllKnowledgeItemQuery.cs" />
		<Compile Include="KnowledgeBase\Queries\GetKnowledgeItemByIdDeepQuery.cs" />
		<Compile Include="KnowledgeBase\Queries\GetKnowledgeItemByIdQuery.cs" />
		<Compile Include="KnowledgeBase\Queries\GetKnowledgeItemByParentIdQuery.cs" />
		<Compile Include="KnowledgeBase\Queries\GetKnowledgeItemListQuery.cs" />
		<Compile Include="KnowledgeBase\Queries\KnowledgeItemData.cs" />
		<Compile Include="KnowledgeBase\Queries\SearchKnowledgeItemByReferenceQuery.cs" />
		<Compile Include="KnowledgeBase\Queries\SearchKnowledgeItemQuery.cs" />
		<Compile Include="Mail\Commands\CreateEditMailCommand.cs" />
		<Compile Include="Mail\Events\MailProcessedEvent.cs" />
		<Compile Include="Mail\MailStatus.cs" />
		<Compile Include="Mail\Queries\AttachmentFileInfo.cs" />
		<Compile Include="Mail\Queries\GetMailAttachmentQuery.cs" />
		<Compile Include="Mail\Queries\GetMailByIdQuery.cs" />
		<Compile Include="Mail\Queries\GetNewMailListQuery.cs" />
		<Compile Include="Maintenance\Commands\CreateEditMaintenanceListItemCommand.cs" />
		<Compile Include="Maintenance\Queries\GetMaintenanceDataByValueGroupIdQuery.cs" />
		<Compile Include="Maintenance\Queries\GetMaintenanceTemplateGroupsQuery.cs" />
		<Compile Include="Maintenance\Queries\GetMaintenanceTemplateItemByGroupIdQuery.cs" />
		<Compile Include="MobileNotification\Commands\CloseMobileNotificationBatchCommand.cs" />
		<Compile Include="MobileNotification\Commands\CloseMobileNotificationCommand.cs" />
		<Compile Include="MobileNotification\Commands\CreateEditMobileNotificationCommand.cs" />
		<Compile Include="MobileNotification\Commands\CreateEditMobileNotificationResultCommand.cs" />
		<Compile Include="MobileNotification\Commands\DeleteMobileNotificationCommand.cs" />
		<Compile Include="MobileNotification\Queries\GetMobileNotificationByIdQuery.cs" />
		<Compile Include="MobileNotification\Queries\GetNewMobileNotificationGroupListQuery.cs" />
		<Compile Include="MobileNotification\Queries\GetNewMobileNotificationQuery.cs" />
		<Compile Include="MobileNotification\Queries\SearchMobileNotificationQuery.cs" />
		<Compile Include="MobileNotification\Tasks\MobileNotificationScheduledTask.cs" />
		<Compile Include="MonthlyPartFeeBatch\Commands\CalculateAndCreateMonthlyPartFeeBatchCommand.cs" />
		<Compile Include="MonthlyPartFeeBatch\Commands\CreateMonthlyPartFeeBatchCommand.cs" />
		<Compile Include="MonthlyPartFeeBatch\Commands\GenerateDebitNoteMonthlyPartFeeBatchCommand.cs" />
		<Compile Include="MonthlyPartFeeBatch\Commands\NotifyMonthlyPartFeeBatchCommand.cs" />
		<Compile Include="MonthlyPartFeeBatch\Commands\StartGenerateDebitNoteMonthlyPartFeeBatchCommand.cs" />
		<Compile Include="MonthlyPartFeeBatch\Commands\StartNotifyMonthlyPartFeeBatchCommand.cs" />
		<Compile Include="MonthlyPartFeeBatch\MonthlyPartFeeBatchEntity.cs" />
		<Compile Include="MonthlyPartFeeBatch\MonthlyPartFeeBatchItemEntity.cs" />
		<Compile Include="MonthlyPartFeeBatch\Queries\GetMonthlyPartFeeBatchByIdQuery.cs" />
		<Compile Include="MonthlyPartFeeBatch\Queries\GetMonthlyPartFeeBatchDataQuery.cs" />
		<Compile Include="MonthlyPartFeeBatch\Queries\GetMonthlyPartFeeItemListByBatchQuery.cs" />
		<Compile Include="MonthlyPartFeeBatch\Queries\GetNotProcessedMonthlyPartFeeItemByBatchQuery.cs" />
		<Compile Include="MonthlyPartFeeBatch\Queries\MonthlyPartFeeBatchData.cs" />
		<Compile Include="MonthlyPartFeeBatch\Queries\MonthlyPartFeeBatchItemData.cs" />
		<Compile Include="MonthlyPartFeeBatch\Tasks\GenerateDebitNoteMonthlyPartFeeBatchScheduledTask.cs" />
		<Compile Include="MonthlyPartFeeBatch\Tasks\NotifyMonthlyPartFeeBatchScheduledTask.cs" />
		<Compile Include="MonthlyPartFeeBatch\Tasks\ProcessMonthlyPartFeeBatchScheduledTask.cs" />
		<Compile Include="MonthlyPartFee\Commands\CalculateAndCreateMonthlyPartFeeCommand.cs" />
		<Compile Include="MonthlyPartFee\Commands\GenerateDebitNoteMonthlyPartFeeBatchItemCommand.cs" />
		<Compile Include="MonthlyPartFee\Commands\GenerateDebitNoteMonthlyPartFeeCommand.cs" />
		<Compile Include="MonthlyPartFee\Commands\GenerateReceiptMonthlyPartFeeCommand.cs" />
		<Compile Include="MonthlyPartFee\Commands\GenerateReceiptMonthlyPartFeeEmailCommand.cs" />
		<Compile Include="MonthlyPartFee\Commands\NotifyMonthlyPartFeeBatchItemCommand.cs" />
		<Compile Include="MonthlyPartFee\Commands\NotifyMonthlyPartFeeCommand.cs" />
		<Compile Include="MonthlyPartFee\Commands\ReCalculateAndCreateMonthlyPartFeeCommand.cs" />
		<Compile Include="MonthlyPartFee\Commands\RemindDebitNodeCommand.cs" />
		<Compile Include="MonthlyPartFee\Commands\RemindMonthlyPartFeeDebitNodeCommand.cs" />
		<Compile Include="MonthlyPartFee\Commands\UpdateMonthlyPartFeeStatusCommand.cs" />
		<Compile Include="MonthlyPartFee\Events\MonthlyPartFeeCreatedEvent.cs" />
		<Compile Include="MonthlyPartFee\ExportServices\IMonthlyFeeDetailExport.cs" />
		<Compile Include="MonthlyPartFee\IMonthlyFeeUtlities.cs" />
		<Compile Include="MonthlyPartFee\MonthlyPartFeeDebitReminderEntity.cs" />
		<Compile Include="MonthlyPartFee\MonthlyPartFeeEntity.cs" />
		<Compile Include="MonthlyPartFee\MonthlyPartFeeFileEntity.cs" />
		<Compile Include="MonthlyPartFee\MonthlyPartFeeItemEntity.cs" />
		<Compile Include="MonthlyPartFee\MonthlyPartFeeItemTaxEntity.cs" />
		<Compile Include="MonthlyPartFee\Queries\GetAllPartMonthlyFeeListQuery.cs" />
		<Compile Include="MonthlyPartFee\Queries\GetFeeCategoryTaxInfoListByMonthlyPartFeeQuery.cs" />
		<Compile Include="MonthlyPartFee\Queries\GetMonthFeeStatistics.cs" />
		<Compile Include="MonthlyPartFee\Queries\GetMonthlyFeeDetailExportTemplateByIdQuery.cs" />
		<Compile Include="MonthlyPartFee\Queries\GetMonthlyPartFeeByCodeQuery.cs" />
		<Compile Include="MonthlyPartFee\Queries\GetMonthlyPartFeeByIdQuery.cs" />
		<Compile Include="MonthlyPartFee\Queries\GetMonthlyPartFeeDebitNoteFileListByBatchQuery.cs" />
		<Compile Include="MonthlyPartFee\Queries\GetMonthlyPartFeeDebitReminderFileListQuery.cs" />
		<Compile Include="MonthlyPartFee\Queries\GetMonthlyPartFeeDebitReminderFileQuery.cs" />
		<Compile Include="MonthlyPartFee\Queries\GetMonthlyPartFeeDebitReminderListQuery.cs" />
		<Compile Include="MonthlyPartFee\Queries\GetMonthlyPartFeeFileByMonthlyPartFeeQuery.cs" />
		<Compile Include="MonthlyPartFee\Queries\GetMonthlyPartFeeItemDetailListQuery.cs" />
		<Compile Include="MonthlyPartFee\Queries\GetMonthlyPartFeeItemListQuery.cs" />
		<Compile Include="MonthlyPartFee\Queries\GetMonthlyPartFeeReceiptFileByMonthlyPartFeeQuery.cs" />
		<Compile Include="MonthlyPartFee\Queries\GetNextPartMonthlyFeeDebtByOwnerIdQuery.cs" />
		<Compile Include="MonthlyPartFee\Queries\GetPaidMonthlyPartFeeItemListQuery.cs" />
		<Compile Include="MonthlyPartFee\Queries\GetPartMonthlyFeeListByCustomerQuery.cs" />
		<Compile Include="MonthlyPartFee\Queries\GetPreviousPartMonthlyFeeDebtByOwnerIdQuery.cs" />
		<Compile Include="MonthlyPartFee\Queries\MonthlyFeeDetailExportTemplateData.cs" />
		<Compile Include="MonthlyPartFee\Queries\MonthlyPartFeeData.cs" />
		<Compile Include="MonthlyPartFee\Queries\MonthlyPartFeeDebitReminderInfo.cs" />
		<Compile Include="MonthlyPartFee\Queries\MonthlyPartFeeExportItemInfo.cs" />
		<Compile Include="MonthlyPartFee\Queries\MonthlyPartFeeFileData.cs" />
		<Compile Include="MonthlyPartFee\Queries\MonthlyPartFeeInfo.cs" />
		<Compile Include="MonthlyPartFee\Queries\MonthlyPartFeeItemData.cs" />
		<Compile Include="MonthlyPartFee\Queries\MonthlyPartFeeItemInfo.cs" />
		<Compile Include="MonthlyPartFee\Queries\ReportMonthlyPartFeeQuery.cs" />
		<Compile Include="MonthlyPartFee\Queries\SearchPartMonthlyFeeListQuery.cs" />
		<Compile Include="NotificationCase\Commands\CloseAllNotificationCaseByRootEntityCommand.cs" />
		<Compile Include="NotificationCase\Commands\CloseNotificationCaseByIdCommand.cs" />
		<Compile Include="NotificationCase\Commands\CloseNotificationCaseCommand.cs" />
		<Compile Include="NotificationCase\Commands\CreateNotificationCaseCommand.cs" />
		<Compile Include="NotificationCase\INotificationCaseService.cs" />
		<Compile Include="NotificationCase\NotificationCaseEntity.cs" />
		<Compile Include="NotificationCase\Queries\GetCloseNotificationCaseIdsWhenNotifyQuery.cs" />
		<Compile Include="NotificationCase\Queries\GetNewNotificationCaseListQuery.cs" />
		<Compile Include="NotificationCase\Queries\NotificationCaseData.cs" />
		<Compile Include="NotificationCase\Tasks\CleanUpAndBackUpNotificationScheduledTask.cs" />
		<Compile Include="NotificationCase\Tasks\NotificationCaseScheduledTask.cs" />
		<Compile Include="NotificationChanelSetting\CustomNotificationChannelConditionServiceTypeEntity.cs" />
		<Compile Include="NotificationChanelSetting\CustomNotificationChannelSettingEntity.cs" />
		<Compile Include="NotificationChanelSetting\CustomNotificationChannelToUsersEntity.cs" />
		<Compile Include="NotificationChanelSetting\NotificationChanelSettingEntity.cs" />
		<Compile Include="NotificationChanelSetting\Queries\CustomNotificationChannelConditionServiceTypeData.cs" />
		<Compile Include="NotificationChanelSetting\Queries\CustomNotificationChannelSettingData.cs" />
		<Compile Include="NotificationChanelSetting\Queries\CustomNotificationChannelSettingListItem.cs" />
		<Compile Include="NotificationChanelSetting\Queries\CustomNotificationChannelToUsersData.cs" />
		<Compile Include="NotificationChanelSetting\Queries\GetCustomNotificationChannelSettingByIdQuery.cs" />
		<Compile Include="NotificationChanelSetting\Queries\GetCustomNotificationChannelSettingByNotificationChannelSettingIdQuery.cs" />
		<Compile Include="NotificationChanelSetting\Queries\GetCustomNotificationChannelToUserSelectorPathsQuery.cs" />
		<Compile Include="NotificationChanelSetting\Queries\GetNotificationChannelSettingByIdQuery.cs" />
		<Compile Include="NotificationChanelSetting\Queries\NotificationChannelSettingData.cs" />
		<Compile Include="NotificationChanelSetting\Queries\SearchNotificationChanelSettingQuery.cs" />
		<Compile Include="Notification\Events\NotificationEvent.cs" />
		<Compile Include="Notification\Tasks\NotificationScheduledTask.cs" />
		<Compile Include="Order\Commands\Customers\InsertStagingECommerceCustomersCommand.cs" />
		<Compile Include="Order\Commands\Customers\ProcessStagingECommerceCustomersCommand.cs" />
		<Compile Include="Order\Commands\Customers\ScanStagingECommerceCustomersGeolocationCommand.cs" />
		<Compile Include="Order\Commands\OrderStatus\InsertStagingECommerceOrderStatusCommand.cs" />
		<Compile Include="Order\Commands\OrderStatus\UpdateOrderStatusesCommand.cs" />
		<Compile Include="Order\Commands\Orders\InsertStagingECommerceOrdersCommand.cs" />
		<Compile Include="Order\Commands\Orders\ProcessImportOrdersCommand.cs" />
		<Compile Include="Order\Commands\Orders\ScanReferenceObjectImportOrdersCommand.cs" />
		<Compile Include="Order\OrderCancelReasonEntity.cs" />
		<Compile Include="Order\OrderDetailEntity.cs" />
		<Compile Include="Order\OrderEntity.cs" />
		<Compile Include="Order\OrderForwardStatusEntity.cs" />
		<Compile Include="Order\OrderPOSStatusEntity.cs" />
		<Compile Include="Order\OrderTypeEntity.cs" />
		<Compile Include="Order\OrderVESStatusEntity.cs" />
		<Compile Include="Order\PaymentMethodEntity.cs" />
		<Compile Include="Order\PaymentStatusEntity.cs" />
		<Compile Include="Order\Queries\GetOrderByContactQuery.cs" />
		<Compile Include="Order\Queries\GetOrderByCustomerIdQuery.cs" />
		<Compile Include="Order\Queries\GetOrderByIdQuery.cs" />
		<Compile Include="Order\Queries\GetOrderDetailByOrderIdQuery.cs" />
		<Compile Include="Order\Queries\OrderData.cs" />
		<Compile Include="Order\Queries\OrderDetailData.cs" />
		<Compile Include="Order\SellChannelEntity.cs" />
		<Compile Include="Organization\Queries\GetAgentMemberQuery.cs" />
		<Compile Include="Organization\Queries\GetAvailableOrganizationQuery.cs" />
		<Compile Include="Organization\Queries\GetFactoryByCodeQuery.cs" />
		<Compile Include="Organization\Queries\GetFactoryQuery.cs" />
		<Compile Include="Organization\Queries\GetOrganizationByIdQuery.cs" />
		<Compile Include="Organization\Queries\GetOrganizationListQuery.cs" />
		<Compile Include="Organization\Queries\GetTaskServiceTypeDefaultOrganizationQuery.cs" />
		<Compile Include="Organization\Queries\GetTicketServiceTypeDefaultOrganizationQuery.cs" />
		<Compile Include="Organization\Queries\GetUserFuncBusinessPermissionConfigsInOrgQuery.cs" />
		<Compile Include="Organization\Queries\OrganizationData.cs" />		<Compile Include="Outbound\Appointment\AppointmentService.cs" />
		<Compile Include="Outbound\CallResult\CallResultEntity.cs" />
		<Compile Include="Outbound\CallResult\Queries\CallResultData.cs" />
		<Compile Include="Outbound\Campaign\CampaignEntity.cs" />
		<Compile Include="Outbound\Campaign\CampaignCoreServiceCallbackEntity.cs" />
		<Compile Include="Outbound\Campaign\CampaignExecutingTimeEntity.cs" />
		<Compile Include="Outbound\Campaign\CampaignParameterEntity.cs" />
		<Compile Include="Outbound\Campaign\CampaignTeamAssignmentEntity.cs" />
		<Compile Include="Outbound\Campaign\TableData.cs" />
		<Compile Include="Outbound\Campaign\Queries\CampaignData.cs" />
		<Compile Include="Outbound\Campaign\Queries\CampaignCoreServiceCallbackData.cs" />
		<Compile Include="Outbound\Campaign\Queries\CampaignExecutingTimeData.cs" />
		<Compile Include="Outbound\Campaign\Queries\CampaignParameterData.cs" />
		<Compile Include="Outbound\Campaign\Queries\CampaignTeamAssignmentData.cs" />
		<Compile Include="Outbound\Campaign\Queries\FWDGetCampaignQuery.cs" />
		<Compile Include="Outbound\CampaignCompletionCode\CampaignCompletionCodeEntity.cs" />
		<Compile Include="Outbound\CampaignTemplateCode\CampaignTemplateCodeEntity.cs" />
		<Compile Include="Outbound\CampaignTemplateCode\Queries\CampaignTemplateCodeData.cs" />
		<Compile Include="Outbound\Company\Queries\CompanyData.cs" />
		<Compile Include="Outbound\CompletionCategory\CompletionCategoryEntity.cs" />
		<Compile Include="Outbound\CompletionCategory\Queries\CompletionCategoryData.cs" />
		<Compile Include="Outbound\Contact\ContactEntity.cs" />
		<Compile Include="Outbound\Contact\Queries\ContactData.cs" />
		<Compile Include="Outbound\ContactCall\Queries\ContactCallData.cs" />
		<Compile Include="Outbound\ContactCall\ContactCallService.cs" />
		<Compile Include="Outbound\ContactCallResultServiceCallback\ContactCallResultServiceCallbackEntity.cs" />
		<Compile Include="Outbound\ContactCallResultServiceCallback\Queries\ContactCallResultServiceCallbackData.cs" />
		<Compile Include="Outbound\Contract\PolicyData.cs" />
		<Compile Include="Outbound\EndorseRequestHistory_ExpenseItem\ExpenseAndAdjustmentHistory.cs" />
		<Compile Include="Outbound\ExcelFormula\ExcelFormulaEntity.cs" />
		<Compile Include="Outbound\ExcelFormula\Queries\ExcelFormulaData.cs" />
		<Compile Include="Outbound\ExcelImport\ExcelDb.cs" />
		<Compile Include="Outbound\HotListCampaignImportSession\HotListCampaignImportSessionEntity.cs" />
		<Compile Include="Outbound\HotListGroup\HotListGroupEntity.cs" />
		<Compile Include="Outbound\HotListGroup\Queries\HotListGroupData.cs" />
		<Compile Include="Outbound\HotListGroup\Queries\HotListGroupUserData.cs" />
		<Compile Include="Outbound\HotListGroupUser\HotListGroupEntity.cs" />
		<Compile Include="Outbound\LeadAssignment\Queries\LeadAssignmentData.cs" />
		<Compile Include="Outbound\Organization\Queries\OrganizationInCampaignData.cs" />
		<Compile Include="Outbound\ParentContact\ParentContactEntity.cs" />
		<Compile Include="Outbound\ParentContact\Queries\ParentContactData.cs" />
		<Compile Include="Outbound\PaymentAdjustment\PaymentAdjustmentEntity.cs" />		<Compile Include="Outbound\Product\ProductInCampaign\ProductInCampaignEntity.cs" />
		<Compile Include="Outbound\ProspectAssignment\ProspectAssignmentEntity.cs" />
		<Compile Include="Outbound\ProspectAssignment\ProspectAssignmentService.cs" />
		<Compile Include="Outbound\Prospect\ProspectEntity.cs" />
		<Compile Include="Outbound\Prospect\Queries\ProspectData.cs" />
		<Compile Include="Outbound\Region\RegionEntity.cs" />
		<Compile Include="Outbound\Region\Queries\RegionData.cs" />
		<Compile Include="Outbound\Reminder\ReminderEntity.cs" />
		<Compile Include="Outbound\Reminder\Queries\ReminderData.cs" />
		<Compile Include="Outbound\Sale\SaleEntity.cs" />
		<Compile Include="Outbound\SlotTime\SlotTimeEntity.cs" />
		<Compile Include="Outbound\SysConfigs\SysConfigsEntity.cs" />		<Compile Include="Outbound\TeamLeadCampaign\GetAgentDistributionPlanQuery.cs" />
		<Compile Include="Outbound\TeamLeadCampaign\GetAssignedQuery.cs" />
		<Compile Include="Outbound\TeamLeadCampaign\SearchToDistributeByTeamLeadQuery.cs" />
		<Compile Include="Outbound\UserAccount\Roles.cs" />
		<Compile Include="PartCustomer\Queries\GetCustomerByPartIdQuery.cs" />
		<Compile Include="Part\Queries\GetPartByIdQuery.cs" />
		<Compile Include="Part\Queries\GetPartTypeByIdQuery.cs" />
		<Compile Include="Phase\Queries\GetTaskByIdQuery.cs" />
		<Compile Include="Phase\Queries\TaskInfo.cs" />
		<Compile Include="Phase\TaskData.cs" />
		<Compile Include="Report\CampignReportServices.cs" />
		<Compile Include="Report\ExchangeDataReportItem.cs" />
		<Compile Include="Report\InboundReportServices.cs" />
		<Compile Include="Report\OverProcessSLAReportItem.cs" />
		<Compile Include="Report\Queries\GetInfoDashboardQuery.cs" />
		<Compile Include="Report\Queries\GetSurveyReportQuery.cs" />
		<Compile Include="Report\ReportServices.cs" />
		<Compile Include="Report\ReportSummaryCallbackData.cs" />
		<Compile Include="Report\RequestTicketSLADetailItem.cs" />
		<Compile Include="Report\SCADAReportServices.cs" />
		<Compile Include="Report\SLAChiTietReportItem.cs" />
		<Compile Include="Report\VenusCorp\VenusCorpReportServices.cs" />
		<Compile Include="RequestTicket\Queries\GetCallHistoriesByRequestTicketIdQuery.cs" />
		<Compile Include="RequestTicket\Queries\GetCloneRequestTicketInfoQuery.cs" />
		<Compile Include="RequestTicket\Queries\GetDefaultOwnerListByServiceTypeQuery.cs" />
		<Compile Include="RequestTicket\Queries\GetDynamicFormValueByTicketIdQuery.cs" />
		<Compile Include="RequestTicket\Queries\GetOwnerListQuery.cs" />
		<Compile Include="RequestTicket\Queries\GetProductExchangeByExecutedTaskIdQuery.cs" />
		<Compile Include="RequestTicket\Queries\GetProductExchangeByIdQuery.cs" />
		<Compile Include="RequestTicket\Queries\GetProductExchangeByRequestTicketIdQuery.cs" />
		<Compile Include="RequestTicket\Queries\GetProductRequestTicketByIdQuery.cs" />
		<Compile Include="RequestTicket\Queries\GetProductRequestTicketByRequestTicketIdQuery.cs" />
		<Compile Include="RequestTicket\Queries\GetProductRequestTicketExecutedByTaskIdQuery.cs" />
		<Compile Include="RequestTicket\Queries\GetRequestTicketBoardItemListQuery.cs" />
		<Compile Include="RequestTicket\Queries\GetRequestTicketByCodeQuery.cs" />
		<Compile Include="RequestTicket\Queries\GetRequestTicketByCustomerAndServiceTypeQuery.cs" />
		<Compile Include="RequestTicket\Queries\GetRequestTicketByDynamicFieldValueIdQuery.cs" />
		<Compile Include="RequestTicket\Queries\GetRequestTicketByDynamicFormValueIdQuery.cs" />
		<Compile Include="RequestTicket\Queries\GetRequestTicketByIdQuery.cs" />
		<Compile Include="RequestTicket\Queries\GetRequestTicketByListCustomerIdAndRpContactQuery.cs" />
		<Compile Include="RequestTicket\Queries\GetRequestTicketByProspectAssignmentIdQuery.cs" />
		<Compile Include="RequestTicket\Queries\GetRequestTicketByTaskIdQuery.cs" />
		<Compile Include="RequestTicket\Queries\GetRequestTicketNotDoneTaskQuery.cs" />
		<Compile Include="RequestTicket\Queries\GetRequestTicketRetrievalQuery.cs" />
		<Compile Include="RequestTicket\Queries\GetRequestTicketWebChatListQuery.cs" />
		<Compile Include="RequestTicket\Queries\GetTicketWorkQuery.cs" />
		<Compile Include="RequestTicket\Queries\GetUserCurrentTicketListByTaskQuery.cs" />
		<Compile Include="RequestTicket\Queries\LandingGetTicketStatusDetailQuery.cs" />
		<Compile Include="RequestTicket\Queries\ProductRequestTicketData.cs" />
		<Compile Include="RequestTicket\Queries\RequestTicketData.cs" />
		<Compile Include="RequestTicket\Queries\SearchInboundTicketsQuery.cs" />
		<Compile Include="RequestTicket\Queries\SearchRequestTicketNABQuery.cs" />
		<Compile Include="RequestTicket\Queries\SearchRequestTicketQuery.cs" />
		<Compile Include="RequestTicket\Queries\TicketWorkListItem.cs" />
		<Compile Include="RequestTicket\RequestTicketCreateEditDynamicModel.cs" />
		<Compile Include="Retrieval\Audit\RetrievalAuditField.cs" />
		<Compile Include="ServiceType\Commands\CloneServiceTypeCommand.cs" />
		<Compile Include="ServiceType\Commands\DeleteServiceTypeCommand.cs" />
		<Compile Include="ServiceType\Commands\ImportServiceTypeCommandCommand.cs" />
		<Compile Include="ServiceType\Commands\InsertServiceTypeCommand.cs" />
		<Compile Include="ServiceType\Commands\UpdateServiceTypeCommand.cs" />
		<Compile Include="ServiceType\Events\ServiceTypeDeleteEvent.cs" />
		<Compile Include="ServiceType\Queries\CheckPermissionQuery.cs" />
		<Compile Include="ServiceType\Queries\ExpenseItemLinkServiceTypeQuery.cs" />
		<Compile Include="ServiceType\Queries\ExportServiceTypeQuery.cs" />
		<Compile Include="ServiceType\Queries\GetAllServiceTypeDataQuery.cs" />
		<Compile Include="ServiceType\Queries\GetBusinessResultByServiceTypeId.cs" />
		<Compile Include="ServiceType\Queries\GetLandingServiceTypeByServiceTypeIdQuery.cs" />
		<Compile Include="ServiceType\Queries\GetLandingServiceTypeListQuery.cs" />
		<Compile Include="ServiceType\Queries\GetRequestTicketExportDetailMappingByServiceTypeQuery.cs" />
		<Compile Include="ServiceType\Queries\GetServiceTypeByDueTimeIdQuery.cs" />
		<Compile Include="ServiceType\Queries\GetServiceTypeByDynamicFieldNameQuery.cs" />
		<Compile Include="ServiceType\Queries\GetServiceTypeByDynamicFormIdQuery.cs" />
		<Compile Include="ServiceType\Queries\GetServiceTypeByIdQuery.cs" />
		<Compile Include="ServiceType\Queries\GetServiceTypeByLevelQuery.cs" />
		<Compile Include="ServiceType\Queries\GetServiceTypeDataByLevelQuery.cs" />
		<Compile Include="ServiceType\Queries\GetServiceTypeDataByWorkflowIdQuery.cs" />
		<Compile Include="ServiceType\Queries\GetServiceTypeForLandingQuery.cs" />
		<Compile Include="ServiceType\Queries\GetServiceTypeTreeQuery.cs" />
		<Compile Include="ServiceType\Queries\SearchServiceTypeByTextQuery.cs" />
		<Compile Include="ServiceType\Queries\SearchServiceTypeQuery.cs" />
		<Compile Include="SMSLog\Commands\CreateEditSMSLogCommand.cs" />
		<Compile Include="SMSLog\Queries\GetSMSLogListQuery.cs" />
		<Compile Include="Sms\Commands\CreateEditGatewayCommand.cs" />
		<Compile Include="Sms\Commands\CreateEditSmsCommand.cs" />
		<Compile Include="Sms\Commands\DeleteGatewayCommand.cs" />
		<Compile Include="Sms\Queries\GatewayListItem.cs" />
		<Compile Include="Sms\Queries\GetGatewayByIdQuery.cs" />
		<Compile Include="Sms\Queries\GetGatewayDefaultQuery.cs" />
		<Compile Include="Sms\Queries\GetNewSmsListQuery.cs" />
		<Compile Include="Sms\Queries\SearchGatewayQuery.cs" />
		<Compile Include="Sms\SmsStatus.cs" />
		<Compile Include="Survey\Commands\CreateEditSurveyAnswerCommand.cs" />
		<Compile Include="Survey\Commands\CreateEditSurveyAnswerSuiteAnswerCommand.cs" />
		<Compile Include="Survey\Commands\CreateEditSurveyAnswerSuiteCommand.cs" />
		<Compile Include="Survey\Commands\CreateEditSurveyCampaignCommand.cs" />
		<Compile Include="Survey\Commands\CreateEditSurveyCampaignExecutionCommand.cs" />
		<Compile Include="Survey\Commands\CreateEditSurveyCampaignResponseExecutionCommand.cs" />
		<Compile Include="Survey\Commands\CreateEditSurveyCommand.cs" />
		<Compile Include="Survey\Commands\CreateEditSurveyFeedbackCommand.cs" />
		<Compile Include="Survey\Commands\CreateEditSurveyQuestionCommand.cs" />
		<Compile Include="Survey\Commands\CreateEditSurveyQuestionSectionCommand.cs" />
		<Compile Include="Survey\Commands\CreateSurveyFeedbackCommand.cs" />
		<Compile Include="Survey\Commands\CreateTicketWorkCommand.cs" />
		<Compile Include="Survey\Commands\DeleteSurveyAnswerCommand.cs" />
		<Compile Include="Survey\Commands\DeleteSurveyAnswerSuiteAnswerCommand.cs" />
		<Compile Include="Survey\Commands\DeleteSurveyAnswerSuiteCommand.cs" />
		<Compile Include="Survey\Commands\DeleteSurveyCampaignCommand.cs" />
		<Compile Include="Survey\Commands\DeleteSurveyCampaignExecutionCommand.cs" />
		<Compile Include="Survey\Commands\DeleteSurveyCampaignResponseExecutionCommand.cs" />
		<Compile Include="Survey\Commands\DeleteSurveyQuestionCommand.cs" />
		<Compile Include="Survey\Commands\DeleteSurveyQuestionSectionCommand.cs" />
		<Compile Include="Survey\Commands\ProcessSurveyCampaignResponseExecutionCommand.cs" />
		<Compile Include="Survey\Commands\SubmitAnwserSurrveyAllCommand.cs" />
		<Compile Include="Survey\Commands\SubmitAnwserSurrveyCommand.cs" />
		<Compile Include="Survey\Commands\SubmitSurveyFeedbackCommand.cs" />
		<Compile Include="Survey\Commands\UpdateAssignmentStatusCommand.cs" />
		<Compile Include="Survey\Queries\CampaignSurveyCampaignInfo.cs" />
		<Compile Include="Survey\Queries\GetAgentDoSurveyByServiceTypeQuery.cs" />
		<Compile Include="Survey\Queries\GetAllSurveyQuery.cs" />
		<Compile Include="Survey\Queries\GetCampaignInfoByCampaignExecutionServiceTypeIdQuery.cs" />
		<Compile Include="Survey\Queries\GetCampaignSurveyQuestionListQuery.cs" />
		<Compile Include="Survey\Queries\GetDistributePlanQuery.cs" />
		<Compile Include="Survey\Queries\GetLoopNextQuestionListQuery.cs" />
		<Compile Include="Survey\Queries\GetQuestionListByAnswerSuiteInCampaignQuery.cs" />
		<Compile Include="Survey\Queries\GetSurrveyAnswerListInSuiteQuery.cs" />
		<Compile Include="Survey\Queries\GetSurrveyAnswersByQuestionQuery.cs" />
		<Compile Include="Survey\Queries\GetSurrveyQuestionItemsBySurveyIdQuery.cs" />
		<Compile Include="Survey\Queries\GetSurrveyQuestionsBySectionIdQuery.cs" />
		<Compile Include="Survey\Queries\GetSurrveyQuestionsBySurveyIdQuery.cs" />
		<Compile Include="Survey\Queries\GetSurveyAnswerByIdQuery.cs" />
		<Compile Include="Survey\Queries\GetSurveyAnswerSuiteByIdQuery.cs" />
		<Compile Include="Survey\Queries\GetSurveyAnswerSuiteListQuery.cs" />
		<Compile Include="Survey\Queries\GetSurveyByIdQuery.cs" />
		<Compile Include="Survey\Queries\GetSurveyCampaignByCampaignIdQuery.cs" />
		<Compile Include="Survey\Queries\GetSurveyCampaignByIdQuery.cs" />
		<Compile Include="Survey\Queries\GetSurveyCampaignByServiceTypeQuery.cs" />
		<Compile Include="Survey\Queries\GetSurveyCampaignExecutionByIdQuery.cs" />
		<Compile Include="Survey\Queries\GetSurveyCampaignExecutionBySurveyCampaignIdQuery.cs" />
		<Compile Include="Survey\Queries\GetSurveyCampaignExecutionResponseBySurveyCampaignIdQuery.cs" />
		<Compile Include="Survey\Queries\GetSurveyCampaignItemListQuery.cs" />
		<Compile Include="Survey\Queries\GetSurveyCampaignQuery.cs" />
		<Compile Include="Survey\Queries\GetSurveyCampaignResponseExecutionByIdQuery.cs" />
		<Compile Include="Survey\Queries\GetSurveyFeedbackByCodeQuery.cs" />
		<Compile Include="Survey\Queries\GetSurveyFeedbackByRequestTicketIdQuery.cs" />
		<Compile Include="Survey\Queries\GetSurveyFeedbackExpiredDate.cs" />
		<Compile Include="Survey\Queries\GetSurveyFeedbackListByCustomerQuery.cs" />
		<Compile Include="Survey\Queries\GetSurveyListQuery.cs" />
		<Compile Include="Survey\Queries\GetSurveyQuestionAndAnswerQuery.cs" />
		<Compile Include="Survey\Queries\GetSurveyQuestionByAnswerQuery.cs" />
		<Compile Include="Survey\Queries\GetSurveyQuestionByIdQuery.cs" />
		<Compile Include="Survey\Queries\GetSurveyQuestionSectionByIdQuery.cs" />
		<Compile Include="Survey\Queries\GetSurveyQuestionSectionListQuery.cs" />
		<Compile Include="Survey\Queries\GetSurveyReportQuery.cs" />
		<Compile Include="Survey\Queries\GetSurveyResultQuery.cs" />
		<Compile Include="Survey\Queries\GetSurveyWithAnswerSuiteReportQuery.cs" />
		<Compile Include="Survey\Queries\GetTargetSurveyQuery.cs" />
		<Compile Include="Survey\Queries\GetTicketOwnerSettingFromSurveyFeedbackQuery.cs" />
		<Compile Include="Survey\Queries\PreviewSurveyResultQuery.cs" />
		<Compile Include="Survey\Queries\SearchCampaignWorkerQuery.cs" />
		<Compile Include="Survey\Queries\SurveyCampaignData.cs" />
		<Compile Include="Survey\SurveyEnum.cs" />
		<Compile Include="TaskType\Commands\CreateEditContentTemplateListInTaskTypeCommand.cs" />
		<Compile Include="TaskType\Commands\CreateEditOrganizationTaskTypeCommand.cs" />
		<Compile Include="TaskType\Commands\CreateEditTaskTypeCommand.cs" />
		<Compile Include="TaskType\Commands\DeleteTaskTypeCommand.cs" />
		<Compile Include="TaskType\Commands\SetAssignmentPriorityOrganizationCommand.cs" />
		<Compile Include="TaskType\Queries\ExportFormQuery.cs" />
		<Compile Include="TaskType\Queries\GetAllDynamicFieldRelatedTaskTypeQuery.cs" />
		<Compile Include="TaskType\Queries\GetContentTemplateByTaskTypeIdQuery.cs" />
		<Compile Include="TaskType\Queries\GetOrganizationTypeQuery.cs" />
		<Compile Include="TaskType\Queries\GetTaskTypeAssignmentPriorityOrganizationQuery.cs" />
		<Compile Include="TaskType\Queries\GetTaskTypeByIdQuery.cs" />
		<Compile Include="TaskType\Queries\GetTaskTypeByTaskIdQuery.cs" />
		<Compile Include="TaskType\Queries\GetTaskTypeForOrganizationQuery.cs" />
		<Compile Include="TaskType\Queries\GetTaskTypeForTicketQuery.cs" />
		<Compile Include="TaskType\Queries\GetTaskTypeInWorkflowByServiceTypeIdQuery.cs" />
		<Compile Include="TaskType\Queries\GetTaskTypeListByServiceTypeQuery.cs" />
		<Compile Include="TaskType\Queries\GetTaskTypeListByWorkflowQuery.cs" />
		<Compile Include="TaskType\Queries\GetTaskTypeNotificationEventQuery.cs" />
		<Compile Include="TaskType\Queries\GetUserAssignByTaskTypeIdQuery.cs" />
		<Compile Include="TaskType\Queries\GetWorkflowListByTaskTypeIdQuery.cs" />
		<Compile Include="TaskType\Queries\GetWorkflowTaskTypeListQuery.cs" />
		<Compile Include="TaskType\Queries\SearchTaskTypeQuery.cs" />
		<Compile Include="TaskType\Queries\TaskTypeData.cs" />
		<Compile Include="TaskType\Queries\TaskTypeInWorkflowInfo.cs" />
		<Compile Include="TaskType\Queries\TaskTypeNotificationEventData.cs" />
		<Compile Include="TaskType\Queries\TaskTypeReportListQuery.cs" />
		<Compile Include="Tax\Commands\CreateEditTaxCommand.cs" />
		<Compile Include="Tax\Commands\DeleteTaxCommand.cs" />
		<Compile Include="Tax\Queries\GetTaxByIdQuery.cs" />
		<Compile Include="Tax\Queries\GetTaxListQuery.cs" />
		<Compile Include="TbCallback\Commands\CreateEditTbCallbackCallCommand.cs" />
		<Compile Include="TbCallback\Commands\CreateEditTbCallbackCommand.cs" />
		<Compile Include="TbCallback\Queries\GetTbCallbackByIdQuery.cs" />
		<Compile Include="TbCallback\Queries\GetTbCallbackByReferenceObjectIdQuery.cs" />
		<Compile Include="TbCallback\Queries\GetTbCallbackCallHistoriesQuery.cs" />
		<Compile Include="TbCallback\Queries\GetTbCallbackCallResultQuery.cs" />
		<Compile Include="TbCallback\Queries\TbCallbackData.cs" />
		<Compile Include="TemplateLibrary\Queries\GetDynamicFieldDefinitionsQuery.cs" />
		<Compile Include="TemplateLibrary\Queries\GetTemplateLibraryItemByRequestTicketQuery.cs" />
		<Compile Include="TicketHotButton\Queries\GetTicketHotButtonByIdQuery.cs" />
		<Compile Include="TicketHotButton\Queries\GetTicketHotButtonListByCustomerQuery.cs" />
		<Compile Include="TicketHotButton\Queries\GetTicketHotButtonListQuery.cs" />
		<Compile Include="TicketHotButton\Queries\TicketHotButtonData.cs" />
		<Compile Include="AutoMapperProfile.cs" />
		<Compile Include="NotificationDefinitionContext.cs" />
		<Compile Include="AutomaticTask\AutoConditionEntity.cs" />
		<Compile Include="AutomaticTask\AutomaticTaskConstants.cs" />
		<Compile Include="AutomaticTask\AutoNextTaskEntity.cs" />
		<Compile Include="AutomaticTask\AutoNextTaskErrorLogEntity.cs" />
		<Compile Include="AutomaticTask\UserPathSelectorEntity.cs" />
		<Compile Include="AutomaticTask\Queries\AutoConditionData.cs" />
		<Compile Include="AutomaticTask\Queries\AutoNextTaskErrorLogData.cs" />
		<Compile Include="AutomaticTask\Queries\RequestTicketFirstTaskAutoNextTaskErrorItem.cs" />
		<Compile Include="AutomaticTask\Queries\UserPathSelectorData.cs" />
		<Compile Include="Behavior\BehaviorEntity.cs" />
		<Compile Include="Behavior\Queries\BehaviorData.cs" />
		<Compile Include="Building\BuildingData.cs" />
		<Compile Include="Building\BuildingEntity.cs" />
		<Compile Include="BuiltInCommand\BuiltInCommandInfo.cs" />
		<Compile Include="BusinessPermission\BusinessPermissionContants.cs" />
		<Compile Include="BusinessResult\BusinessResultEntity.cs" />
		<Compile Include="BusinessResult\Queries\BusinessResultData.cs" />
		<Compile Include="Callback\CallbackDetailEntity.cs" />
		<Compile Include="Callback\CallbackEntity.cs" />
		<Compile Include="Callback\CallbackSettingsEntity.cs" />
		<Compile Include="Campaign\CampaignAssignmentEntity.cs" />
		<Compile Include="Campaign\CampaignDfoContactEntity.cs" />
		<Compile Include="Campaign\CampaignLinkEntity.cs" />
		<Compile Include="Campaign\CampaignWorkEntity.cs" />
		<Compile Include="Campaign\CampaignWorkerEntity.cs" />
		<Compile Include="Campaign\Queries\CampaignAssignmentData.cs" />
		<Compile Include="Campaign\Queries\CampaignListItem.cs" />
		<Compile Include="Campaign\Queries\CampaignWorkData.cs" />
		<Compile Include="Campaign\Queries\CampaignWorkerData.cs" />
		<Compile Include="Campaign\Queries\CampaignWorkerListItem.cs" />
		<Compile Include="Campaign\Queries\CampaignWorkSummaryInfo.cs" />
		<Compile Include="Campaign\Queries\TicketAssignmentListItem.cs" />
		<Compile Include="Campaign\Queries\WorkCustomerListItem.cs" />
		<Compile Include="Campaign\Queries\WorkTicketListItem.cs" />
		<Compile Include="Channel\ChannelEntity.cs" />
		<Compile Include="ContentTemplate\AlternativeNotiChannelContentTemplateEntity.cs" />
		<Compile Include="ContentTemplate\AutoCompleteContentTemplateEntity.cs" />
		<Compile Include="ContentTemplate\ContentTemplateEntity.cs" />
		<Compile Include="ContentTemplate\ContentTemplateTaskTypeEntity.cs" />
		<Compile Include="ContentTemplate\Queries\AutoCompleteContentTemplateData.cs" />
		<Compile Include="Customer\ClassificationChannelEntity.cs" />
		<Compile Include="Customer\ClassificationEntity.cs" />
		<Compile Include="Customer\CustomerAlternativeAddressEntity.cs" />
		<Compile Include="Customer\CustomerBillingCodeEntity.cs" />
		<Compile Include="Customer\CustomerEntity.cs" />
		<Compile Include="Customer\CustomerFieldConfigurationEntity.cs" />
		<Compile Include="Customer\CustomerVersioningEntity.cs" />
		<Compile Include="Customer\Queries\ClassificationData.cs" />
		<Compile Include="Customer\Queries\CustomerAlternativeAddressData.cs" />
		<Compile Include="Customer\Queries\CustomerAlternativeAddressItem.cs" />
		<Compile Include="Customer\Queries\CustomerAppartmentInfo.cs" />
		<Compile Include="Customer\Queries\CustomerBillingCodeData.cs" />
		<Compile Include="Customer\Queries\CustomerData.cs" />
		<Compile Include="Customer\Queries\CustomerDedupDefinition.cs" />
		<Compile Include="Customer\Queries\CustomerFieldConfigurationData.cs" />
		<Compile Include="Customer\Queries\CustomerHierarchySummaryData.cs" />
		<Compile Include="Customer\Queries\CustomerListItem.cs" />
		<Compile Include="CustomerContext\CustomerContextHistoryEntity.cs" />
		<Compile Include="CustomerContext\Queries\CustomerContextHistoryData.cs" />
		<Compile Include="CustomerVersionName\CustomerVersionNameEntity.cs" />
		<Compile Include="Dashboard\Bucket\BucketItem.cs" />
		<Compile Include="DigitalCampaign\Queries\CampaignAnonymousDigitalContactData.cs" />
		<Compile Include="DigitalCampaign\Queries\CampaignAnonymousDigitalContactRawData.cs" />
		<Compile Include="DigitalCampaign\Queries\CampaignCustomerDigitalContactData.cs" />
		<Compile Include="DigitalCampaign\Queries\CampaignCustomerDigitalContactRawData.cs" />
		<Compile Include="DigitalCampaign\Queries\CampaignPushCodeInfoForCostEstimationData.cs" />
		<Compile Include="DigitalChannel\DigitalChannelEntity.cs" />
		<Compile Include="DigitalChannel\DigitalContactTypeEntity.cs" />
		<Compile Include="DigitalChannel\DigitalServiceConfigurationEntity.cs" />
		<Compile Include="DigitalChannel\Queries\DigitalContactTypeData.cs" />
		<Compile Include="DigitalChannelMessageTemplate\DigitalChannelMessageTemplateEntity.cs" />
		<Compile Include="DigitalChannelMessageTemplate\Queries\DigitalChannelMessageTemplateData.cs" />
		<Compile Include="DigitalContact\DigitalContactData.cs" />
		<Compile Include="DigitalContact\DigitalContactEntity.cs" />
		<Compile Include="DigitalContact\ProspectDigitalContactEntity.cs" />
		<Compile Include="DigitalContact\Queries\CustomerDigitalContactListItem.cs" />
		<Compile Include="DigitalContact\Queries\CustomerDigitalContactSummaryItem.cs" />
		<Compile Include="DigitalContact\Queries\DigitalContactListItem.cs" />
		<Compile Include="DigitalMessage\DigitalDeliverMessagesData.cs" />
		<Compile Include="DigitalMessage\DigitalDeliverMessagesHistoryData.cs" />
		<Compile Include="DigitalMessage\DigitalMessageEntity.cs" />
		<Compile Include="DigitalMessage\DigitalMessageLogEntity.cs" />
		<Compile Include="DigitalMessageTemplate\DigitalMessageTemplateEntity.cs" />
		<Compile Include="DigitalPushCode\DigitalPushCodeEntity.cs" />
		<Compile Include="DigitalPushCode\DigitalPushCodeOnChannelEntity.cs" />
		<Compile Include="DigitalPushCode\Queries\DigitalPushCodeData.cs" />
		<Compile Include="DigitalPushCode\Queries\DigitalPushCodeOnChannelData.cs" />
		<Compile Include="DigitalPushCode\Queries\DigitalPushCodeOnChannelInfo.cs" />
		<Compile Include="DynamicDefinedTable\DynamicDefinedTableCellValueEntity.cs" />
		<Compile Include="DynamicDefinedTable\DynamicDefinedTableColumnEntity.cs" />
		<Compile Include="DynamicDefinedTable\DynamicDefinedTableColumnOnDynamicFormEntity.cs" />
		<Compile Include="DynamicDefinedTable\DynamicDefinedTableLinkedTicketColumnEntity.cs" />
		<Compile Include="DynamicDefinedTable\DynamicDefinedTableSchemaEntity.cs" />
		<Compile Include="DynamicDefinedTable\Queries\DynamicDefinedTableLinkedTicketColumnData.cs" />
		<Compile Include="DynamicDefinedTable\Queries\DynamicDefinedTableSchemaData.cs" />
		<Compile Include="ECommerce\ECommerceUploadedFileEntity.cs" />
		<Compile Include="EntityLink\EntityLinkBusinessSpecificEntity.cs" />
		<Compile Include="EntityLink\EntityLinkEntity.cs" />
		<Compile Include="EntityLink\Queries\EntityLinkBusinessSpecificData.cs" />
		<Compile Include="EntityLink\Queries\EntityLinkData.cs" />
		<Compile Include="EntityLink\Queries\GeneralLinkEntityListData.cs" />
		<Compile Include="ExpenseItem\ExpenseItemEntity.cs" />
		<Compile Include="ExternalApiEndpoint\ExternalApiEndpointEntity.cs" />
		<Compile Include="ExternalApiEndpoint\Queries\ExternalApiEndpointData.cs" />
		<Compile Include="FeeCategory\FeeCategoryEntity.cs" />
		<Compile Include="FeeCategory\FeeCategoryFormulaEntity.cs" />
		<Compile Include="FeeCategory\FeeCategoryTaxEntity.cs" />
		<Compile Include="FeeCategory\Queries\FeeCategoryData.cs" />
		<Compile Include="FeeCategory\Queries\FeeCategoryFormulaData.cs" />
		<Compile Include="FeeCategory\Queries\FeeCategoryTaxData.cs" />
		<Compile Include="FeeCategory\Queries\FeeCategoryTaxInfo.cs" />
		<Compile Include="FeeCategoryPart\FeeCategoryPartEntity.cs" />
		<Compile Include="FeeCategoryPart\Queries\FeeCategoryPartData.cs" />
		<Compile Include="FeeCategoryPart\Queries\FeeCategoryPartInfo.cs" />
		<Compile Include="FinishRequestTicketByImport\DoneRequestTicketImportSessionEntity.cs" />
		<Compile Include="FinishRequestTicketByImport\Queries\DoneRequestTicketImportSessionData.cs" />
		<Compile Include="FinishRequestTicketByImport\Queries\DoneRequestTicketImportSessionResultItem.cs" />
		<Compile Include="GanttProject\GanttProject_TaskDependencyEntity.cs" />
		<Compile Include="Geolocation\EmployeeGeolocationEntity.cs" />
		<Compile Include="Geolocation\GeolocationEntity.cs" />
		<Compile Include="Geolocation\Queries\GeolocationData.cs" />
		<Compile Include="GridDynamicField\DynamicFieldFinishStepConentGrid\DynamicFieldFinishStepConentListItemEntity.cs" />
		<Compile Include="GridDynamicField\DynamicFieldKnowledgeGrid\DynamicFieldKnowledgeListItemEntity.cs" />
		<Compile Include="GridDynamicField\DynamicFieldKnowledgeGrid\Queries\DynamicFieldKnowledgeListItemData.cs" />
		<Compile Include="GridDynamicField\DynamicFieldVerifierListItem\DynamicFieldVerifierListItemEntity.cs" />
		<Compile Include="GridDynamicField\DynamicFieldVerifierListItem\Queries\DynamicFieldVerifierListItemData.cs" />
		<Compile Include="GridDynamicField\DynamicFieldVerifyResultGrid\DynamicFieldVerifyResultListItemEntity.cs" />
		<Compile Include="GridDynamicField\DynamicFieldVerifyResultGrid\Queries\DynamicFieldVerifyResultListItemData.cs" />
		<Compile Include="Image\ImageEntity.cs" />
		<Compile Include="Import\ImportDataStatisticItem.cs" />
		<Compile Include="ImportB2BCustomersSession\ImportB2BCustomersSessionEntity.cs" />
		<Compile Include="ImportB2BCustomersSession\Queries\ImportB2BCustomersResultItem.cs" />
		<Compile Include="ImportCustomerRaw\ImportCustomerSessionEntity.cs" />
		<Compile Include="ImportCustomerSession\ImportCustomerSessionEntity.cs" />
		<Compile Include="ImportRequestTicket\ImportRequestTicketDataItemEntity.cs" />
		<Compile Include="ImportRequestTicket\ImportRequestTicketSessionCancelledEntity.cs" />
		<Compile Include="ImportRequestTicket\ImportRequestTicketSessionEntity.cs" />
		<Compile Include="ImportRequestTicket\Queries\ImportRequestTicketDataItemData.cs" />
		<Compile Include="ImportRequestTicket\Queries\ImportRequestTicketSessionData.cs" />
		<Compile Include="ImportRequestTicket\Queries\ImportRequestTicketSessionListItem.cs" />
		<Compile Include="ImportSOSession\ImportSOSessionEntity.cs" />
		<Compile Include="ImportSOSession\Queries\ImportSOResultItem.cs" />
		<Compile Include="ImportTask\ImportTaskDataItemEntity.cs" />
		<Compile Include="ImportTask\ImportTaskSessionCancelledEntity.cs" />
		<Compile Include="ImportTask\ImportTaskSessionEntity.cs" />
		<Compile Include="ImportTask\Queries\ImportTaskDataItemData.cs" />
		<Compile Include="ImportTask\Queries\ImportTaskSessionData.cs" />
		<Compile Include="ImportTask\Queries\ImportTaskSessionListItem.cs" />
		<Compile Include="InfoList\InfoListEntity.cs" />
		<Compile Include="InfoList\Queries\InfoListData.cs" />
		<Compile Include="KnowledgeBase\KnowledgeItemEntity.cs" />
		<Compile Include="KnowledgeBase\KnowledgeReferenceEntity.cs" />
		<Compile Include="KnowledgeBase\Queries\KnowledgeItem.cs" />
		<Compile Include="KnowledgeBase\Queries\KnowledgeItemApiData.cs" />
		<Compile Include="Mail\Mail.cs" />
		<Compile Include="Mail\MailEntity.cs" />
		<Compile Include="Mail\MailGateway.cs" />
		<Compile Include="Mail\MailLogEntity.cs" />
		<Compile Include="Mail\Queries\MailData.cs" />
		<Compile Include="Maintenance\MaintenanceData.cs" />
		<Compile Include="Maintenance\MaintenanceEntity.cs" />
		<Compile Include="Maintenance\MaintenanceTemplateGroupData.cs" />
		<Compile Include="Maintenance\MaintenanceTemplateGroupEntity.cs" />
		<Compile Include="Maintenance\MaintenanceTemplateItemData.cs" />
		<Compile Include="Maintenance\MaintenanceTemplateItemEntity.cs" />
		<Compile Include="MobileNotification\MobileApiResult.cs" />
		<Compile Include="MobileNotification\MobileNotificationData.cs" />
		<Compile Include="MobileNotification\MobileNotificationEntity.cs" />
		<Compile Include="MobileNotification\MobileNotificationPartCustomerEntity.cs" />
		<Compile Include="MobileNotification\MobileNotificationResultData.cs" />
		<Compile Include="MobileNotification\MobileNotificationResultEntity.cs" />
		<Compile Include="MobileNotification\Queries\MobileNotificationListItem.cs" />
		<Compile Include="MonthlyPartFee\MonthlyFeeDetailExportTemplateEntity.cs" />
		<Compile Include="Outbound\SysConfigs\Queries\SysConfigsData.cs" />
		<Compile Include="Outbound\Team\TeamEntity.cs" />
		<Compile Include="Outbound\Team\Queries\CampaignTeamSelectingData.cs" />
		<Compile Include="Outbound\Team\Queries\ChienDichTeamData.cs" />
		<Compile Include="Outbound\Team\Queries\TeamData.cs" />
		<Compile Include="Outbound\TeamLeadCampaign\ContactDataInCampaign.cs" />
		<Compile Include="Outbound\TemplateCodeCallResult\TemplateCodeCallResultEntity.cs" />
		<Compile Include="Outbound\UserCheckIn\UserCheckInEntity.cs" />
		<Compile Include="Outbound\UserCheckIn\Queries\UserCheckInData.cs" />
		<Compile Include="Outbound\VNMContactImportSession\GetImportPlanDetailsEntity.cs" />
		<Compile Include="Outbound\VNMContactImportSession\NotInsertContactInImportSessionEntity.cs" />
		<Compile Include="Outbound\VNMContactImportSession\ScanInputDataErrorEntity.cs" />
		<Compile Include="Outbound\VNMContactImportSession\StagingContactEntity.cs" />
		<Compile Include="Outbound\VNMContactImportSession\VNMContactImportSessionEntity.cs" />
		<Compile Include="Outbound\VNMContactImportSession\Queries\VNMContactImportSessionData.cs" />
		<Compile Include="Outbound\Ward\WardEntity.cs" />
		<Compile Include="Outbound\Ward\Events\CreateEditWardEvent.cs" />
		<Compile Include="Outbound\Ward\Queries\WardListItem.cs" />
		<Compile Include="Part\PartBookableTimeEntity.cs" />
		<Compile Include="Part\PartCategoryEntity.cs" />
		<Compile Include="Part\PartData.cs" />
		<Compile Include="Part\PartEntity.cs" />
		<Compile Include="Part\PartErrorCategoryEntity.cs" />
		<Compile Include="Part\PartMaintenanceListItem.cs" />
		<Compile Include="Part\PartTemplateData.cs" />
		<Compile Include="Part\PartTemplateEntity.cs" />
		<Compile Include="Part\PartTypeEntity.cs" />
		<Compile Include="Part\SearchPartServiceTypeConfigEntity.cs" />
		<Compile Include="Part\ServiceTypePartEntity.cs" />
		<Compile Include="Part\Queries\PartBookableTimeData.cs" />
		<Compile Include="Part\Queries\PartTypeData.cs" />
		<Compile Include="Part\Queries\SearchPartServiceTypeConfigData.cs" />
		<Compile Include="PartBooking\PartBookingEntity.cs" />
		<Compile Include="PartBooking\Queries\PartBookingData.cs" />
		<Compile Include="PartBooking\Queries\PartBookingListItem.cs" />
		<Compile Include="PartCustomer\PartCustomerData.cs" />
		<Compile Include="PartCustomer\PartCustomerEntity.cs" />
		<Compile Include="PartCustomer\Queries\PartCustomerInfo.cs" />
		<Compile Include="PartCustomer\Queries\PartCustomerItem.cs" />
		<Compile Include="PartRepairing\PartRepairingData.cs" />
		<Compile Include="PartRepairing\PartRepairingEntity.cs" />
		<Compile Include="PartRepairing\PartRepairReportItem.cs" />
		<Compile Include="PartServiceUsedHistory\PartServiceUsedHistoryEntity.cs" />
		<Compile Include="PartServiceUsedHistory\Queries\PartServiceUsedHistoryData.cs" />
		<Compile Include="PartServiceUsedHistory\Queries\PartServiceUsedHistoryInfo.cs" />
		<Compile Include="PartServiceUsedHistory\Queries\ScanImportResultInfo.cs" />
		<Compile Include="Phase\PhaseEntity.cs" />
		<Compile Include="Phase\TaskEntity.cs" />
		<Compile Include="Phase\TaskFeedbackEntity.cs" />
		<Compile Include="Phase\TaskTypeBusinessPermissionEntity.cs" />
		<Compile Include="Phase\UpdateTaskFromEmailContent.cs" />
		<Compile Include="Phase\WorkflowGroupEntity.cs" />
		<Compile Include="Phase\Queries\PhaseInfo.cs" />
		<Compile Include="Phase\Queries\TaskSummaryData.cs" />
		<Compile Include="PlanJob\PlanJobEntity.cs" />
		<Compile Include="PlanJob\ServiceTypeComplianceEntity.cs" />
		<Compile Include="PlanJob\Queries\PlanJobData.cs" />
		<Compile Include="PlanJob\Queries\PlanJobInfo.cs" />
		<Compile Include="PlanJob\Queries\ServiceTypeComplianceData.cs" />
		<Compile Include="Product\ProductEntity.cs" />
		<Compile Include="Product\Queries\ProductData.cs" />
		<Compile Include="Product\Queries\ProductHistory.cs" />
		<Compile Include="Product\Queries\ProductListItem.cs" />
		<Compile Include="Properties\Settings.Designer.cs" />
		<Compile Include="Query\QueryEntity.cs" />
		<Compile Include="Report\AgentSurveyRatingSummaryReportItem.cs" />
		<Compile Include="Report\BaoCaoNangSuatLaoDong02Item.cs" />
		<Compile Include="Report\BehaviorsReportItem.cs" />
		<Compile Include="Report\ComplainedProductQuantityItem.cs" />
		<Compile Include="Report\LeadTimeDataReportItem.cs" />
		<Compile Include="Report\PredefinedListReportItem.cs" />
		<Compile Include="Report\ReportByServiceCategoryData.cs" />
		<Compile Include="Report\ReportByServiceCategoryItem.cs" />
		<Compile Include="Report\ReportF3Item.cs" />
		<Compile Include="Report\ReportF4Item.cs" />
		<Compile Include="Report\RequestTicketDataConversionItem.cs" />
		<Compile Include="Report\RequestTicketOwnershipSummaryReportItem.cs" />
		<Compile Include="Report\RequestTicketProcessStatusReportItem.cs" />
		<Compile Include="Report\RequestTicketReportItem.cs" />
		<Compile Include="Report\RequestTicketSumaryItem.cs" />
		<Compile Include="Report\RequestTicketSummaryByOwnerItem.cs" />
		<Compile Include="Report\RequestTicketSummaryByServiceTypeItem.cs" />
		<Compile Include="Report\RequestTicketSummaryByTimeData.cs" />
		<Compile Include="Report\RetrievalDataReportItem.cs" />
		<Compile Include="Report\SLATongHopReportItem.cs" />
		<Compile Include="Report\SLATraSoatReportItem.cs" />
		<Compile Include="Report\SourceChanelReportItem.cs" />
		<Compile Include="Report\TaskProgressInfo.cs" />
		<Compile Include="Report\TheoDoiSoLuongKhieuNai.cs" />
		<Compile Include="Report\TicketReportByOwnerItem.cs" />
		<Compile Include="Report\TicketReportByServiceTypeItem.cs" />
		<Compile Include="Report\TinhHinhGiaiQuyetKhieuNaiTheoKhuVucItem.cs" />
		<Compile Include="Report\Queries\RequestTicketDetailReportItem.cs" />
		<Compile Include="Report\VenusCorp\ChecklistReportItem.cs" />
		<Compile Include="Report\VenusCorp\DetailChecklistReportItem.cs" />
		<Compile Include="RequestTicket\InteractionHistoryData.cs" />
		<Compile Include="RequestTicket\ProductExchangeEntity.cs" />
		<Compile Include="RequestTicket\ProductRequestTicketEntity.cs" />
		<Compile Include="RequestTicket\RequestTicketBehaviorEntity.cs" />
		<Compile Include="RequestTicket\RequestTicketCallEntity.cs" />
		<Compile Include="RequestTicket\RequestTicketEntity.cs" />
		<Compile Include="RequestTicket\RequestTicketWebchatEntity.cs" />
		<Compile Include="RequestTicket\Commands\CustomerTicketByServiceType.cs" />
		<Compile Include="RequestTicket\Queries\ExportRequestTicketItem.cs" />
		<Compile Include="RequestTicket\Queries\ExportSearchRequestTicketResultItem.cs" />
		<Compile Include="RequestTicket\Queries\ExportTicketProductExchangeItem.cs" />
		<Compile Include="RequestTicket\Queries\ProductExchangeData.cs" />
		<Compile Include="RequestTicket\Queries\ProductRequestTicketItem.cs" />
		<Compile Include="RequestTicket\Queries\RequestTicketBoardData.cs" />
		<Compile Include="RequestTicket\Queries\RequestTicketCallData.cs" />
		<Compile Include="RequestTicket\Queries\RequestTicketWebchatData.cs" />
		<Compile Include="RequestTicketDynamicModel\RequestTicketDynamicModelEntity.cs" />
		<Compile Include="ResultCode\ResultCodeEntity.cs" />
		<Compile Include="ResultCode\ResultCodeSuiteEntity.cs" />
		<Compile Include="Retrieval\RequestTicketTracingItemEntity.cs" />
		<Compile Include="Retrieval\RetrievalEntity.cs" />
		<Compile Include="Retrieval\TracingItemEntity.cs" />
		<Compile Include="Retrieval\Audit\RetrievalAuditEntity.cs" />
		<Compile Include="Retrieval\Queries\RetrievalData.cs" />
		<Compile Include="Retrieval\Queries\TracingItemData.cs" />
		<Compile Include="ScheduledTaskConfig\ScheduledTaskConfigEntity.cs" />
		<Compile Include="ServiceCategory\ServiceCategoryEntity.cs" />
		<Compile Include="ServiceCategory\ServiceCategoryOrganizationEntity.cs" />
		<Compile Include="ServiceCategory\Queries\ServiceCategoryData.cs" />
		<Compile Include="ServiceCategory\Queries\ServiceCategoryOrganizationData.cs" />
		<Compile Include="ServiceCategory\Queries\ServiceCategoryTreeData.cs" />
		<Compile Include="ServiceType\LandingServiceTypeEntity.cs" />
		<Compile Include="ServiceType\RequestTicketExportDetailMappingEntity.cs" />
		<Compile Include="ServiceType\ServiceTypeEntity.cs" />
		<Compile Include="ServiceType\ServiceTypeRoleEntity.cs" />
		<Compile Include="ServiceType\Queries\LandingServiceTypeData.cs" />
		<Compile Include="ServiceType\Queries\RequestTicketExportDetailMappingData.cs" />
		<Compile Include="ServiceType\Queries\ServiceTypeAndDynamicFieldData.cs" />
		<Compile Include="ServiceType\Queries\ServiceTypeData.cs" />
		<Compile Include="ServiceType\Queries\ServiceTypeTreeData.cs" />
		<Compile Include="Sms\GatewayEntity.cs" />
		<Compile Include="Sms\GatewayIntergration.cs" />
		<Compile Include="Sms\ISmsBroker.cs" />
		<Compile Include="Sms\SmsEntity.cs" />
		<Compile Include="Sms\SmsLogEntity.cs" />
		<Compile Include="Sms\Queries\GatewayData.cs" />
		<Compile Include="Sms\Queries\SmsData.cs" />
		<Compile Include="SMSLog\SMSLogEntity.cs" />
		<Compile Include="SMSLog\Queries\SMSLogListItem.cs" />
		<Compile Include="Survey\EmailTemplateLinkEntityParams.cs" />
		<Compile Include="Survey\SurveyAnswerEntity.cs" />
		<Compile Include="Survey\SurveyAnswerSuiteAnswerEntity.cs" />
		<Compile Include="Survey\SurveyAnswerSuiteEntity.cs" />
		<Compile Include="Survey\SurveyCampaignEntity.cs" />
		<Compile Include="Survey\SurveyCampaignExecutionEntity.cs" />
		<Compile Include="Survey\SurveyCampaignResponseExecutionEntity.cs" />
		<Compile Include="Survey\SurveyEntity.cs" />
		<Compile Include="Survey\SurveyFeedbackAnswerEntity.cs" />
		<Compile Include="Survey\SurveyFeedbackEntity.cs" />
		<Compile Include="Survey\SurveyQuestionEntity.cs" />
		<Compile Include="Survey\SurveyQuestionSectionEntity.cs" />
		<Compile Include="Survey\Queries\SurveyAnswerData.cs" />
		<Compile Include="Survey\Queries\SurveyAnswerItem.cs" />
		<Compile Include="Survey\Queries\SurveyAnswerSuiteData.cs" />
		<Compile Include="Survey\Queries\SurveyCampaignExecutionData.cs" />
		<Compile Include="Survey\Queries\SurveyCampaignExecutionListItem.cs" />
		<Compile Include="Survey\Queries\SurveyCampaignListItem.cs" />
		<Compile Include="Survey\Queries\SurveyCampaignResponseExecutionData.cs" />
		<Compile Include="Survey\Queries\SurveyCampaignResponseExecutionListItem.cs" />
		<Compile Include="Survey\Queries\SurveyData.cs" />
		<Compile Include="Survey\Queries\SurveyFeedbackData.cs" />
		<Compile Include="Survey\Queries\SurveyListItem.cs" />
		<Compile Include="Survey\Queries\SurveyQuestionAnswerSuiteReportItem.cs" />
		<Compile Include="Survey\Queries\SurveyQuestionData.cs" />
		<Compile Include="Survey\Queries\SurveyQuestionListItem.cs" />
		<Compile Include="Survey\Queries\SurveyQuestionSectionData.cs" />
		<Compile Include="TaskType\BusinessResultReferenceEntity.cs" />
		<Compile Include="TaskType\TaskTypeEntity.cs" />
		<Compile Include="TaskType\TaskTypeNotificationEventEntity.cs" />
		<Compile Include="TaskType\Queries\TaskTypeInfo.cs" />
		<Compile Include="TaskType\Queries\TaskTypeListItem.cs" />
		<Compile Include="TaskType\Queries\TaskTypeReportInfo.cs" />
		<Compile Include="Tax\TaxEntity.cs" />
		<Compile Include="Tax\Queries\TaxData.cs" />
		<Compile Include="TbCallback\TbCallbackCallEntity.cs" />
		<Compile Include="TbCallback\TbCallbackCallResultEntity.cs" />
		<Compile Include="TbCallback\TbCallbackEntity.cs" />
		<Compile Include="TbCallback\Queries\TbCallbackCallData.cs" />
		<Compile Include="TbCallback\Queries\TbCallbackCallInfo.cs" />
		<Compile Include="TbCallback\Queries\TbCallbackCallResultData.cs" />
		<Compile Include="TemplateLibrary\Queries\TemplateLibraryItem.cs" />
		<Compile Include="TicketHotButton\TicketHotButtonEntity.cs" />
		<Compile Include="Ticket\Queries\GetDropdownLoadUserOptionQuery.cs" />
		<Compile Include="Ticket\Queries\GetOrganizationForTicketQuery.cs" />
		<Compile Include="UserAccount\Queries\AspNetUserData.cs" />
		<Compile Include="UserAccount\Queries\AspNetUserListItem.cs" />
		<Compile Include="UserAccount\Queries\SearchUsersQuery.cs" />
		<Compile Include="UserAccount\UserProfileEntityData.cs" />
		<Compile Include="UserTaskAssignmentRouting\Commands\CreateEditUserTaskAssignmentRoutingCommand.cs" />
		<Compile Include="UserTaskAssignmentRouting\Queries\GetUserTaskAssignmentRoutingByIdQuery.cs" />
		<Compile Include="UserTaskAssignmentRouting\Queries\GetUserTaskAssignmentRoutingListQuery.cs" />
		<Compile Include="User\OwnerData.cs" />
		<Compile Include="UserTaskAssignmentRouting\UserTaskAssignmentRoutingEntity.cs" />
		<Compile Include="UserTaskAssignmentRouting\Queries\UserTaskAssignmentRoutingData.cs" />
		<Compile Include="UserTaskAssignmentRouting\Queries\UserTaskAssignmentRoutingListItem.cs" />
		<Compile Include="Utility\DateTimeUtility.cs" />
		<Compile Include="Utility\LanguageHelper.cs" />
		<Compile Include="Utility\ServerDirectoryHelper.cs" />
		<Compile Include="VirtualOffice\VirtualOfficeDisplaySlotEntity.cs" />
		<Compile Include="VirtualOffice\VirtualOfficeEmployeeEntity.cs" />
		<Compile Include="VirtualOffice\VirtualOfficeFloorEntity.cs" />
		<Compile Include="VirtualOffice\VirtualOfficeWorkspaceEntity.cs" />
		<Compile Include="VirtualOffice\Data\VirtualOfficeEmployeeData.cs" />
		<Compile Include="WebChat\Commands\CreateEditMsTeamsAccountByAadObjectIdCommand.cs" />
		<Compile Include="WebChat\Commands\InsertWebChatCommand.cs" />
		<Compile Include="WebChat\Commands\InsertWebChatMessageCommand.cs" />
		<Compile Include="WebChat\Commands\InsertWebChatMessageListCommand.cs" />
		<Compile Include="WebChat\Commands\InsertWebChatUserMemberCommand.cs" />
		<Compile Include="WebChat\Commands\UpdateMsTeamsAccountNamesByAadObjectIdCommand.cs" />
		<Compile Include="WebChat\MsTeamsAccountEntity.cs" />
		<Compile Include="WebChat\Queries\GetMsTeamsAccountListQuery.cs" />
		<Compile Include="WebChat\Queries\GetWebChatGatewayCustomDataQuery.cs" />
		<Compile Include="WebChat\Queries\GetWebChatMessageListQuery.cs" />
		<Compile Include="WebChat\Queries\SearchWebChatListQuery.cs" />
		<Compile Include="WebChat\WebChatEntity.cs" />
		<Compile Include="WebChat\WebChatGatewayCustomDataEntity.cs" />
		<Compile Include="WebChat\WebChatMessageEntity.cs" />
		<Compile Include="WebChat\WebChatUserMemberEntity.cs" />
		<Compile Include="WebChat\Queries\MsTeamsAccountData.cs" />
		<Compile Include="WebChat\Queries\WebChatData.cs" />
		<Compile Include="WebChat\Queries\WebChatGatewayCustomData.cs" />
		<Compile Include="WebChat\Queries\WebChatMessageData.cs" />
		<Compile Include="WorkflowTaskTypeGroup\Commands\AddRemoveTaskToGroupCommand.cs" />
		<Compile Include="WorkflowTaskTypeGroup\Commands\CreateEditGroupCommand.cs" />
		<Compile Include="WorkflowTaskTypeGroup\Commands\CreateLinkToGroup.cs" />
		<Compile Include="WorkflowTaskTypeGroup\Commands\CreateTaskTypeGroupCommand.cs" />
		<Compile Include="WorkflowTaskTypeGroup\Commands\DeleteWorkflowTaskTypeGroupCommand.cs" />
		<Compile Include="WorkflowTaskTypeGroup\Queries\GetTaskTypeDataListByTaskTypeGroupIdQuery.cs" />
		<Compile Include="WorkflowTaskTypeGroup\Queries\GetTaskTypeListInGroupQuery.cs" />
		<Compile Include="WorkflowTaskTypeGroup\Queries\GetWorkflowTaskTypeGroupByIdQuery.cs" />
		<Compile Include="WorkflowTaskTypeGroup\Queries\GetWorkflowTaskTypeGroupListQuery.cs" />
		<Compile Include="WorkflowTaskTypeStage\Commands\CreateEditListTaskTypeStageCommand.cs" />
		<Compile Include="WorkflowTaskTypeStage\Commands\CreateTaskTypeStageCommand.cs" />
		<Compile Include="WorkflowTaskTypeStage\Commands\DeleteWorkflowTaskTypeGroupCommand.cs" />
		<Compile Include="WorkflowTaskTypeStage\Queries\GetWorkflowTaskTypeStageByIdQuery.cs" />
		<Compile Include="Workflow\ApproveItemWorkflowEvent.cs" />
		<Compile Include="Workflow\Commands\CloneWorkflowCommand.cs" />
		<Compile Include="Workflow\Commands\CreateEditTaskTypeInWorkflowCommand.cs" />
		<Compile Include="Workflow\Commands\CreateEditTaskTypeListInWorkflowCommand.cs" />
		<Compile Include="Workflow\Commands\CreateEditWorkflowCommand.cs" />
		<Compile Include="Workflow\Commands\CreateEditWrapForWorkflowCommand.cs" />
		<Compile Include="Workflow\Commands\DeleteWorkflowCommand.cs" />
		<Compile Include="Workflow\Commands\DeleteWorkflowTaskTypeCommand.cs" />
		<Compile Include="Workflow\Commands\FullCloneWorkflowCommand.cs" />
		<Compile Include="Workflow\Commands\ImportWorkflowCommand.cs" />
		<Compile Include="Workflow\Queries\ExportWorkflowQuery.cs" />
		<Compile Include="Workflow\Queries\GetAllWorkflowQuery.cs" />
		<Compile Include="Workflow\Queries\GetWorkflowByIdQuery.cs" />
		<Compile Include="Workflow\Queries\GetWorkflowMermaidCodeQuery.cs" />
		<Compile Include="Workflow\Queries\GetWorkflowTaskTypeByIdQuery.cs" />
		<Compile Include="Workflow\Queries\GetWorkflowTaskTypeByWorkflowAndTaskGroupIdQuery.cs" />
		<Compile Include="Workflow\Queries\GetWorkflowTaskTypeByWorkflowAndTaskTypeIdQuery.cs" />
		<Compile Include="Workflow\Queries\GetWorkflowTaskTypeByWorkflowIdQuery.cs" />
		<Compile Include="Workflow\Queries\SearchWorkflowQuery.cs" />
		<Compile Include="Workflow\Queries\WorkflowData.cs" />
		<Compile Include="Workflow\Queries\WorkflowTaskTypeData.cs" />
		<Compile Include="Workflow\WorkflowBusinessResultEntity.cs" />
		<Compile Include="Workflow\WorkflowEntity.cs" />
		<Compile Include="Workflow\WorkflowTaskTypeEntity.cs" />
		<Compile Include="Workflow\ApprovedItems_Workflow\IApprovedItemInWorkflow.cs" />
		<Compile Include="WorkflowTaskTypeGroup\WorkflowTaskTypeGroupEntity.cs" />
		<Compile Include="WorkflowTaskTypeGroup\Queries\WorkflowTaskTypeGroupData.cs" />
		<Compile Include="WorkflowTaskTypeStage\WorkflowTaskTypeStageEntity.cs" />
		<Compile Include="WorkflowTaskTypeStage\Queries\WorkflowTaskTypeStageData.cs" />
		<Compile Include="WorkItemSummary\Commands\CreateEditWorkItemSummaryCommand.cs" />
		<Compile Include="WorkItemSummary\Commands\CreateEditWorkItemSummaryCustomerServiceCommand.cs" />
		<Compile Include="WorkItemSummary\Commands\CreateEditWorkItemSummarySaleEffectivenessCommand.cs" />
		<Compile Include="WorkItemSummary\Queries\GetWorkItemSummaryByIdQuery.cs" />
		<Compile Include="WorkItemSummary\Queries\GetWorkItemSummaryByInteractionIdQuery.cs" />
		<Compile Include="WorkItemSummary\Queries\GetWorkItemSummaryCustomerServiceByWorkItemIdQuery.cs" />
		<Compile Include="WorkItemSummary\Queries\GetWorkItemSummarySaleEffectivenessByWorkItemIdQuery.cs" />
		<Compile Include="WorkItemSummary\WorkItemSummaryCustomerServiceEntity.cs" />
		<Compile Include="WorkItemSummary\WorkItemSummaryEntity.cs" />
		<Compile Include="WorkItemSummary\WorkItemSummarySaleEffectivenessEntity.cs" />
		<Compile Include="WorkItemSummary\Queries\WorkItemSummaryCustomerServiceData.cs" />
		<Compile Include="WorkItemSummary\Queries\WorkItemSummaryData.cs" />
		<Compile Include="WorkItemSummary\Queries\WorkItemSummarySaleEffectivenessData.cs" />
	</ItemGroup>
	<ItemGroup>
		<None Remove="TinyCRM.csproj.vspscc" />
	</ItemGroup>
	<ItemGroup>
		<PackageReference Include="EPPlus" Version="8.0.5" />
		<PackageReference Include="HtmlAgilityPack" Version="1.12.1" />
		<PackageReference Include="Irony" Version="1.5.3" />
		<PackageReference Include="Microsoft.Exchange.WebServices.NETStandard" Version="1.1.3" />
		<PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL" Version="9.0.4" />
		<PackageReference Include="RazorLight" Version="2.3.1" />
		<PackageReference Include="ReportViewerCore.NETCore" Version="15.1.17" />
		<PackageReference Include="XLParser" Version="1.7.5" />
	</ItemGroup>
	<ItemGroup>
		<ProjectReference Include="..\Poptech.CEP.ClientIntegration\Poptech.CEP.ClientIntegration.csproj" />
		<ProjectReference Include="..\Webaby.Core\Webaby.Core.csproj" />
		<ProjectReference Include="..\Webaby\Webaby.csproj" />
	</ItemGroup>
	<ItemGroup>
		<Folder Include="DigitalMessageLog\" />
		<Folder Include="Endorsement\Commands\" />
		<Folder Include="EquipmentSystem\Queries\" />
		<Folder Include="ImportTask\Tasks\" />
		<Folder Include="KnowledgeBase\Command\" />
		<Folder Include="GanttProject\Command\" /> 
		<Folder Include="Maintenance\Commands\" />
		<Folder Include="Maintenance\Queries\" />
		<Folder Include="Outbound\HotListCampaignImportSession\Commands\" />
		<Folder Include="Phase\Notifications\" />
		<Folder Include="Report\Tasks\" />
		<Folder Include="UserAccount\CustomerIdentity\" />
	</ItemGroup>
	<ItemGroup>
		<Compile Remove="**\*.cs" />
		<Compile Remove="Appeal\**" />
		<Compile Remove="DetailPayment\**" />
		<Compile Remove="EastSpring\**" />
		<Compile Remove="Endorsement\**" />
		<Compile Remove="FlightRoute\**" />
		<Compile Remove="GanttProject\Command\**" />
		<Compile Remove="GanttProject\Queries\**" />
		<Compile Remove="NotificationCase\NotificationCaseServices\**" />
		<Compile Remove="obj\**" />
		<Compile Remove="Organization\**" />
		<Compile Remove="Outbound\Campaign\Commands\**" />
		<Compile Remove="Outbound\Campaign\Events\**" />
		<Compile Remove="Outbound\Campaign\Tasks\**" />
		<Compile Remove="Sms\Events\**" />
		<Compile Remove="TripRoute\**" />
	</ItemGroup>
	<ItemGroup>
		<EmbeddedResource Remove="Appeal\**" />
		<EmbeddedResource Remove="DetailPayment\**" />
		<EmbeddedResource Remove="EastSpring\**" />
		<EmbeddedResource Remove="Endorsement\**" />
		<EmbeddedResource Remove="FlightRoute\**" />
		<EmbeddedResource Remove="GanttProject\Command\**" />
		<EmbeddedResource Remove="GanttProject\Queries\**" />
		<EmbeddedResource Remove="NotificationCase\NotificationCaseServices\**" />
		<EmbeddedResource Remove="obj\**" />
		<EmbeddedResource Remove="Organization\**" />
		<EmbeddedResource Remove="Outbound\Campaign\Commands\**" />
		<EmbeddedResource Remove="Outbound\Campaign\Events\**" />
		<EmbeddedResource Remove="Outbound\Campaign\Tasks\**" />
		<EmbeddedResource Remove="Sms\Events\**" />
		<EmbeddedResource Remove="TicketHotButton\Commands\**" />
		<EmbeddedResource Remove="TripRoute\**" />
		<EmbeddedResource Remove="UserAccount\ActiveDirectory\**" />
		<None Remove="Appeal\**" />
		<None Remove="DetailPayment\**" />
		<None Remove="EastSpring\**" />
		<None Remove="Endorsement\**" />
		<None Remove="FlightRoute\**" />
		<None Remove="GanttProject\Command\**" />
		<None Remove="GanttProject\Queries\**" />
		<None Remove="NotificationCase\NotificationCaseServices\**" />
		<None Remove="obj\**" />
		<None Remove="Organization\**" />
		<None Remove="Outbound\Campaign\Commands\**" />
		<None Remove="Outbound\Campaign\Events\**" />
		<None Remove="Outbound\Campaign\Tasks\**" />
		<None Remove="Sms\Events\**" />
		<None Remove="TicketHotButton\Commands\**" />
		<None Remove="TripRoute\**" />
		<None Remove="UserAccount\ActiveDirectory\**" />
	</ItemGroup>
	<ItemGroup>
		<Compile Include="Access\GetUserProfileByAccessIdQuery.cs" />
		<Compile Include="Access\GetUserProfileByAccessOrgQuery.cs" />
		<Compile Include="AutoMapperProfile.cs" />
		<Compile Include="AutomaticTask\AutoConditionEntity.cs" />
		<Compile Include="AutomaticTask\AutomaticTaskConstants.cs" />
		<Compile Include="AutomaticTask\AutoNextTaskEntity.cs" />
		<Compile Include="AutomaticTask\AutoNextTaskErrorLogEntity.cs" />
		<Compile Include="AutomaticTask\Command\CreateAutoNextTaskErrorLogCommand.cs" />
		<Compile Include="AutomaticTask\Command\CreateEditAutoNextTaskCommand.cs" />
		<Compile Include="AutomaticTask\Command\CreateEditAutoNextTaskListCommand.cs" />
		<Compile Include="AutomaticTask\Command\CreateEditUserPathSelectorCommand.cs" />
		<Compile Include="AutomaticTask\Command\CreateTasksByAutoNextTasksCommand.cs" />
		<Compile Include="AutomaticTask\Command\DeleteAutoNextTaskCommand.cs" />
		<Compile Include="AutomaticTask\Command\DeleteAutoNextTaskErrorLogCommand.cs" />
		<Compile Include="AutomaticTask\Command\DeleteRequestTicketFirstTaskAutoNextTaskErrorLogCommand.cs" />
		<Compile Include="AutomaticTask\Command\DeleteUserPathSelectorCommand.cs" />
		<Compile Include="AutomaticTask\Command\ParseAutoNextTaskConditionCommand.cs" />
		<Compile Include="AutomaticTask\Command\ParseAutoNextTaskUserPathSelectorCommand.cs" />
		<Compile Include="AutomaticTask\EventHandlers\AutoNextTaskTaskDoneHandler.cs" />
		<Compile Include="AutomaticTask\Events\ChildWorkflowEndedEvent.cs" />
		<Compile Include="AutomaticTask\Events\FinishRequestTicketByAutoNextTaskMatchedEvent.cs" />
		<Compile Include="AutomaticTask\Queries\AutoConditionData.cs" />
		<Compile Include="AutomaticTask\Queries\AutoNextTaskData.cs" />
		<Compile Include="AutomaticTask\Queries\AutoNextTaskErrorLogData.cs" />
		<Compile Include="AutomaticTask\Queries\AutoNextTaskInfo.cs" />
		<Compile Include="AutomaticTask\Queries\ExecuteEvaluateAutoNextTaskConditionQuery.cs" />
		<Compile Include="AutomaticTask\Queries\GetAllAutoConditionsQuery.cs" />
		<Compile Include="AutomaticTask\Queries\GetAllDynamicFieldConditionsQuery.cs" />
		<Compile Include="AutomaticTask\Queries\GetAllUserSelectorPathQuery.cs" />
		<Compile Include="AutomaticTask\Queries\GetAutoNextTaskByIdQuery.cs" />
		<Compile Include="AutomaticTask\Queries\GetAutoNextTaskByReferenceObjectIdQuery.cs" />
		<Compile Include="AutomaticTask\Queries\GetAutoNextTaskByWorkflowAndTaskTypeQuery.cs" />
		<Compile Include="AutomaticTask\Queries\GetAutoNextTaskErrorLogListByRequestTicketIdQuery.cs" />
		<Compile Include="AutomaticTask\Queries\GetAutoNextTaskInfoByWorkflowAndTaskTypeQuery.cs" />
		<Compile Include="AutomaticTask\Queries\GetIdsFromSelectorPathQuery.cs" />
		<Compile Include="AutomaticTask\Queries\GetOriginOrgIdQuery.cs" />
		<Compile Include="AutomaticTask\Queries\GetRequestTicketFirstTaskAutoNextTaskErrorLisQuery.cs" />
		<Compile Include="AutomaticTask\Queries\GetUserAssignmentByRoutingQuery.cs" />
		<Compile Include="AutomaticTask\Queries\GetUserListByUserSelectorPathQuery.cs" />
		<Compile Include="AutomaticTask\Queries\GetUserSelectorPathByIdQuery.cs" />
		<Compile Include="AutomaticTask\Queries\RequestTicketFirstTaskAutoNextTaskErrorItem.cs" />
		<Compile Include="AutomaticTask\Queries\UserPathSelectorData.cs" />
		<Compile Include="AutomaticTask\UserPathSelectorEntity.cs" />
		<Compile Include="Behavior\BehaviorEntity.cs" />
		<Compile Include="Behavior\Commands\CreateEditBehaviorCommand.cs" />
		<Compile Include="Behavior\Commands\DeleteBehaviorCommand.cs" />
		<Compile Include="Behavior\Queries\BehaviorData.cs" />
		<Compile Include="Behavior\Queries\GetBehaviorByIdQuery.cs" />
		<Compile Include="Behavior\Queries\GetBehaviorListQuery.cs" />
		<Compile Include="Behavior\Queries\SearchBehaviorQuery.cs" />
		<Compile Include="Building\BuildingData.cs" />
		<Compile Include="Building\BuildingEntity.cs" />
		<Compile Include="Building\Commands\CreateEditBuildingCommand.cs" />
		<Compile Include="Building\Queries\GetBuildingByIdQuery.cs" /> 
		<Compile Include="Building\Queries\SearchBuildingQuery.cs" /> 
		<Compile Include="BuiltInCommand\BuiltInCommandExecutor.cs" /> 
		<Compile Include="BuiltInCommand\BuiltInCommandInfo.cs" />
		<Compile Include="BusinessPermission\BusinessPermissionContants.cs" />
		<Compile Include="BusinessPermission\Commands\SaveBusinessPermissionDeletedInfoListCommand.cs" />
		<Compile Include="BusinessPermission\Queries\GetBusinessPermissionsByParentIdQuery.cs" />
		<Compile Include="BusinessPermission\Queries\GetRoleBusinessPermissionListQuery.cs" />
		<Compile Include="BusinessPermission\Queries\GetUserProfileByBusinessPermissionIdQuery.cs" />
		<Compile Include="BusinessResult\BusinessResultEntity.cs" />
		<Compile Include="BusinessResult\Commands\AddBusinessResultCommand.cs" />
		<Compile Include="BusinessResult\Commands\AddTaskTypeBusinessResultCommand.cs" />
		<Compile Include="BusinessResult\Commands\AddTaskTypeLv2BusinessResultCommand.cs" />
		<Compile Include="BusinessResult\Commands\AddWorkflowBusinessResultCommand.cs" />
		<Compile Include="BusinessResult\Commands\CreateEditBusinessResultCommand.cs" />
		<Compile Include="BusinessResult\Commands\DeleteBusinessResultParentOrChildCommand.cs" />
		<Compile Include="BusinessResult\Commands\DeleteTaskTypeAndChildBusinessResultCommand.cs" />
		<Compile Include="BusinessResult\Commands\DeleteTaskTypeBusinessResultCommand.cs" />
		<Compile Include="BusinessResult\Commands\DeleteWorkflowBusinessResultCommand.cs" />
		<Compile Include="BusinessResult\Commands\EditColorBusinessResultCommand.cs" />
		<Compile Include="BusinessResult\Queries\BusinessResultData.cs" />
		<Compile Include="BusinessResult\Queries\GetAllLv2BusinessResultByParentIdQuery.cs" />
		<Compile Include="BusinessResult\Queries\GetBusinessResultByIdQuery.cs" />
		<Compile Include="BusinessResult\Queries\GetBusinessResultListHierarchyQuery.cs" />
		<Compile Include="BusinessResult\Queries\GetBusinessResultListNotInTaskTypeQuery.cs" />
		<Compile Include="BusinessResult\Queries\GetBusinessResultListNotInWorkflowQuery.cs" />
		<Compile Include="BusinessResult\Queries\GetBusinessResultListQuery.cs" />
		<Compile Include="BusinessResult\Queries\GetDefaultTaskTypeBusinessResultListQuery.cs" />
		<Compile Include="BusinessResult\Queries\GetHiddenQuery.cs" />
		<Compile Include="BusinessResult\Queries\GetListDependOnBusinessResultQuery.cs" />
		<Compile Include="BusinessResult\Queries\GetRequestTicketBusinessResultListHierarchyQuery.cs" />
		<Compile Include="BusinessResult\Queries\GetServiceTypeLv2BusinessResultListQuery.cs" />
		<Compile Include="BusinessResult\Queries\GetTaskTypeBusinessResultListQuery.cs" />
		<Compile Include="BusinessResult\Queries\GetTaskTypeOrBusinessResultListQuery .cs" />
		<Compile Include="BusinessResult\Queries\GetWorkflowBusinessResultListQuery.cs" />
		<Compile Include="BusinessResult\Validators\AllTaskSuccessTicketBusinessResultValidator.cs" />
		<Compile Include="BusinessResult\Validators\ITicketBusinessResultValidator.cs" />
		<Compile Include="Callback\CallbackDetailEntity.cs" />
		<Compile Include="Callback\CallbackEntity.cs" />
		<Compile Include="Callback\CallbackSettingsEntity.cs" />
		<Compile Include="Callback\Commands\UpdateCallbackSettingCommand.cs" />
		<Compile Include="Callback\Commands\UpdateCallbackStatusResultCommand.cs" />
		<Compile Include="Callback\Queries\CallbackData.cs" />
		<Compile Include="Callback\Queries\CallbackDetailItem.cs" />
		<Compile Include="Callback\Queries\CallbackItem.cs" />
		<Compile Include="Callback\Queries\CallbackSettingListQuery.cs" />
		<Compile Include="Callback\Queries\CallbackSettingsData.cs" />
		<Compile Include="Callback\Queries\GetCallbackByIdQuery.cs" />
		<Compile Include="Callback\Queries\GetCallbackDetailsByCallbackID.cs" />
		<Compile Include="Callback\Queries\GetCallbackSettingByIdQuery.cs" />
		<Compile Include="Callback\Queries\GetReportSummaryCallback.cs" />
		<Compile Include="Callback\Queries\SearchCallbackDetailsQuery.cs" />
		<Compile Include="Callback\Queries\SearchCallbackListQuery.cs" />
		<Compile Include="Campaign\CampaignAssignmentEntity.cs" />
		<Compile Include="Campaign\CampaignDfoContactEntity.cs" />
		<Compile Include="Campaign\CampaignEntity.cs" />
		<Compile Include="Campaign\CampaignLinkEntity.cs" />
		<Compile Include="Campaign\CampaignWorkEntity.cs" />
		<Compile Include="Campaign\CampaignWorkerEntity.cs" />
		<Compile Include="Campaign\Commands\AddCustomerIntoCampaignByDynamicFieldDefinitionNameCommand.cs" />
		<Compile Include="Campaign\Commands\AddWorkTicketsToCampaignCommand.cs" />
		<Compile Include="Campaign\Commands\CreateCampaignWorkerCommand.cs" />
		<Compile Include="Campaign\Commands\CreateEditCampaignAssignmentCommand.cs" />
		<Compile Include="Campaign\Commands\CreateEditCampaignCommand.cs" />
		<Compile Include="Campaign\Commands\CreateEditCampaignWorkCommand.cs" />
		<Compile Include="Campaign\Commands\CreateSurveyFeedbackFromCampaignTicketAssignWorkCommand.cs" />
		<Compile Include="Campaign\Commands\DeleteCampaignCommand.cs" />
		<Compile Include="Campaign\Commands\DeleteCampaignWorkCommand.cs" />
		<Compile Include="Campaign\Commands\DeleteCampaignWorkerCommand.cs" />
		<Compile Include="Campaign\Commands\FinishCampaignAssignmentCommand.cs" />
		<Compile Include="Campaign\Commands\RegainTicketAssignmentCommand.cs" />
		<Compile Include="Campaign\Commands\UpdateCampaignAssignmentResultCommand.cs" />
		<Compile Include="Campaign\Queries\AgentCampaignListItem.cs" />
		<Compile Include="Campaign\Queries\AgentCampaignTicketAssginmentListItem.cs" />
		<Compile Include="Campaign\Queries\CampaignAssignmentData.cs" />
		<Compile Include="Campaign\Queries\CampaignAssignmentSumaryInfo.cs" />
		<Compile Include="Campaign\Queries\CampaignData.cs" />
		<Compile Include="Campaign\Queries\CampaignInfo.cs" />
		<Compile Include="Campaign\Queries\CampaignListItem.cs" />
		<Compile Include="Campaign\Queries\CampaignWorkData.cs" />
		<Compile Include="Campaign\Queries\CampaignWorkerData.cs" />
		<Compile Include="Campaign\Queries\CampaignWorkerListItem.cs" />
		<Compile Include="Campaign\Queries\CampaignWorkSummaryInfo.cs" />
		<Compile Include="Campaign\Queries\GetAgentCampaignAssignmentSumaryInfoQuery.cs" />
		<Compile Include="Campaign\Queries\GetAgentCampaignListQuery.cs" />
		<Compile Include="Campaign\Queries\GetCampaignAssignmentByIdQuery.cs" />
		<Compile Include="Campaign\Queries\GetCampaignAssignmentByWorkIdQuery.cs" />
		<Compile Include="Campaign\Queries\GetCampaignAssignmentForWorkerByQuery.cs" />
		<Compile Include="Campaign\Queries\GetCampaignByFeedbackIdQuery.cs" />
		<Compile Include="Campaign\Queries\GetCampaignByIdQuery.cs" />
		<Compile Include="Campaign\Queries\GetCampaignFileFromEntityLinkQuery.cs" />
		<Compile Include="Campaign\Queries\GetCampaignInfoByIdQuery.cs" />
		<Compile Include="Campaign\Queries\GetCampaignWorkByCurrentAssginementIdQuery.cs" />
		<Compile Include="Campaign\Queries\GetCampaignWorkByIdQuery.cs" />
		<Compile Include="Campaign\Queries\GetCampaignWorkCustomerQuery.cs" />
		<Compile Include="Campaign\Queries\GetCampaignWorkerByIdQuery.cs" />
		<Compile Include="Campaign\Queries\GetCampaignWorkerListQuery.cs" />
		<Compile Include="Campaign\Queries\GetCampaignWorkerQuery.cs" />
		<Compile Include="Campaign\Queries\GetCampaignWorkSummaryQuery.cs" />
		<Compile Include="Campaign\Queries\GetCampaignWorkTicketQuery.cs" />
		<Compile Include="Campaign\Queries\GetFilesFromEntityLinkWithFieldEditFilesQuery.cs" />
		<Compile Include="Campaign\Queries\GetSurveyFeedbackFromCampaignWorkQuery.cs" />
		<Compile Include="Campaign\Queries\GetUserWorkerQuery.cs" />
		<Compile Include="Campaign\Queries\SearchAgentCampaignTicketAssginmentListQuery.cs" />
		<Compile Include="Campaign\Queries\SearchCampaignByQuery.cs" />
		<Compile Include="Campaign\Queries\SearchTicketAssignmentListQuery.cs" />
		<Compile Include="Campaign\Queries\TicketAssignmentListItem.cs" />
		<Compile Include="Campaign\Queries\WorkCustomerListItem.cs" />
		<Compile Include="Campaign\Queries\WorkTicketListItem.cs" />
		<Compile Include="Campaign\Tasks\CampaignDialResultEvent.cs" />
		<Compile Include="Channel\ChannelEntity.cs" />
		<Compile Include="Channel\Commands\CreateEditChannelCommand.cs" />
		<Compile Include="Channel\Commands\DeleteChannelCommand.cs" />
		<Compile Include="Channel\Queries\ChannelData.cs" />
		<Compile Include="Channel\Queries\GetChannelByIdQuery.cs" />
		<Compile Include="Channel\Queries\GetChannelListQuery.cs" />
		<Compile Include="Channel\Queries\SearchChannelQuery.cs" />
		<Compile Include="ConfigureTicket\UpdateConfigTicketCommand.cs" />
		<Compile Include="ContentTemplate\AlternativeNotiChannelContentTemplateEntity.cs" />
		<Compile Include="ContentTemplate\AutoCompleteContentTemplateEntity.cs" />
		<Compile Include="ContentTemplate\Command\CreateEditAlternativeNotiChannelContentTemplateCommand.cs" />
		<Compile Include="ContentTemplate\Command\CreateEditAutoCompleteContentTemplateCommand.cs" />
		<Compile Include="ContentTemplate\Command\CreateEditContentTemplateCommand.cs" />
		<Compile Include="ContentTemplate\Command\DeleteAlternativeNotiChannelContentTemplateCommand.cs" />
		<Compile Include="ContentTemplate\Command\DeleteAutoCompleteContentTemplateCommand.cs" />
		<Compile Include="ContentTemplate\Command\DeleteContentTemplateCommand.cs" />
		<Compile Include="ContentTemplate\Command\ParseContentCommand.cs" />
		<Compile Include="ContentTemplate\Command\ParseMonthlyPartFeeContentCommand.cs" />
		<Compile Include="ContentTemplate\Command\ParseMonthlyPartFeeReceiptContentCommand.cs" />
		<Compile Include="ContentTemplate\Command\ParseStaticContentCommand.cs" />
		<Compile Include="ContentTemplate\Command\SaveAlternativeNotiChannelContentTemplateOrdersCommand.cs" />
		<Compile Include="ContentTemplate\ContentTemplateEntity.cs" />
		<Compile Include="ContentTemplate\ContentTemplateTaskTypeEntity.cs" />
		<Compile Include="ContentTemplate\ContentTemplateUitls.cs" />
		<Compile Include="ContentTemplate\PhaseListData.cs" />
		<Compile Include="ContentTemplate\Queries\AlternativeNotiChannelContentTemplateData.cs" />
		<Compile Include="ContentTemplate\Queries\AlternativeNotiChannelContentTemplateItem.cs" />
		<Compile Include="ContentTemplate\Queries\AutoCompleteContentTemplateData.cs" />
		<Compile Include="ContentTemplate\Queries\ContentTemplateData.cs" />
		<Compile Include="ContentTemplate\Queries\GetAlternativeContentTemplateNotificationChanelSettingQuery.cs" />
		<Compile Include="ContentTemplate\Queries\GetAlternativeNotiChannelContentTemplateByIdQuery.cs" />
		<Compile Include="ContentTemplate\Queries\GetAlternativeNotiChannelContentTemplateByNotificationChannelSettingIdQuery.cs" />
		<Compile Include="ContentTemplate\Queries\GetAutoCompleteContentTemplateByIdQuery.cs" />
		<Compile Include="ContentTemplate\Queries\GetContentTemplateByTaskIdQuery.cs" />
		<Compile Include="ContentTemplate\Queries\GetDynamicFormContentTemplateLinkEntityParamsQuery.cs" />
		<Compile Include="ContentTemplate\Queries\SearchAutoCompleteContentTemplateByTypeQuery.cs" />
		<Compile Include="ContentTemplate\Queries\SearchAutoCompleteContentTemplateQuery.cs" />
		<Compile Include="ContentTemplate\Queries\SearchContentTemplateQuery.cs" />
		<Compile Include="CustomerContext\Commands\InsertCustomerContextHistoryCommand.cs" />
		<Compile Include="CustomerContext\CustomerContextHistoryEntity.cs" />
		<Compile Include="CustomerContext\Events\CustomerContextEvent.cs" />
		<Compile Include="CustomerContext\Queries\CustomerContextHistoryData.cs" />
		<Compile Include="CustomerVersionName\CustomerVersionNameEntity.cs" />
		<Compile Include="Customer\ClassificationChannelEntity.cs" />
		<Compile Include="Customer\ImportCustomerRawLogEntity.cs" />
		<Compile Include="Customer\ClassificationEntity.cs" />
		<Compile Include="Customer\CustomerAlternativeAddressEntity.cs" />
		<Compile Include="Customer\CustomerBillingCodeEntity.cs" />
		<Compile Include="Customer\CustomerEntity.cs" />
		<Compile Include="Customer\CustomerFieldConfigurationEntity.cs" />
		<Compile Include="Customer\CustomerVersioningEntity.cs" />
		<Compile Include="Customer\Events\CustomerMassImportedEvent.cs" />
		<Compile Include="Customer\Queries\ClassificationData.cs" />
		<Compile Include="Customer\Queries\CustomerAlternativeAddressData.cs" />
		<Compile Include="Customer\Queries\CustomerAlternativeAddressItem.cs" />
		<Compile Include="Customer\Queries\CustomerAppartmentInfo.cs" />
		<Compile Include="Customer\Queries\CustomerBillingCodeData.cs" />
		<Compile Include="Customer\Queries\CustomerData.cs" />
		<Compile Include="Customer\Queries\CustomerDedupDefinition.cs" />
		<Compile Include="Customer\Queries\CustomerFieldConfigurationData.cs" />
		<Compile Include="Customer\Queries\CustomerHierarchySummaryData.cs" />
		<Compile Include="Customer\Queries\CustomerListItem.cs" />
		<Compile Include="Customer\Queries\GetCustomerByIdQuery.cs" />
		<Compile Include="Dashboard\Bucket\BucketItem.cs" />
		<Compile Include="Dashboard\Bucket\BucketTime.cs" />
		<Compile Include="Dashboard\Bucket\GetBucketQuery.cs" />
		<Compile Include="DigitalCampaign\Queries\CampaignAnonymousDigitalContactData.cs" />
		<Compile Include="DigitalCampaign\Queries\CampaignAnonymousDigitalContactRawData.cs" />
		<Compile Include="DigitalCampaign\Queries\CampaignCustomerDigitalContactData.cs" />
		<Compile Include="DigitalCampaign\Queries\CampaignCustomerDigitalContactRawData.cs" />
		<Compile Include="DigitalCampaign\Queries\CampaignPushCodeInfoForCostEstimationData.cs" />
		<Compile Include="DigitalCampaign\Queries\CampaignSummaryForCostEstimationData.cs" />
		<Compile Include="DigitalChannelMessageTemplate\DigitalChannelMessageTemplateEntity.cs" />
		<Compile Include="DigitalChannelMessageTemplate\Queries\DigitalChannelMessageTemplateData.cs" />
		<Compile Include="DigitalChannel\DigitalChannelEntity.cs" />
		<Compile Include="DigitalChannel\DigitalContactTypeEntity.cs" />
		<Compile Include="DigitalChannel\DigitalServiceConfigurationEntity.cs" />
		<Compile Include="DigitalChannel\Queries\DigitalChannelData.cs" />
		<Compile Include="DigitalChannel\Queries\DigitalContactTypeData.cs" />
		<Compile Include="DigitalChannel\Queries\GetDigitalChannelByCodeQuery.cs" />
		<Compile Include="DigitalChannel\Queries\GetDigitalChannelByIdQuery.cs" />
		<Compile Include="DigitalChannel\Queries\GetDigitalChannelListQuery.cs" />
		<Compile Include="DigitalChannel\Queries\GetDigitalContactTypeByIdQuery.cs" />
		<Compile Include="DigitalChannel\Queries\GetDigitalContactTypeListByCampaignQuery.cs" />
		<Compile Include="DigitalChannel\Queries\GetDigitalContactTypeListQuery.cs" />
		<Compile Include="DigitalChannel\Queries\GetDigitalServiceConfigurationQuery.cs" />
		<Compile Include="DigitalContact\Commands\AddAnonymousDigitalContactListToCampaignCommand.cs" />
		<Compile Include="DigitalContact\Commands\AddCustomerDigitalContactToCampaignCommand.cs" />
		<Compile Include="DigitalContact\Commands\AddDigitalContactsToCampaignCommand.cs" />
		<Compile Include="DigitalContact\Commands\CreateEditDigitalContactCommand.cs" />
		<Compile Include="DigitalContact\Commands\CreateLogLinkDigitalContactCommand.cs" />
		<Compile Include="DigitalContact\Commands\MergeCustomerAndUpdateLinkDigitalContactCommand.cs" />
		<Compile Include="DigitalContact\Commands\SetDigitalContactAnonymousByContactTypeCommand.cs" />
		<Compile Include="DigitalContact\Commands\UpdateTokenDigitalContactCommand.cs" />
		<Compile Include="DigitalContact\DigitalContactData.cs" />
		<Compile Include="DigitalContact\DigitalContactEntity.cs" />
		<Compile Include="DigitalContact\ProspectDigitalContactEntity.cs" />
		<Compile Include="DigitalContact\Queries\CustomerDigitalContactListItem.cs" />
		<Compile Include="DigitalContact\Queries\CustomerDigitalContactSummaryItem.cs" />
		<Compile Include="DigitalContact\Queries\DigitalContactListItem.cs" />
		<Compile Include="DigitalContact\Queries\GetDigitalContactByCusIdQuery.cs" />
		<Compile Include="DigitalContact\Queries\GetDigitalContactByUserIdAndContactTypeQuery.cs" />
		<Compile Include="DigitalContact\Queries\GetDigitalContactInfoByCusIdOrDigitalIdQuery.cs" />
		<Compile Include="DigitalContact\Queries\GetDigitalResultInCampaignQuery.cs" />
		<Compile Include="DigitalContact\Queries\GetHistoryCustomerLinkDigitalContactByCustomerIdQuery.cs" />
		<Compile Include="DigitalContact\Queries\SearchAnonymousDigitalContactListToAddToCampaignQuery.cs" />
		<Compile Include="DigitalMessageTemplate\Command\CreateEditMessageTemplateCommand.cs" />
		<Compile Include="DigitalMessageTemplate\Command\DeleteChannelMessageTemplateCommand.cs" />
		<Compile Include="DigitalMessageTemplate\Command\DeleteMessageTemplateCommand.cs" />
		<Compile Include="DigitalMessageTemplate\DigitalMessageTemplateEntity.cs" />
		<Compile Include="DigitalMessageTemplate\Queries\GetDigitalTemplateListQuery.cs" />
		<Compile Include="DigitalMessageTemplate\Queries\GetMessageTemplateByIdQuery.cs" />
		<Compile Include="DigitalMessageTemplate\Queries\SearchDigitalMessageTemplateQuery.cs" />
		<Compile Include="DigitalMessage\Commands\UpdateDigitalMessageCommand.cs" />
		<Compile Include="DigitalMessage\Commands\UpdateSendMessageResultCommand.cs" />
		<Compile Include="DigitalMessage\DigitalDeliverMessagesData.cs" />
		<Compile Include="DigitalMessage\DigitalDeliverMessagesHistoryData.cs" />
		<Compile Include="DigitalMessage\DigitalMessageEntity.cs" />
		<Compile Include="DigitalMessage\DigitalMessageLogEntity.cs" />
		<Compile Include="DigitalMessage\Events\DigitalMessageStatusChangedEvent.cs" />
		<Compile Include="DigitalMessage\Queries\GetDigitalDeliverMessageByIdQuery.cs" />
		<Compile Include="DigitalMessage\Queries\GetDigitalDeliverMessagesHistoryQuery.cs" />
		<Compile Include="DigitalMessage\Queries\GetDigitalDeliverMessagesQuery.cs" />
		<Compile Include="DigitalPushCode\Commands\CreateEditPushCodeCommand.cs" />
		<Compile Include="DigitalPushCode\Commands\CreateEditPushCodeOnChannelCommand.cs" />
		<Compile Include="DigitalPushCode\Commands\CreateEditPushCodeOnChannelItemCommand.cs" />
		<Compile Include="DigitalPushCode\Commands\DeleteDigitalPushCodeCommand.cs" />
		<Compile Include="DigitalPushCode\Commands\DeletePushCodeOnChannelItemCommand.cs" />
		<Compile Include="DigitalPushCode\DigitalPushCodeEntity.cs" />
		<Compile Include="DigitalPushCode\DigitalPushCodeOnChannelEntity.cs" />
		<Compile Include="DigitalPushCode\Queries\DigitalPushCodeData.cs" />
		<Compile Include="DigitalPushCode\Queries\DigitalPushCodeOnChannelData.cs" />
		<Compile Include="DigitalPushCode\Queries\DigitalPushCodeOnChannelInfo.cs" />
		<Compile Include="DigitalPushCode\Queries\GetDigitalPushCodeByCodeNameQuery.cs" />
		<Compile Include="DigitalPushCode\Queries\GetDigitalPushCodeByIdQuery.cs" />
		<Compile Include="DigitalPushCode\Queries\GetDigitalPushCodeListQuery.cs" />
		<Compile Include="DigitalPushCode\Queries\GetDigitalPushCodeOnChannelByIdQuery.cs" />
		<Compile Include="DigitalPushCode\Queries\GetDigitalPushCodeOnChannelInfoListQuery.cs" />
		<Compile Include="DigitalPushCode\Queries\GetDigitalPushCodeOnChannelListQuery.cs" />
		<Compile Include="DynamicDefinedTable\AutoMapperRegistration.cs" />
		<Compile Include="DynamicDefinedTable\Commands\CreateEditDynamicDefinedTableCellValueListCommand.cs" />
		<Compile Include="DynamicDefinedTable\Commands\CreateEditFileCommand.cs" />
		<Compile Include="DynamicDefinedTable\Commands\CreateLinkedTicketColumnByDynamicTableColumnCommand.cs" />
		<Compile Include="DynamicDefinedTable\Commands\DeleteDynamicDefinedTableCellValuesCommand.cs" />
		<Compile Include="DynamicDefinedTable\Commands\DeleteDynamicDefinedTableRowCommand.cs" />
		<Compile Include="DynamicDefinedTable\Commands\EditLinkedTicketColumnByDynamicTableColumnCommand.cs" />
		<Compile Include="DynamicDefinedTable\Commands\SaveColumnFormulaListCommand.cs" />
		<Compile Include="DynamicDefinedTable\Commands\SaveDynamicDefinedTableColumnInjectsCommand.cs" />
		<Compile Include="DynamicDefinedTable\Commands\SyncOwnDbTableColumnForDynamicDefinedTableSchemaCommand.cs" />
		<Compile Include="DynamicDefinedTable\Commands\UpdateEntityLinkByLinkedTicketColumnCommand.cs" />
		<Compile Include="DynamicDefinedTable\DynamicDefinedTableCellValueEntity.cs" />
		<Compile Include="DynamicDefinedTable\DynamicDefinedTableColumnEntity.cs" />
		<Compile Include="DynamicDefinedTable\DynamicDefinedTableColumnOnDynamicFormEntity.cs" />
		<Compile Include="DynamicDefinedTable\DynamicDefinedTableLinkedTicketColumnEntity.cs" />
		<Compile Include="DynamicDefinedTable\DynamicDefinedTableRowJsonValueEntity.cs" />
		<Compile Include="DynamicDefinedTable\DynamicDefinedTableSchemaEntity.cs" />
		<Compile Include="DynamicDefinedTable\IDynamicDefinedTableUtility.cs" />
		<Compile Include="DynamicDefinedTable\Queries\CalculateDynamicFieldWithDynamicTableQuery.cs" />
		<Compile Include="DynamicDefinedTable\Queries\DynamicDefinedTableCellValueData.cs" />
		<Compile Include="DynamicDefinedTable\Queries\DynamicDefinedTableColumnData.cs" />
		<Compile Include="DynamicDefinedTable\Queries\DynamicDefinedTableFileCellValue.cs" />
		<Compile Include="DynamicDefinedTable\Queries\DynamicDefinedTableLinkedTicketColumnData.cs" />
		<Compile Include="DynamicDefinedTable\Queries\DynamicDefinedTableSchemaData.cs" />
		<Compile Include="DynamicDefinedTable\Queries\GetDynamicDefinedTableCellValueListByDynamicFieldValueIdQuery.cs" />
		<Compile Include="DynamicDefinedTable\Queries\GetDynamicDefinedTableCellValuesQuery.cs" />
		<Compile Include="DynamicDefinedTable\Queries\GetDynamicDefinedTableColumnByIdQuery.cs" />
		<Compile Include="DynamicDefinedTable\Queries\GetDynamicDefinedTableColumnListByTableSchemaQuery.cs" />
		<Compile Include="DynamicDefinedTable\Queries\GetDynamicDefinedTableColumnOnFormListByTableSchemaQuery.cs" />
		<Compile Include="DynamicDefinedTable\Queries\GetDynamicDefinedTableLinkedTicketColumnListByColumnIdQuery.cs" />
		<Compile Include="DynamicDefinedTable\Queries\GetDynamicDefinedTableLinkedTicketColumnListByIdQuery.cs" />
		<Compile Include="DynamicDefinedTable\Queries\GetDynamicDefinedTableSchemaQuery.cs" />
		<Compile Include="DynamicDefinedTable\Queries\PreviewDataStructWithDynamicTableQuery.cs" />
		<Compile Include="DynamicForm\Command\CloneDynamicFormCommand.cs" />
		<Compile Include="DynamicForm\Command\CreateEditDynamicFieldDefinitionCommandHandler.cs" />
		<Compile Include="DynamicForm\Command\DeleteDynamicFormCommand.cs" />
		<Compile Include="DynamicForm\Command\ReCalculateDynamicFormValueCommand.cs" />
		<Compile Include="DynamicForm\DynamicFieldHelper.cs" />
		<Compile Include="DynamicForm\IDynamicFormUtility.cs" />
		<Compile Include="DynamicForm\IDynamicFormValueUtility.cs" />
		<Compile Include="DynamicForm\Queries\CalculateDynamicFieldQueryHandler.cs" />
		<Compile Include="DynamicForm\Queries\CheckDynamicFieldValueDupOnTaskQuery.cs" />
		<Compile Include="DynamicForm\Queries\EditorControlDynamicFormData.cs" />
		<Compile Include="DynamicForm\Queries\GetAllDynamicFieldDefinitionInfoQuery.cs" />
		<Compile Include="DynamicForm\Queries\GetAllDynamicFormByLevelQuery.cs" />
		<Compile Include="DynamicForm\Queries\GetAllDynamicFormByServiceTypeIdQuery.cs" />
		<Compile Include="DynamicForm\Queries\GetCalculatedFieldsTreeQuery.cs" />
		<Compile Include="DynamicForm\Queries\GetDynamicFieldByFormIdAndNameQuery.cs" />
		<Compile Include="DynamicForm\Queries\GetDynamicFieldByFormIdQuery.cs" />
		<Compile Include="DynamicForm\Queries\GetDynamicFormByEntityLinkTicketQuery.cs" />
		<Compile Include="DynamicForm\Queries\GetRefObjectOfDynamicFormQuery.cs" />
		<Compile Include="DynamicForm\Queries\MemoryCalculateDynamicFieldQuery.cs" />
		<Compile Include="DynamicTable\CreateEditDynamicTableColumnCommand.cs" />
		<Compile Include="DynamicTable\CreateEditDynamicTableColumnKPICommand.cs" />
		<Compile Include="DynamicTable\CreateEditDynamicTableColumnOnDynamicFormCommand.cs" />
		<Compile Include="DynamicTable\CreateEditDynamicTableCommand.cs" />
		<Compile Include="DynamicTable\DeleteDynamicTableColumnCommand.cs" />
		<Compile Include="DynamicTable\DeleteDynamicTableCommand.cs" />
		<Compile Include="DynamicTable\GetTableColumnHideOnDynamicFormQuery.cs" />
		<Compile Include="DynamicTable\GetTableColumnOnDynamicFormQuery.cs" />
		<Compile Include="DynamicTable\SearchDynamicTableColumnQuery.cs" />
		<Compile Include="DynamicTable\SearchDynamicTableQuery.cs" />
		<Compile Include="ECommerce\Commands\CreateECommerceUploadedFileCommand.cs" />
		<Compile Include="ECommerce\ECommerceUploadedFileEntity.cs" />
		<Compile Include="Endorsement\EndorsementContestEntity.cs" />
		<Compile Include="EntityLink\Commands\AssignEntityLinkCommand.cs" />
		<Compile Include="EntityLink\Commands\CreateEditEntityLinkCommand.cs" />
		<Compile Include="EntityLink\Commands\CreateOrRefreshLinkTicketCommand.cs" />
		<Compile Include="EntityLink\Commands\DeleteEntityLinkCommand.cs" />
		<Compile Include="EntityLink\EntityLinkBusinessSpecificEntity.cs" />
		<Compile Include="EntityLink\EntityLinkEntity.cs" />
		<Compile Include="EntityLink\Queries\EntityLinkBusinessSpecificData.cs" />
		<Compile Include="EntityLink\Queries\EntityLinkData.cs" />
		<Compile Include="EntityLink\Queries\GeneralLinkEntityListData.cs" />
		<Compile Include="EntityLink\Queries\GetEntityLinkBusinessSpecificByNameQuery.cs" />
		<Compile Include="EntityLink\Queries\GetEntityLinkBusinessSpecificListQuery.cs" />
		<Compile Include="EntityLink\Queries\GetEntityLinkBusinessSpecificQuery.cs" />
		<Compile Include="EntityLink\Queries\GetEntityLinkListQuery.cs" />
		<Compile Include="EntityLink\Queries\GetTicketForAddToEntityLinkQuery.cs" />
		<Compile Include="Enums\AssignmentStatus.cs" />
		<Compile Include="Enums\Behavior.cs" />
		<Compile Include="Enums\CampaignStatus.cs" />
		<Compile Include="Enums\CampaignType.cs" />
		<Compile Include="Enums\Channel.cs" />
		<Compile Include="Enums\CheckStatus.cs" />
		<Compile Include="Enums\CustomerType.cs" />
		<Compile Include="Enums\Difficulty.cs" />
		<Compile Include="Enums\DigitalMessageResultCode.cs" />
		<Compile Include="Enums\DigitalMessageStatus.cs" />
		<Compile Include="Enums\ExchangeDataCompareOptions.cs" />
		<Compile Include="Enums\ExchangeSourceType.cs" />
		<Compile Include="Enums\Gender.cs" />
		<Compile Include="Enums\GeolocationType.cs" />
		<Compile Include="Enums\OrganizationType.cs" />
		<Compile Include="Enums\PartEnum.cs" />
		<Compile Include="Enums\PrimaryCustomer.cs" />
		<Compile Include="Enums\RequestTicketStatus.cs" />
		<Compile Include="Enums\RetrievalStatus.cs" />
		<Compile Include="Enums\SchedulerType.cs" />
		<Compile Include="Enums\ServiceCategoryType.cs" />
		<Compile Include="Enums\TaskEnum.cs" />
		<Compile Include="Enums\TaskTypeEnum.cs" />
		<Compile Include="Enums\UsedStatus.cs" />
		<Compile Include="Enums\WorkMode.cs" />
		<Compile Include="ExpenseItem\Commands\BulkInsertExpenseItemCommand.cs" />
		<Compile Include="ExpenseItem\Commands\CommitContestExpenseCommand.cs" />
		<Compile Include="ExpenseItem\Commands\UpdateExpenseItemsCommand.cs" />
		<Compile Include="ExpenseItem\CommitContestExpenseResult.cs" />
		<Compile Include="ExpenseItem\ExpenseItemData.cs" />
		<Compile Include="ExpenseItem\ExpenseItemEntity.cs" />
		<Compile Include="ExpenseItem\Queries\GetExpenseItemByContestQuery.cs" />
		<Compile Include="ExternalApiEndpoint\Commands\CreateEditExternalApiEndpointCommand.cs" />
		<Compile Include="ExternalApiEndpoint\ExternalApiEndpointEntity.cs" />
		<Compile Include="ExternalApiEndpoint\Queries\ExternalApiEndpointData.cs" />
		<Compile Include="ExternalApiEndpoint\Queries\GetExternalApiEndpointByIdQuery.cs" />
		<Compile Include="ExternalApiEndpoint\Queries\GetExternalApiEndpointListQuery.cs" />
		<Compile Include="FeeCategoryPart\Commands\CleanupImportFeeCategoryPartsCommand.cs" />
		<Compile Include="FeeCategoryPart\Commands\CreateEditFeeCategoryPartCommand.cs" />
		<Compile Include="FeeCategoryPart\Commands\DeleteFeeCategoryPartCommand.cs" />
		<Compile Include="FeeCategoryPart\Commands\DoImportFeeCategoryPartsCommand.cs" />
		<Compile Include="FeeCategoryPart\Commands\ImportStagingFeeCategoryPartsCommand.cs" />
		<Compile Include="FeeCategoryPart\Commands\ScanImportFeeCategoryPartsCommand.cs" />
		<Compile Include="FeeCategoryPart\FeeCategoryPartEntity.cs" />
		<Compile Include="FeeCategoryPart\Queries\FeeCategoryPartData.cs" />
		<Compile Include="FeeCategoryPart\Queries\FeeCategoryPartInfo.cs" />
		<Compile Include="FeeCategoryPart\Queries\FeeCategoryPartListByMonthlyQuery.cs" />
		<Compile Include="FeeCategoryPart\Queries\GetFeeCategoryPartByIdQuery.cs" />
		<Compile Include="FeeCategoryPart\Queries\GetFeeCategoryPartInfoListQuery.cs" />
		<Compile Include="FeeCategoryPart\StagingFeeCategoryPartsEntity.cs" />
		<Compile Include="FeeCategory\Commands\CreateEditFeeCategoryCommand.cs" />
		<Compile Include="FeeCategory\Commands\CreateEditFeeCategoryFormulaCommand.cs" />
		<Compile Include="FeeCategory\Commands\CreateFeeCategoryTaxCommand.cs" />
		<Compile Include="FeeCategory\Commands\DeleteFeeCategoryCommand.cs" />
		<Compile Include="FeeCategory\Commands\DeleteFeeCategoryFormulaCommand.cs" />
		<Compile Include="FeeCategory\Commands\DeleteFeeCategoryTaxCommand.cs" />
		<Compile Include="FeeCategory\FeeCategoryEntity.cs" />
		<Compile Include="FeeCategory\FeeCategoryFormulaEntity.cs" />
		<Compile Include="FeeCategory\FeeCategoryTaxEntity.cs" />
		<Compile Include="FeeCategory\Queries\FeeCategoryData.cs" />
		<Compile Include="FeeCategory\Queries\FeeCategoryFormulaData.cs" />
		<Compile Include="FeeCategory\Queries\FeeCategoryTaxData.cs" />
		<Compile Include="FeeCategory\Queries\FeeCategoryTaxInfo.cs" />
		<Compile Include="FeeCategory\Queries\GetFeeCategoryByIdQuery.cs" />
		<Compile Include="FeeCategory\Queries\GetFeeCategoryFormulaListQuery.cs" />
		<Compile Include="FeeCategory\Queries\GetFeeCategoryListQuery.cs" />
		<Compile Include="FeeCategory\Queries\GetFeeCategoryTaxInfoListQuery.cs" />
		<Compile Include="FinishRequestTicketByImport\Commands\BatchUpdateSoonerProcessMinutesCommand.cs" />
		<Compile Include="FinishRequestTicketByImport\Commands\BulkInsertDoneRequestTicketStagingCommand.cs" />
		<Compile Include="FinishRequestTicketByImport\Commands\CreateEditFinishRequestTicketByImportCommand.cs" />
		<Compile Include="FinishRequestTicketByImport\Commands\ProcessDoneRequestTicketImportSessionCommand.cs" />
		<Compile Include="FinishRequestTicketByImport\Commands\ScanDoneRequestTicketImportSessionCommand.cs" />
		<Compile Include="FinishRequestTicketByImport\DoneRequestTicketImportSessionEntity.cs" />
		<Compile Include="FinishRequestTicketByImport\Queries\DoneRequestTicketImportSessionData.cs" />
		<Compile Include="FinishRequestTicketByImport\Queries\DoneRequestTicketImportSessionResultItem.cs" />
		<Compile Include="FinishRequestTicketByImport\Queries\GetDoneRequestTicketImportSessionByIdQuery.cs" />
		<Compile Include="FinishRequestTicketByImport\Queries\GetDoneRequestTicketImportSessionResultDetailQuery.cs" />
		<Compile Include="FinishRequestTicketByImport\Queries\GetDoneRequestTicketListImportSessionQuery.cs" />
		<Compile Include="FinishRequestTicketByImport\Queries\ScanDoneRequestTicketImportSessionResultQuery.cs" />
		<Compile Include="GanttProject\Command\CreateGanttProjectCommand.cs" />
		<Compile Include="GanttProject\GanttProject_TaskDependencyEntity.cs" />
		<Compile Include="Geolocation\EmployeeGeolocationEntity.cs" />
		<Compile Include="Geolocation\GeolocationEntity.cs" />
		<Compile Include="Geolocation\Queries\GeolocationData.cs" />
		<Compile Include="Geolocation\Queries\GetAllGeolocationQuery.cs" />
		<Compile Include="Geolocation\Queries\GetGeolocationByIdQuery.cs" />
		<Compile Include="Geolocation\Queries\GetGeolocationByParentIdQuery.cs" />
		<Compile Include="Geolocation\Queries\GetGeolocationByTypeQuery.cs" />
		<Compile Include="GridDynamicField\DynamicFieldFinishStepConentGrid\AutoMapperRegistration.cs" />
		<Compile Include="GridDynamicField\DynamicFieldFinishStepConentGrid\DynamicFieldFinishStepConentListItemEntity.cs" />
		<Compile Include="GridDynamicField\DynamicFieldFinishStepConentGrid\Queries\DynamicFieldFinishStepConentListItemData.cs" />
		<Compile Include="GridDynamicField\DynamicFieldFinishStepConentGrid\Queries\GetDynamicFieldFinishStepConentListItemDataByValueGroupIdQuery.cs" />
		<Compile Include="GridDynamicField\DynamicFieldKnowledgeGrid\AutoMapperRegistration.cs" />
		<Compile Include="GridDynamicField\DynamicFieldKnowledgeGrid\DynamicFieldKnowledgeListItemEntity.cs" />
		<Compile Include="GridDynamicField\DynamicFieldKnowledgeGrid\Queries\DynamicFieldKnowledgeListItemData.cs" />
		<Compile Include="GridDynamicField\DynamicFieldKnowledgeGrid\Queries\GetDynamicFieldKnowledgeListItemDataByValueGroupIdQuery.cs" />
		<Compile Include="GridDynamicField\DynamicFieldVerifierListItem\AutoMapperRegistration.cs" />
		<Compile Include="GridDynamicField\DynamicFieldVerifierListItem\DynamicFieldVerifierListItemEntity.cs" />
		<Compile Include="GridDynamicField\DynamicFieldVerifierListItem\Queries\DynamicFieldVerifierListItemData.cs" />
		<Compile Include="GridDynamicField\DynamicFieldVerifierListItem\Queries\GetDynamicFieldVerifierListItemDataByValueGroupIdQuery.cs" />
		<Compile Include="GridDynamicField\DynamicFieldVerifyResultGrid\AutoMapperRegistration.cs" />
		<Compile Include="GridDynamicField\DynamicFieldVerifyResultGrid\DynamicFieldVerifyResultListItemEntity.cs" />
		<Compile Include="GridDynamicField\DynamicFieldVerifyResultGrid\Queries\DynamicFieldVerifyResultListItemData.cs" />
		<Compile Include="GridDynamicField\DynamicFieldVerifyResultGrid\Queries\GetDynamicFieldVerifyResultListItemDataByValueGroupIdQuery.cs" />
		<Compile Include="HubEvents\LandingUploadFileEvent.cs" />
		<Compile Include="Image\Commands\DeleteImageCommand.cs" />
		<Compile Include="Image\Commands\InsertImageCommand.cs" />
		<Compile Include="Image\Commands\InsertListImageCommand.cs" />
		<Compile Include="Image\ImageEntity.cs" />
		<Compile Include="Image\Queries\GetImageByIdQuery.cs" />
		<Compile Include="Image\Queries\GetImageInfoByIdQuery.cs" />
		<Compile Include="Image\Queries\ImageData.cs" />
		<Compile Include="ImportB2BCustomersSession\Commands\CleanupImportB2BCustomersCommand.cs" />
		<Compile Include="ImportB2BCustomersSession\Commands\CreateEditImportB2BCustomersSessionCommand.cs" />
		<Compile Include="ImportB2BCustomersSession\Commands\DoImportB2BCustomersCommand.cs" />
		<Compile Include="ImportB2BCustomersSession\Commands\GeolocationScanImportB2BCustomersCommand.cs" />
		<Compile Include="ImportB2BCustomersSession\Commands\ScanImportB2BClassificationsCommand.cs" />
		<Compile Include="ImportB2BCustomersSession\Commands\ScanImportB2BCustomersCommand.cs" />
		<Compile Include="ImportB2BCustomersSession\ImportB2BCustomersSessionEntity.cs" />
		<Compile Include="ImportB2BCustomersSession\Queries\GetImportB2BCustomersResultsQuery.cs" />
		<Compile Include="ImportB2BCustomersSession\Queries\GetImportB2BDataStatisticsQuery.cs" />
		<Compile Include="ImportB2BCustomersSession\Queries\ImportB2BCustomersResultItem.cs" />
		<Compile Include="ImportB2BCustomersSession\StagingCustomersEntity.cs" />
		<Compile Include="ImportCustomerRaw\ImportCustomerSessionEntity.cs" />
		<Compile Include="ImportCustomerSession\Commands\CreateEditImportCustomerSessionCommand.cs" />
		<Compile Include="ImportCustomerSession\Commands\InsertImportStagingCustomersCommand.cs" />
		<Compile Include="ImportCustomerSession\Commands\ProcessImportStagingCustomerCommand.cs" />
		<Compile Include="ImportCustomerSession\ImportCustomerSessionEntity.cs" />
		<Compile Include="ImportRequestTicket\AutoMapperRegistration.cs" />
		<Compile Include="ImportRequestTicket\Commands\CancelImportRequestTicketSessionCommand.cs" />
		<Compile Include="ImportRequestTicket\Commands\CreateEditImportRequestTicketSessionCommand.cs" />
		<Compile Include="ImportRequestTicket\Commands\InsertImportRequestTicketDataItemListCommand.cs" />
		<Compile Include="ImportRequestTicket\ImportRequestTicketDataItemEntity.cs" />
		<Compile Include="ImportRequestTicket\ImportRequestTicketSessionCancelledEntity.cs" />
		<Compile Include="ImportRequestTicket\ImportRequestTicketSessionEntity.cs" />
		<Compile Include="ImportRequestTicket\Queries\GetImportRequestTicketByIdQuery.cs" />
		<Compile Include="ImportRequestTicket\Queries\GetImportRequestTicketDataItemListQuery.cs" />
		<Compile Include="ImportRequestTicket\Queries\GetImportRequestTicketHistoriesQuery.cs" />
		<Compile Include="ImportRequestTicket\Queries\ImportRequestTicketDataItemData.cs" />
		<Compile Include="ImportRequestTicket\Queries\ImportRequestTicketSessionData.cs" />
		<Compile Include="ImportRequestTicket\Queries\ImportRequestTicketSessionListItem.cs" />
		<Compile Include="ImportSOSession\Commands\CleanUpImportSOCommand.cs" />
		<Compile Include="ImportSOSession\Commands\CreateEditImportSOSessionCommand.cs" />
		<Compile Include="ImportSOSession\Commands\DoImportSOCommand.cs" />
		<Compile Include="ImportSOSession\Commands\InsertStagingSOCommand.cs" />
		<Compile Include="ImportSOSession\Commands\ScanImportSOCommand.cs" />
		<Compile Include="ImportSOSession\Commands\ScanImportSODistributorCommand.cs" />
		<Compile Include="ImportSOSession\Commands\ScanImportSOProductCommand.cs" />
		<Compile Include="ImportSOSession\Commands\ScanImportSORequestTicketCommand.cs" />
		<Compile Include="ImportSOSession\ImportSOSessionEntity.cs" />
		<Compile Include="ImportSOSession\Queries\GetImportSODataStatisticsQuery.cs" />
		<Compile Include="ImportSOSession\Queries\GetImportSOResultsQuery.cs" />
		<Compile Include="ImportSOSession\Queries\GetImportSOSessionDateTimeRangeQuery.cs" />
		<Compile Include="ImportSOSession\Queries\ImportSOResultItem.cs" />
		<Compile Include="ImportTask\AutoMapperRegistration.cs" />
		<Compile Include="ImportTask\Commands\CancelImportTaskSessionCommand.cs" />
		<Compile Include="ImportTask\Commands\CreateEditImportTaskSessionCommand.cs" />
		<Compile Include="ImportTask\Commands\InsertImportTaskDataItemListCommand.cs" />
		<Compile Include="ImportTask\ImportTaskDataItemEntity.cs" />
		<Compile Include="ImportTask\ImportTaskSessionCancelledEntity.cs" />
		<Compile Include="ImportTask\ImportTaskSessionEntity.cs" />
		<Compile Include="ImportTask\Queries\GetImportTaskByIdQuery.cs" />
		<Compile Include="ImportTask\Queries\GetImportTaskDataItemListQuery.cs" />
		<Compile Include="ImportTask\Queries\GetImportTaskListHistoriesQuery.cs" />
		<Compile Include="ImportTask\Queries\ImportTaskDataItemData.cs" />
		<Compile Include="ImportTask\Queries\ImportTaskSessionData.cs" />
		<Compile Include="ImportTask\Queries\ImportTaskSessionListItem.cs" />
		<Compile Include="ImportTask\Tasks\BatchUpdateTaskListTask.cs" />
		<Compile Include="Import\ImportDataStatisticItem.cs" />
		<Compile Include="InfoList\Commands\CreateEditInfoListCommand.cs" />
		<Compile Include="InfoList\InfoListEntity.cs" />
		<Compile Include="InfoList\Queries\GetInfoListByIdQuery.cs" />
		<Compile Include="InfoList\Queries\GetInfoListByTypeQuery.cs" />
		<Compile Include="InfoList\Queries\InfoListData.cs" />
		<Compile Include="KnowledgeBase\Command\CreateEditKnowledgeItemCommand.cs" />
		<Compile Include="KnowledgeBase\Command\CreateEditKnowledgeReferenceCommand.cs" />
		<Compile Include="KnowledgeBase\Command\DeleteKnowledgeItemCommand.cs" />
		<Compile Include="KnowledgeBase\Command\UpdateConvertContentKnowledgeItemCommand.cs" />
		<Compile Include="KnowledgeBase\KnowledgeItemEntity.cs" />
		<Compile Include="KnowledgeBase\KnowledgeReferenceEntity.cs" />
		<Compile Include="KnowledgeBase\Queries\GetAllKnowledgeItemQuery.cs" />
		<Compile Include="KnowledgeBase\Queries\GetKnowledgeItemByIdDeepQuery.cs" />
		<Compile Include="KnowledgeBase\Queries\GetKnowledgeItemByIdQuery.cs" />
		<Compile Include="KnowledgeBase\Queries\GetKnowledgeItemByParentIdQuery.cs" />
		<Compile Include="KnowledgeBase\Queries\GetKnowledgeItemListQuery.cs" />
		<Compile Include="KnowledgeBase\Queries\KnowledgeItem.cs" />
		<Compile Include="KnowledgeBase\Queries\KnowledgeItemApiData.cs" />
		<Compile Include="KnowledgeBase\Queries\KnowledgeItemData.cs" />
		<Compile Include="KnowledgeBase\Queries\SearchKnowledgeItemByReferenceQuery.cs" />
		<Compile Include="KnowledgeBase\Queries\SearchKnowledgeItemQuery.cs" />
		<Compile Include="Mail\Commands\CreateEditMailCommand.cs" />
		<Compile Include="Mail\Events\MailProcessedEvent.cs" />
		<Compile Include="Mail\Mail.cs" />
		<Compile Include="Mail\MailEntity.cs" />
		<Compile Include="Mail\MailGateway.cs" />
		<Compile Include="Mail\MailLogEntity.cs" />
		<Compile Include="Mail\MailNotificationService.cs" />
		<Compile Include="Mail\MailScheduler.cs" />
		<Compile Include="Mail\MailStatus.cs" />
		<Compile Include="Mail\Queries\AttachmentFileInfo.cs" />
		<Compile Include="Mail\Queries\GetMailAttachmentQuery.cs" />
		<Compile Include="Mail\Queries\GetMailByIdQuery.cs" />
		<Compile Include="Mail\Queries\GetNewMailListQuery.cs" />
		<Compile Include="Mail\Queries\MailData.cs" />
		<Compile Include="MobileNotification\Commands\CloseMobileNotificationBatchCommand.cs" />
		<Compile Include="MobileNotification\Commands\CloseMobileNotificationCommand.cs" />
		<Compile Include="MobileNotification\Commands\CreateEditMobileNotificationCommand.cs" />
		<Compile Include="MobileNotification\Commands\CreateEditMobileNotificationResultCommand.cs" />
		<Compile Include="MobileNotification\Commands\DeleteMobileNotificationCommand.cs" />
		<Compile Include="MobileNotification\MobileApiResult.cs" />
		<Compile Include="MobileNotification\MobileNotificationData.cs" />
		<Compile Include="MobileNotification\MobileNotificationEntity.cs" />
		<Compile Include="MobileNotification\MobileNotificationPartCustomerEntity.cs" />
		<Compile Include="MobileNotification\MobileNotificationResultData.cs" />
		<Compile Include="MobileNotification\MobileNotificationResultEntity.cs" />
		<Compile Include="MobileNotification\Queries\GetMobileNotificationByIdQuery.cs" />
		<Compile Include="MobileNotification\Queries\GetNewMobileNotificationGroupListQuery.cs" />
		<Compile Include="MobileNotification\Queries\GetNewMobileNotificationQuery.cs" />
		<Compile Include="MobileNotification\Queries\MobileNotificationListItem.cs" />
		<Compile Include="MobileNotification\Queries\SearchMobileNotificationQuery.cs" />
		<Compile Include="MobileNotification\Tasks\MobileNotificationScheduledTask.cs" />
		<Compile Include="MonthlyPartFeeBatch\Commands\CalculateAndCreateMonthlyPartFeeBatchCommand.cs" />
		<Compile Include="MonthlyPartFeeBatch\Commands\CreateMonthlyPartFeeBatchCommand.cs" />
		<Compile Include="MonthlyPartFeeBatch\Commands\GenerateDebitNoteMonthlyPartFeeBatchCommand.cs" />
		<Compile Include="MonthlyPartFeeBatch\Commands\NotifyMonthlyPartFeeBatchCommand.cs" />
		<Compile Include="MonthlyPartFeeBatch\Commands\StartGenerateDebitNoteMonthlyPartFeeBatchCommand.cs" />
		<Compile Include="MonthlyPartFeeBatch\Commands\StartNotifyMonthlyPartFeeBatchCommand.cs" />
		<Compile Include="MonthlyPartFeeBatch\MonthlyPartFeeBatchEntity.cs" />
		<Compile Include="MonthlyPartFeeBatch\MonthlyPartFeeBatchItemEntity.cs" />
		<Compile Include="MonthlyPartFeeBatch\Queries\GetMonthlyPartFeeBatchByIdQuery.cs" />
		<Compile Include="MonthlyPartFeeBatch\Queries\GetMonthlyPartFeeBatchDataQuery.cs" />
		<Compile Include="MonthlyPartFeeBatch\Queries\GetMonthlyPartFeeItemListByBatchQuery.cs" />
		<Compile Include="MonthlyPartFeeBatch\Queries\GetNotProcessedMonthlyPartFeeItemByBatchQuery.cs" />
		<Compile Include="MonthlyPartFeeBatch\Queries\MonthlyPartFeeBatchData.cs" />
		<Compile Include="MonthlyPartFeeBatch\Queries\MonthlyPartFeeBatchItemData.cs" />
		<Compile Include="MonthlyPartFeeBatch\Tasks\GenerateDebitNoteMonthlyPartFeeBatchScheduledTask.cs" />
		<Compile Include="MonthlyPartFeeBatch\Tasks\NotifyMonthlyPartFeeBatchScheduledTask.cs" />
		<Compile Include="MonthlyPartFeeBatch\Tasks\ProcessMonthlyPartFeeBatchScheduledTask.cs" />
		<Compile Include="MonthlyPartFee\Commands\CalculateAndCreateMonthlyPartFeeCommand.cs" />
		<Compile Include="MonthlyPartFee\Commands\GenerateDebitNoteMonthlyPartFeeBatchItemCommand.cs" />
		<Compile Include="MonthlyPartFee\Commands\GenerateDebitNoteMonthlyPartFeeCommand.cs" />
		<Compile Include="MonthlyPartFee\Commands\GenerateReceiptMonthlyPartFeeCommand.cs" />
		<Compile Include="MonthlyPartFee\Commands\GenerateReceiptMonthlyPartFeeEmailCommand.cs" />
		<Compile Include="MonthlyPartFee\Commands\NotifyMonthlyPartFeeBatchItemCommand.cs" />
		<Compile Include="MonthlyPartFee\Commands\NotifyMonthlyPartFeeCommand.cs" />
		<Compile Include="MonthlyPartFee\Commands\ReCalculateAndCreateMonthlyPartFeeCommand.cs" />
		<Compile Include="MonthlyPartFee\Commands\RemindDebitNodeCommand.cs" />
		<Compile Include="MonthlyPartFee\Commands\RemindMonthlyPartFeeDebitNodeCommand.cs" />
		<Compile Include="MonthlyPartFee\Commands\UpdateMonthlyPartFeeStatusCommand.cs" />
		<Compile Include="MonthlyPartFee\Events\MonthlyPartFeeCreatedEvent.cs" />
		<Compile Include="MonthlyPartFee\ExportServices\IMonthlyFeeDetailExport.cs" />
		<Compile Include="MonthlyPartFee\IMonthlyFeeUtlities.cs" />
		<Compile Include="MonthlyPartFee\MonthlyFeeDetailExportTemplateEntity.cs" />
		<Compile Include="MonthlyPartFee\MonthlyPartFeeDebitReminderEntity.cs" />
		<Compile Include="MonthlyPartFee\MonthlyPartFeeEntity.cs" />
		<Compile Include="MonthlyPartFee\MonthlyPartFeeFileEntity.cs" />
		<Compile Include="MonthlyPartFee\MonthlyPartFeeItemEntity.cs" />
		<Compile Include="MonthlyPartFee\MonthlyPartFeeItemTaxEntity.cs" />
		<Compile Include="MonthlyPartFee\Queries\GetAllPartMonthlyFeeListQuery.cs" />
		<Compile Include="MonthlyPartFee\Queries\GetFeeCategoryTaxInfoListByMonthlyPartFeeQuery.cs" />
		<Compile Include="MonthlyPartFee\Queries\GetMonthFeeStatistics.cs" />
		<Compile Include="MonthlyPartFee\Queries\GetMonthlyFeeDetailExportTemplateByIdQuery.cs" />
		<Compile Include="MonthlyPartFee\Queries\GetMonthlyPartFeeByCodeQuery.cs" />
		<Compile Include="MonthlyPartFee\Queries\GetMonthlyPartFeeByIdQuery.cs" />
		<Compile Include="MonthlyPartFee\Queries\GetMonthlyPartFeeDebitNoteFileListByBatchQuery.cs" />
		<Compile Include="MonthlyPartFee\Queries\GetMonthlyPartFeeDebitReminderFileListQuery.cs" />
		<Compile Include="MonthlyPartFee\Queries\GetMonthlyPartFeeDebitReminderFileQuery.cs" />
		<Compile Include="MonthlyPartFee\Queries\GetMonthlyPartFeeDebitReminderListQuery.cs" />
		<Compile Include="MonthlyPartFee\Queries\GetMonthlyPartFeeFileByMonthlyPartFeeQuery.cs" />
		<Compile Include="MonthlyPartFee\Queries\GetMonthlyPartFeeItemDetailListQuery.cs" />
		<Compile Include="MonthlyPartFee\Queries\GetMonthlyPartFeeItemListQuery.cs" />
		<Compile Include="MonthlyPartFee\Queries\GetMonthlyPartFeeReceiptFileByMonthlyPartFeeQuery.cs" />
		<Compile Include="MonthlyPartFee\Queries\GetNextPartMonthlyFeeDebtByOwnerIdQuery.cs" />
		<Compile Include="MonthlyPartFee\Queries\GetPaidMonthlyPartFeeItemListQuery.cs" />
		<Compile Include="MonthlyPartFee\Queries\GetPartMonthlyFeeListByCustomerQuery.cs" />
		<Compile Include="MonthlyPartFee\Queries\GetPreviousPartMonthlyFeeDebtByOwnerIdQuery.cs" />
		<Compile Include="MonthlyPartFee\Queries\MonthlyFeeDetailExportTemplateData.cs" />
		<Compile Include="MonthlyPartFee\Queries\MonthlyPartFeeData.cs" />
		<Compile Include="MonthlyPartFee\Queries\MonthlyPartFeeDebitReminderInfo.cs" />
		<Compile Include="MonthlyPartFee\Queries\MonthlyPartFeeExportItemInfo.cs" />
		<Compile Include="MonthlyPartFee\Queries\MonthlyPartFeeFileData.cs" />
		<Compile Include="MonthlyPartFee\Queries\MonthlyPartFeeInfo.cs" />
		<Compile Include="MonthlyPartFee\Queries\MonthlyPartFeeItemData.cs" />
		<Compile Include="MonthlyPartFee\Queries\MonthlyPartFeeItemInfo.cs" />
		<Compile Include="MonthlyPartFee\Queries\ReportMonthlyPartFeeQuery.cs" />
		<Compile Include="MonthlyPartFee\Queries\SearchPartMonthlyFeeListQuery.cs" />
		<Compile Include="NotificationCase\Commands\CloseAllNotificationCaseByRootEntityCommand.cs" />
		<Compile Include="NotificationCase\Commands\CloseNotificationCaseByIdCommand.cs" />
		<Compile Include="NotificationCase\Commands\CloseNotificationCaseCommand.cs" />
		<Compile Include="NotificationCase\Commands\CreateNotificationCaseCommand.cs" />
		<Compile Include="NotificationCase\DefaultNotificationChannelContentTemplates.cs" />
		<Compile Include="NotificationCase\INotificationCaseService.cs" />
		<Compile Include="NotificationCase\NotificationCaseEntity.cs" />
		<Compile Include="NotificationCase\NotificationCaseServices\Outbound\OutboundReminderNotificationService.cs" />
		<Compile Include="NotificationCase\NotificationCaseServices\Task\AutoNextTaskNotFound_Message01_NotificationService.cs" />
		<Compile Include="NotificationCase\NotificationCaseServices\Task\AutoNextTaskNotFound_Message02_NotificationService.cs" />
		<Compile Include="NotificationCase\NotificationCaseServices\Task\AutoNextTaskNotFound_Message03_NotificationService.cs" />
		<Compile Include="NotificationCase\NotificationCaseServices\Task\AutoNextTaskNotFound_Message04_NotificationService.cs" />
		<Compile Include="NotificationCase\NotificationCaseServices\Task\TaskAcceptDueTimeParentTaskOwnerNotificationService.cs" />
		<Compile Include="NotificationCase\NotificationCaseServices\Task\TaskAcceptDueTimeTaskOwnerNotificationService.cs" />
		<Compile Include="NotificationCase\NotificationCaseServices\Task\TaskAcceptDueTimeTicketOwnerNotificationService.cs" />
		<Compile Include="NotificationCase\NotificationCaseServices\Task\TaskAssignedNotificationService.cs" />
		<Compile Include="NotificationCase\NotificationCaseServices\Task\TaskCommitedPowerUserNotificationService.cs" />
		<Compile Include="NotificationCase\NotificationCaseServices\Task\TaskCommitedTaskCreatorNotificationService.cs" />
		<Compile Include="NotificationCase\NotificationCaseServices\Task\TaskCommitedTicketOwnerNotificationService.cs" />
		<Compile Include="NotificationCase\NotificationCaseServices\Task\TaskCreateCustomerNotificationService.cs" />
		<Compile Include="NotificationCase\NotificationCaseServices\Task\TaskDoneCustomerNotificationService.cs" />
		<Compile Include="NotificationCase\NotificationCaseServices\Task\TaskDoneEndWorkflowParentTaskOwnerNotificationService.cs" />
		<Compile Include="NotificationCase\NotificationCaseServices\Task\TaskDoneParentTaskOwnerNotificationService.cs" />
		<Compile Include="NotificationCase\NotificationCaseServices\Task\TaskDoneTaskCreatorNotificationService.cs" />
		<Compile Include="NotificationCase\NotificationCaseServices\Task\TaskDoneTaskOwnerNotificationService.cs" />
		<Compile Include="NotificationCase\NotificationCaseServices\Task\TaskDoneTicketOwnerNotificationService.cs" />
		<Compile Include="NotificationCase\NotificationCaseServices\Task\TaskProcessDueTimeParentTaskOwnerNotificationService.cs" />
		<Compile Include="NotificationCase\NotificationCaseServices\Task\TaskProcessDueTimeTaskOwnerNotificationService.cs" />
		<Compile Include="NotificationCase\NotificationCaseServices\Task\TaskProcessDueTimeTicketOwnerNotificationService.cs" />
		<Compile Include="NotificationCase\NotificationCaseServices\Task\TaskReturnedNotificationService.cs" />
		<Compile Include="NotificationCase\NotificationCaseServices\Task\TaskSoonProcessDueTimeTaskOwnerNotificationService.cs" />
		<Compile Include="NotificationCase\NotificationCaseServices\Ticket\NoOwnerTicketAcceptDueTimeNotificationService.cs" />
		<Compile Include="NotificationCase\NotificationCaseServices\Ticket\NoOwnerTicketCreatedNotificationService.cs" />
		<Compile Include="NotificationCase\NotificationCaseServices\Ticket\NoOwnerTicketProcessDueTimeNotificationService.cs" />
		<Compile Include="NotificationCase\NotificationCaseServices\Ticket\TicketAcceptDueTimeCreatorNotificationService.cs" />
		<Compile Include="NotificationCase\NotificationCaseServices\Ticket\TicketAcceptDueTimeOwnerNotificationService.cs" />
		<Compile Include="NotificationCase\NotificationCaseServices\Ticket\TicketAcceptedCreatorNotificationService.cs" />
		<Compile Include="NotificationCase\NotificationCaseServices\Ticket\TicketAssignedOwnerNotificationService.cs" />
		<Compile Include="NotificationCase\NotificationCaseServices\Ticket\TicketFinishedCreatorNotificationService.cs" />
		<Compile Include="NotificationCase\NotificationCaseServices\Ticket\TicketFinishedCustomerNotificationService.cs" />
		<Compile Include="NotificationCase\NotificationCaseServices\Ticket\TicketProcessDueTimeOwnerNotificationService.cs" />
		<Compile Include="NotificationCase\NotificationCaseServices\Ticket\TicketSoonProcessDueTimeOwnerNotificationService.cs" />
		<Compile Include="NotificationCase\Queries\GetCloseNotificationCaseIdsWhenNotifyQuery.cs" />
		<Compile Include="NotificationCase\Queries\GetNewNotificationCaseListQuery.cs" />
		<Compile Include="NotificationCase\Queries\NotificationCaseData.cs" />
		<Compile Include="NotificationCase\Tasks\CleanUpAndBackUpNotificationScheduledTask.cs" />
		<Compile Include="NotificationCase\Tasks\NotificationCaseScheduledTask.cs" />
		<Compile Include="NotificationChanelSetting\Command\CreateEditCustomNotificationChannelSettingCommand.cs" />
		<Compile Include="NotificationChanelSetting\Command\CreateEditNotificationChanelSettingCommand.cs" />
		<Compile Include="NotificationChanelSetting\Command\DeleteCustomNotificationChannelSettingCommand.cs" />
		<Compile Include="NotificationChanelSetting\CustomNotificationChannelConditionServiceTypeEntity.cs" />
		<Compile Include="NotificationChanelSetting\CustomNotificationChannelSettingEntity.cs" />
		<Compile Include="NotificationChanelSetting\CustomNotificationChannelToUsersEntity.cs" />
		<Compile Include="NotificationChanelSetting\NotificationChanelSettingEntity.cs" />
		<Compile Include="NotificationChanelSetting\Queries\CustomNotificationChannelConditionServiceTypeData.cs" />
		<Compile Include="NotificationChanelSetting\Queries\CustomNotificationChannelSettingData.cs" />
		<Compile Include="NotificationChanelSetting\Queries\CustomNotificationChannelSettingListItem.cs" />
		<Compile Include="NotificationChanelSetting\Queries\CustomNotificationChannelToUsersData.cs" />
		<Compile Include="NotificationChanelSetting\Queries\GetCustomNotificationChannelSettingByIdQuery.cs" />
		<Compile Include="NotificationChanelSetting\Queries\GetCustomNotificationChannelSettingByNotificationChannelSettingIdQuery.cs" />
		<Compile Include="NotificationChanelSetting\Queries\GetCustomNotificationChannelToUserSelectorPathsQuery.cs" />
		<Compile Include="NotificationChanelSetting\Queries\GetNotificationChannelSettingByIdQuery.cs" />
		<Compile Include="NotificationChanelSetting\Queries\NotificationChannelSettingData.cs" />
		<Compile Include="NotificationChanelSetting\Queries\SearchNotificationChanelSettingQuery.cs" />
		<Compile Include="NotificationDefinitionContext.cs" />
		<Compile Include="Notification\Events\NotificationEvent.cs" />
		<Compile Include="Notification\Tasks\NotificationScheduledTask.cs" />
		<Compile Include="Order\Commands\Customers\InsertStagingECommerceCustomersCommand.cs" />
		<Compile Include="Order\Commands\Customers\ProcessStagingECommerceCustomersCommand.cs" />
		<Compile Include="Order\Commands\Customers\ScanStagingECommerceCustomersGeolocationCommand.cs" />
		<Compile Include="Order\Commands\OrderStatus\InsertStagingECommerceOrderStatusCommand.cs" />
		<Compile Include="Order\Commands\OrderStatus\UpdateOrderStatusesCommand.cs" />
		<Compile Include="Order\Commands\Orders\InsertStagingECommerceOrdersCommand.cs" />
		<Compile Include="Order\Commands\Orders\ProcessImportOrdersCommand.cs" />
		<Compile Include="Order\Commands\Orders\ScanReferenceObjectImportOrdersCommand.cs" />
		<Compile Include="Order\OrderCancelReasonEntity.cs" />
		<Compile Include="Order\OrderDetailEntity.cs" />
		<Compile Include="Order\OrderEntity.cs" />
		<Compile Include="Order\OrderForwardStatusEntity.cs" />
		<Compile Include="Order\OrderPOSStatusEntity.cs" />
		<Compile Include="Order\OrderTypeEntity.cs" />
		<Compile Include="Order\OrderVESStatusEntity.cs" />
		<Compile Include="Order\PaymentMethodEntity.cs" />
		<Compile Include="Order\PaymentStatusEntity.cs" />
		<Compile Include="Order\Queries\GetOrderByContactQuery.cs" />
		<Compile Include="Order\Queries\GetOrderByCustomerIdQuery.cs" />
		<Compile Include="Order\Queries\GetOrderByIdQuery.cs" />
		<Compile Include="Order\Queries\GetOrderDetailByOrderIdQuery.cs" />
		<Compile Include="Order\Queries\OrderData.cs" />
		<Compile Include="Order\Queries\OrderDetailData.cs" />
		<Compile Include="Order\SellChannelEntity.cs" />
		<Compile Include="Organization\Queries\GetAgentMemberQuery.cs" />
		<Compile Include="Organization\Queries\GetAvailableOrganizationQuery.cs" />
		<Compile Include="Organization\Queries\GetFactoryByCodeQuery.cs" />
		<Compile Include="Organization\Queries\GetFactoryQuery.cs" />
		<Compile Include="Organization\Queries\GetOrganizationByIdQuery.cs" />
		<Compile Include="Organization\Queries\GetOrganizationListQuery.cs" />
		<Compile Include="Organization\Queries\GetTaskServiceTypeDefaultOrganizationQuery.cs" />
		<Compile Include="Organization\Queries\GetTicketServiceTypeDefaultOrganizationQuery.cs" />
		<Compile Include="Organization\Queries\GetUserFuncBusinessPermissionConfigsInOrgQuery.cs" />
		<Compile Include="Organization\Queries\OrganizationData.cs" />
		<Compile Include="Outbound\AdditionalDataTemplate\AdditionalDataTemplateEntity.cs" />
		<Compile Include="Outbound\AdditionalDataTemplate\Commands\CreateAdditionalDataTemplateCommand.cs" />
		<Compile Include="Outbound\AdditionalDataTemplate\Queries\AdditionalDataTemplateInfo.cs" />
		<Compile Include="Outbound\AgentFieldSaleCouple\AgentFieldSaleCoupleData.cs" />
		<Compile Include="Outbound\AgentFieldSaleCouple\AgentFieldSaleCoupleEntity.cs" />
		<Compile Include="Outbound\AgentFieldSaleCouple\Queries\FieldSaleAppointmentInfo.cs" />
		<Compile Include="Outbound\AppointmentResultCode\AppointmentResultCodeEntity.cs" />
		<Compile Include="Outbound\AppointmentResultCode\Queries\AppointmentResultCodeData.cs" />
		<Compile Include="Outbound\AppointmentResultImport\AppointmentExcelImportResult.cs" />
		<Compile Include="Outbound\AppointmentResultImport\Queries\GetAppointmentExcelImportSummaryQuery.cs" />
		<Compile Include="Outbound\AppointmentResultImport\Queries\GetAppointmentImportExcelDetailQuery.cs" />
		<Compile Include="Outbound\Appointment\AppointmentEntity.cs" />
		<Compile Include="Outbound\Appointment\AppointmentImportResult.cs" />
		<Compile Include="Outbound\Appointment\AppointmentService.cs" />
		<Compile Include="Outbound\Appointment\Commands\DistributeAppointmentCoupleFieldSalesCommand.cs" />
		<Compile Include="Outbound\Appointment\Commands\SalesSupportDistributeAppointmentsCommand.cs" />
		<Compile Include="Outbound\Appointment\Queries\AppointmentData.cs" />
		<Compile Include="Outbound\Appointment\Queries\AppointmentDetailInfo.cs" />
		<Compile Include="Outbound\Appointment\Queries\AppointmentHistoryData.cs" />
		<Compile Include="Outbound\Appointment\Queries\AppointmentInfo.cs" />
		<Compile Include="Outbound\Appointment\Queries\Aviva\GetAppointmentSumaryForAgentQuery.cs" />
		<Compile Include="Outbound\Appointment\Queries\GetAgentCampaignAppointmentQuery.cs" />
		<Compile Include="Outbound\Appointment\Queries\GetAgentTeamAppointmentQuery.cs" />
		<Compile Include="Outbound\Appointment\Queries\GetAppointmentDetailInfoQuery.cs" />
		<Compile Include="Outbound\Appointment\Queries\GetAppointmentFieldSalesQuery.cs" />
		<Compile Include="Outbound\Appointment\Queries\GetAppointmentHistoriesQuery.cs" />
		<Compile Include="Outbound\Appointment\Queries\GetContactAppointmentHistoriesQuery.cs" />
		<Compile Include="Outbound\Appointment\Queries\GetCoupleFieldSalesQuery.cs" />
		<Compile Include="Outbound\Appointment\Queries\GetOldMeetAppointmentFieldSaleQuery.cs" />
		<Compile Include="Outbound\Appointment\Queries\GetOldNotMeetAppointmentFieldSaleTeamQuery.cs" />
		<Compile Include="Outbound\Appointment\Queries\GetSuggestionFieldSalesQuery.cs" />
		<Compile Include="Outbound\Appointment\Queries\GetTeamAssignedSummryListQuery.cs" />
		<Compile Include="Outbound\Appointment\Queries\ImportAppointmentQuery.cs" />
		<Compile Include="Outbound\Appointment\Queries\OldMeetAppointmentFieldSale.cs" />
		<Compile Include="Outbound\Appointment\Queries\OldNotMeetAppointmentFieldSaleTeam.cs" />
		<Compile Include="Outbound\Appointment\Queries\SuggestionAppointmentFieldSale.cs" />
		<Compile Include="Outbound\Appointment\Queries\TeamAssignedSummryInfo.cs" />
		<Compile Include="Outbound\Appointment\RawAppointment.cs" />
		<Compile Include="Outbound\AutoMapperRegistration.cs" />
		<Compile Include="Outbound\Brand\BrandEntity.cs" />
		<Compile Include="Outbound\Brand\Commands\EditBrandCommand.cs" />
		<Compile Include="Outbound\Brand\Events\BrandCreatedEvent.cs" />
		<Compile Include="Outbound\Brand\Events\BrandUpdatedEvent.cs" />
		<Compile Include="Outbound\Brand\Queries\BrandCompanyData.cs" />
		<Compile Include="Outbound\Brand\Queries\BrandData.cs" />
		<Compile Include="Outbound\Brand\Queries\GetAllBrandQuery.cs" />
		<Compile Include="Outbound\Brand\Queries\GetBrandListByCompanyListQuery.cs" />
		<Compile Include="Outbound\CallResult\CallResultCallPlanStrategyEntity.cs" />
		<Compile Include="Outbound\CallResult\CallResultEntity.cs" />
		<Compile Include="Outbound\CallResult\Commands\CreateEditCallResultCommand.cs" />
		<Compile Include="Outbound\CallResult\Commands\CreateEditCallStrategyCommand.cs" />
		<Compile Include="Outbound\CallResult\Commands\DeleteCallResultCommand.cs" />
		<Compile Include="Outbound\CallResult\Commands\DeleteCallStrategyCommand.cs" />
		<Compile Include="Outbound\CallResult\Commands\DeleteResultCodeSuiteCommand.cs" />
		<Compile Include="Outbound\CallResult\Commands\UpdateCallResultCommand.cs" />
		<Compile Include="Outbound\CallResult\Queries\CallResultData.cs" />
		<Compile Include="Outbound\CallResult\Queries\CallResultListItem.cs" />
		<Compile Include="Outbound\CallResult\Queries\GetCallResultListToAddToTemplateQuery.cs" />
		<Compile Include="Outbound\CallResult\Queries\ResultCodeSuiteData.cs" />
		<Compile Include="Outbound\CallResult\Queries\SearchCallResultListQuery.cs" />
		<Compile Include="Outbound\CallResult\Queries\SearchCallResultsQuery.cs" />
		<Compile Include="Outbound\CallResult\ResultCodeSuiteContants.cs" />
		<Compile Include="Outbound\CampaignCompletionCode\CampaignCompletionCodeEntity.cs" />
		<Compile Include="Outbound\CampaignCompletionCode\Events\CampaignCompletionCodeCreatedEvent.cs" />
		<Compile Include="Outbound\CampaignCompletionCode\Events\CampaignCompletionCodeDeletedEvent.cs" />
		<Compile Include="Outbound\CampaignTemplateCode\CampaignTemplateCodeEntity.cs" />
		<Compile Include="Outbound\CampaignTemplateCode\Events\CompletionCategoryCreatedEvent.cs" />
		<Compile Include="Outbound\CampaignTemplateCode\Events\CompletionCategoryUpdatedEvent.cs" />
		<Compile Include="Outbound\CampaignTemplateCode\Queries\CampaignTemplateCodeData.cs" />
		<Compile Include="Outbound\CampaignTemplateCode\Queries\GetCampaignTemplateCodeListAddToCallResultQuery.cs" />
		<Compile Include="Outbound\Campaign\CampaignCoreServiceCallbackEntity.cs" />
		<Compile Include="Outbound\Campaign\CampaignEntity.cs" />
		<Compile Include="Outbound\Campaign\CampaignExecutingTimeEntity.cs" />
		<Compile Include="Outbound\Campaign\CampaignParameterEntity.cs" />
		<Compile Include="Outbound\Campaign\CampaignSearchAndRegainContactDynamicModel.cs" />
		<Compile Include="Outbound\Campaign\CampaignTeamAssignmentEntity.cs" />
		<Compile Include="Outbound\Campaign\Commands\CreateEditCampaignCommand.cs" />
		<Compile Include="Outbound\Campaign\Commands\CreateEditCoreServiceCallBackCommand.cs" />
		<Compile Include="Outbound\Campaign\Commands\DeleteCampaignCommand.cs" />
		<Compile Include="Outbound\Campaign\DistributionPolicySetting.cs" />
		<Compile Include="Outbound\Campaign\Events\CampaignCreatedEvent.cs" />
		<Compile Include="Outbound\Campaign\Events\CampaignDeletedEvent.cs" />
		<Compile Include="Outbound\Campaign\Events\ChienDichUpdatedEvent.cs" />
		<Compile Include="Outbound\Campaign\Queries\AgentCampaignAssignmentListItem.cs" />
		<Compile Include="Outbound\Campaign\Queries\CampaignAssignedListItem.cs" />
		<Compile Include="Outbound\Campaign\Queries\CampaignCoreServiceCallbackData.cs" />
		<Compile Include="Outbound\Campaign\Queries\CampaignCustomerDigitalContactListItem.cs" />
		<Compile Include="Outbound\Campaign\Queries\CampaignData.cs" />
		<Compile Include="Outbound\Campaign\Queries\CampaignExecutingTimeData.cs" />
		<Compile Include="Outbound\Campaign\Queries\CampaignListItem.cs" />
		<Compile Include="Outbound\Campaign\Queries\CampaignParameterData.cs" />
		<Compile Include="Outbound\Campaign\Queries\CampaignSaleSupportListItem.cs" />
		<Compile Include="Outbound\Campaign\Queries\CampaignTeamAssignmentData.cs" />
		<Compile Include="Outbound\Campaign\Queries\CampaignTeamLeadListItem.cs" />
		<Compile Include="Outbound\Campaign\Queries\CampaignWorkSummaryInfo.cs" />
		<Compile Include="Outbound\Campaign\Queries\GetAllCampaignParameterListQuery.cs" />
		<Compile Include="Outbound\Campaign\Queries\GetCampaignExecutingTimeInfoListByCampaignIdQuery.cs" />
		<Compile Include="Outbound\Campaign\Queries\GetCampaignParameterContentTemplateLinkEntityParamsQuery.cs" />
		<Compile Include="Outbound\Campaign\Queries\FWDGetCampaignQuery.cs" /> 
		<Compile Include="Outbound\Campaign\Queries\GetAllCampaignQuery.cs" />
		<Compile Include="Outbound\Campaign\Queries\GetCampaignAssignmentByUserIdQuery.cs" /> 
		<Compile Include="Outbound\Campaign\Queries\GetCampaignExecutingTimeListQuery.cs" />
		<Compile Include="Outbound\Campaign\Queries\GetCampaignHistoriesQuery.cs" /> 
		<Compile Include="Outbound\Campaign\Queries\GetCampaignParameterListQuery.cs" />
		<Compile Include="Outbound\Campaign\Queries\GetCampaignTeamAssigmentQuery.cs" />
		<Compile Include="Outbound\Campaign\Queries\GetCampaignWorkSummaryQuery.cs" />
		<Compile Include="Outbound\Campaign\Queries\GetCoreServiceCallbackByCallResultIdQuery.cs" />
		<Compile Include="Outbound\Campaign\Queries\GetSentMailCountByCampaignIdQuery.cs" />
		<Compile Include="Outbound\Campaign\Queries\SearchAssignedCampaignListQuery.cs" /> 
		<Compile Include="Outbound\Campaign\Queries\SearchCampaignDropdownListQuery.cs" />
		<Compile Include="Outbound\Campaign\Queries\SearchCampaignListQuery.cs" />
		<Compile Include="Outbound\Campaign\Queries\SearchContactBySaleSupportAndDistributeAgentQuery.cs" />
		<Compile Include="Outbound\Campaign\Queries\SearchContactBySaleSupportAndDistributeCustomerQuery.cs" />
		<Compile Include="Outbound\Campaign\Queries\SearchCustomerBySaleSupportQuery.cs" />
		<Compile Include="Outbound\Campaign\Queries\SearchProspectListInCampaignQuery.cs" />
		<Compile Include="Outbound\Campaign\Queries\SearchSaleSupportCampaignListQuery.cs" />
		<Compile Include="Outbound\Campaign\Queries\SearchUserInCampaignQuery.cs" />
		<Compile Include="Outbound\Campaign\TableData.cs" />
		<Compile Include="Outbound\Campaign\Tasks\AddCustomerToCampaignScheduledTask.cs" />
		<Compile Include="Outbound\Company\CompanyEntity.cs" />
		<Compile Include="Outbound\Company\Events\CompanyAddedEvent.cs" />
		<Compile Include="Outbound\Company\Events\CompanyUpdatedEvent.cs" />
		<Compile Include="Outbound\Company\Queries\CompanyData.cs" />
		<Compile Include="Outbound\CompletionCategory\CompletionCategoryEntity.cs" />
		<Compile Include="Outbound\CompletionCategory\Events\CompletionCategoryCreatedEvent.cs" />
		<Compile Include="Outbound\CompletionCategory\Events\CompletionCategoryUpdatedEvent.cs" />
		<Compile Include="Outbound\CompletionCategory\Queries\CompletionCategoryData.cs" />
		<Compile Include="Outbound\ContactCallResultServiceCallback\AutoMapperRegistration.cs" />
		<Compile Include="Outbound\ContactCallResultServiceCallback\ContactCallResultServiceCallbackEntity.cs" />
		<Compile Include="Outbound\ContactCallResultServiceCallback\Queries\ContactCallResultServiceCallbackData.cs" />
		<Compile Include="Outbound\ContactCall\ContactCallEntity.cs" />
		<Compile Include="Outbound\ContactCall\ContactCallService.cs" />
		<Compile Include="Outbound\ContactCall\Events\ContactCallSubmittedEvent.cs" />
		<Compile Include="Outbound\ContactCall\Queries\ContactCallItem.cs" />
		<Compile Include="Outbound\ContactCall\Queries\ContactCallListItem.cs" />
		<Compile Include="Outbound\ContactCall\Queries\GetContactCallByProspectAssignmentQuery.cs" />
		<Compile Include="Outbound\ContactCall\Queries\GetDigitalMessDetailByProspectAssignmentQuery.cs" />
		<Compile Include="Outbound\ContactRelationship\ContactRelationshipEntity.cs" />
		<Compile Include="Outbound\ContactRelationship\Queries\ContactRelationshipData.cs" />
		<Compile Include="Outbound\Contact\Commands\AgentCreateContactCommand.cs" />
		<Compile Include="Outbound\Contact\ContactEntity.cs" />
		<Compile Include="Outbound\Contact\Queries\GetContactNotesHistoriesQuery.cs" />
		<Compile Include="Outbound\Contact\Queries\GetDetailContactInfoQuery.cs" />
		<Compile Include="Outbound\Contact\Queries\GetDuplicateContactQuery.cs" />
		<Compile Include="Outbound\Contact\Queries\SearchContactInfoQuery.cs" />
		<Compile Include="Outbound\Contact\Queries\SearchContactListToAddToCampaignQuery.cs" />
		<Compile Include="Outbound\Contract\ContractStatus.cs" />
		<Compile Include="Outbound\Contract\PolicyData.cs" />
		<Compile Include="Outbound\Contract\Queries\SearchListPolicyQuery.cs" />
		<Compile Include="Outbound\District\Commands\CreateEditDistrictCommand.cs" />
		<Compile Include="Outbound\District\Commands\DeleteDistrictCommand.cs" />
		<Compile Include="Outbound\District\DistrictEntity.cs" />
		<Compile Include="Outbound\District\Events\CreateEditDistrictEvent.cs" />
		<Compile Include="Outbound\District\Queries\DistrictData.cs" />
		<Compile Include="Outbound\District\Queries\DistrictProviceData.cs" />
		<Compile Include="Outbound\District\Queries\GetDistrictListByProvinceQuery.cs" />
		<Compile Include="Outbound\ExcelFormula\ExcelFormulaEntity.cs" />
		<Compile Include="Outbound\ExcelFormula\Queries\ExcelFormulaData.cs" />
		<Compile Include="Outbound\ExcelFormula\Queries\GetExcelFormulaListQuery.cs" />
		<Compile Include="Outbound\ExcelImport\ColumnMappingEntity.cs" />
		<Compile Include="Outbound\ExcelImport\ContactRawEntity.cs" />
		<Compile Include="Outbound\ExcelImport\ExcelDb.cs" />
		<Compile Include="Outbound\ExcelImport\ExcelImportOptions.cs" />
		<Compile Include="Outbound\ExcelImport\ImportSessionEntity.cs" />
		<Compile Include="Outbound\ExcelImport\MassData\DeleteMappingOptionsCommand.cs" />
		<Compile Include="Outbound\ExcelImport\Queries\ColumnMappingData.cs" />
		<Compile Include="Outbound\ExcelImport\Queries\CustomerImportReport.cs" />
		<Compile Include="Outbound\ExcelImport\Queries\CustomerImportResult.cs" />
		<Compile Include="Outbound\ExcelImport\Queries\GetImportSessionByIdQuery.cs" />
		<Compile Include="Outbound\ExcelImport\Queries\GetImportSessionByNameQuery.cs" />
		<Compile Include="Outbound\ExcelImport\Queries\GetImportSessionQuery.cs" />
		<Compile Include="Outbound\ExcelImport\Queries\GetMappingOptionsQuery.cs" />
		<Compile Include="Outbound\ExcelImport\Queries\ImportSessionData.cs" />
		<Compile Include="Outbound\HotListCampaignImportSession\HotListCampaignImportSessionEntity.cs" />
		<Compile Include="Outbound\HotListCampaignImportSession\Queries\AgentDistributionItem.cs" />
		<Compile Include="Outbound\HotListCampaignImportSession\Queries\GetHotListCampaignImportSessionByIdQuery.cs" />
		<Compile Include="Outbound\HotListCampaignImportSession\Queries\ScanInputDataError.cs" />
		<Compile Include="Outbound\HotListCampaignImportSession\Queries\ScanInputStatistic.cs" />
		<Compile Include="Outbound\HotListGroupUser\HotListGroupEntity.cs" />
		<Compile Include="Outbound\HotListGroup\HotListGroupEntity.cs" />
		<Compile Include="Outbound\HotListGroup\Queries\HotListGroupData.cs" />
		<Compile Include="Outbound\ImportSession\ContactDedupEntity.cs" />
		<Compile Include="Outbound\ImportSession\Events\ImportSessionCreatedEvent.cs" />
		<Compile Include="Outbound\ImportSession\ImportFileSchema.cs" />
		<Compile Include="Outbound\ImportSession\ImportMappingSchema.cs" />
		<Compile Include="Outbound\ImportSession\ImportSessionEntity.cs" />
		<Compile Include="Outbound\LeadAssignment\LeadAssignmentEntity.cs" />
		<Compile Include="Outbound\LeadAssignment\Queries\LeadAssignmentData.cs" />
		<Compile Include="Outbound\Lead\Commands\CreateEditLeadCommand.cs" />
		<Compile Include="Outbound\Lead\LeadEntity.cs" />
		<Compile Include="Outbound\Lead\LeadSummaryStatusEntity.cs" />
		<Compile Include="Outbound\Lead\Queries\FieldSaleQuotaItem.cs" />
		<Compile Include="Outbound\Lead\Queries\LeadData.cs" />
		<Compile Include="Outbound\Lead\Queries\LeadItem.cs" />
		<Compile Include="Outbound\Lead\Queries\LeadListItem.cs" />
		<Compile Include="Outbound\Organization\OrganizationTypes.cs" />
		<Compile Include="Outbound\Organization\Queries\FieldSaleTeamInfo.cs" />
		<Compile Include="Outbound\ParentContact\ParentContactEntity.cs" />
		<Compile Include="Outbound\Product\ProductInCampaign\ProductInCampaignEntity.cs" />
		<Compile Include="Outbound\Product\Queries\GetProductByIdQuery.cs" />
		<Compile Include="Outbound\Product\Queries\GetProductListQuery.cs" />
		<Compile Include="Outbound\ProspectAssignment\Commands\UpdateProspectStatusByPaListCommand.cs" />
		<Compile Include="Outbound\ProspectAssignment\Events\DistributeContactCustomerEvent.cs" />
		<Compile Include="Outbound\ProspectAssignment\ProspectAssignmentEntity.cs" />
		<Compile Include="Outbound\ProspectAssignment\ProspectAssignmentService.cs" />
		<Compile Include="Outbound\ProspectAssignment\Queries\CustomerProspectAssignmentData.cs" />
		<Compile Include="Outbound\ProspectAssignment\Queries\ProspectAssignmentData.cs" />
		<Compile Include="Outbound\Prospect\ProspectEntity.cs" />
		<Compile Include="Outbound\Prospect\Queries\AgentAssignmentInfo.cs" />
		<Compile Include="Outbound\Prospect\Queries\GetAgentAssignmentByProspectIdQuery.cs" />
		<Compile Include="Outbound\Prospect\Queries\GetProspectByAgentQuery.cs" />
		<Compile Include="Outbound\Prospect\Queries\GetProspectByCustomerQuery.cs" />
		<Compile Include="Outbound\Prospect\Queries\GetProspectByIdQuery.cs" />
		<Compile Include="Outbound\Prospect\Queries\GetProspectSummaryQuery.cs" />
		<Compile Include="Outbound\Prospect\Queries\HotListImportedResultItem.cs" />
		<Compile Include="Outbound\Prospect\Queries\ProspectData.cs" />
		<Compile Include="Outbound\Prospect\Queries\ProspectSummary.cs" />
		<Compile Include="Outbound\Prospect\Queries\ProspectSummaryInfo.cs" />
		<Compile Include="Outbound\Prospect\Queries\TeamContactNumberAssignment.cs" />
		<Compile Include="Outbound\Prospect\Queries\TeamRegionContactNumberAssignment.cs" />
		<Compile Include="Outbound\Province\Commands\CreateEditProvinceCommand.cs" />
		<Compile Include="Outbound\Province\Commands\DeleteProvinceCommand.cs" />
		<Compile Include="Outbound\Province\Events\CreateEditProvinceEvent.cs" />
		<Compile Include="Outbound\Province\ProvinceEntity.cs" />
		<Compile Include="Outbound\Province\Queries\GetProvinceListQuery.cs" />
		<Compile Include="Outbound\Province\Queries\ProvinceData.cs" />
		<Compile Include="Outbound\Province\Queries\ProvinceListItem.cs" />
		<Compile Include="Outbound\Region\Commands\CreateEditRegionCommand.cs" />
		<Compile Include="Outbound\Region\Commands\DeleteRegionCommand.cs" />
		<Compile Include="Outbound\Region\Events\CreateEditRegionEvent.cs" />
		<Compile Include="Outbound\Region\Queries\RegionData.cs" />
		<Compile Include="Outbound\Region\RegionEntity.cs" />
		<Compile Include="Outbound\Reminder\Commands\CreateEditReminderCommand.cs" />
		<Compile Include="Outbound\Reminder\ReminderEntity.cs" />
		<Compile Include="Outbound\Report\DynamicFormValue\DynamicFormValueStatusReportItem.cs" />
		<Compile Include="Outbound\Report\DynamicFormValue\GetTotalSaleReportQuery.cs" />
		<Compile Include="Outbound\Report\TMRSpeedAndRessultCareAppointmentItem.cs" />
		<Compile Include="Outbound\Report\TMRSpeedAndRessultCareProtectedLeadItem.cs" />
		<Compile Include="Outbound\Report\TMRSpeedAndRessultNewCallingItem.cs" />
		<Compile Include="Outbound\Role\StaticRole.cs" />
		<Compile Include="Outbound\Sale\Queries\GetAllVariableOfSaleTemplateQuery.cs" />
		<Compile Include="Outbound\Sale\SaleEntity.cs" />
		<Compile Include="Outbound\SearchFieldConfig.cs" />
		<Compile Include="Outbound\SlotTime\SlotTimeEntity.cs" />
		<Compile Include="Outbound\StagingContact\Events\StagingContactCreatedEvent.cs" />
		<Compile Include="Outbound\StagingContact\Queries\StagingContactData.cs" />
		<Compile Include="Outbound\StagingContact\StagingContactEntity.cs" />
		<Compile Include="Outbound\StaticAccessRegistor.cs" />
		<Compile Include="Outbound\SubContact\Commands\CreateEditSubContactCommand.cs" />
		<Compile Include="Outbound\SubContact\Events\ChildContactUpdatedEvent.cs" />
		<Compile Include="Outbound\SubContact\Queries\SubContactData.cs" />
		<Compile Include="Outbound\SubContact\SubContactEntity.cs" />
		<Compile Include="Outbound\Brand\Queries\GetAllParentBrandQuery.cs" />
		<Compile Include="Outbound\ImportSession\Queries\GetDuplicationStagingContactQuery.cs" />
		<Compile Include="Outbound\Lead\Queries\GetFieldSaleQuotaListQuery.cs" />
		<Compile Include="Outbound\Lead\Queries\GetLeadByIdQuery.cs" />
		<Compile Include="Outbound\Report\CallResultFunnelSumaryQuery.cs" />
		<Compile Include="Outbound\Report\DataCallResultFunnelSumaryQuery.cs" />
		<Compile Include="Outbound\Report\GetCallResultSumaryQuery.cs" />
		<Compile Include="Outbound\Report\GetDataCallResultSumaryQuery.cs" />
		<Compile Include="Outbound\Report\GetDataSourceReportQuery.cs" />
		<Compile Include="Outbound\Report\GetDmoReportQuery.cs" />
		<Compile Include="Outbound\Report\GetReportDataResultQuery.cs" />
		<Compile Include="Outbound\Report\GetSumaryPerformanceReportQuery.cs" />
		<Compile Include="Outbound\Report\GetTmrPerformanceReportQuery.cs" />
		<Compile Include="Outbound\SysConfigs\Queries\SysConfigsData.cs" />
		<Compile Include="Outbound\SysConfigs\SysConfigsEntity.cs" />
		<Compile Include="Outbound\TeamLeadCampaign\ContactDataInCampaign.cs" />
		<Compile Include="Outbound\TeamLeadCampaign\GetAgentDistributionPlanQuery.cs" />
		<Compile Include="Outbound\TeamLeadCampaign\GetAssignedQuery.cs" />
		<Compile Include="Outbound\TeamLeadCampaign\SearchToDistributeByTeamLeadQuery.cs" />
		<Compile Include="Outbound\Team\Commands\AddTeamToCampaignCommand.cs" />
		<Compile Include="Outbound\Team\Commands\RemoveTeamFromCampaignCommand.cs" />
		<Compile Include="Outbound\Team\Queries\CampaignTeamSelectingData.cs" />
		<Compile Include="Outbound\Team\Queries\ChienDichTeamData.cs" />
		<Compile Include="Outbound\Team\Queries\TeamData.cs" />
		<Compile Include="Outbound\Team\TeamEntity.cs" />
		<Compile Include="Outbound\TemplateCodeCallResult\Commands\DeleteTemplateCodeCallResultCommand.cs" />
		<Compile Include="Outbound\TemplateCodeCallResult\TemplateCodeCallResultEntity.cs" />
		<Compile Include="Outbound\UserAccount\Roles.cs" />
		<Compile Include="Outbound\UserCheckIn\Queries\UserCheckInData.cs" />
		<Compile Include="Outbound\UserCheckIn\UserCheckInEntity.cs" />
		<Compile Include="Outbound\Utilities\CsvExporter.cs" />
		<Compile Include="Outbound\Utilities\ExcelFileHelper.cs" />
		<Compile Include="Outbound\Utilities\ImportFileSchema.cs" />
		<Compile Include="Outbound\Utilities\TypeConverterExtensions.cs" />
		<Compile Include="Outbound\Utilities\XmlHelper.cs" />
		<Compile Include="Outbound\VNMContactImportSession\Commands\CreateVNMContactImportSessionCommand.cs" />
		<Compile Include="Outbound\VNMContactImportSession\Commands\ScanInputDataAndPreprocessCommand.cs" />
		<Compile Include="Outbound\VNMContactImportSession\ParentContactDuplicationData.cs" />
		<Compile Include="Outbound\VNMContactImportSession\Queries\VNMContactImportSessionData.cs" />
		<Compile Include="Outbound\VNMContactImportSession\ScanInputDataErrorEntity.cs" />
		<Compile Include="Outbound\VNMContactImportSession\StagingContactEntity.cs" />
		<Compile Include="Outbound\VNMContactImportSession\VNMContactImportSessionEntity.cs" />
		<Compile Include="Outbound\Ward\Commands\CreateEditWardCommand.cs" />
		<Compile Include="Outbound\Ward\Commands\DeleteWardCommand.cs" />
		<Compile Include="Outbound\Ward\Events\CreateEditWardEvent.cs" />
		<Compile Include="Outbound\Ward\Queries\WardData.cs" />
		<Compile Include="Outbound\Ward\Queries\WardDistrictData.cs" />
		<Compile Include="Outbound\Team\Queries\GetTeamByIdQuery.cs" />
		<Compile Include="Outbound\Team\Queries\GetTeamListQuery.cs" />
		<Compile Include="Outbound\Ward\Queries\WardListItem.cs" />
		<Compile Include="Outbound\Ward\WardEntity.cs" />
		<Compile Include="PartBooking\Commands\CreateEditPartBookingCommand.cs" />
		<Compile Include="PartBooking\Commands\DeletePartBookingCommand.cs" />
		<Compile Include="PartBooking\PartBookingEntity.cs" />
		<Compile Include="PartBooking\Queries\GetPartBookingByIdQuery.cs" />
		<Compile Include="PartBooking\Queries\GetPartBookingListQuery.cs" />
		<Compile Include="PartBooking\Queries\GetValdatePartBookingListQuery.cs" />
		<Compile Include="PartBooking\Queries\PartBookingData.cs" />
		<Compile Include="PartBooking\Queries\PartBookingListItem.cs" />
		<Compile Include="PartCustomer\Commands\CreatePartCustomerCommand.cs" />
		<Compile Include="PartCustomer\Commands\DeletePartCustomerCommand.cs" />
		<Compile Include="PartCustomer\Commands\ReallocatePartCustomerCommand.cs" />
		<Compile Include="PartCustomer\PartCustomerData.cs" />
		<Compile Include="PartCustomer\PartCustomerEntity.cs" />
		<Compile Include="PartCustomer\Queries\GetCustomerByPartIdQuery.cs" />
		<Compile Include="PartCustomer\Queries\GetPartByPartCategoryIdQuery.cs" />
		<Compile Include="PartCustomer\Queries\GetPartCustomerByCustomerAndPartQuery.cs" />
		<Compile Include="PartCustomer\Queries\GetPartCustomerByIdQuery.cs" />
		<Compile Include="PartCustomer\Queries\GetPartCustomerByPartIdQuery.cs" />
		<Compile Include="PartCustomer\Queries\GetPartCustomerListQuery.cs" />
		<Compile Include="PartCustomer\Queries\PartCustomerInfo.cs" />
		<Compile Include="PartCustomer\Queries\PartCustomerItem.cs" />
		<Compile Include="PartRepairing\Command\CreateEditPartRepairingCommand.cs" />
		<Compile Include="PartRepairing\Command\UpdateResultOfPredefinedListConcreteRecordCommand.cs" />
		<Compile Include="PartRepairing\PartRepairingData.cs" />
		<Compile Include="PartRepairing\PartRepairingEntity.cs" />
		<Compile Include="PartRepairing\PartRepairReportItem.cs" />
		<Compile Include="PartRepairing\Queries\GetPartRepairingByIdQuery.cs" />
		<Compile Include="PartRepairing\Queries\GetPartRepairingByMaintenanceIdQuery.cs" />
		<Compile Include="PartRepairing\Queries\GetPartRepairingByTaskIdQuery.cs" />
		<Compile Include="PartRepairing\Queries\GetRepairListQuery.cs" />
		<Compile Include="PartServiceUsedHistory\Commands\CreateEditPartServiceUsedHistoryListCommand.cs" />
		<Compile Include="PartServiceUsedHistory\Commands\DoImportPartServiceUsedHistoryCommand.cs" />
		<Compile Include="PartServiceUsedHistory\Commands\ImportStagingPartServiceUsedHistoryCommand.cs" />
		<Compile Include="PartServiceUsedHistory\Commands\ScanImportPartServiceUsedHistoryCommand.cs" />
		<Compile Include="PartServiceUsedHistory\PartServiceUsedHistoryEntity.cs" />
		<Compile Include="PartServiceUsedHistory\Queries\GetPartServiceUsedHistoryQuery.cs" />
		<Compile Include="PartServiceUsedHistory\Queries\GetScanImportPartServiceUsedHistoryResultQuery.cs" />
		<Compile Include="PartServiceUsedHistory\Queries\PartServiceUsedHistoryData.cs" />
		<Compile Include="PartServiceUsedHistory\Queries\PartServiceUsedHistoryInfo.cs" />
		<Compile Include="PartServiceUsedHistory\Queries\ScanImportResultInfo.cs" />
		<Compile Include="PartServiceUsedHistory\Queries\SearchPartServiceUsedHistoryListQuery.cs" />
		<Compile Include="Part\Commands\CleanupImportAppartmentsCommand.cs" />
		<Compile Include="Part\Commands\CleanupImportPartsCommand.cs" />
		<Compile Include="Part\Commands\CreateEditBookablePartTimeCommand.cs" />
		<Compile Include="Part\Commands\CreateEditPartCommand.cs" />
		<Compile Include="Part\Commands\CreatePartsWithTemplateCommand.cs" />
		<Compile Include="Part\Commands\DeletePartBookableTimeByPartCommand.cs" />
		<Compile Include="Part\Commands\DeletePartBookableTimeCommand.cs" />
		<Compile Include="Part\Commands\DeletePartCommand.cs" />
		<Compile Include="Part\Commands\DoImportAppartmentsCommand.cs" />
		<Compile Include="Part\Commands\DoImportPartsCommand.cs" />
		<Compile Include="Part\Commands\ImportPartDetailComand.cs" />
		<Compile Include="Part\Commands\ImportPartErrorComand.cs" />
		<Compile Include="Part\Commands\ImportPartV2Command.cs" />
		<Compile Include="Part\Commands\ImportStagingAppartmentsCommand.cs" />
		<Compile Include="Part\Commands\ImportStagingParts1Command.cs" />
		<Compile Include="Part\Commands\ImportStagingParts2Command.cs" />
		<Compile Include="Part\Commands\LinkTicketTaskPartByNameCommand.cs" />
		<Compile Include="Part\Commands\ScanImportAppartmentsCommand.cs" />
		<Compile Include="Part\Commands\ScanImportPartsCommand.cs" />
		<Compile Include="Part\PartBookableTimeEntity.cs" />
		<Compile Include="Part\PartCategoryEntity.cs" />
		<Compile Include="Part\PartData.cs" />
		<Compile Include="Part\PartEntity.cs" />
		<Compile Include="Part\PartErrorCategoryData.cs" />
		<Compile Include="Part\PartErrorCategoryEntity.cs" />
		<Compile Include="Part\PartListItem.cs" />
		<Compile Include="Part\PartMaintenanceListItem.cs" />
		<Compile Include="Part\PartTemplateData.cs" />
		<Compile Include="Part\PartTypeEntity.cs" />
		<Compile Include="Part\Queries\GetAllPartTemplateQuery.cs" />
		<Compile Include="Part\Queries\GetAllRootPartQuery.cs" />
		<Compile Include="Part\Queries\GetBookablePartListQuery.cs" />
		<Compile Include="Part\Queries\GetBookablePartTypeListQuery.cs" />
		<Compile Include="Part\Queries\GetLastPartWithParentIdQuery.cs" />
		<Compile Include="Part\Queries\GetPartBookableTimeByIdQuery.cs" />
		<Compile Include="Part\Queries\GetPartBookableTimeListByPartQuery.cs" />
		<Compile Include="Part\Queries\GetPartByCodeQuery.cs" />
		<Compile Include="Part\Queries\GetPartByIdQuery.cs" />
		<Compile Include="Part\Queries\GetPartByParentIdQuery.cs" />
		<Compile Include="Part\Queries\GetPartByPartTypeIdQuery.cs" />
		<Compile Include="Part\Queries\GetPartErrorCategoryByLevelQuery.cs" />
		<Compile Include="Part\Queries\GetPartMaintenanceDetailQuery.cs" />
		<Compile Include="Part\Queries\GetPartTypeByIdQuery.cs" />
		<Compile Include="Part\Queries\GetPartTypeListQuery.cs" />
		<Compile Include="Part\Queries\GetPathOfPartByIdQuery.cs" />
		<Compile Include="Part\Queries\GetSearchPartServiceTypeConfigByIdQuery.cs" />
		<Compile Include="Part\Queries\GetSearchPartServiceTypeConfigListQuery.cs" />
		<Compile Include="Part\Queries\PartBookableTimeData.cs" />
		<Compile Include="Part\Queries\PartTypeData.cs" />
		<Compile Include="Part\Queries\ReportPartStatusQuery.cs" />
		<Compile Include="Part\Queries\SearchAppartmentPartListQuery.cs" />
		<Compile Include="Part\Queries\SearchPartDataSetQuery.cs" />
		<Compile Include="Part\Queries\SearchPartMaintenanceQuery.cs" />
		<Compile Include="Part\Queries\SearchPartMaintenanceWithRepairQuery.cs" />
		<Compile Include="Part\Queries\SearchPartQuery.cs" />
		<Compile Include="Part\Queries\SearchPartServiceTypeConfigData.cs" />
		<Compile Include="Part\RepairPartListItem.cs" />
		<Compile Include="Part\SearchPartServiceTypeConfigEntity.cs" />
		<Compile Include="Part\ServiceTypePartEntity.cs" />
		<Compile Include="Part\StagingAppartmentsEntity.cs" />
		<Compile Include="PaymentRequest\Commands\AddExpenseItemsToPaymentRequestCommand.cs" />
		<Compile Include="PaymentRequest\Commands\BulkInsertImportRawPaymentRequestCommand.cs" />
		<Compile Include="PaymentRequest\Commands\CreatePaymentRequestImportSessionCommand.cs" />
		<Compile Include="PaymentRequest\Commands\ProcessPaymentRequestImportCommand.cs" />
		<Compile Include="PaymentRequest\Commands\RemoveExpenseItemsFromPaymentRequestCommand.cs" />
		<Compile Include="PaymentRequest\Commands\ScanPaymentRequestImportCommand.cs" />
		<Compile Include="PaymentRequest\PaymentRequestImportSessionEntity.cs" />
		<Compile Include="PaymentRequest\Queries\EndorsementInfo.cs" />
		<Compile Include="PaymentRequest\Queries\EndorsementItemInfo.cs" />
		<Compile Include="PaymentRequest\Queries\GetEndorsementItemsForPaymentQuery.cs" />
		<Compile Include="PaymentRequest\Queries\GetEndorsementSummaryGroupByContestForPaymentQuery.cs" />
		<Compile Include="PaymentRequest\Queries\GetPaymentRequestItemListByValueGroupIdQuery.cs" />
		<Compile Include="PaymentRequest\Queries\GetPaymentRequestSummaryByValueGroupIdQuery.cs" />
		<Compile Include="PaymentRequest\Queries\GetScanPaymentRequestImportResultQuery.cs" />
		<Compile Include="PaymentRequest\Queries\PaymentRequestItem.cs" />
		<Compile Include="PaymentRequest\Queries\PaymentRequestListItemData.cs" />
		<Compile Include="PaymentRequest\Queries\PaymentRequestSummaryInfo.cs" />
		<Compile Include="PaymentRequest\Queries\SearchEndorsementForPaymentQuery.cs" />
		<Compile Include="Phase\Command\ChangeTaskOwnedByOrganizationIdBatchCommand.cs" />
		<Compile Include="Phase\Command\CloseTaskCommand.cs" />
		<Compile Include="Phase\Command\CreateTaskCommand.cs" />
		<Compile Include="Phase\Command\UpdatePhaseCommand.cs" />
		<Compile Include="Phase\Command\UpdateTaskCommand.cs" />
		<Compile Include="Phase\Command\UpdateTaskFeedbackCommand.cs" />
		<Compile Include="Phase\Command\UpdateTaskStatusCommand.cs" />
		<Compile Include="Phase\DefaultTaskCommand.cs" />
		<Compile Include="Phase\EventHandlers\NotificationTaskDoneEventHandler.cs" />
		<Compile Include="Phase\EventHandlers\UpdateBusinessResultAndStatusTaskChangedEventHandler.cs" />
		<Compile Include="Phase\Events\TaskChangedEvent.cs" />
		<Compile Include="Phase\Events\TaskDoneEvent.cs" />
		<Compile Include="Phase\ITaskCommand.cs" />
		<Compile Include="Phase\PhaseEntity.cs" />
		<Compile Include="Phase\Queries\GetCurrentPhaseByRequestTicketIdQuery.cs" />
		<Compile Include="Phase\Queries\GetLatestDynamicFieldValueByPhaseAndFieldIdsQuery.cs" />
		<Compile Include="Phase\Queries\GetLatestTasksByTaskTypeQuery.cs" />
		<Compile Include="Phase\Queries\GetPhaseByIdQuery.cs" />
		<Compile Include="Phase\Queries\GetPhaseByTaskIdQuery.cs" />
		<Compile Include="Phase\Queries\GetPhaseDataQuery.cs" />
		<Compile Include="Phase\Queries\GetTaskBoardItemListQuery.cs" />
		<Compile Include="Phase\Queries\GetTaskBoardItemQuery.cs" />
		<Compile Include="Phase\Queries\GetTaskByCodeQuery.cs" />
		<Compile Include="Phase\Queries\GetTaskByIdQuery.cs" />
		<Compile Include="Phase\Queries\GetTaskByRequestTicketIdQuery.cs" />
		<Compile Include="Phase\Queries\GetTaskFeedbackQuery.cs" />
		<Compile Include="Phase\Queries\GetTaskHistoryQuery.cs" />
		<Compile Include="Phase\Queries\GetTaskListInTaskTypeGroupQuery.cs" />
		<Compile Include="Phase\Queries\GetTaskQuery.cs" />
		<Compile Include="Phase\Queries\GetTaskSummaryQuery.cs" />
		<Compile Include="Phase\Queries\GetTaskSummaryQueryCPC.cs" />
		<Compile Include="Phase\Queries\GetUserFromOrganizationToAssignTaskQuery.cs" />
		<Compile Include="Phase\Queries\PhaseInfo.cs" />
		<Compile Include="Phase\Queries\TaskBoardData.cs" />
		<Compile Include="Phase\Queries\TaskInfo.cs" />
		<Compile Include="Phase\Queries\TaskSummaryData.cs" />
		<Compile Include="Phase\TaskData.cs" />
		<Compile Include="Phase\TaskEntity.cs" />
		<Compile Include="Phase\TaskFeedbackData.cs" />
		<Compile Include="Phase\TaskFeedbackEntity.cs" />
		<Compile Include="Phase\TaskServices.cs" />
		<Compile Include="Phase\TaskTypeBusinessPermissionEntity.cs" />
		<Compile Include="Phase\UpdateTaskFromEmailContent.cs" />
		<Compile Include="Phase\WorkflowGroupEntity.cs" />
		<Compile Include="PlanJob\Commands\CreatePlanJobListCommand.cs" />
		<Compile Include="PlanJob\Commands\CreateServiceTypeComplianceListCommand.cs" />
		<Compile Include="PlanJob\PlanJobEntity.cs" />
		<Compile Include="PlanJob\Queries\GetPlanJobByIdQuery.cs" />
		<Compile Include="PlanJob\Queries\GetPlanJobListQuery.cs" />
		<Compile Include="PlanJob\Queries\GetServiceTypeComplianceListQuery.cs" />
		<Compile Include="PlanJob\Queries\PlanJobData.cs" />
		<Compile Include="PlanJob\Queries\PlanJobInfo.cs" />
		<Compile Include="PlanJob\Queries\ServiceTypeComplianceData.cs" />
		<Compile Include="PlanJob\ServiceTypeComplianceEntity.cs" />
		<Compile Include="Product\ProductEntity.cs" />
		<Compile Include="Product\Queries\GetProductByIdQuery.cs" />
		<Compile Include="Product\Queries\ProductData.cs" />
		<Compile Include="Product\Queries\ProductHistory.cs" />
		<Compile Include="Product\Queries\ProductListItem.cs" />
		<Compile Include="Query\Command\CreateEditQueryCommand.cs" />
		<Compile Include="Query\Command\DeleteDuplicateLocalizeCommand.cs" />
		<Compile Include="Query\Command\DeleteSavedQueryCommand.cs" />
		<Compile Include="Query\Command\SavePriorityQueryCommand.cs" />
		<Compile Include="Query\Queries\GetListFavoriteQuery.cs" />
		<Compile Include="Query\Queries\GetLocalizationQuery.cs" />
		<Compile Include="Query\Queries\GetQueryQuery.cs" />
		<Compile Include="Query\Queries\GetValueSearchTicketQuery.cs" />
		<Compile Include="Query\QueryEntity.cs" />
		<Compile Include="Report\AgentSurveyRatingSummaryReportItem.cs" />
		<Compile Include="Report\BaoCaoNangSuatLaoDong02Item.cs" />
		<Compile Include="Report\BehaviorsReportItem.cs" />
		<Compile Include="Report\CampignReportServices.cs" />
		<Compile Include="Report\ComplainedProductQuantityItem.cs" />
		<Compile Include="Report\ExchangeDataReportItem.cs" />
		<Compile Include="Report\InboundReportServices.cs" />
		<Compile Include="Report\LeadTimeDataReportItem.cs" />
		<Compile Include="Report\OverProcessSLAReportItem.cs" />
		<Compile Include="Report\PredefinedListReportItem.cs" />
		<Compile Include="Report\Queries\GetCustomerComplainedAnalysisQuery.cs" />
		<Compile Include="Report\Queries\GetTopComplainedProductListQuery.cs" />
		<Compile Include="Report\Queries\GetInfoDashboardQuery.cs" />
		<Compile Include="Report\Queries\GetSurveyReportQuery.cs" />
		<Compile Include="Report\Queries\RequestTicketDetailReportItem.cs" />
		<Compile Include="Report\ReportByServiceCategoryData.cs" />
		<Compile Include="Report\ReportByServiceCategoryItem.cs" />
		<Compile Include="Report\ReportF3Item.cs" />
		<Compile Include="Report\ReportF4Item.cs" />
		<Compile Include="Report\ReportServices.cs" />
		<Compile Include="Report\ReportSummaryCallbackData.cs" />
		<Compile Include="Report\RequestTicketDataConversionItem.cs" />
		<Compile Include="Report\RequestTicketOwnershipSummaryReportItem.cs" />
		<Compile Include="Report\RequestTicketProcessStatusReportItem.cs" />
		<Compile Include="Report\RequestTicketReportItem.cs" />
		<Compile Include="Report\RequestTicketSLADetailItem.cs" />
		<Compile Include="Report\RequestTicketSumaryItem.cs" />
		<Compile Include="Report\RequestTicketSummaryByOwnerItem.cs" />
		<Compile Include="Report\RequestTicketSummaryByServiceTypeItem.cs" />
		<Compile Include="Report\RequestTicketSummaryByTimeData.cs" />
		<Compile Include="Report\RetrievalDataReportItem.cs" />
		<Compile Include="Report\SCADAReportServices.cs" />
		<Compile Include="Report\SLAChiTietReportItem.cs" />
		<Compile Include="Report\SLATongHopReportItem.cs" />
		<Compile Include="Report\SLATraSoatReportItem.cs" />
		<Compile Include="Report\SourceChanelReportItem.cs" />
		<Compile Include="Report\TaskProgressInfo.cs" />
		<Compile Include="Report\TheoDoiSoLuongKhieuNai.cs" />
		<Compile Include="Report\TicketReportByOwnerItem.cs" />
		<Compile Include="Report\TicketReportByServiceTypeItem.cs" />
		<Compile Include="Report\TinhHinhGiaiQuyetKhieuNaiTheoKhuVucItem.cs" />
		<Compile Include="Report\VenusCorp\ChecklistReportItem.cs" />
		<Compile Include="Report\VenusCorp\DetailChecklistReportItem.cs" />
		<Compile Include="Report\VenusCorp\VenusCorpReportServices.cs" />
		<Compile Include="RequestTicketDynamicModel\Commands\CreateRequestTicketDynamicModelCommand.cs" />
		<Compile Include="RequestTicketDynamicModel\Queries\GetRequestTicketDynamicModelByLevelAndModeQuery.cs" />
		<Compile Include="RequestTicketDynamicModel\Queries\GetRequestTicketDynamicModelQuery.cs" />
		<Compile Include="RequestTicketDynamicModel\Queries\RequestTicketDynamicModelData.cs" />
		<Compile Include="RequestTicketDynamicModel\RequestTicketDynamicModelEntity.cs" />
		<Compile Include="RequestTicket\BatchRecalculateRequestTicketExecutor.cs" />
		<Compile Include="RequestTicket\Commands\BatchCreateRequestTicketListCommand.cs" />
		<Compile Include="RequestTicket\Commands\ChangeRequestTicketCustomerCommand.cs" />
		<Compile Include="RequestTicket\Commands\ChangeTicketOwnedByOrganizationIdBatchCommand.cs" />
		<Compile Include="RequestTicket\Commands\CreateTicketAutomaticSurveyFeedbacksCommand.cs" />
		<Compile Include="RequestTicket\Commands\CustomerTicketByServiceType.cs" />
		<Compile Include="RequestTicket\Commands\DeleteCustomerRequestTicketListCommand.cs" />
		<Compile Include="RequestTicket\Commands\DeleteProductExchangeCommand.cs" />
		<Compile Include="RequestTicket\Commands\DeleteProductRequestTicketCommand.cs" />
		<Compile Include="RequestTicket\Commands\DeleteRequestTicketCommand.cs" />
		<Compile Include="RequestTicket\Commands\DeleteRequestTicketWebChatCommand.cs" />
		<Compile Include="RequestTicket\Commands\InsertProductExchangeCommand.cs" />
		<Compile Include="RequestTicket\Commands\InsertProductRequestTicketCommand.cs" />
		<Compile Include="RequestTicket\Commands\InsertRequestTicketCallCommand.cs" />
		<Compile Include="RequestTicket\Commands\InsertRequestTicketCommand.cs" />
		<Compile Include="RequestTicket\Commands\InsertRequestTicketWebchatCommand.cs" />
		<Compile Include="RequestTicket\Commands\ReallocateRequestTicketCustomerCommand.cs" />
		<Compile Include="RequestTicket\Commands\ReCalculateDynamicFormValueListCommand.cs" />
		<Compile Include="RequestTicket\Commands\SearchAndAddRequestTicketToCampaignCommand.cs" />
		<Compile Include="RequestTicket\Commands\SyncMissedDynamicFormValueAndDynamicDefinedTableFieldValuesCommand.cs" />
		<Compile Include="RequestTicket\Commands\UpdateProductExchangeCommand.cs" />
		<Compile Include="RequestTicket\Commands\UpdateProductRequestTicketCommand.cs" />
		<Compile Include="RequestTicket\Commands\UpdateRequestTicketBusinessResultCommand.cs" />
		<Compile Include="RequestTicket\Commands\UpdateRequestTicketCommand.cs" />
		<Compile Include="RequestTicket\Commands\UpdateRequestTicketStatusCommand.cs" />
		<Compile Include="RequestTicket\Events\RequestTicketClosedEvent.cs" />
		<Compile Include="RequestTicket\Events\RequestTicketCreatedEvent.cs" />
		<Compile Include="RequestTicket\Events\RequestTicketDataSynchronizedEvent.cs" />
		<Compile Include="RequestTicket\Events\RequestTicketReOpenEvent.cs" />
		<Compile Include="RequestTicket\Events\RequestTicketUpdatedEvent.cs" />
		<Compile Include="RequestTicket\InteractionHistoryData.cs" />
		<Compile Include="RequestTicket\IsoCodePrefixGenerator.cs" />
		<Compile Include="RequestTicket\ProductExchangeEntity.cs" />
		<Compile Include="RequestTicket\ProductRequestTicketEntity.cs" />
		<Compile Include="RequestTicket\Queries\ExportRequestTicketItem.cs" />
		<Compile Include="RequestTicket\Queries\ExportSearchRequestTicketResultItem.cs" />
		<Compile Include="RequestTicket\Queries\ExportTicketProductExchangeItem.cs" />
		<Compile Include="RequestTicket\Queries\GetCallHistoriesByRequestTicketIdQuery.cs" />
		<Compile Include="RequestTicket\Queries\GetCloneRequestTicketInfoQuery.cs" />
		<Compile Include="RequestTicket\Queries\GetDefaultOwnerListByServiceTypeQuery.cs" />
		<Compile Include="RequestTicket\Queries\GetDynamicFormValueByTicketIdQuery.cs" />
		<Compile Include="RequestTicket\Queries\GetOwnerListQuery.cs" />
		<Compile Include="RequestTicket\Queries\GetProductExchangeByExecutedTaskIdQuery.cs" />
		<Compile Include="RequestTicket\Queries\GetProductExchangeByIdQuery.cs" />
		<Compile Include="RequestTicket\Queries\GetProductExchangeByRequestTicketIdQuery.cs" />
		<Compile Include="RequestTicket\Queries\GetProductRequestTicketByIdQuery.cs" />
		<Compile Include="RequestTicket\Queries\GetProductRequestTicketByRequestTicketIdQuery.cs" />
		<Compile Include="RequestTicket\Queries\GetProductRequestTicketExecutedByTaskIdQuery.cs" />
		<Compile Include="RequestTicket\Queries\GetRequestTicketBoardItemListQuery.cs" />
		<Compile Include="RequestTicket\Queries\GetRequestTicketByCodeQuery.cs" />
		<Compile Include="RequestTicket\Queries\GetRequestTicketByCustomerAndServiceTypeQuery.cs" />
		<Compile Include="RequestTicket\Queries\GetRequestTicketByDynamicFieldValueIdQuery.cs" />
		<Compile Include="RequestTicket\Queries\GetRequestTicketByDynamicFormValueIdQuery.cs" />
		<Compile Include="RequestTicket\Queries\GetRequestTicketByIdQuery.cs" />
		<Compile Include="RequestTicket\Queries\GetRequestTicketByListCustomerIdAndRpContactQuery.cs" />
		<Compile Include="RequestTicket\Queries\GetRequestTicketByProspectAssignmentIdQuery.cs" />
		<Compile Include="RequestTicket\Queries\GetRequestTicketByTaskIdQuery.cs" />
		<Compile Include="RequestTicket\Queries\GetRequestTicketNotDoneTaskQuery.cs" />
		<Compile Include="RequestTicket\Queries\GetRequestTicketRetrievalQuery.cs" />
		<Compile Include="RequestTicket\Queries\GetRequestTicketWebChatListQuery.cs" />
		<Compile Include="RequestTicket\Queries\GetTicketWorkQuery.cs" />
		<Compile Include="RequestTicket\Queries\GetUserCurrentTicketListByTaskQuery.cs" />
		<Compile Include="RequestTicket\Queries\LandingGetTicketStatusDetailQuery.cs" />
		<Compile Include="RequestTicket\Queries\ProductExchangeData.cs" />
		<Compile Include="RequestTicket\Queries\ProductRequestTicketData.cs" />
		<Compile Include="RequestTicket\Queries\ProductRequestTicketItem.cs" />
		<Compile Include="RequestTicket\Queries\RequestTicketBoardData.cs" />
		<Compile Include="RequestTicket\Queries\RequestTicketCallData.cs" />
		<Compile Include="RequestTicket\Queries\RequestTicketData.cs" />
		<Compile Include="RequestTicket\Queries\RequestTicketWebchatData.cs" />
		<Compile Include="RequestTicket\Queries\SearchInboundTicketsQuery.cs" />
		<Compile Include="RequestTicket\Queries\SearchRequestTicketNABQuery.cs" />
		<Compile Include="RequestTicket\Queries\SearchRequestTicketQuery.cs" />
		<Compile Include="RequestTicket\Queries\TicketWorkListItem.cs" />
		<Compile Include="RequestTicket\RequestTicketBehaviorEntity.cs" />
		<Compile Include="RequestTicket\RequestTicketCallEntity.cs" />
		<Compile Include="RequestTicket\RequestTicketCreateEditDynamicModel.cs" />
		<Compile Include="RequestTicket\RequestTicketEntity.cs" />
		<Compile Include="RequestTicket\RequestTicketServices.cs" />
		<Compile Include="RequestTicket\RequestTicketWebchatEntity.cs" />
		<Compile Include="RequestTicket\Validators\AllTaskDoneSuccessTicketStatusValidator.cs" />
		<Compile Include="RequestTicket\Validators\ITicketStatusValidator.cs" />
		<Compile Include="ResultCode\ResultCodeEntity.cs" />
		<Compile Include="ResultCode\ResultCodeSuiteEntity.cs" />
		<Compile Include="Retrieval\Audit\RetrievalAuditEntity.cs" />
		<Compile Include="Retrieval\Audit\RetrievalAuditField.cs" />
		<Compile Include="Retrieval\Commands\DeleteRetrievalCommand.cs" />
		<Compile Include="Retrieval\Commands\DeleteTracingItemCommand.cs" />
		<Compile Include="Retrieval\Commands\ImportRetrievalCommand.cs" />
		<Compile Include="Retrieval\Commands\InsertRequestTicketTracingItemCommand.cs" />
		<Compile Include="Retrieval\Commands\InsertRetrievalCommand.cs" />
		<Compile Include="Retrieval\Commands\InsertTracingItemCommand.cs" />
		<Compile Include="Retrieval\Commands\UpdateRetrievalCommand.cs" />
		<Compile Include="Retrieval\Commands\UpdateRetrievalStatusCommand.cs" />
		<Compile Include="Retrieval\Commands\UpdateTracingItemCommand.cs" />
		<Compile Include="Retrieval\Queries\GetRetrievalByGroupKeyQuery.cs" />
		<Compile Include="Retrieval\Queries\GetRetrievalByIdQuery.cs" />
		<Compile Include="Retrieval\Queries\GetRetrievalByRequestTicketIdQuery.cs" />
		<Compile Include="Retrieval\Queries\GetRetrievalByRequestTicketIdviaProductRequestTicketQuery.cs" />
		<Compile Include="Retrieval\Queries\GetRetrievalDataQuery.cs" />
		<Compile Include="Retrieval\Queries\GetRetrievalDetailDataQuery.cs" />
		<Compile Include="Retrieval\Queries\GetTracingItemByIdQuery.cs" />
		<Compile Include="Retrieval\Queries\GetTracingItemByTaskIdQuery.cs" />
		<Compile Include="Retrieval\Queries\GetTracingItemQuery.cs" />
		<Compile Include="Retrieval\Queries\RetrievalData.cs" />
		<Compile Include="Retrieval\Queries\TracingItemData.cs" />
		<Compile Include="Retrieval\RequestTicketTracingItemEntity.cs" />
		<Compile Include="Retrieval\RetrievalEntity.cs" />
		<Compile Include="Retrieval\TracingItemEntity.cs" />
		<Compile Include="ScheduledTaskConfig\ScheduledTaskConfigData.cs" />
		<Compile Include="Customer\Commands\CreateEditClassificationCommand.cs" />
		<Compile Include="Customer\Commands\CreateEditCustomerCommand.cs" />
		<Compile Include="Customer\Commands\DedupBackendAndTempCustomersCommand.cs" />
		<Compile Include="Customer\Commands\DedupSignleBackendAndTempCustomersCommand.cs" />
		<Compile Include="Customer\Commands\DeleteClassificationCommand.cs" />
		<Compile Include="Customer\Commands\DeleteCustomerAlternativeAddressCommand.cs" />
		<Compile Include="Customer\Commands\InsertCustomerAlternativeAddressCommand.cs" />
		<Compile Include="Customer\Commands\InsertCustomerCommand.cs" />
		<Compile Include="Customer\Commands\InsertStagingCustomersCommand.cs" />
		<Compile Include="Customer\Commands\ProcessImportMassSessionCustomerVersioningCommand.cs" />
		<Compile Include="Customer\Commands\UpdateCustomerAlternativeAddressCommand.cs" />
		<Compile Include="Customer\Commands\UpdateCustomerCommand.cs" />
		<Compile Include="Customer\Commands\UpdateCustomerExtendedFieldsCommand.cs" />
		<Compile Include="Customer\Commands\UpdateCustomerFieldConfigCommand.cs" />
		<Compile Include="Customer\Queries\GetAgencyHierarchyQuery.cs" />
		<Compile Include="Customer\Queries\GetAgencyHierarchySummaryQuery.cs" />
		<Compile Include="Customer\Queries\GetClassificationByIdQuery.cs" />
		<Compile Include="Customer\Queries\GetClassificationQuery.cs" />
		<Compile Include="Customer\Queries\GetCustomerAlternativeAddressByCustomerIdQuery.cs" />
		<Compile Include="Customer\Queries\GetCustomerAlternativeAddressByIdQuery.cs" />
		<Compile Include="Customer\Queries\GetCustomerAlternativeAddressByListCustomerIdAndRpContactQuery.cs" />
		<Compile Include="Customer\Queries\GetCustomerAlternativeAddressQuery.cs" />
		<Compile Include="Customer\Queries\GetCustomerAppartmentInfoMultiQuery.cs" />
		<Compile Include="Customer\Queries\GetCustomerAppartmentInfoQuery.cs" />
		<Compile Include="Customer\Queries\GetCustomerBillingCodeByClassificationChannelIdQuery.cs" />
		<Compile Include="Customer\Queries\GetCustomerByCodeQuery.cs" />
		<Compile Include="Customer\Queries\GetCustomerByEmailQuery.cs" />
		<Compile Include="Customer\Queries\GetCustomerByFacebookIdQuery.cs" />
		<Compile Include="Customer\Queries\GetCustomerByJobQuery.cs" />
		<Compile Include="Customer\Queries\GetCustomerByPhoneQuery.cs" />
		<Compile Include="Customer\Queries\GetCustomerByRequestTicketIdQuery.cs" />
		<Compile Include="Customer\Queries\GetCustomerByRpContactQuery.cs" />
		<Compile Include="Customer\Queries\GetCustomerByTaskIdQuery.cs" />
		<Compile Include="Customer\Queries\GetCustomerDuplicateQuery.cs" />
		<Compile Include="Customer\Queries\GetCustomerFieldConfigurationByFunctionNameQuery.cs" />
		<Compile Include="Customer\Queries\GetCustomerNotLinkDigitalContactQuery.cs" />
		<Compile Include="Customer\Queries\GetCustomerQuery.cs" />
		<Compile Include="Customer\Queries\GetCustomersByClassificationChannelQuery.cs" />
		<Compile Include="Customer\Queries\GetCustomersByClassificationQuery.cs" />
		<Compile Include="Customer\Queries\GetSelectCustomerVersioningQuery.cs" />
		<Compile Include="Customer\Queries\SearchAgencyWithoutTicketsQuery.cs" />
		<Compile Include="Customer\Queries\SearchClassificationQuery.cs" />
		<Compile Include="Customer\Queries\SearchCustomerAlternativeAddressQuery.cs" />
		<Compile Include="Customer\Queries\SearchCustomersQuery.cs" />
		<Compile Include="Customer\Queries\SearchInboundCustomerAlternativeAddressesQuery.cs" />
		<Compile Include="Customer\Queries\SearchInboundCustomersQuery.cs" />
		<Compile Include="CustomerContext\Queries\GetContextHistoriesQuery.cs" />
		<Compile Include="CustomerVersionName\Commands\CreateEditCustomerVersionNameCommand.cs" />
		<Compile Include="CustomerVersionName\Queries\GetAllCustomerVersionNameQuery.cs" />
		<Compile Include="DigitalCampaign\Queries\GetAnonymousDigitalContactListInCampaignQuery.cs" />
		<Compile Include="DigitalCampaign\Queries\GetCampaignDataSummaryForCostEstimationQuery.cs" />
		<Compile Include="DigitalCampaign\Queries\GetCampaignPushCodeInfoForCostEstimationQuery.cs" />
		<Compile Include="DigitalCampaign\Queries\GetCustomerDigitalContactListInCampaignQuery.cs" />
		<Compile Include="DigitalChannel\Commands\CreateEditDigitalChannelCommand.cs" />
		<Compile Include="DigitalChannel\Commands\CreateEditDigitalServiceConfigurationCommand.cs" />
		<Compile Include="DigitalChannel\Commands\DeleteDigitalChannelCommand.cs" />
		<Compile Include="DigitalChannel\Commands\UpdateDigitalChannelCommand.cs" />
		<Compile Include="DigitalChannelMessageTemplate\Commands\CreateEditDigitalMessageTemplateCommand.cs" />
		<Compile Include="DigitalChannelMessageTemplate\Queries\GetDigitalChannelMessageTemplateByIdQuery.cs" />
		<Compile Include="DigitalChannelMessageTemplate\Queries\GetDigitalChannelMessageTemplateByTemplateIdQuery.cs" />
		<Compile Include="Outbound\AdditionalDataTemplate\Queries\GetAdditionalDataTemplateByIdQuery.cs" />
		<Compile Include="Outbound\Appointment\Queries\GetAppointmentByIdQuery.cs" />
		<Compile Include="Outbound\Appointment\Queries\GetAppointmentListByProspectAssignmentQuery.cs" />
		<Compile Include="Outbound\AppointmentResultCode\Queries\GetAppointmentResultCodeListQuery.cs" />
		<Compile Include="Outbound\Brand\Queries\GetBrandByIdQuery.cs" />
		<Compile Include="Outbound\Brand\Queries\GetBrandListByCompanyQuery.cs" />
		<Compile Include="Outbound\Brand\Queries\GetParentBrandListByCompanyQuery.cs" />
		<Compile Include="Outbound\Brand\Queries\SearchBrandListQuery.cs" />
		<Compile Include="Outbound\CallResult\Queries\GetCallResultByIdQuery.cs" />
		<Compile Include="Outbound\CallResult\Queries\GetCallResultListByTemplateQuery.cs" />
		<Compile Include="Outbound\CallResult\Queries\GetCallResultListQuery.cs" />
		<Compile Include="Outbound\CallResult\Queries\GetCallStrategyByIdQuery.cs" />
		<Compile Include="Outbound\CallResult\Queries\GetCallStrategyListQuery.cs" />
		<Compile Include="Outbound\CallResult\Queries\CallStrategyData.cs" />
		<Compile Include="Outbound\CallResult\Queries\GetResultCodeSuiteByIdQuery.cs" />
		<Compile Include="Outbound\CallResult\Queries\GetResultCodeSuiteListQuery.cs" />
		<Compile Include="Outbound\Campaign\Queries\GetCampaignByCampaignNameQuery.cs" />
		<Compile Include="Outbound\Campaign\Queries\GetCampaignByDynamicFormIdQuery.cs" />
		<Compile Include="Outbound\Campaign\Queries\GetCampaignByIdQuery.cs" />
		<Compile Include="Outbound\Contact\Queries\GetContactByAssignmentIdQuery.cs" />
		<Compile Include="Outbound\ContactRelationship\Queries\GetContactRelationshipListByContactQuery.cs" />
		<Compile Include="Outbound\District\Queries\GetDistrictByIdQuery.cs" />
		<Compile Include="Outbound\District\Queries\GetDistrictListByProvinceListQuery.cs" />
		<Compile Include="Outbound\District\Queries\SearchDistrictListQuery.cs" />
		<Compile Include="Outbound\ExcelImport\Queries\GetMappingOptionsByIdQuery.cs" />
		<Compile Include="Outbound\ImportSession\Queries\GetImportSessionByIdQuery.cs" />
		<Compile Include="Outbound\Lead\Queries\GetLeadByProspectAssignmentQuery.cs" />
		<Compile Include="Outbound\ProspectAssignment\Queries\GetProspectAssignmentByIdQuery.cs" />
		<Compile Include="Outbound\ProspectAssignment\Queries\GetProspectAssignmentByReferenceObjectIdQuery.cs" />
		<Compile Include="Outbound\ProspectAssignment\Queries\GetProspectAssignmentByReferenceResultIdQuery.cs" />
		<Compile Include="Outbound\Province\Queries\GetProvinceByIdQuery.cs" />
		<Compile Include="Outbound\StagingContact\Queries\GetStagingContactByIdQuery.cs" />
		<Compile Include="Outbound\UserCheckIn\Queries\GetUserCheckInByIdQuery.cs" />
		<Compile Include="Outbound\VNMContactImportSession\Queries\GetImportSessionByIdQuery.cs" />
		<Compile Include="Outbound\VNMContactImportSession\Queries\GetImportSessionListQuery.cs" />
		<Compile Include="Outbound\VNMContactImportSession\Queries\GetScanInputDataErrorsQuery.cs" />
		<Compile Include="Outbound\VNMContactImportSession\Queries\GetScanInputDataErrorsQuerySummary.cs" />
		<Compile Include="Outbound\Ward\Queries\GetWardByIdQuery.cs" />
		<Compile Include="Outbound\Ward\Queries\GetWardListByDistrictListQuery.cs" />
		<Compile Include="Outbound\Ward\Queries\GetWardListByDistrictQuery.cs" />
		<Compile Include="Outbound\Ward\Queries\SearchWardListQuery.cs" />
		<Compile Include="ResultCode\Queries\ResultCodeData.cs" />
		<Compile Include="ScheduledTaskConfig\Commands\CreateEditScheduledTaskConfigCommand.cs" />
		<Compile Include="ScheduledTaskConfig\Queries\GetScheduledTaskConfigByIdQuery.cs" />
		<Compile Include="ScheduledTaskConfig\ScheduledTaskConfigEntity.cs" />
		<Compile Include="SelfServiceFlow\Queries\GetProductivityUserQuery.cs" />
		<Compile Include="SelfServiceFlow\Queries\GetUWCountQuery.cs" />
		<Compile Include="ServiceCategory\Commands\CreateEditServiceCategoryCommand.cs" />
		<Compile Include="ServiceCategory\Commands\CreateEditServiceCategoryOrganizationCommand.cs" />
		<Compile Include="ServiceCategory\Commands\DeleteServiceCategoryCommand.cs" />
		<Compile Include="ServiceCategory\Queries\GetProductServiceCategoriesQuery.cs" />
		<Compile Include="ServiceCategory\Queries\GetServiceCategoryByIdQuery.cs" />
		<Compile Include="ServiceCategory\Queries\GetServiceCategoryByProductIdQuery.cs" />
		<Compile Include="ServiceCategory\Queries\GetServiceCategoryByServiceTypeFilterQuery.cs" />
		<Compile Include="ServiceCategory\Queries\GetServiceCategoryByTypeQuery.cs" />
		<Compile Include="ServiceCategory\Queries\SearchServiceCategoryQuery.cs" />
		<Compile Include="ServiceCategory\Queries\SearchServiceCategoryWithServiceTypeQuery.cs" />
		<!-- Outbound Queries - Refactored Files -->
		<Compile Include="Outbound\VNMContactImportSession\Queries\GetInternalDuplicationStagingContactQuery.cs" />
		<Compile Include="Outbound\TeamLeadCampaign\Queries\SearchCampaignByTeamLeadQuery.cs" />		<Compile Include="Outbound\ReportPayment\Queries\GetReportPaymentListQuery.cs" />
		<Compile Include="Outbound\ReportPayment\Queries\GetReportPaymentByContestQuery.cs" />
		<Compile Include="Outbound\ReportPayment\Queries\GetReportExpenseItemByContestQuery.cs" />
		<Compile Include="Outbound\Report\DynamicFormValue\GetDynamicFormValueStatusReportQuery.cs" />
		<Compile Include="Outbound\Report\DynamicFormValue\GetTempLockDynamicFormValueReportQuery.cs" />
		<Compile Include="Outbound\SubContact\Queries\GetSubContactListByContactListQuery.cs" />
		<Compile Include="Outbound\SubContact\Queries\GetChildListByParentContact.cs" />
		<Compile Include="Outbound\ExcelImport\Queries\GetImportCustomerSummaryQuery.cs" />
		<Compile Include="Outbound\ExcelImport\Queries\GetImportCustomerSummaryReportQuery.cs" />
		<Compile Include="Outbound\ExcelImport\Queries\GetImportedCustomerByImportSessionIdQuery.cs" />
		<Compile Include="Outbound\ExcelImport\Queries\GetImportFWDContractResultQuery.cs" />
		<Compile Include="Outbound\ExcelImport\Queries\GetImportFWDContractReportQuery.cs" />
		<Compile Include="Outbound\Team\Queries\GetTeamListInCampaignQuery.cs" />
		<Compile Include="Outbound\Team\Queries\GetTeamListNotInCampaignQuery.cs" />
		<Compile Include="Outbound\ExcelImport\MassData\RetrieveCustomerCommand.cs" />
		<Compile Include="Outbound\ExcelImport\MassData\ExecuteImportCustomerCommand.cs" />
		<Compile Include="Outbound\HotListGroup\Command\EditHotGroupCommand.cs" />
		<Compile Include="Outbound\Contact\Commands\ApproveEventContactListCommand.cs" />
		<Compile Include="Outbound\Reminder\Commands\StopReminderCommand.cs" />
		<Compile Include="Outbound\Reminder\Commands\ExpendReminderCommand.cs" />
		<Compile Include="Outbound\Prospect\Commands\DistributeContactsToTeamByQuantityCommand.cs" />
		<Compile Include="Outbound\Prospect\Commands\UpdateContactAssignmentCommand.cs" />
		<Compile Include="Outbound\Prospect\Commands\UpdateProspectStatusCommand.cs" />
		<Compile Include="Outbound\Prospect\Commands\UpdateProspectResultCommand.cs" />
		<Compile Include="Outbound\Prospect\Commands\CreateContactAssignmentListCommand.cs" />
		<Compile Include="Outbound\Prospect\Commands\CreateContactAssignmentCommand.cs" />
		<Compile Include="Outbound\VNMContactImportSession\Commands\ImportAllValidStagingContactIntoDbCommand.cs" />
		<Compile Include="Outbound\VNMContactImportSession\Commands\ImportMappedRecordsFromFileCommand.cs" />
		<Compile Include="Outbound\VNMContactImportSession\Commands\MarkParentDuplicationSolutionCommand.cs" />
		<Compile Include="Outbound\TeamLeadCampaign\DistributeWithProvinceCommand.cs" />
		<Compile Include="Outbound\TeamLeadCampaign\DistributeContactsToAgentBySelectionCommand.cs" />
		<Compile Include="Outbound\TeamLeadCampaign\ReassignCommand.cs" />
		<Compile Include="Outbound\TeamLeadCampaign\UnassignCommand.cs" />
		<Compile Include="Outbound\Sale\Command\CreateCustomerCommand.cs" />
		<Compile Include="Outbound\StagingContact\Commands\CreateStagingContactCommand.cs" />
		<Compile Include="Outbound\StagingContact\Commands\ImportMappedRecordsFromFileCommand.cs" />
		<Compile Include="Outbound\ProspectAssignment\Commands\NotifyToCustomersCommand.cs" />
		<Compile Include="Outbound\ProspectAssignment\Commands\SendWelcomEmailCommand.cs" />
		<Compile Include="Outbound\ProspectAssignment\Commands\UpdateProspectAssignmentResultCommand.cs" />
		<Compile Include="Outbound\ProspectAssignment\Commands\UpdateProspectAssignmentStatusCommand.cs" />
		<!-- Additional Refactored Command Files - Batch 3 -->
		<Compile Include="Outbound\HotListCampaignImportSession\Commands\CreateHotListCampaignImportSessionCommand.cs" />
		<Compile Include="Outbound\HotListGroup\Command\AddUserToHotGroupCommand.cs" />
		<Compile Include="Outbound\ExcelImport\MassData\BulkInsertContractFWDCommand.cs" />
		<Compile Include="Outbound\ExcelImport\MassData\ImportOnlyDigitalContactCommand.cs" />
		<Compile Include="Phase\Command\SetAssignedConditionCountCommand.cs" />
		<Compile Include="Phase\Command\CloseAllTodoTaskStatusCommand.cs" />
		<!-- Additional Refactored Command Files - Batch 2 -->
		<Compile Include="Outbound\ExcelImport\MassData\UpdateImportCustomerRawCommand.cs" />
		<Compile Include="Outbound\ExcelImport\MassData\ExecuteImportFWDContractCommand.cs" />
		<Compile Include="RequestTicket\Commands\BatchInsertNewDynamicFieldToExistedTicketListCommand.cs" />
		<Compile Include="Outbound\Appointment\Commands\DistributeAppointmentCommand.cs" />
		<Compile Include="Outbound\Appointment\Commands\ReturnAppointmentCommand.cs" />
		<Compile Include="Outbound\Campaign\Commands\UpdateCampaignStatusCommand.cs" />
		<Compile Include="Outbound\Contact\Commands\AddContactsToCampaignCommand.cs" />
		<Compile Include="ServiceCategory\Queries\ServiceCategoryData.cs" />
		<Compile Include="ServiceCategory\Queries\ServiceCategoryOrganizationData.cs" />
		<Compile Include="ServiceCategory\Queries\ServiceCategoryTreeData.cs" />
		<Compile Include="ServiceCategory\ServiceCategoryEntity.cs" />
		<Compile Include="ServiceCategory\ServiceCategoryOrganizationEntity.cs" />
		<Compile Include="ServiceType\Commands\CloneServiceTypeCommand.cs" />
		<Compile Include="ServiceType\Commands\DeleteServiceTypeCommand.cs" />
		<Compile Include="ServiceType\Commands\ImportServiceTypeCommandCommand.cs" />
		<Compile Include="ServiceType\Commands\InsertServiceTypeCommand.cs" />
		<Compile Include="ServiceType\Commands\UpdateServiceTypeCommand.cs" />
		<Compile Include="ServiceType\Events\ServiceTypeDeleteEvent.cs" />
		<Compile Include="ServiceType\LandingServiceTypeEntity.cs" />
		<Compile Include="ServiceType\Queries\CheckPermissionQuery.cs" />
		<Compile Include="ServiceType\Queries\ExpenseItemLinkServiceTypeQuery.cs" />
		<Compile Include="ServiceType\Queries\ExportServiceTypeQuery.cs" />
		<Compile Include="ServiceType\Queries\GetAllServiceTypeDataQuery.cs" />
		<Compile Include="ServiceType\Queries\GetBusinessResultByServiceTypeId.cs" />
		<Compile Include="ServiceType\Queries\GetLandingServiceTypeByServiceTypeIdQuery.cs" />
		<Compile Include="ServiceType\Queries\GetLandingServiceTypeListQuery.cs" />
		<Compile Include="ServiceType\Queries\GetRequestTicketExportDetailMappingByServiceTypeQuery.cs" />
		<Compile Include="ServiceType\Queries\GetServiceTypeByDueTimeIdQuery.cs" />
		<Compile Include="ServiceType\Queries\GetServiceTypeByDynamicFieldNameQuery.cs" />
		<Compile Include="ServiceType\Queries\GetServiceTypeByDynamicFormIdQuery.cs" />
		<Compile Include="ServiceType\Queries\GetServiceTypeByIdQuery.cs" />
		<Compile Include="ServiceType\Queries\GetServiceTypeByLevelQuery.cs" />
		<Compile Include="ServiceType\Queries\GetServiceTypeDataByLevelQuery.cs" />
		<Compile Include="ServiceType\Queries\GetServiceTypeDataByWorkflowIdQuery.cs" />
		<Compile Include="ServiceType\Queries\GetServiceTypeForLandingQuery.cs" />
		<Compile Include="ServiceType\Queries\GetServiceTypeTreeQuery.cs" />
		<Compile Include="ServiceType\Queries\LandingServiceTypeData.cs" />
		<Compile Include="ServiceType\Queries\RequestTicketExportDetailMappingData.cs" />
		<Compile Include="ServiceType\Queries\SearchServiceTypeByTextQuery.cs" />
		<Compile Include="ServiceType\Queries\SearchServiceTypeQuery.cs" />
		<Compile Include="ServiceType\Queries\ServiceTypeAndDynamicFieldData.cs" />
		<Compile Include="ServiceType\Queries\ServiceTypeData.cs" />
		<Compile Include="ServiceType\Queries\ServiceTypeTreeData.cs" />
		<Compile Include="ServiceType\RequestTicketExportDetailMappingEntity.cs" />
		<Compile Include="ServiceType\ServiceTypeEntity.cs" />
		<Compile Include="ServiceType\ServiceTypeRoleEntity.cs" />
		<Compile Include="SMSLog\Commands\CreateEditSMSLogCommand.cs" />
		<Compile Include="SMSLog\Queries\GetSMSLogListQuery.cs" />
		<Compile Include="SMSLog\Queries\SMSLogListItem.cs" />
		<Compile Include="SMSLog\SMSLogEntity.cs" />
		<Compile Include="Sms\Commands\CreateEditGatewayCommand.cs" />
		<Compile Include="Sms\Commands\CreateEditSmsCommand.cs" />
		<Compile Include="Sms\Commands\DeleteGatewayCommand.cs" />
		<Compile Include="Sms\Events\SmsProcessedEvent.cs" />
		<Compile Include="Sms\GatewayEntity.cs" />
		<Compile Include="Sms\GatewayIntergration.cs" />
		<Compile Include="Sms\ISmsBroker.cs" />
		<Compile Include="Sms\Queries\GatewayData.cs" />
		<Compile Include="Sms\Queries\GatewayListItem.cs" />
		<Compile Include="Sms\Queries\GetGatewayByIdQuery.cs" />
		<Compile Include="Sms\Queries\GetGatewayDefaultQuery.cs" />
		<Compile Include="Sms\Queries\GetNewSmsListQuery.cs" />
		<Compile Include="Sms\Queries\SearchGatewayQuery.cs" />
		<Compile Include="Sms\Queries\SmsData.cs" />
		<Compile Include="Sms\SmsEntity.cs" />
		<Compile Include="Sms\SmsLogEntity.cs" />
		<Compile Include="Sms\SmsStatus.cs" />
		<Compile Include="Survey\Commands\CreateEditSurveyAnswerCommand.cs" />
		<Compile Include="Survey\Commands\CreateEditSurveyAnswerSuiteAnswerCommand.cs" />
		<Compile Include="Survey\Commands\CreateEditSurveyAnswerSuiteCommand.cs" />
		<Compile Include="Survey\Commands\CreateEditSurveyCampaignCommand.cs" />
		<Compile Include="Survey\Commands\CreateEditSurveyCampaignExecutionCommand.cs" />
		<Compile Include="Survey\Commands\CreateEditSurveyCampaignResponseExecutionCommand.cs" />
		<Compile Include="Survey\Commands\CreateEditSurveyCommand.cs" />
		<Compile Include="Survey\Commands\CreateEditSurveyFeedbackCommand.cs" />
		<Compile Include="Survey\Commands\CreateEditSurveyQuestionCommand.cs" />
		<Compile Include="Survey\Commands\CreateEditSurveyQuestionSectionCommand.cs" />
		<Compile Include="Survey\Commands\CreateSurveyFeedbackCommand.cs" />
		<Compile Include="Survey\Commands\CreateTicketWorkCommand.cs" />
		<Compile Include="Survey\Commands\DeleteSurveyAnswerCommand.cs" />
		<Compile Include="Survey\Commands\DeleteSurveyAnswerSuiteAnswerCommand.cs" />
		<Compile Include="Survey\Commands\DeleteSurveyAnswerSuiteCommand.cs" />
		<Compile Include="Survey\Commands\DeleteSurveyCampaignCommand.cs" />
		<Compile Include="Survey\Commands\DeleteSurveyCampaignExecutionCommand.cs" />
		<Compile Include="Survey\Commands\DeleteSurveyCampaignResponseExecutionCommand.cs" />
		<Compile Include="Survey\Commands\DeleteSurveyQuestionCommand.cs" />
		<Compile Include="Survey\Commands\DeleteSurveyQuestionSectionCommand.cs" />
		<Compile Include="Survey\Commands\ProcessSurveyCampaignResponseExecutionCommand.cs" />
		<Compile Include="Survey\Commands\SubmitAnwserSurrveyAllCommand.cs" />
		<Compile Include="Survey\Commands\SubmitAnwserSurrveyCommand.cs" />
		<Compile Include="Survey\Commands\SubmitSurveyFeedbackCommand.cs" />
		<Compile Include="Survey\Commands\UpdateAssignmentStatusCommand.cs" />
		<Compile Include="Survey\EmailTemplateLinkEntityParams.cs" />
		<Compile Include="Survey\Queries\CampaignSurveyCampaignInfo.cs" />
		<Compile Include="Survey\Queries\GetAgentDoSurveyByServiceTypeQuery.cs" />
		<Compile Include="Survey\Queries\GetAllSurveyQuery.cs" />
		<Compile Include="Survey\Queries\GetCampaignInfoByCampaignExecutionServiceTypeIdQuery.cs" />
		<Compile Include="Survey\Queries\GetCampaignSurveyQuestionListQuery.cs" />
		<Compile Include="Survey\Queries\GetDistributePlanQuery.cs" />
		<Compile Include="Survey\Queries\GetLoopNextQuestionListQuery.cs" />
		<Compile Include="Survey\Queries\GetQuestionListByAnswerSuiteInCampaignQuery.cs" />
		<Compile Include="Survey\Queries\GetSurrveyAnswerListInSuiteQuery.cs" />
		<Compile Include="Survey\Queries\GetSurrveyAnswersByQuestionQuery.cs" />
		<Compile Include="Survey\Queries\GetSurrveyQuestionItemsBySurveyIdQuery.cs" />
		<Compile Include="Survey\Queries\GetSurrveyQuestionsBySectionIdQuery.cs" />
		<Compile Include="Survey\Queries\GetSurrveyQuestionsBySurveyIdQuery.cs" />
		<Compile Include="Survey\Queries\GetSurveyAnswerByIdQuery.cs" />
		<Compile Include="Survey\Queries\GetSurveyAnswerSuiteByIdQuery.cs" />
		<Compile Include="Survey\Queries\GetSurveyAnswerSuiteListQuery.cs" />
		<Compile Include="Survey\Queries\GetSurveyByIdQuery.cs" />
		<Compile Include="Survey\Queries\GetSurveyCampaignByCampaignIdQuery.cs" />
		<Compile Include="Survey\Queries\GetSurveyCampaignByIdQuery.cs" />
		<Compile Include="Survey\Queries\GetSurveyCampaignByServiceTypeQuery.cs" />
		<Compile Include="Survey\Queries\GetSurveyCampaignExecutionByIdQuery.cs" />
		<Compile Include="Survey\Queries\GetSurveyCampaignExecutionBySurveyCampaignIdQuery.cs" />
		<Compile Include="Survey\Queries\GetSurveyCampaignExecutionResponseBySurveyCampaignIdQuery.cs" />
		<Compile Include="Survey\Queries\GetSurveyCampaignItemListQuery.cs" />
		<Compile Include="Survey\Queries\GetSurveyCampaignQuery.cs" />
		<Compile Include="Survey\Queries\GetSurveyCampaignResponseExecutionByIdQuery.cs" />
		<Compile Include="Survey\Queries\GetSurveyFeedbackByCodeQuery.cs" />
		<Compile Include="Survey\Queries\GetSurveyFeedbackByRequestTicketIdQuery.cs" />
		<Compile Include="Survey\Queries\GetSurveyFeedbackExpiredDate.cs" />
		<Compile Include="Survey\Queries\GetSurveyFeedbackListByCustomerQuery.cs" />
		<Compile Include="Survey\Queries\GetSurveyListQuery.cs" />
		<Compile Include="Survey\Queries\GetSurveyQuestionAndAnswerQuery.cs" />
		<Compile Include="Survey\Queries\GetSurveyQuestionByAnswerQuery.cs" />
		<Compile Include="Survey\Queries\GetSurveyQuestionByIdQuery.cs" />
		<Compile Include="Survey\Queries\GetSurveyQuestionSectionByIdQuery.cs" />
		<Compile Include="Survey\Queries\GetSurveyQuestionSectionListQuery.cs" />
		<Compile Include="Survey\Queries\GetSurveyReportQuery.cs" />
		<Compile Include="Survey\Queries\GetSurveyResultQuery.cs" />
		<Compile Include="Survey\Queries\GetSurveyWithAnswerSuiteReportQuery.cs" />
		<Compile Include="Survey\Queries\GetTargetSurveyQuery.cs" />
		<Compile Include="Survey\Queries\GetTicketOwnerSettingFromSurveyFeedbackQuery.cs" />
		<Compile Include="Survey\Queries\PreviewSurveyResultQuery.cs" />
		<Compile Include="Survey\Queries\SearchCampaignWorkerQuery.cs" />
		<Compile Include="Survey\Queries\SurveyAnswerData.cs" />
		<Compile Include="Survey\Queries\SurveyAnswerItem.cs" />
		<Compile Include="Survey\Queries\SurveyAnswerSuiteData.cs" />
		<Compile Include="Survey\Queries\SurveyCampaignData.cs" />
		<Compile Include="Survey\Queries\SurveyCampaignExecutionData.cs" />
		<Compile Include="Survey\Queries\SurveyCampaignExecutionListItem.cs" />
		<Compile Include="Survey\Queries\SurveyCampaignListItem.cs" />
		<Compile Include="Survey\Queries\SurveyCampaignResponseExecutionData.cs" />
		<Compile Include="Survey\Queries\SurveyCampaignResponseExecutionListItem.cs" />
		<Compile Include="Survey\Queries\SurveyData.cs" />
		<Compile Include="Survey\Queries\SurveyFeedbackData.cs" />
		<Compile Include="Survey\Queries\SurveyListItem.cs" />
		<Compile Include="Survey\Queries\SurveyQuestionAnswerSuiteReportItem.cs" />
		<Compile Include="Survey\Queries\SurveyQuestionData.cs" />
		<Compile Include="Survey\Queries\SurveyQuestionListItem.cs" />
		<Compile Include="Survey\Queries\SurveyQuestionSectionData.cs" />
		<Compile Include="Survey\SurveyAnswerEntity.cs" />
		<Compile Include="Survey\SurveyAnswerSuiteAnswerEntity.cs" />
		<Compile Include="Survey\SurveyAnswerSuiteEntity.cs" />
		<Compile Include="Survey\SurveyCampaignEntity.cs" />
		<Compile Include="Survey\SurveyCampaignExecutionEntity.cs" />
		<Compile Include="Survey\SurveyCampaignResponseExecutionEntity.cs" />
		<Compile Include="Survey\SurveyEntity.cs" />
		<Compile Include="Survey\SurveyEnum.cs" />
		<Compile Include="Survey\SurveyFeedbackAnswerEntity.cs" />
		<Compile Include="Survey\SurveyFeedbackEntity.cs" />
		<Compile Include="Survey\SurveyQuestionEntity.cs" />
		<Compile Include="Survey\SurveyQuestionSectionEntity.cs" />
		<Compile Include="TaskType\AutoSetStatusMethods\ITaskAutoSetStatusMethod.cs" />
		<Compile Include="TaskType\AutoSetStatusMethods\TaskSuccessFailedAutoSetStatusMethod.cs" />
		<Compile Include="TaskType\BusinessResultReferenceEntity.cs" />
		<Compile Include="TaskType\Commands\CreateEditContentTemplateListInTaskTypeCommand.cs" />
		<Compile Include="TaskType\Commands\CreateEditOrganizationTaskTypeCommand.cs" />
		<Compile Include="TaskType\Commands\CreateEditTaskTypeCommand.cs" />
		<Compile Include="TaskType\Commands\DeleteTaskTypeCommand.cs" />
		<Compile Include="TaskType\Commands\SetAssignmentPriorityOrganizationCommand.cs" />
		<Compile Include="TaskType\Queries\ExportFormQuery.cs" />
		<Compile Include="TaskType\Queries\GetAllDynamicFieldRelatedTaskTypeQuery.cs" />
		<Compile Include="TaskType\Queries\GetContentTemplateByTaskTypeIdQuery.cs" />
		<Compile Include="TaskType\Queries\GetOrganizationTypeQuery.cs" />
		<Compile Include="TaskType\Queries\GetTaskTypeAssignmentPriorityOrganizationQuery.cs" />
		<Compile Include="TaskType\Queries\GetTaskTypeByIdQuery.cs" />
		<Compile Include="TaskType\Queries\GetTaskTypeByTaskIdQuery.cs" />
		<Compile Include="TaskType\Queries\GetTaskTypeForOrganizationQuery.cs" />
		<Compile Include="TaskType\Queries\GetTaskTypeForTicketQuery.cs" />
		<Compile Include="TaskType\Queries\GetTaskTypeInWorkflowByServiceTypeIdQuery.cs" />
		<Compile Include="TaskType\Queries\GetTaskTypeListByServiceTypeQuery.cs" />
		<Compile Include="TaskType\Queries\GetTaskTypeListByWorkflowQuery.cs" />
		<Compile Include="TaskType\Queries\GetTaskTypeNotificationEventQuery.cs" />
		<Compile Include="TaskType\Queries\GetUserAssignByTaskTypeIdQuery.cs" />
		<Compile Include="TaskType\Queries\GetWorkflowListByTaskTypeIdQuery.cs" />
		<Compile Include="TaskType\Queries\GetWorkflowTaskTypeListQuery.cs" />
		<Compile Include="TaskType\Queries\SearchTaskTypeQuery.cs" />
		<Compile Include="TaskType\Queries\TaskTypeData.cs" />
		<Compile Include="TaskType\Queries\TaskTypeInfo.cs" />
		<Compile Include="TaskType\Queries\TaskTypeInWorkflowInfo.cs" />
		<Compile Include="TaskType\Queries\TaskTypeListItem.cs" />
		<Compile Include="TaskType\Queries\TaskTypeNotificationEventData.cs" />
		<Compile Include="TaskType\Queries\TaskTypeReportInfo.cs" />
		<Compile Include="TaskType\Queries\TaskTypeReportListQuery.cs" />
		<Compile Include="TaskType\TaskTypeExtendedEntity.cs" />
		<Compile Include="TaskType\TaskTypeEntity.cs" />
		<Compile Include="TaskType\TaskTypeNotificationEventEntity.cs" />
		<Compile Include="Tax\Commands\CreateEditTaxCommand.cs" />
		<Compile Include="Tax\Commands\DeleteTaxCommand.cs" />
		<Compile Include="Tax\Queries\GetTaxByIdQuery.cs" />
		<Compile Include="Tax\Queries\GetTaxListQuery.cs" />
		<Compile Include="Tax\Queries\TaxData.cs" />
		<Compile Include="Tax\TaxEntity.cs" />
		<Compile Include="TbCallback\Commands\CreateEditTbCallbackCallCommand.cs" />
		<Compile Include="TbCallback\Commands\CreateEditTbCallbackCommand.cs" />
		<Compile Include="TbCallback\Queries\GetTbCallbackByIdQuery.cs" />
		<Compile Include="TbCallback\Queries\GetTbCallbackByReferenceObjectIdQuery.cs" />
		<Compile Include="TbCallback\Queries\GetTbCallbackCallHistoriesQuery.cs" />
		<Compile Include="TbCallback\Queries\GetTbCallbackCallResultQuery.cs" />
		<Compile Include="TbCallback\Queries\TbCallbackCallData.cs" />
		<Compile Include="TbCallback\Queries\TbCallbackCallInfo.cs" />
		<Compile Include="TbCallback\Queries\TbCallbackCallResultData.cs" />
		<Compile Include="TbCallback\Queries\TbCallbackData.cs" />
		<Compile Include="TbCallback\TbCallbackCallEntity.cs" />
		<Compile Include="TbCallback\TbCallbackCallResultEntity.cs" />
		<Compile Include="TbCallback\TbCallbackEntity.cs" />
		<Compile Include="TemplateLibrary\Queries\GetDynamicFieldDefinitionsQuery.cs" />
		<Compile Include="TemplateLibrary\Queries\GetTemplateLibraryItemByRequestTicketQuery.cs" />
		<Compile Include="TemplateLibrary\Queries\TemplateLibraryItem.cs" />
		<Compile Include="TicketHotButton\Commands\CreateEditTicketHotButtonCommand.cs" />
		<Compile Include="TicketHotButton\Commands\SavedListTicketHotButtonCommand.cs" />
		<Compile Include="TicketHotButton\Queries\GetTicketHotButtonByIdQuery.cs" />
		<Compile Include="TicketHotButton\Queries\GetTicketHotButtonListByCustomerQuery.cs" />
		<Compile Include="TicketHotButton\Queries\GetTicketHotButtonListQuery.cs" />
		<Compile Include="TicketHotButton\Queries\TicketHotButtonData.cs" />
		<Compile Include="TicketHotButton\TicketHotButtonEntity.cs" />
		<Compile Include="Ticket\Queries\GetDropdownLoadUserOptionQuery.cs" />
		<Compile Include="Ticket\Queries\GetOrganizationForTicketQuery.cs" />
		<Compile Include="Ticket\Queries\GetUserListForTicketQuery.cs" />
		<Compile Include="UserAccount\IUserAccountCommand.cs" />
		<Compile Include="UserAccount\Queries\AspNetUserData.cs" />
		<Compile Include="UserAccount\Queries\AspNetUserListItem.cs" />
		<Compile Include="UserAccount\Queries\SearchUsersQuery.cs" />
		<Compile Include="UserAccount\UserProfileEntityData.cs" />
		<Compile Include="UserTaskAssignmentRouting\Commands\CreateEditUserTaskAssignmentRoutingCommand.cs" />
		<Compile Include="UserTaskAssignmentRouting\Queries\GetUserTaskAssignmentRoutingByIdQuery.cs" />
		<Compile Include="UserTaskAssignmentRouting\Queries\GetUserTaskAssignmentRoutingListQuery.cs" />
		<Compile Include="UserTaskAssignmentRouting\Queries\UserTaskAssignmentRoutingData.cs" />
		<Compile Include="UserTaskAssignmentRouting\Queries\UserTaskAssignmentRoutingListItem.cs" />
		<Compile Include="UserTaskAssignmentRouting\UserTaskAssignmentRoutingEntity.cs" />
		<Compile Include="Utilities\CsvExporter.cs" />
		<Compile Include="Utility\DateTimeUtility.cs" />
		<Compile Include="Utility\LanguageHelper.cs" />
		<Compile Include="Utility\ServerDirectoryHelper.cs" />
		<Compile Include="VirtualOffice\VirtualOfficeDisplaySlotEntity.cs" />
		<Compile Include="VirtualOffice\VirtualOfficeEmployeeEntity.cs" />
		<Compile Include="VirtualOffice\VirtualOfficeFloorEntity.cs" />
		<Compile Include="VirtualOffice\VirtualOfficeWorkspaceEntity.cs" />
		<Compile Include="WebChat\Commands\CreateEditMsTeamsAccountByAadObjectIdCommand.cs" />
		<Compile Include="WebChat\Commands\InsertWebChatCommand.cs" />
		<Compile Include="WebChat\Commands\InsertWebChatMessageCommand.cs" />
		<Compile Include="WebChat\Commands\InsertWebChatMessageListCommand.cs" />
		<Compile Include="WebChat\Commands\InsertWebChatUserMemberCommand.cs" />
		<Compile Include="WebChat\Commands\UpdateMsTeamsAccountNamesByAadObjectIdCommand.cs" />
		<Compile Include="WebChat\MsTeamsAccountEntity.cs" />
		<Compile Include="WebChat\Queries\GetMsTeamsAccountListQuery.cs" />
		<Compile Include="WebChat\Queries\GetWebChatGatewayCustomDataQuery.cs" />
		<Compile Include="WebChat\Queries\GetWebChatMessageListQuery.cs" />
		<Compile Include="WebChat\Queries\MsTeamsAccountData.cs" />
		<Compile Include="WebChat\Queries\SearchWebChatListQuery.cs" />
		<Compile Include="WebChat\Queries\WebChatData.cs" />
		<Compile Include="WebChat\Queries\WebChatGatewayCustomData.cs" />
		<Compile Include="WebChat\Queries\WebChatMessageData.cs" />
		<Compile Include="WebChat\WebChatEntity.cs" />
		<Compile Include="WebChat\WebChatGatewayCustomDataEntity.cs" />
		<Compile Include="WebChat\WebChatMessageEntity.cs" />
		<Compile Include="WebChat\WebChatUserMemberEntity.cs" />
		<Compile Include="WorkflowTaskTypeGroup\Commands\AddRemoveTaskToGroupCommand.cs" />
		<Compile Include="WorkflowTaskTypeGroup\Commands\CreateEditGroupCommand.cs" />
		<Compile Include="WorkflowTaskTypeGroup\Commands\CreateLinkToGroup.cs" />
		<Compile Include="WorkflowTaskTypeGroup\Commands\CreateTaskTypeGroupCommand.cs" />
		<Compile Include="WorkflowTaskTypeGroup\Commands\DeleteWorkflowTaskTypeGroupCommand.cs" />
		<Compile Include="WorkflowTaskTypeGroup\Queries\GetTaskTypeDataListByTaskTypeGroupIdQuery.cs" />
		<Compile Include="WorkflowTaskTypeGroup\Queries\GetTaskTypeListInGroupQuery.cs" />
		<Compile Include="WorkflowTaskTypeGroup\Queries\GetWorkflowTaskTypeGroupByIdQuery.cs" />
		<Compile Include="WorkflowTaskTypeGroup\Queries\GetWorkflowTaskTypeGroupListQuery.cs" />
		<Compile Include="WorkflowTaskTypeGroup\Queries\WorkflowTaskTypeGroupData.cs" />
		<Compile Include="WorkflowTaskTypeGroup\WorkflowTaskTypeGroupEntity.cs" />
		<Compile Include="WorkflowTaskTypeStage\Commands\CreateEditListTaskTypeStageCommand.cs" />
		<Compile Include="WorkflowTaskTypeStage\Commands\CreateTaskTypeStageCommand.cs" />
		<Compile Include="WorkflowTaskTypeStage\Commands\DeleteWorkflowTaskTypeStageCommand.cs" />
		<Compile Include="WorkflowTaskTypeStage\Queries\GetWorkflowTaskTypeStageByIdQuery.cs" />
		<Compile Include="WorkflowTaskTypeStage\Queries\WorkflowTaskTypeStageData.cs" />
		<Compile Include="WorkflowTaskTypeStage\WorkflowTaskTypeStageEntity.cs" />
		<Compile Include="Workflow\ApprovedItems_Workflow\Campaign_Approved\ApprovedCampaignInWorkflow.cs" />
		<Compile Include="Workflow\ApprovedItems_Workflow\IApprovedItemInWorkflow.cs" />
		<Compile Include="Workflow\ApproveItemWorkflowEvent.cs" />
		<Compile Include="Workflow\ApproveItemWorkflowHandler.cs" />
		<Compile Include="Workflow\Commands\CloneWorkflowCommand.cs" />
		<Compile Include="Workflow\Commands\CreateEditTaskTypeInWorkflowCommand.cs" />
		<Compile Include="Workflow\Commands\CreateEditTaskTypeListInWorkflowCommand.cs" />
		<Compile Include="Workflow\Commands\CreateEditWorkflowCommand.cs" />
		<Compile Include="Workflow\Commands\CreateEditWrapForWorkflowCommand.cs" />
		<Compile Include="Workflow\Commands\DeleteWorkflowCommand.cs" />
		<Compile Include="Workflow\Commands\DeleteWorkflowTaskTypeCommand.cs" />
		<Compile Include="Workflow\Commands\FullCloneWorkflowCommand.cs" />
		<Compile Include="Workflow\Commands\ImportWorkflowCommand.cs" />
		<Compile Include="Workflow\Queries\ExportWorkflowQuery.cs" />
		<Compile Include="Workflow\Queries\GetAllWorkflowQuery.cs" />
		<Compile Include="Workflow\Queries\GetWorkflowByIdQuery.cs" />
		<Compile Include="Workflow\Queries\GetWorkflowMermaidCodeQuery.cs" />
		<Compile Include="Workflow\Queries\GetWorkflowTaskTypeByIdQuery.cs" />
		<Compile Include="Workflow\Queries\GetWorkflowTaskTypeByWorkflowAndTaskGroupIdQuery.cs" />
		<Compile Include="Workflow\Queries\GetWorkflowTaskTypeByWorkflowAndTaskTypeIdQuery.cs" />
		<Compile Include="Workflow\Queries\GetWorkflowTaskTypeByWorkflowIdQuery.cs" />
		<Compile Include="Workflow\Queries\SearchWorkflowQuery.cs" />
		<Compile Include="Workflow\Queries\WorkflowData.cs" />
		<Compile Include="Workflow\Queries\WorkflowTaskTypeData.cs" />
		<Compile Include="Workflow\WorkflowBusinessResultEntity.cs" />
		<Compile Include="Workflow\WorkflowEntity.cs" />
		<Compile Include="Workflow\WorkflowTaskTypeEntity.cs" />
		<Compile Include="WorkItemSummary\Commands\CreateEditWorkItemSummaryCommand.cs" />
		<Compile Include="WorkItemSummary\Commands\CreateEditWorkItemSummaryCustomerServiceCommand.cs" />
		<Compile Include="WorkItemSummary\Commands\CreateEditWorkItemSummarySaleEffectivenessCommand.cs" />
		<Compile Include="WorkItemSummary\Queries\GetWorkItemSummaryByIdQuery.cs" />
		<Compile Include="WorkItemSummary\Queries\GetWorkItemSummaryByInteractionIdQuery.cs" />
		<Compile Include="WorkItemSummary\Queries\GetWorkItemSummaryCustomerServiceByWorkItemIdQuery.cs" />
		<Compile Include="WorkItemSummary\Queries\GetWorkItemSummarySaleEffectivenessByWorkItemIdQuery.cs" />
		<Compile Include="WorkItemSummary\Queries\WorkItemSummaryCustomerServiceData.cs" />
		<Compile Include="WorkItemSummary\Queries\WorkItemSummaryData.cs" />
		<Compile Include="WorkItemSummary\Queries\WorkItemSummarySaleEffectivenessData.cs" />
		<Compile Include="WorkItemSummary\WorkItemSummaryCustomerServiceEntity.cs" />
		<Compile Include="WorkItemSummary\WorkItemSummaryEntity.cs" />
		<Compile Include="WorkItemSummary\WorkItemSummarySaleEffectivenessEntity.cs" /> 
		<Compile Include="Outbound\Appointment\Commands\CancelAppointmentCommand.cs" />
		<Compile Include="Outbound\Appointment\Commands\CreateAppointmentCommand.cs" />
		<Compile Include="Outbound\Appointment\Commands\CreateNewAppointmentCommand.cs" />
		<Compile Include="Outbound\Appointment\Commands\UpdateAppointmentCommand.cs" />
		<Compile Include="Outbound\Appointment\Commands\UpdateAppointmentLeadBudgetCommand.cs" />
		<Compile Include="Outbound\Appointment\Commands\UpdateAppointmentResultCommand.cs" />
		<Compile Include="Outbound\Brand\Commands\CreateBrandCommand.cs" />
		<Compile Include="Outbound\CallResult\Commands\CreateResultCodeSuiteCommand.cs" /> 
		<Compile Include="Outbound\Campaign\Commands\SendEmailCampaignCommand.cs" /> 
		<Compile Include="Outbound\CampaignCompletionCode\Commands\CreateCampaignCompletionCodeCommand.cs" />
		<Compile Include="Outbound\CampaignCompletionCode\Commands\DeleteCampaignCompletionCodeCommand.cs" />
		<Compile Include="Outbound\CampaignTemplateCode\Commands\CreateCompletionCategoryCommand.cs" />
		<Compile Include="Outbound\CampaignTemplateCode\Commands\EditCompletionCategoryCommand.cs" />
		<Compile Include="Outbound\Company\Commands\AddCompanyCommand.cs" />
		<Compile Include="Outbound\Company\Commands\UpdateCompanyCommand.cs" />
		<Compile Include="Outbound\CompletionCategory\Commands\CreateCompletionCategoryCommand.cs" />
		<Compile Include="Outbound\CompletionCategory\Commands\EditCompletionCategoryCommand.cs" />
		<Compile Include="Outbound\Contact\Commands\CreateEditContactCommand.cs" />
		<Compile Include="Outbound\ContactCall\Commands\CreateAutoDialContactCallCommand.cs" />
		<Compile Include="Outbound\ContactCall\Commands\CreateContactCallCommand.cs" />
		<Compile Include="Outbound\ContactCall\Commands\DeleteContactCallCommand.cs" />
		<Compile Include="Outbound\ContactCall\Commands\UpdateResultContactCallCommand.cs" />
		<Compile Include="Outbound\ContactCallResultServiceCallback\Commands\CreateContactCallResultServiceCallbackCommand.cs" />
		<Compile Include="Outbound\ContactRelationship\Commands\ConvertRelationshipToContactCommand.cs" />
		<Compile Include="Outbound\ContactRelationship\Commands\CreateEditContactRelationshipCommand.cs" />
		<Compile Include="Outbound\HotListGroup\Command\DistributeProspectAssignmentCommand.cs" />
		<Compile Include="Outbound\HotListGroup\Command\RemoveUserInHotGroupCommand.cs" />
		<Compile Include="Outbound\ImportSession\Commands\CheckDuplciationStatusWithExistingContactCommand.cs" />
		<Compile Include="Outbound\ImportSession\Commands\ReportProcessingResultOfImportSessionCommand.cs" />
		<Compile Include="Outbound\ImportSession\Commands\UpdateMappingSchemaOfImportSessionCommand.cs" />
		<Compile Include="Outbound\ImportSession\Commands\MarkInternalDuplicationSolutionCommand.cs" />
		<Compile Include="Outbound\ImportSession\Commands\MarkDuplicationSolutionCommand.cs" />
		<Compile Include="Outbound\ImportSession\Commands\ImportAllValidStagingContactIntoDbCommand.cs" />
		<Compile Include="Outbound\ImportSession\Commands\CreateImportSessionCommand.cs" />
		<Compile Include="Outbound\ImportSession\Commands\CheckDuplicationInInternalStagingContactCommand.cs" />
		<Compile Include="Outbound\LeadAssignment\Commands\UpdateLeadAssignmentSuggestionCommand.cs" />
		<Compile Include="Outbound\LeadAssignment\Commands\UpdateLeadAssignmentDistributionCommand.cs" />
		<Compile Include="Outbound\LeadAssignment\Commands\UnassignLeadAssignmentCommand.cs" />
		<Compile Include="Outbound\LeadAssignment\Commands\AssignLeadToSaleFieldCommand.cs" />
		<Compile Include="Outbound\LeadAssignment\Commands\AddDistributeAppointmentNotifyCommand.cs" />
		<Compile Include="Outbound\ExcelImport\MassData\UpdateImportSessionCommand.cs" />
		<Compile Include="Outbound\ExcelImport\MassData\UpdateMappingOptionsCommand.cs" />
		<Compile Include="Outbound\ExcelImport\MassData\UpdateImportDigitalContactCommand.cs" />
		<Compile Include="Outbound\ExcelImport\MassData\UpdateImportCustomerCodeCommand.cs" />
		<Compile Include="Outbound\ExcelImport\MassData\ScanImportFWDContractCommand.cs" />
		<Compile Include="Outbound\ExcelImport\MassData\ScanImportDataCommand.cs" />
		<Compile Include="Outbound\ExcelImport\MassData\QuickScanImportDataCommand.cs" />
		<Compile Include="Outbound\ExcelImport\MassData\OverwiteCustomerInfoCommand.cs" />
		<Compile Include="Outbound\ExcelImport\MassData\LogCustomerImportCommand.cs" />
		<Compile Include="Outbound\ExcelImport\MassData\ImportCustomerToCampaignCommand.cs" />
		<Compile Include="Outbound\ExcelImport\MassData\CreateMappingOptionsCommand.cs" />
		<Compile Include="Outbound\ExcelImport\MassData\CreateImportSessionCommand.cs" />
		<Compile Include="Outbound\ExcelImport\MassData\BulkInsertCustomerRawCommand.cs" />
	</ItemGroup>
	<ItemGroup>
		<None Remove="TinyCRM.csproj.vspscc" />
	</ItemGroup>
	<ItemGroup>
		<!--<PackageReference Include="ReportViewerCore.NETCore" Version="15.1.26" />-->
		<!--<PackageReference Include="System.ComponentModel.Composition" Version="9.0.0" />-->
	</ItemGroup>
	<ItemGroup>
		<ProjectReference Include="..\Poptech.CEP.ClientIntegration\Poptech.CEP.ClientIntegration.csproj" />
		<ProjectReference Include="..\Webaby.Core\Webaby.Core.csproj" />
		<ProjectReference Include="..\Webaby\Webaby.csproj" />
	</ItemGroup>
	<ItemGroup>
		<Folder Include="DigitalMessageLog\" />
		<Folder Include="EquipmentSystem\Queries\" />
		<Folder Include="GanttProject\Command\" />
		<Folder Include="Maintenance\Commands\" />
		<Folder Include="Maintenance\Queries\" />
		<Folder Include="NotificationCase\NotificationCaseServices\TbCallback\" />
		<Folder Include="Phase\Notifications\" />
		<Folder Include="UserAccount\CustomerIdentity\" />
	</ItemGroup>
	<ItemGroup>
		<Reference Include="SpreadsheetGear">
			<HintPath>..\Libs\SpreadsheetGear.dll</HintPath>
		</Reference>
	</ItemGroup>
</Project>