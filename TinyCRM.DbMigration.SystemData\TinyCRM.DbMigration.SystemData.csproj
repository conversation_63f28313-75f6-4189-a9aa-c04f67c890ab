﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup Label="Globals">
    <SccProjectName>SAK</SccProjectName>
    <SccProvider>SAK</SccProvider>
    <SccAuxPath>SAK</SccAuxPath>
    <SccLocalPath>SAK</SccLocalPath>
  </PropertyGroup>

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>disable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <None Remove="DataSeeding\AspNetUserSeedingData_01.json" />
    <None Remove="TinyCRM.DbMigration.SystemData.csproj.vspscc" />
    <None Remove="TinyCRM.DbMigration.SystemData.SystemData.csproj.vspscc" />
  </ItemGroup>

  <ItemGroup>
    <EmbeddedResource Include="DataSeeding\SeedingData_07__DynamicForm_01.json" />
    <EmbeddedResource Include="DataSeeding\SeedingData_12__BusinessPermission_SystemDynamicForm_01.json" />
    <EmbeddedResource Include="DataSeeding\SeedingData_12__BusinessPermission_RequestTicketFirstTaskAutoNextTaskErrorLogs_01.json" />
    <EmbeddedResource Include="DataSeeding\SeedingData_11__BusinessPermission_CORE_01.json" />
    <EmbeddedResource Include="DataSeeding\SeedingData_12__BusinessPermission_ConstCodePermissions_01.json" />
    <EmbeddedResource Include="DataSeeding\SeedingData_12__BusinessPermission_TaskContentTemplate_01.json" />
    <EmbeddedResource Include="DataSeeding\SeedingData_12__BusinessPermission_ImportWorkflow_01.json" />
    <EmbeddedResource Include="DataSeeding\SeedingData_12__BusinessPermission_ImportTicketByExcel_01.json" />
    <EmbeddedResource Include="DataSeeding\SeedingData_12__BusinessPermission_ImportTaskByExcel_01.json" />
    <EmbeddedResource Include="DataSeeding\SeedingData_12__BusinessPermission_ImportServiceType_01.json" />
    <EmbeddedResource Include="DataSeeding\SeedingData_12__BusinessPermission_ConfigHotButton_01.json" />
    <EmbeddedResource Include="DataSeeding\SeedingData_12__BusinessPermission_Classification_01.json" />
    <EmbeddedResource Include="DataSeeding\SeedingData_12__BusinessPermission_Campaign_01.json" />
    <EmbeddedResource Include="DataSeeding\SeedingData_12__BusinessPermission_AlternativeNotiChannelContentTemplate_01.json" />
    <EmbeddedResource Include="DataSeeding\SeedingData_10__NotificationChannelSettings_01.json" />
    <EmbeddedResource Include="DataSeeding\SeedingData_09__MenuItem_01.json" />
    <EmbeddedResource Include="DataSeeding\SeedingData_08__TaskType_01.json" />
    <EmbeddedResource Include="DataSeeding\SeedingData_06__BusinessResult_01.json" />
    <EmbeddedResource Include="DataSeeding\SeedingData_05__ResultCodeSuite_01.json" />
    <EmbeddedResource Include="DataSeeding\SeedingData_04__ContentTemplate_01.json" />
    <EmbeddedResource Include="DataSeeding\SeedingData_03__CallResult_01.json" />
    <EmbeddedResource Include="DataSeeding\SeedingData_02__DueTime_01.json" />
    <EmbeddedResource Include="DataSeeding\SeedingData_01__BusinessSettings_01.json" />
    <EmbeddedResource Include="DataSeeding_AccountAdmin\SeedingData_00__AspNetRole_01.json" />
    <EmbeddedResource Include="DataSeeding_AccountAdmin\SeedingData_00__AspNetUser_01.json" />
    <EmbeddedResource Include="DataSeeding_AccountAdmin\SeedingData_00__AspNetUserRoles_01.json" />
    <EmbeddedResource Include="DataSeeding_AccountAdmin\SeedingData_00__RoleBusinessPermission_01.json" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\TinyCRM\TinyCRM.csproj" />
    <ProjectReference Include="..\Webaby.Core\Webaby.Core.csproj" />
    <ProjectReference Include="..\Webaby\Webaby.csproj" />
  </ItemGroup>

</Project>
