﻿using System;
using System.Data;
using System.Data.SqlClient;
using System.Threading.Tasks;
using AutoMapper;
using Microsoft.EntityFrameworkCore;
using TinyCRM.Customer.Queries;
using Webaby;
using Webaby.Data;
using Webaby.Localization;

namespace TinyCRM.Outbound.ExcelImport.MassData
{
    public class ScanImportFWDContractCommand : CommandBase
    {
        public Guid ImportSessionId { get; set; }
    }
    internal class ScanImportFWDContractCommandHandler : CommandHandlerBase<ScanImportFWDContractCommand>
    {
        public ScanImportFWDContractCommandHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task ExecuteAsync(ScanImportFWDContractCommand command)
        {
            var cmd = EntitySet.CreateDbCommand();
            cmd.CommandText = "fwd.ScanImportContractRaw";
            cmd.CommandType = CommandType.StoredProcedure;
            cmd.Parameters.Add(DbParameterHelper.AddNullableGuid(cmd, "@ImportSessionId", command.ImportSessionId));
            await EntitySet.ExecuteNonQueryAsync(cmd);
        }
    }
}
