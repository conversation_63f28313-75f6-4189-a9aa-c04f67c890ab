﻿using System;
using System.Data;
using System.Data.SqlClient;
using System.Threading.Tasks;
using TinyCRM.Enums;
using TinyCRM.Outbound.Campaign;
using TinyCRM.Outbound.Campaign.Queries;
using Webaby;
using Webaby.Data;

namespace TinyCRM.Outbound.TeamLeadCampaign.Queries
{
    public class SearchCampaignByTeamLeadQuery : QueryBase<CampaignTeamLeadListItem>
    {
        public string CampaignName { get; set; }

        public DateTime? SearchDate { get; set; }

        public CampaignStatus? Status { get; set; }
        ////
        public Guid OrganizationId { get; set; }
    }
    internal class SearchCampaignByTeamLeadQueryHandler : QueryHandlerBase<SearchCampaignByTeamLeadQuery, CampaignTeamLeadListItem>
    {
        public SearchCampaignByTeamLeadQueryHandler(IServiceProvider serviceProvider) : base(serviceProvider)
        {
        }
        public override async Task<QueryResult<CampaignTeamLeadListItem>> ExecuteAsync(SearchCampaignByTeamLeadQuery query)
        {
            var cmd = EntitySet.CreateDbCommand();
            cmd.Parameters.AddRange(new[]
            {
                DbParameterHelper.AddNullableGuid(cmd, "@OrganizationId", query.OrganizationId),
                DbParameterHelper.NewNullableDateTimeParameter(cmd, "@SearchDate", query.SearchDate),
                DbParameterHelper.AddNullableEnum(cmd, "@Status", query.Status),
                DbParameterHelper.AddNullableString(cmd, "@CampaignName", query.CampaignName),
            });
            cmd.Parameters.Add(DbParameterHelper.AddNullableInt(cmd, "@StartRow", query.Pagination.StartRow));
            cmd.Parameters.Add(DbParameterHelper.AddNullableInt(cmd, "@EndRow", query.Pagination.EndRow));

            cmd.CommandText = "telesale.SearchTeamLeadCampaignList";
            cmd.CommandType = CommandType.StoredProcedure;

            var mainQuery = await EntitySet.ExecuteReadCommandAsync<CampaignTeamLeadListItem>(cmd);
            return QueryResult.Create(mainQuery);
        }
    }
}
