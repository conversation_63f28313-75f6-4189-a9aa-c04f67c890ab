﻿using System;
using System.Data;
using System.Data.Common;
using System.Threading.Tasks;
using Webaby;
using Webaby.Core.Organization.Queries;
using Webaby.Data;

namespace TinyCRM.Organization.Queries
{
    public class GetAvailableOrganizationQuery : QueryBase<OrganizationData>
    {
        public Guid UserId { get; set; }

        public GetAvailableOrganizationQuery(Guid userId)
        {
            this.UserId = userId;
        }
    }

    internal class GetAvailableOrganizationQueryHandler :
        QueryHandlerBase<GetAvailableOrganizationQuery, OrganizationData>
    {
        public GetAvailableOrganizationQueryHandler(IServiceProvider serviceProvider) : base(serviceProvider)
        {
        }

        public override async Task<QueryResult<OrganizationData>> ExecuteAsync(GetAvailableOrganizationQuery query)
        {
            var cmd = EntitySet.CreateDbCommand(CommandType.StoredProcedure, "GetAvailableOrganization");
            cmd.Parameters.Add(DbParameterHelper.AddNullableGuid(cmd, "@UserId", query.UserId));

            var result = await EntitySet.ExecuteReadCommandAsync<OrganizationData>(cmd);
            await Task.CompletedTask;
            return QueryResult.Create(result);
        }
    }
}