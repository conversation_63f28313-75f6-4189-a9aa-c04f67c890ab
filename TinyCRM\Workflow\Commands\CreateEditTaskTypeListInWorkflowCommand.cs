﻿using AutoMapper;
using DocumentFormat.OpenXml.Spreadsheet;
using LinqToDB;
using LinqToDB.Data;
using LinqToDB.EntityFrameworkCore;
using LinqToDB.Tools;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using Microsoft.Exchange.WebServices.Data;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Graph.Models.IdentityGovernance;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using System.Linq;
using System.Threading.Tasks;
using TinyCRM.AutomaticTask;
using TinyCRM.TaskType;
using Webaby;
using Webaby.Data;
using Webaby.Localization;
using Webaby.Security;
using Task = System.Threading.Tasks.Task;

namespace TinyCRM.Workflow.Commands
{
    public class CreateEditTaskTypeListInWorkflowCommand : CommandBase
    {
        public Guid WorkflowId { get; set; }

        public List<TaskTypeInWorkflowItem> TaskTypeInWorkflowItems { get; set; }
    }

    internal class CreateEditTaskTypeListInWorkflowCommandHandler : CommandHandlerBase<CreateEditTaskTypeListInWorkflowCommand>
    {        
        public IUserService _userService { get; set; }
        public CreateEditTaskTypeListInWorkflowCommandHandler(IServiceProvider serviceProvider) : base(serviceProvider)
        { _userService = ServiceProvider.GetRequiredService<IUserService>(); }

        public override async Task ExecuteAsync(CreateEditTaskTypeListInWorkflowCommand command)
        {
            var linq2dbCtx = (DataConnection)EntitySet.GetLinqToDBContext();            
            var curentUser = _userService.GetCurrentUser().Id;
            var curentdDate = DateTime.Now;

            var WorkflowTaskType = EntitySet.Get<WorkflowTaskTypeEntity>();
            var TaskType = EntitySet.Get<TaskTypeEntity>();
            var AutoNextTask = EntitySet.Get<AutoNextTaskEntity>();

            // 1. Xóa (mark deleted) các tasktype không còn trong danh sách mới

            await WorkflowTaskType.Where(wtt => wtt.WorkflowId == command.WorkflowId && !command.TaskTypeInWorkflowItems.Select(x => x.TaskTypeId).Contains(wtt.TaskTypeId))
                .UpdateAsync(wf => new WorkflowTaskTypeEntity { 
                    Deleted = true, 
                    DeletedBy = curentUser, 
                    DeletedDate = curentdDate
                });

            // 2. Cập nhật các tasktype đã tồn tại
            foreach (var item in command.TaskTypeInWorkflowItems)
            {
                var entityUpdate = EntitySet.Get<WorkflowTaskTypeEntity>().Where(wtt => wtt.WorkflowId == command.WorkflowId && wtt.TaskTypeId == item.TaskTypeId).FirstOrDefault();
                if (entityUpdate != null)
                {
                    entityUpdate.WorkingOrder = item.WorkingOrder;
                    entityUpdate.AssignedConditionCount = item.AssignedConditionCount;
                    await Repository.SaveAsync(entityUpdate);
                }
            }

            // 3. Thêm mới những tasktype chưa tồn tại
            var existingIds = await EntitySet.Get<WorkflowTaskTypeEntity>()
                .Where(wtt => wtt.WorkflowId == command.WorkflowId)
                .Select(wtt => wtt.TaskTypeId).ToListAsyncLinqToDB();


            var newTaskTypes = command.TaskTypeInWorkflowItems
                .Where(x => !existingIds.Contains(x.TaskTypeId))
                .Select(x => new WorkflowTaskTypeEntity
                {
                    Id = Guid.NewGuid(),
                    WorkflowId = command.WorkflowId,
                    TaskTypeId = x.TaskTypeId,
                    WorkingOrder = x.WorkingOrder,
                    AssignedConditionCount = x.AssignedConditionCount,
                    CreatedBy = curentUser,
                    CreatedDate = curentdDate,
                    Deleted = false
                }).ToList();

            linq2dbCtx.BulkCopy(new BulkCopyOptions {
                TableName = "WorkflowTaskType",
                BulkCopyType = BulkCopyType.Default
            }, newTaskTypes);

            // 4. Update AutoNextTask.NextTaskFormula (dựa trên WorkflowTaskType.Id)
            var query1Cte =
                (from wtt in WorkflowTaskType
                join tt in TaskType on wtt.TaskTypeId equals tt.Id
                from ant in AutoNextTask
                    .Where(x => x.ReferenceObjectId == wtt.Id)
                    .DefaultIfEmpty() // left join
                where wtt.WorkflowId == command.WorkflowId
                select new
                {
                    ID = wtt.Id,
                    TaskTypeId = tt.Id,
                    WorkingOrder = wtt.WorkingOrder,
                    WorkflowId = wtt.WorkflowId,                    
                    AutoId = ant != null ? ant.Id : (Guid?)null,
                    NextTaskId = ant.NextTaskId,
                    NextTaskFormula = ant.NextTaskFormula,
                    TaskType = tt.TaskType,
                }).ToLinqToDB().AsCte();

            var updateQuery = (from ant in AutoNextTask
                               join cte in query1Cte on ant.Id equals cte.AutoId
                               join temp in query1Cte on ant.NextTaskId equals temp.TaskTypeId
                               select new { ant, temp.WorkingOrder });

            await updateQuery.Set(x => x.ant.NextTaskFormula, x => x.WorkingOrder.ToString())
                .Set(x => x.ant.ModifiedBy, x => curentUser)
                .Set(x => x.ant.ModifiedDate, x => curentdDate)
                .UpdateAsync();

            // 5. Update AutoNextTask.NextTaskFormula (dựa trên WorkflowId làm ReferenceObjectId)

            var query2Cte =
                (from wtt in WorkflowTaskType
                 join tt in TaskType on wtt.TaskTypeId equals tt.Id
                 from ant in AutoNextTask
                     .Where(x => x.ReferenceObjectId == wtt.WorkflowId)
                     .DefaultIfEmpty() // left join
                 where wtt.WorkflowId == command.WorkflowId
                 select new
                 {
                     ID = wtt.Id,
                     TaskTypeId = tt.Id,
                     WorkingOrder = wtt.WorkingOrder,
                     WorkflowId = wtt.WorkflowId,
                     AutoId = ant != null ? ant.Id : (Guid?)null,
                     NextTaskId = ant.NextTaskId,
                     NextTaskFormula = ant.NextTaskFormula,
                     TaskType = tt.TaskType,
                 }).ToLinqToDB().AsCte();

            var update2Query = (from ant in AutoNextTask
                               join cte in query2Cte on ant.Id equals cte.AutoId
                               join temp in query2Cte on ant.NextTaskId equals temp.TaskTypeId
                               select new { ant, temp.WorkingOrder });

            await update2Query.Set(x => x.ant.NextTaskFormula, x => x.WorkingOrder.ToString())
                .Set(x=> x.ant.ModifiedBy, x => curentUser )
                .Set(x => x.ant.ModifiedDate, x => curentdDate)
                .UpdateAsync();


            //var cmd = EntitySet.CreateDbCommand();
            //cmd.Parameters.Add(DbParameterHelper.AddNullableGuid(cmd, "@WorkflowId", command.WorkflowId));
            //cmd.Parameters.Add(DbParameterHelper.AddNullableGuid(cmd, "@UserId", _userService.GetCurrentUser().Id));

            //DataTable taskTypeList = new DataTable();
            //taskTypeList.Columns.Add("Id", typeof(Guid));
            //taskTypeList.Columns.Add("Number", typeof(int));
            //taskTypeList.Columns.Add("SortOrder", typeof(int));

            //foreach (var taskType in command.TaskTypeInWorkflowItems)
            //{
            //    taskTypeList.Rows.Add(taskType.TaskTypeId, taskType.AssignedConditionCount, taskType.WorkingOrder);
            //}

            //var taskTypeListParameter = new SqlParameter();
            //taskTypeListParameter.ParameterName = "@TaskTypeList";
            //taskTypeListParameter.Value = taskTypeList;
            //taskTypeListParameter.SqlDbType = SqlDbType.Structured;
            //taskTypeListParameter.TypeName = "dbo.ObjectNumberList";

            //cmd.Parameters.Add(taskTypeListParameter);

            //cmd.CommandText = "dbo.CreateEditTaskTypeListInWorkflow";
            //cmd.CommandType = CommandType.StoredProcedure;

            //await EntitySet.ExecuteNonQueryAsync(cmd);
        }
    }

    public class TaskTypeInWorkflowItem
    {
        public Guid TaskTypeId { get; set; }

        public int WorkingOrder { get; set; }

        public int? AssignedConditionCount { get; set; }
    }
}