﻿using System;
using System.Data;
using System.Threading.Tasks;
using Webaby;
using Webaby.Data;

namespace TinyCRM.Outbound.ImportSession.Queries
{
    public class GetDuplicationStagingContactQuery : QueryBase<ContactDedupEntity>
    {
        public Guid ImportSessionId { get; set; }
    }
    public class GetDuplicationStagingContactQueryHandler : QueryHandlerBase<GetDuplicationStagingContactQuery, ContactDedupEntity>
    {
        public GetDuplicationStagingContactQueryHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task<QueryResult<ContactDedupEntity>> ExecuteAsync(GetDuplicationStagingContactQuery query)
        {
            var cmd = EntitySet.CreateDbCommand();
            cmd.CommandText = "Exec ImportContact_Deduplication_GetDuplicationListWithDb @ImportSessionId,5,10";
            cmd.CommandType = CommandType.Text;

            cmd.Parameters.Add(DbParameterHelper.AddNullableGuid(cmd, "@ImportSessionId", query.ImportSessionId));

            var contactDedupQuery = await EntitySet.ExecuteReadCommandAsync<ContactDedupEntity>(cmd);

            return new QueryResult<ContactDedupEntity>(contactDedupQuery);
        }
    }
}
