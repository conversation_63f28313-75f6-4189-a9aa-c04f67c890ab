﻿using System;
using System.Data;
using System.Data.SqlClient;
using System.Threading.Tasks;
using Webaby;
using Webaby.Data;

namespace TinyCRM.Outbound.ExcelImport.Queries
{

    public class GetImportCustomerSummaryQuery : QueryBase<CustomerImportResult>
    {
        public Guid ImportSessionId { get; set; }

        public Guid? CampaignId { get; set; }
    }
    internal class GetImportCustomerSummaryHandler : QueryHandlerBase<GetImportCustomerSummaryQuery, CustomerImportResult>
    {
        public GetImportCustomerSummaryHandler(IServiceProvider serviceProvider) : base(serviceProvider)
        {
        }
        public override async Task<QueryResult<CustomerImportResult>> ExecuteAsync(GetImportCustomerSummaryQuery query)
        {
            var command = EntitySet.CreateDbCommand();
            command.CommandText = "dbo.ImportCustomer_GetScanDataSummary";
            command.CommandType = CommandType.StoredProcedure;

            command.Parameters.Add(DbParameterHelper.AddNullableGuid(command, "@ImportSessionId", query.ImportSessionId));
            command.Parameters.Add(DbParameterHelper.AddNullableGuid(command, "@CampaignId", query.CampaignId));

            var result = await EntitySet.ExecuteReadCommandAsync<CustomerImportResult>(command);
            return QueryResult.Create(result);
        }
    }
}
