﻿using System;
using System.Data;
using System.Data.SqlClient;
using System.Threading.Tasks;
using Webaby;
using Webaby.Data;

namespace TinyCRM.Outbound.ExcelImport.Queries
{
    public class GetImportCustomerSummaryReportQuery : QueryBase<CustomerImportReport>
    {
        public Guid ImportSessionId { get; set; }

        public Guid? CampaignId { get; set; }

        public string ErrorCode { get; set; }

        public int? WarningCode { get; set; }
    }
    internal class GetImportCustomerSummaryReportQueryHandler : QueryHandlerBase<GetImportCustomerSummaryReportQuery, CustomerImportReport>
    {
        public GetImportCustomerSummaryReportQueryHandler(IServiceProvider serviceProvider) : base(serviceProvider)
        {
        }
        public override async Task<QueryResult<CustomerImportReport>> ExecuteAsync(GetImportCustomerSummaryReportQuery query)
        {
            var command = EntitySet.CreateDbCommand();
            command.CommandText = "dbo.ImportCustomer_GetScanDataSummaryReport";
            command.CommandType = CommandType.StoredProcedure;

            command.Parameters.Add(DbParameterHelper.AddNullableGuid(command, "@ImportSessionId", query.ImportSessionId));
            command.Parameters.Add(DbParameterHelper.AddNullableGuid(command, "@CampaignId", query.CampaignId));
            command.Parameters.Add(DbParameterHelper.AddNullableString(command, "@ErrorCode", query.ErrorCode));
            command.Parameters.Add(DbParameterHelper.AddNullableInt(command, "@WarningCode", query.WarningCode));

            var result = await EntitySet.ExecuteReadCommandAsync<CustomerImportReport>(command);
            return QueryResult.Create(result);
        }
    }
}