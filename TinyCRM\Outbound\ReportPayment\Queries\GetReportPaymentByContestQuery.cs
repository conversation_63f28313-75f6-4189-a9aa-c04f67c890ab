﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Webaby;
using Webaby.Data;

namespace TinyCRM.Outbound.ReportPayment.Queries
{
    public class GetReportPaymentByContestQuery : QueryBase<DataSet>
    {
        public string ContestCode { get; set; }
        public string ContestName { get; set; }
    }
    internal class GetReportPaymentByContestQueryHandler : QueryHandlerBase<GetReportPaymentByContestQuery, DataSet>
    {
        public GetReportPaymentByContestQueryHandler(IServiceProvider serviceProvider) : base(serviceProvider)
        {
        }
        public override async Task<QueryResult<DataSet>> ExecuteAsync(GetReportPaymentByContestQuery query)
        {
            var cmd = EntitySet.CreateDbCommand();
            cmd.Parameters.Add(DbParameterHelper.AddNullableString(cmd, "@ContestCode", query.ContestCode));
            cmd.Parameters.Add(DbParameterHelper.AddNullableString(cmd, "@ContestName", query.ContestName));

            cmd.CommandText = "fwd.GetReportPaymentByContest";
            cmd.CommandType = CommandType.StoredProcedure;
            var mainQuery = await EntitySet.ExecuteReadCommandAsync(cmd);
            return QueryResult.Create(new DataSet[] { mainQuery });
        }
    }
}