﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using System.Linq;
using System.Threading.Tasks;
using TinyCRM.PartCustomer.Queries;
using Webaby;
using Webaby.Data;

namespace TinyCRM.MonthlyPartFeeBatch.Queries
{
    public class GetMonthlyPartFeeItemListByBatchQuery : QueryBase<MonthlyPartFeeBatchItemData>
    {
        public Guid MonthlyPartFeeBatchId { get; set; }

        public MonthlyPartFeeBatchItemStatus? FeeStatus { get; set; }

        public MonthlyPartFeeBatchItemStatus? DebitNoteStatus { get; set; }

        public MonthlyPartFeeBatchItemStatus? NotificationStatus { get; set; }

        public Guid? BlockId { get; set; }

        public Guid? FloorId { get; set; }

        public string PartCode { get; set; }
    }

    public class GetMonthlyPartFeeItemListByBatchQueryHandler : QueryHandlerBase<GetMonthlyPartFeeItemListByBatchQuery, MonthlyPartFeeBatchItemData>
    {
        public GetMonthlyPartFeeItemListByBatchQueryHandler(IServiceProvider serviceProvider) : base(serviceProvider)
        {
        }

        public override async Task<QueryResult<MonthlyPartFeeBatchItemData>> ExecuteAsync(GetMonthlyPartFeeItemListByBatchQuery query)
        {
            int startRow = query.Pagination.Index * query.Pagination.Size + 1;
            int endRow = query.Pagination.Index * query.Pagination.Size + query.Pagination.Size;

            var cmd = EntitySet.CreateDbCommand(CommandType.StoredProcedure, "dbo.GetMonthlyPartFeeItemListByBatch");
            cmd.Parameters.Add(DbParameterHelper.AddNullableGuid(cmd, "@MonthlyPartFeeBatchId", query.MonthlyPartFeeBatchId));
            cmd.Parameters.Add(DbParameterHelper.AddNullableEnum(cmd, "@FeeStatus", query.FeeStatus));
            cmd.Parameters.Add(DbParameterHelper.AddNullableEnum(cmd, "@DebitNoteStatus", query.DebitNoteStatus));
            cmd.Parameters.Add(DbParameterHelper.AddNullableEnum(cmd, "@NotificationStatus", query.NotificationStatus));
            cmd.Parameters.Add(DbParameterHelper.AddNullableGuid(cmd, "@BlockId", query.BlockId));
            cmd.Parameters.Add(DbParameterHelper.AddNullableGuid(cmd, "@FloorId", query.FloorId));
            cmd.Parameters.Add(DbParameterHelper.AddNullableString(cmd, "@PartCode", query.PartCode));
            cmd.Parameters.Add(DbParameterHelper.AddNullableInt(cmd, "@StartRow", startRow));
            cmd.Parameters.Add(DbParameterHelper.AddNullableInt(cmd, "@EndRow", endRow));

            var result = (await EntitySet.ExecuteReadCommandAsync<MonthlyPartFeeBatchItemData>(cmd)).ToList();
            return QueryResult.Create(result);
        }
    }
}