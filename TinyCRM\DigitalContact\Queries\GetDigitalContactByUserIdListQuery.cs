﻿using System.Collections.Generic;
using System.Data;
using System.Threading.Tasks;
using Webaby;
using Webaby.Data;
using AutoMapper;
using Webaby.Localization;

namespace TinyCRM.DigitalContact.Queries
{
    public class GetDigitalContactByUserIdListQuery : QueryBase<DigitalContactData>
    {
        public List<string> UserIdList { get; set; }
    }

    internal class GetDigitalContactByUserIdListQueryHandler : QueryHandlerBase<GetDigitalContactByUserIdListQuery, DigitalContactData>
    {
        public GetDigitalContactByUserIdListQueryHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task<QueryResult<DigitalContactData>> ExecuteAsync(GetDigitalContactByUserIdListQuery query)
        { 
            var cmd = EntitySet.CreateDbCommand();
            cmd.Parameters.AddRange(new[]
            {
                DbParameterHelper.NewStringListParameter("@UserIdList", query.UserIdList),
            });
            cmd.CommandText = "dbo.GetDigitalContactByUserIdList";
            cmd.CommandType = CommandType.StoredProcedure;

            var mainQuery = await EntitySet.ExecuteReadCommandAsync<DigitalContactData>(cmd);
            return new QueryResult<DigitalContactData>(mainQuery);
        }
    }
}
