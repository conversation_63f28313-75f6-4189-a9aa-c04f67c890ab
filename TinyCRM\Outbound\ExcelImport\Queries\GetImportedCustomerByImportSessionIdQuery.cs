﻿using System;
using System.Data;
using System.Data.SqlClient;
using System.Threading.Tasks;
using TinyCRM.Customer.Queries;
using Webaby;
using Webaby.Data;

namespace TinyCRM.Outbound.ExcelImport.Queries
{
    public class GetImportedCustomerByImportSessionIdQuery : QueryBase<CustomerListItem>
    {
        public Guid ImportSessionId { get; set; }

        public bool IsQuickImport { get; set; }
    }
    internal class GetImportedCustomerByImportSessionIdQueryHandler : QueryHandlerBase<GetImportedCustomerByImportSessionIdQuery, CustomerListItem>
    {
        public GetImportedCustomerByImportSessionIdQueryHandler(IServiceProvider serviceProvider) : base(serviceProvider)
        {
        }
        public override async Task<QueryResult<CustomerListItem>> ExecuteAsync(GetImportedCustomerByImportSessionIdQuery query)
        {
            int startRow = query.Pagination.Index * query.Pagination.Size + 1;
            int endRow = query.Pagination.Index * query.Pagination.Size + query.Pagination.Size;

            var command = EntitySet.CreateDbCommand();
            command.CommandText = "dbo.ImportCustomer_GetResult";
            command.CommandType = CommandType.StoredProcedure;

            command.Parameters.Add(DbParameterHelper.AddNullableGuid(command, "@ImportSessionId", query.ImportSessionId));
            command.Parameters.Add(DbParameterHelper.NewNullableBooleanParameter(command, "@IsQuickImport", query.IsQuickImport));
            command.Parameters.Add(DbParameterHelper.AddNullableInt(command, "@StartRow", startRow));
            command.Parameters.Add(DbParameterHelper.AddNullableInt(command, "@EndRow", endRow));

            var result = await EntitySet.ExecuteReadCommandAsync<CustomerListItem>(command);
            return QueryResult.Create(result);
        }
    }
}
