﻿@model TinyCRM.Web.Models.CustomerListModel
@{
    //Layout = "~/Views/Shared/_EmptyView-v5.cshtml";
    Layout = null;
}
@{
    var array = Model.Items.ToArray();
    var pageCount = (Int32)Math.Ceiling((Double)Model.Total / Model.PageSize);
    var configs = Model.Configs;
}

<style>
    [data-field="Tên KH"] {
        display: flex !important;
    }
</style>

@if (Model.Total > 0)
{
    <table id="table-id" class="table table-striped table-bordered table-hover table-checkable responsive no-wrap" width="100%">
        <thead>
            <tr>
                @foreach (var config in configs.GroupBy(x => new { x.DisplayName, x.Order, x.<PERSON>idden }).OrderBy(x => x.Key.Order))
                {
                    <th class="@(config.Key.IsHidden ? "none" :"all")">@T[config.Key.DisplayName] </th>
                }
                @if (!Model.IsDisabled && Html.IsEnable("Create", "RequestTicket"))
                {
                    <th class="all"></th>
                }
            </tr>
        </thead>
        <tbody>
            @foreach (var cusItem in array)
            {
                string badgeClass = "m-badge--info";
                string customerSourceTitle = T["Khách hàng từ core"];
                if (!cusItem.IsBackendCustomer)
                {
                    badgeClass = "m-badge--warning";
                    customerSourceTitle = T["Khách hàng vãng lai"];
                }
                string primaryCustomerClass = (cusItem.IsPrimary.HasValue && cusItem.IsPrimary.Value) ? string.Format("<span class=\"fa fa-crown m--font-warning\" style=\"display: inline; margin-left: 5px;\"></span>") : "";
                <tr>
                    @foreach (var c in configs.GroupBy(x => new { x.DisplayName, x.Order }).OrderBy(x => x.Key.Order))
                    {
                        var gKey = c.Key;
                        <td>
                            @{
                                var combine = new List<string>();
                                foreach (var gItem in c)
                                {
                                    var val = cusItem.GetPropertyValue(gItem.FieldName);
                                    if (val != null)
                                    {
                                        if (gItem.FieldName.Equals("Name"))
                                        {
                                            string html = string.Format("<span class=\"m-badge m-badge--dot {0}\" style=\"float: left; margin-right: 5px; margin-top: 6px;\"></span>", badgeClass);
                                            html += string.Format("<span style=\"float:left\" title=\"{0}\">{1}</span>", customerSourceTitle, val?.ToString() ?? "");
                                            combine.Add(html);
                                        }
                                        else if (gItem.FieldName.Equals("PartName"))
                                        {
                                            string html = "<div style=\"float:left; display: flex; \">" + string.Format("<span style=\"display: inline; \" >{0}</span>", val?.ToString() ?? "") + primaryCustomerClass + "</div>";
                                            combine.Add(html);
                                        }
                                        else if (gItem.FieldName.Equals("Code"))
                                        {
                                            string html = string.Format("{0}", val?.ToString() ?? "");
                                            if (Html.IsEnable("Create", "RequestTicket") && Configuration.GetValue<string>("Project") != "fwd")
                                            {
                                                html = string.Format("<a class=\"m-portlet__nav-link\"  href=\"{0}\" target=\"_blank\">{1}</a>", @Url.Action("Create", "RequestTicket", new { CustomerId = @cusItem.Id, ibPhoneNumber = Model.ibPhoneNumber, ibEmail = Model.ibEmail, ibUCID = Model.ibUCID }), val?.ToString() ?? "");

                                            }
                                            combine.Add(html);
                                        }
                                        else
                                        {
                                            combine.Add(string.Format("{0}", val?.ToString() ?? ""));
                                        }
                                    }
                                }
                                @Html.Raw(string.Join(" - ", combine.ToArray()))
                            }
                        </td>
                    }
                    @if (!Model.IsDisabled && (Html.IsEnable("Create", "RequestTicket") || Html.IsEnable("AddCustomerToAppartment", "Customer")))
                    {
                        <td>
                            @if (Html.IsEnable("Create", "RequestTicket") && Configuration.GetValue<string>("Project") != "fwd")
                            {
                                <a class="m-portlet__nav-link btn m-btn m-btn--hover-primary m-btn--icon m-btn--icon-only m-btn--pill" title="@T["Tạo phiếu"]" href="@Url.Action("Create", "RequestTicket", new { CustomerId = @cusItem.Id, ibPhoneNumber = Model.ibPhoneNumber, ibEmail = Model.ibEmail, ibUCID = Model.ibUCID, ibEmcAiTopic = Model.ibEmcAiTopic })"><i class="la la-plus-circle"></i></a>
                            }
                            @if (Html.IsEnable("AddCustomerToAppartment", "Customer"))
                            {
                                <a class="m-portlet__nav-link btn m-btn m-btn--hover-accent m-btn--icon m-btn--icon-only m-btn--pill" title="@T["Thêm căn hộ"]" href="javascript:;" onclick="addCustomerToAppartment('@(cusItem.Id)',@(Html.Raw(cusItem.IsPrimary.HasValue && cusItem.IsPrimary.Value ? "true" : "false")))"><i class="la la-home"></i></a>
                            }
                        </td>
                    }
                </tr>
            }
        </tbody>
    </table>
    <input type="hidden" id="lblTotalCustomerCount" value="@Model.Total" />
    @(await Html.PartialAsync("~/Views/Shared/Partials/Pagination.cshtml", new TinyCRM.Web.Models.PaginationModel
    {
        CurrentPage = Model.PageIndex,
        PageSize = Model.PageSize,
        PageCount = pageCount,
        TotalCount = (int)Model.Total,
        Href = "javascript:goPage({0})"
    }))

    using (Foot())
    {
        <script type="text/javascript">

            var DatatablesRequestTicketResponsive = function () {
                var initTable1 = function () {
                    var table = $('#table-id');
                    table.mDatatable({
                        //"filter": false,
                        //"info": false,
                        //"bLengthChange": false,
                        //"ordering": false,
                        //"paging": false,
                        //"responsive": true,
                        //"scrollX": !true,
                        //columnDefs: [
                        //]
                        "pagination": false,
                        "sortable": false,
                    });

                };
                return {
                    init: function () {
                        initTable1();
                    }
                };
            }();

            DatatablesRequestTicketResponsive.init();

        </script>
    }
}