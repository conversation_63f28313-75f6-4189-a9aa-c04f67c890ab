﻿using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using System.Data;
using TinyCRM.FeeCategory;
using TinyCRM.FeeCategory.Queries;
using TinyCRM.MonthlyPartFee.Queries;
using TinyCRM.Tax;
using Webaby;
using Webaby.Core.Sequence;
using Webaby.Data;

namespace TinyCRM.MonthlyPartFee.Command
{
    public class CalculateAndCreateMonthlyPartFeeCommand : CommandBase
    {
        public Guid PartId { get; set; }

        public Guid OwnerCustomerId { get; set; }

        public string OwnerCustomerName { get; set; }

        public int Year { get; set; }

        public int Month { get; set; }

        public List<FeeCategoryPartAmountInfo> FeeCategoryPartAmountInfoList { get; set; }

        public string Notes { get; set; }

        public string PropertyName { get; set; }

        public Guid CreatedBy { get; set; }

        public Guid? MonthlyPartFeeBatchItemId { get; set; }

        public bool GenerateDebitNote { get; set; }

        public bool NotifyMonthlyPartFee { get; set; }
    }

    internal class CalculateAndCreateMonthlyPartFeeCommandHandler : CommandHandlerBase<CalculateAndCreateMonthlyPartFeeCommand>
    {
        string _partMonthlyFeeCodeFormat;
        IMonthlyFeeUtlities _monthlyFeeUtlities;

        ISequenceUtility _sequenceUtility;

        public CalculateAndCreateMonthlyPartFeeCommandHandler(IServiceProvider serviceProvider,
            ISequenceUtility sequenceUtility, IMonthlyFeeUtlities monthlyFeeUtlities, IConfiguration configuration) : base(serviceProvider)
        {
            _sequenceUtility = sequenceUtility ?? throw new Exception("");
            _monthlyFeeUtlities = monthlyFeeUtlities ?? throw new Exception("");
            _partMonthlyFeeCodeFormat = configuration.GetValue("PartMonthlyFeeCodeFormat", string.Empty);
        }

        public override async Task ExecuteAsync(CalculateAndCreateMonthlyPartFeeCommand command)
        {
            Guid monthlyPartFeeId = Guid.NewGuid();

            List<IEntity> savedEntities = new List<IEntity>();

            DateTime previousMonthDate = new DateTime(command.Year, command.Month, 1).AddMonths(-1);

            // Duyệt qua danh sách các loại phí, tính cho từng loại phí --> Sum lại
            int totalFee = 0;
            foreach (var partFeeCategory in command.FeeCategoryPartAmountInfoList)
            {
                // Lấy danh sách thuế cho mỗi loại phí
                var feeCategoryTaxList = await (from fct in EntitySet.Get<FeeCategoryTaxEntity>()
                                                join t in EntitySet.Get<TaxEntity>() on fct.TaxId equals t.Id
                                                where fct.FeeCategoryId == partFeeCategory.FeeCategoryId
                                                select new FeeCategoryTaxInfo
                                                {
                                                    TaxId = t.Id,
                                                    Tax = t.Name,
                                                    DisplayOrder = t.DisplayOrder,
                                                    Percent = fct.Percent == 0 ? t.Percent : fct.Percent,
                                                    IncludeLossPercent = t.IncludeLossPercent
                                                }).ToListAsync();

                int partFeeCategoryItemTotal = 0; Guid? feeCategoryFormulaId = null;

                string feeCategoryFormulaStr = string.Empty;
                // Trường hợp tính Một giá
                if (partFeeCategory.FormulaMethod == FeeCategoryFormulaMethod.OnePrice)
                {
                    partFeeCategoryItemTotal = _monthlyFeeUtlities.CalculateFeeAmountItem(partFeeCategory.Amount, partFeeCategory.FeePerUnit, partFeeCategory.LossPercent, feeCategoryTaxList);
                }
                // Trường hợp tính Bậc thang theo Nhân khẩu
                else
                {
                    var feeCategoryFormula = await (from fcf in EntitySet.Get<FeeCategoryFormulaEntity>()
                                                    where fcf.FeeCategoryId == partFeeCategory.FeeCategoryId
                                                    && fcf.NumberOfPersonFrom <= partFeeCategory.FeeAmountCount
                                                    && partFeeCategory.FeeAmountCount <= fcf.NumberOfPersonTo
                                                    select fcf).SingleOrDefaultAsync();
                    if (feeCategoryFormula != null)
                    {
                        feeCategoryFormulaId = feeCategoryFormula.Id;
                        string quotaFormula = feeCategoryFormula.QuotaFormula;
                        if (quotaFormula.IsNotNullOrEmpty())
                        {
                            feeCategoryFormulaStr = feeCategoryFormula.QuotaFormula;
                            partFeeCategoryItemTotal = _monthlyFeeUtlities.CalculateFeeByQuota(partFeeCategory.Amount, quotaFormula, partFeeCategory.LossPercent, feeCategoryTaxList);
                        }
                    }
                }

                #region Xóa hóa đã đã lập trước đó, nhưng chưa Thanh toán (Tránh lập trùng)

                var deleteCommand = EntitySet.CreateDbCommand(CommandType.StoredProcedure, "dbo.DeleteMonthlyPartFee");
                deleteCommand.Parameters.Add(DbParameterHelper.AddNullableGuid(deleteCommand, "@PartId", command.PartId));
                deleteCommand.Parameters.Add(DbParameterHelper.AddNullableGuid(deleteCommand, "@OwnerCustomerId", command.OwnerCustomerId));
                deleteCommand.Parameters.Add(DbParameterHelper.AddNullableInt(deleteCommand, "@Month", command.Month));
                deleteCommand.Parameters.Add(DbParameterHelper.AddNullableInt(deleteCommand, "@Year", command.Year));

                await EntitySet.ExecuteNonQueryAsync(deleteCommand);

                #endregion

                Guid monthlyPartFeeItemId = Guid.NewGuid();
                MonthlyPartFeeItemEntity monthlyPartFeeItemEntity = new MonthlyPartFeeItemEntity
                {
                    Id = monthlyPartFeeItemId,
                    MonthlyPartFeeId = monthlyPartFeeId,
                    PartFeeCategoryId = partFeeCategory.Id,
                    PartId = partFeeCategory.PartId,
                    FeeCategoryId = partFeeCategory.FeeCategoryId,
                    Amount = partFeeCategory.Amount,
                    FeePerUnit = partFeeCategory.FeePerUnit,
                    QuotaFormula = feeCategoryFormulaStr,
                    LossPercent = partFeeCategory.LossPercent,
                    PartServiceUsedHistoryId = partFeeCategory.PartServiceUsedHistoryId,
                    FeeCategoryFormulaId = feeCategoryFormulaId,
                    PropertyName = command.PropertyName,
                    Total = partFeeCategoryItemTotal,
                    Month = partFeeCategory.PayAferUsed ? previousMonthDate.Month : command.Month,
                    Year = partFeeCategory.PayAferUsed ? previousMonthDate.Year : command.Year
                };
                savedEntities.Add(monthlyPartFeeItemEntity);

                foreach (var feeCategoryTax in feeCategoryTaxList)
                {
                    MonthlyPartFeeItemTaxEntity monthlyPartFeeItemTaxEntity = new MonthlyPartFeeItemTaxEntity
                    {
                        Id = Guid.NewGuid(),
                        MonthlyPartFeeItemId = monthlyPartFeeItemId,
                        Percent = feeCategoryTax.Percent,
                        TaxId = feeCategoryTax.TaxId,
                        IncludeLossPercent = feeCategoryTax.IncludeLossPercent
                    };
                    savedEntities.Add(monthlyPartFeeItemTaxEntity);
                }

                totalFee += partFeeCategoryItemTotal;
            }

            int monthTotalFee = totalFee;

            int previousFeeDebt = 0;
            var previousMonthlyPartFeeDebtList = await QueryExecutor.ExecuteManyAsync(new GetPreviousPartMonthlyFeeDebtByOwnerIdQuery
            {
                PartId = command.PartId,
                OwnerCustomerId = command.OwnerCustomerId,
                Year = command.Year,
                Month = command.Month
            });
            foreach (var previousMonthlyPartFeeDebt in previousMonthlyPartFeeDebtList)
            {
                previousFeeDebt += previousMonthlyPartFeeDebt.MonthTotalFee;

                // Mỗi tháng Nợ ghi vào 1 dòng FeeCategory nợ tháng trước
                MonthlyPartFeeItemEntity debtMonthlyPartFeeItemEntity = new MonthlyPartFeeItemEntity
                {
                    Id = Guid.NewGuid(),
                    MonthlyPartFeeId = monthlyPartFeeId,
                    PartId = command.PartId,
                    FeeCategoryId = SystemFeeCategories.MonthlyPartFeeDebtFeeCategoryId,
                    Amount = 0,
                    FeePerUnit = 0,
                    PropertyName = T["Nợ tháng {0}/{1}", previousMonthlyPartFeeDebt.Month.ToString("D2"), previousMonthlyPartFeeDebt.Year],
                    Total = previousMonthlyPartFeeDebt.MonthTotalFee,
                    Month = command.Month,
                    Year = command.Year
                };
                savedEntities.Add(debtMonthlyPartFeeItemEntity);
            }
            totalFee += previousFeeDebt;

            #region Generate MonthlyPartFee Code

            string partMonthlyFeeCodeFormat = _partMonthlyFeeCodeFormat;
            if (partMonthlyFeeCodeFormat.IsNullOrEmpty())
            {
                partMonthlyFeeCodeFormat = "{FeeDigit}{MonthString}{YearString}";
            }

            string dayString = DateTime.Now.Day.ToString().PadLeft(2, '0');
            string monthString = DateTime.Now.Month.ToString().PadLeft(2, '0');
            string yearString = DateTime.Now.Year.ToString().Remove(0, 2);
            if (partMonthlyFeeCodeFormat.Contains("{YearString:4}"))
            {
                yearString = DateTime.Now.Year.ToString();
            }
            if (partMonthlyFeeCodeFormat.Contains("{YearString:2}"))
            {
                yearString = DateTime.Now.Year.ToString().Remove(0, 2);
            }

            int nextMonthlyFeeDigit = await _sequenceUtility.GetNextValue("PartMonthlyFee", SequenceCycleType.Monthly);
            string nextMonthlyFeeDigitString = nextMonthlyFeeDigit.ToString().PadLeft(4, '0');

            partMonthlyFeeCodeFormat = partMonthlyFeeCodeFormat.Replace("{FeeDigit}", "{0}");
            partMonthlyFeeCodeFormat = partMonthlyFeeCodeFormat.Replace("{MonthString}", "{1}");
            partMonthlyFeeCodeFormat = partMonthlyFeeCodeFormat.Replace("{YearString}", "{2}");
            partMonthlyFeeCodeFormat = partMonthlyFeeCodeFormat.Replace("{YearString:2}", "{2}");
            partMonthlyFeeCodeFormat = partMonthlyFeeCodeFormat.Replace("{YearString:4}", "{2}");
            partMonthlyFeeCodeFormat = partMonthlyFeeCodeFormat.Replace("{DayString}", "{3}");

            string partMonthlyFeeCode = string.Format(partMonthlyFeeCodeFormat, nextMonthlyFeeDigitString, monthString, yearString, dayString);

            #endregion            

            MonthlyPartFeeEntity monthlyPartFeeEntity = new MonthlyPartFeeEntity
            {
                Id = monthlyPartFeeId,
                Code = partMonthlyFeeCode,
                PartId = command.PartId,
                CustomerId = command.OwnerCustomerId,
                Month = command.Month,
                Year = command.Year,
                PreviousFeeDebt = previousFeeDebt,
                Total = totalFee,
                MonthTotalFee = monthTotalFee,
                Notes = command.Notes,
                Status = MonthlyPartFeeStatus.NotPaidYet,
                CreatedBy = command.CreatedBy,
                OwnerCustomerId = command.OwnerCustomerId,
                OwnerCustomerName = command.OwnerCustomerName,
                MonthlyPartFeeBatchItemId = command.MonthlyPartFeeBatchItemId
            };

            savedEntities.Add(monthlyPartFeeEntity);
            await Repository.SaveAsync(savedEntities);

            if (command.GenerateDebitNote)
            {
                await CommandExecutor.ExecuteAsync(new GenerateDebitNoteMonthlyPartFeeCommand { PartId = command.PartId, MonthlyPartFeeId = monthlyPartFeeId });
            }
            if (command.NotifyMonthlyPartFee)
            {
                await CommandExecutor.ExecuteAsync(new NotifyMonthlyPartFeeCommand { MonthlyPartFeeId = monthlyPartFeeId });
            }
            await Task.CompletedTask;
        }
    }

    public class FeeCategoryPartAmountInfo
    {
        public Guid Id { get; set; }

        public Guid FeeCategoryId { get; set; }

        public string PropertyName { get; set; }

        public Guid PartId { get; set; }

        public string FeeCategory { get; set; }

        public string Unit { get; set; }

        public double FeeAmountCount { get; set; }

        public double Amount { get; set; }

        public int FeePerUnit { get; set; }

        public FeeCategoryGetAmountFrom GetAmountFrom { get; set; }

        public FeeCategoryFormulaMethod FormulaMethod { get; set; }

        public double? LossPercent { get; set; }

        public bool PayAferUsed { get; set; }

        public Guid? PartServiceUsedHistoryId { get; set; }

        public int DisplayOrder { get; set; }

        public Guid? OwnerCustomerId { get; set; }
    }
}