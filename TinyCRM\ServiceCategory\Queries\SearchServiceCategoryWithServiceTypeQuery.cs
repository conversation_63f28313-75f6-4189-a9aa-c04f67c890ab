﻿using AutoMapper;
using DocumentFormat.OpenXml.InkML;
using LinqToDB;
using LinqToDB.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using TinyCRM.Enums;
using TinyCRM.ServiceType;
using Webaby;
using Webaby.Data;
using Webaby.Localization;
using Webaby.Security;
using static LinqToDB.Sql;
using static Microsoft.Exchange.WebServices.Data.SearchFilter;

namespace TinyCRM.ServiceCategory.Queries
{
    public class SearchServiceCategoryWithServiceTypeQuery : QueryBase<ServiceCategoryTreeData>
    {
        public string Search { get; set; }

        public Guid? Id { get; set; }

        public string ParentId { get; set; }

        public int? Level { get; set; }

        public Guid? Value { get; set; }

        public bool Include { get; set; }
    }

    internal class SearchServiceCategoryWithServiceTypeQueryHandler : QueryHandlerBase<SearchServiceCategoryWithServiceTypeQuery, ServiceCategoryTreeData>
    {
        public IUserService _userService { get; set; }

        public SearchServiceCategoryWithServiceTypeQueryHandler(IServiceProvider serviceProvider, IUserService userService) : base(serviceProvider) { _userService = userService; }

        public override async Task<QueryResult<ServiceCategoryTreeData>> ExecuteAsync(SearchServiceCategoryWithServiceTypeQuery query)
        {
            Guid? dataAuthorizedId = null;
            var currentUser = _userService.GetCurrentUser();
            if (currentUser != null)
            {
                dataAuthorizedId = currentUser.DataAuthorizedId;
            }

            Guid? parentLv1Id = null, parentLv2Id = null, parentLv3Id = null;
            if (query.ParentId.IsNotNullOrEmpty())
            {
                var q_ParentId = query.ParentId;
                if (q_ParentId.StartsWith("_"))
                {
                    q_ParentId = q_ParentId.Substring(1);
                }
                var parentIdArr = q_ParentId.Split('_');

                parentLv1Id = parentIdArr.Length >= 1 ? new Guid(parentIdArr[0]) : (Guid?)null;
                parentLv2Id = parentIdArr.Length >= 2 ? new Guid(parentIdArr[1]) : (Guid?)null;
                parentLv3Id = parentIdArr.Length >= 3 ? new Guid(parentIdArr[2]) : (Guid?)null;
            }

            int level = query.Level.HasValue ? query.Level.Value : 0;
            level++;

            // Store "SearchServiceCategoryWithServiceType" -> LinQ

            IQueryable<ServiceCategoryTreeData> serviceCategoryTreeQuery = null;

            if (query.Id.IsNotNullOrEmpty())
            {
                serviceCategoryTreeQuery = (from st in EntitySet.Get<ServiceTypeEntity>()
                                            join sc1 in EntitySet.Get<ServiceCategoryEntity>() on st.Level1Id equals sc1.Id
                                            join _sc2 in EntitySet.Get<ServiceCategoryEntity>() on st.Level2Id equals _sc2.Id into sc2Join
                                            from sc2 in sc2Join.DefaultIfEmpty()
                                            join _sc3 in EntitySet.Get<ServiceCategoryEntity>() on st.Level3Id equals _sc3.Id into sc3Join
                                            from sc3 in sc3Join.DefaultIfEmpty()
                                            join _sc4 in EntitySet.Get<ServiceCategoryEntity>() on st.Level4Id equals _sc4.Id into sc4Join
                                            from sc4 in sc4Join.DefaultIfEmpty()
                                            where st.Id == query.Id
                                            select new ServiceCategoryTreeData
                                            {
                                                Id =
                                                    sc2 == null && sc3 == null && sc4 == null ? sc1.Id :
                                                    sc2 != null && sc3 == null && sc4 == null ? sc2.Id :
                                                    sc2 != null && sc3 != null && sc4 == null ? sc3.Id :
                                                    sc2 != null && sc3 != null && sc4 != null ? sc4.Id : (Guid?)null,

                                                Name =
                                                    sc2 == null && sc3 == null && sc4 == null ? sc1.Name :
                                                    sc2 != null && sc3 == null && sc4 == null ? sc2.Name :
                                                    sc2 != null && sc3 != null && sc4 == null ? sc3.Name :
                                                    sc2 != null && sc3 != null && sc4 != null ? sc4.Name : null,

                                                ParentId =
                                                    (sc2 != null ? sc1.Id.ToString() : "") +
                                                    (sc2 != null && sc3 != null ? "_" + sc2.Id.ToString() : "") +
                                                    (sc3 != null && sc4 != null ? "_" + sc3.Id.ToString() : ""),

                                                Level =
                                                    sc2 == null && sc3 == null && sc4 == null ? (int)sc1.Type :
                                                    sc2 != null && sc3 == null && sc4 == null ? (int)sc2.Type :
                                                    sc2 != null && sc3 != null && sc4 == null ? (int)sc3.Type :
                                                    sc2 != null && sc3 != null && sc4 != null ? (int)sc4.Type : 0,

                                                Value = st.Id,
                                                Code = st.Code,
                                                PricePerUnit = st.PricePerUnit,
                                                Unit = st.Unit,

                                                Path = sc1.Name +
                                                       (sc2 != null ? "/" + sc2.Name : "") +
                                                       (sc3 != null ? "/" + sc3.Name : "") +
                                                       (sc4 != null ? "/" + sc4.Name : "")
                                            });

            }

            if (query.Search.IsNullOrEmpty() && query.Id.IsNullOrEmpty())
            {
                var subServiceTypeQuery = EntitySet.Get<ServiceTypeEntity>(true).ToLinqToDB();
                if (dataAuthorizedId.IsNotNullOrEmpty())
                {
                    subServiceTypeQuery = subServiceTypeQuery.Where(st => st.DataAuthorizedId == dataAuthorizedId);
                }
                if (level == 2)
                {
                    subServiceTypeQuery = subServiceTypeQuery.Where(st => st.Level1Id == parentLv1Id);
                }
                if (level == 3)
                {
                    subServiceTypeQuery = subServiceTypeQuery.Where(st => st.Level2Id == parentLv2Id);
                }
                if (level == 4)
                {
                    subServiceTypeQuery = subServiceTypeQuery.Where(st => st.Level3Id == parentLv3Id);
                }

                var scfQuery = (from st in subServiceTypeQuery
                                join lv1 in EntitySet.Get<ServiceCategoryEntity>(true).ToLinqToDB() on st.Level1Id equals lv1.Id

                                let rowNumber = Sql.Ext.RowNumber()
                                                        .Over()
                                                        .PartitionBy(lv1.Id)
                                                        .OrderBy(st.Deleted)
                                                        .ToValue()

                                select new SubServiceCategoryQueryItem
                                {
                                    Id = lv1.Id,
                                    Name = lv1.Name,
                                    Level = (int)lv1.Type,
                                    Path = lv1.Name,
                                    Order = lv1.Order,
                                    Deleted = st.Deleted,
                                    RowNumber = rowNumber,
                                });

                IQueryable<ServiceTypeLeafQueryItem> stLeafFinalQuery = null;
                var stLeafQuery = EntitySet.Get<ServiceTypeEntity>(true).ToLinqToDB();
                if (dataAuthorizedId.IsNotNullOrEmpty())
                {
                    stLeafQuery = stLeafQuery.Where(st => st.DataAuthorizedId == dataAuthorizedId);
                }

                IQueryable<ServiceCategoryQueryItem> subScaQuery = null;
                IQueryable<ServiceCategoryTreeQueryItem> tempQuery = null;

                if (level == 0 || level == 1)
                {
                    stLeafFinalQuery = (from st in stLeafQuery
                                        where st.Level2Id == null && st.Level3Id == null && st.Level4Id == null
                                        select new ServiceTypeLeafQueryItem
                                        {
                                            Id = st.Id,
                                            Code = st.Code,
                                            PricePerUnit = st.PricePerUnit,
                                            Unit = st.Unit,
                                            LeafId = st.Level1Id
                                        });

                    subScaQuery = (from st in EntitySet.Get<ServiceTypeEntity>(true).ToLinqToDB()
                                   join sc in EntitySet.Get<ServiceCategoryEntity>(true).ToLinqToDB() on st.Level2Id equals sc.Id
                                   select new ServiceCategoryQueryItem
                                   {
                                       Id = sc.Id,
                                       Level1Id = st.Level1Id,
                                       Level2Id = st.Level2Id,
                                       Level3Id = st.Level3Id,
                                       Level4Id = st.Level4Id
                                   });

                    tempQuery = (from scf in scfQuery
                                 join _leaft in stLeafFinalQuery on scf.Id equals _leaft.LeafId into leafJoin
                                 from leaf in leafJoin.DefaultIfEmpty()
                                 join _sca in subScaQuery on scf.Id equals _sca.Level1Id into scaJoin
                                 from sca in scaJoin.DefaultIfEmpty()
                                 
                                 let rowNumber = Sql.Ext.RowNumber()
                                                         .Over()
                                                         .PartitionBy(scf.Id)
                                                         .OrderBy(scf.Deleted)
                                                         .ToValue()

                                 where scf.RowNumber == 1
                                 select new ServiceCategoryTreeQueryItem
                                 {
                                     Id = scf.Id,
                                     Name = scf.Name,
                                     Level = scf.Level,
                                     IsLeaf = sca != null ? false : true,
                                     Path = scf.Path,
                                     Order = scf.Order,
                                     Value = leaf != null ? leaf.Id : (Guid?)null,
                                     Code = leaf != null ? leaf.Code : null,
                                     PricePerUnit = leaf != null ? leaf.PricePerUnit : (int?)null,
                                     Unit = leaf != null ? leaf.Unit : null,
                                     Deleted = scf.Deleted,
                                     RowNumber = rowNumber
                                 });
                }

                if (level == 2)
                {
                    scfQuery = (from st in subServiceTypeQuery
                                join lv1 in EntitySet.Get<ServiceCategoryEntity>(true).ToLinqToDB() on st.Level1Id equals lv1.Id
                                join lv2 in EntitySet.Get<ServiceCategoryEntity>(true).ToLinqToDB() on st.Level2Id equals lv2.Id

                                let rowNumber = Sql.Ext.RowNumber()
                                                        .Over()
                                                        .PartitionBy(lv1.Id, lv2.Id)
                                                        .OrderBy(st.Deleted)
                                                        .ToValue()

                                select new SubServiceCategoryQueryItem
                                {
                                    Id = lv2.Id,
                                    Name = lv2.Name,
                                    Level = (int)lv2.Type,
                                    Path = lv1.Name + "/" + lv2.Name,
                                    Order = lv2.Order,
                                    Deleted = st.Deleted,
                                    RowNumber = rowNumber,
                                });

                    stLeafFinalQuery = (from st in stLeafQuery
                                        where st.Level2Id != null && st.Level3Id == null && st.Level4Id == null
                                        select new ServiceTypeLeafQueryItem
                                        {
                                            Id = st.Id,
                                            Code = st.Code,
                                            PricePerUnit = st.PricePerUnit,
                                            Unit = st.Unit,
                                            LeafId = st.Level2Id
                                        });

                    subScaQuery = (from st in EntitySet.Get<ServiceTypeEntity>(true).ToLinqToDB()
                                   join sc in EntitySet.Get<ServiceCategoryEntity>(true).ToLinqToDB() on st.Level3Id equals sc.Id
                                   select new ServiceCategoryQueryItem
                                   {
                                       Id = sc.Id,
                                       Level1Id = st.Level1Id,
                                       Level2Id = st.Level2Id,
                                       Level3Id = st.Level3Id,
                                       Level4Id = st.Level4Id
                                   });

                    tempQuery = (from scf in scfQuery
                                 join _leaft in stLeafFinalQuery on scf.Id equals _leaft.LeafId into leafJoin
                                 from leaf in leafJoin.DefaultIfEmpty()
                                 join _sca in subScaQuery on scf.Id equals _sca.Level1Id into scaJoin
                                 from sca in scaJoin.DefaultIfEmpty()
                                 
                                 let rowNumber = Sql.Ext.RowNumber()
                                                         .Over()
                                                         .PartitionBy(scf.Id)
                                                         .OrderBy(scf.Deleted)
                                                         .ToValue()

                                 where scf.RowNumber == 1
                                 select new ServiceCategoryTreeQueryItem
                                 {
                                     Id = scf.Id,
                                     Name = scf.Name,
                                     ParentId = scf.Level1Id.Value.ToString(),
                                     Level = scf.Level,
                                     IsLeaf = sca != null ? false : true,
                                     Path = scf.Path,
                                     Order = scf.Order,
                                     Value = leaf != null ? leaf.Id : (Guid?)null,
                                     Code = leaf != null ? leaf.Code : null,
                                     PricePerUnit = leaf != null ? leaf.PricePerUnit : (int?)null,
                                     Unit = leaf != null ? leaf.Unit : null,
                                     Deleted = scf.Deleted,
                                     RowNumber = rowNumber
                                 });
                }

                if (level == 3)
                {
                    scfQuery = (from st in subServiceTypeQuery
                                join lv1 in EntitySet.Get<ServiceCategoryEntity>(true).ToLinqToDB() on st.Level1Id equals lv1.Id
                                join lv2 in EntitySet.Get<ServiceCategoryEntity>(true).ToLinqToDB() on st.Level2Id equals lv2.Id
                                join lv3 in EntitySet.Get<ServiceCategoryEntity>(true).ToLinqToDB() on st.Level3Id equals lv3.Id

                                let rowNumber = Sql.Ext.RowNumber()
                                                        .Over()
                                                        .PartitionBy(lv1.Id, lv2.Id, lv3.Id)
                                                        .OrderBy(st.Deleted)
                                                        .ToValue()

                                select new SubServiceCategoryQueryItem
                                {
                                    Id = lv3.Id,
                                    Name = lv3.Name,
                                    Level = (int)lv3.Type,
                                    Path = lv1.Name + "/" + lv2.Name + "/" + lv3.Name,
                                    Order = lv3.Order,
                                    Deleted = st.Deleted,
                                    RowNumber = rowNumber,
                                });

                    stLeafFinalQuery = (from st in stLeafQuery
                                        where st.Level2Id != null && st.Level3Id != null && st.Level4Id == null
                                        select new ServiceTypeLeafQueryItem
                                        {
                                            Id = st.Id,
                                            Code = st.Code,
                                            PricePerUnit = st.PricePerUnit,
                                            Unit = st.Unit,
                                            LeafId = st.Level3Id
                                        });

                    subScaQuery = (from st in EntitySet.Get<ServiceTypeEntity>(true).ToLinqToDB()
                                   join sc in EntitySet.Get<ServiceCategoryEntity>(true).ToLinqToDB() on st.Level4Id equals sc.Id
                                   select new ServiceCategoryQueryItem
                                   {
                                       Id = sc.Id,
                                       Level1Id = st.Level1Id,
                                       Level2Id = st.Level2Id,
                                       Level3Id = st.Level3Id,
                                       Level4Id = st.Level4Id
                                   });

                    tempQuery = (from scf in scfQuery
                                 join _leaft in stLeafFinalQuery on scf.Id equals _leaft.LeafId into leafJoin
                                 from leaf in leafJoin.DefaultIfEmpty()
                                 join _sca in subScaQuery on scf.Id equals _sca.Level1Id into scaJoin
                                 from sca in scaJoin.DefaultIfEmpty()
                                 
                                 let rowNumber = Sql.Ext.RowNumber()
                                                         .Over()
                                                         .PartitionBy(scf.Id)
                                                         .OrderBy(scf.Deleted)
                                                         .ToValue()

                                 where scf.RowNumber == 1
                                 select new ServiceCategoryTreeQueryItem
                                 {
                                     Id = scf.Id,
                                     Name = scf.Name,
                                     ParentId = scf.Level1Id.Value.ToString() + "_" + scf.Level2Id.Value.ToString(),
                                     Level = scf.Level,
                                     IsLeaf = sca != null ? false : true,
                                     Path = scf.Path,
                                     Order = scf.Order,
                                     Value = leaf != null ? leaf.Id : (Guid?)null,
                                     Code = leaf != null ? leaf.Code : null,
                                     PricePerUnit = leaf != null ? leaf.PricePerUnit : (int?)null,
                                     Unit = leaf != null ? leaf.Unit : null,
                                     Deleted = scf.Deleted,
                                     RowNumber = rowNumber
                                 });
                }

                if (level == 4)
                {
                    scfQuery = (from st in subServiceTypeQuery
                                join lv1 in EntitySet.Get<ServiceCategoryEntity>(true).ToLinqToDB() on st.Level1Id equals lv1.Id
                                join lv2 in EntitySet.Get<ServiceCategoryEntity>(true).ToLinqToDB() on st.Level2Id equals lv2.Id
                                join lv3 in EntitySet.Get<ServiceCategoryEntity>(true).ToLinqToDB() on st.Level3Id equals lv3.Id
                                join lv4 in EntitySet.Get<ServiceCategoryEntity>(true).ToLinqToDB() on st.Level4Id equals lv4.Id

                                let rowNumber = Sql.Ext.RowNumber()
                                                        .Over()
                                                        .PartitionBy(lv1.Id, lv2.Id, lv3.Id, lv3.Id)
                                                        .OrderBy(st.Deleted)
                                                        .ToValue()

                                select new SubServiceCategoryQueryItem
                                {
                                    Id = lv4.Id,
                                    Name = lv4.Name,
                                    Level = (int)lv4.Type,
                                    Path = lv1.Name + "/" + lv2.Name + "/" + lv3.Name + "/" + lv4.Name,
                                    Order = lv4.Order,
                                    Deleted = st.Deleted,
                                    RowNumber = rowNumber,
                                });

                    stLeafFinalQuery = (from st in stLeafQuery
                                        where st.Level2Id != null && st.Level3Id != null && st.Level4Id != null
                                        select new ServiceTypeLeafQueryItem
                                        {
                                            Id = st.Id,
                                            Code = st.Code,
                                            PricePerUnit = st.PricePerUnit,
                                            Unit = st.Unit,
                                            LeafId = st.Level4Id
                                        });

                    tempQuery = (from scf in scfQuery
                                 join _leaft in stLeafFinalQuery on scf.Id equals _leaft.LeafId into leafJoin
                                 from leaf in leafJoin.DefaultIfEmpty()
                                 
                                 let rowNumber = Sql.Ext.RowNumber()
                                                         .Over()
                                                         .PartitionBy(scf.Id)
                                                         .OrderBy(scf.Deleted)
                                                         .ToValue()

                                 where scf.RowNumber == 1
                                 select new ServiceCategoryTreeQueryItem
                                 {
                                     Id = scf.Id,
                                     Name = scf.Name,
                                     ParentId = scf.Level1Id.Value.ToString() + "_" + scf.Level2Id.Value.ToString() + "_" + scf.Level3Id.Value.ToString(),
                                     Level = scf.Level,
                                     IsLeaf = true,
                                     Path = scf.Path,
                                     Order = scf.Order,
                                     Value = leaf != null ? leaf.Id : (Guid?)null,
                                     Code = leaf != null ? leaf.Code : null,
                                     PricePerUnit = leaf != null ? leaf.PricePerUnit : (int?)null,
                                     Unit = leaf != null ? leaf.Unit : null,
                                     Deleted = scf.Deleted,
                                     RowNumber = rowNumber
                                 });
                }

                serviceCategoryTreeQuery = tempQuery.Where(sc => sc.RowNumber == 1).OrderBy(sc => sc.Order).Select(x => new ServiceCategoryTreeData
                {
                    Id = x.Id,
                    Name = x.Name,
                    Level = x.Level,
                    Path = x.Path,
                    Value = x.Value,
                    Code = x.Code,
                    PricePerUnit = x.PricePerUnit,
                    Unit = x.Unit,
                    Deleted = x.Deleted,
                    IsLeaf = x.IsLeaf,
                });
            }

            if (query.Search.IsNullOrEmpty())
            {
                if (query.Value.HasValue && query.Include)
                {
                    #region Store "SearchServiceCategoryRelatedWithServiceType" -> LinQ

                    var st4LevelQuery = (from st in EntitySet.Get<ServiceTypeEntity>(true).ToLinqToDB()
                                         join st4 in EntitySet.Get<ServiceTypeEntity>(true).ToLinqToDB() on st.Level3Id equals st4.Level3Id
                                         join sc2 in EntitySet.Get<ServiceCategoryEntity>(true).ToLinqToDB() on st4.Level2Id equals sc2.Id
                                         join sc3 in EntitySet.Get<ServiceCategoryEntity>(true).ToLinqToDB() on st4.Level3Id equals sc3.Id
                                         join sc4 in EntitySet.Get<ServiceCategoryEntity>(true).ToLinqToDB() on st4.Level4Id equals sc4.Id

                                         let rowNumber = Sql.Ext.RowNumber()
                                                             .Over()
                                                             .PartitionBy(sc4.Id, st4.Level1Id, st4.Level2Id, st4.Level3Id)
                                                             .OrderBy(st4.Deleted)
                                                             .ToValue()

                                         where st.Id == query.Value
                                         select new SubServiceCategoryRelatedWithServiceTypeQueryItem
                                         {
                                             Id = sc4.Id,
                                             ParentLevel1Id = st.Level1Id,
                                             ParentLevel2Id = st.Level2Id,
                                             ParentLevel3Id = st.Level3Id,
                                             Name = sc4.Name,
                                             ParentId = st.Level1Id.ToString() + "_" + st.Level2Id.ToString() + "_" + st.Level3Id.ToString(),
                                             Path = sc2.Name + "/" + sc3.Name + "/" + sc4.Name,
                                             Level = (int)sc4.Type,
                                             Order = sc4.Order,
                                             Deleted = st4.Deleted,
                                             RowNumber = rowNumber
                                         });

                    IQueryable<SubServiceCategoryRelatedWithServiceTypeQueryItem> st2LevelQuery = null;
                    if (level + 1 < 3)
                    {
                        st2LevelQuery = (from st in EntitySet.Get<ServiceTypeEntity>(true).ToLinqToDB()
                                         join st2 in EntitySet.Get<ServiceTypeEntity>(true).ToLinqToDB() on st.Level1Id equals st2.Level1Id
                                         join sc2 in EntitySet.Get<ServiceCategoryEntity>(true).ToLinqToDB() on st2.Level2Id equals sc2.Id

                                         let rowNumber = Sql.Ext.RowNumber()
                                                             .Over()
                                                             .PartitionBy(sc2.Id, st2.Level1Id)
                                                             .OrderBy(st2.Deleted)
                                                             .ToValue()

                                         where st.Id == query.Value
                                         select new SubServiceCategoryRelatedWithServiceTypeQueryItem
                                         {
                                             Id = sc2.Id,
                                             ParentLevel1Id = st.Level1Id,
                                             ParentLevel2Id = (Guid?)null,
                                             ParentLevel3Id = (Guid?)null,
                                             Name = sc2.Name,
                                             ParentId = st.Level1Id.ToString(),
                                             Path = sc2.Name,
                                             Level = (int)sc2.Type,
                                             Order = sc2.Order,
                                             Deleted = st2.Deleted,
                                             RowNumber = rowNumber
                                         });
                    }

                    IQueryable<SubServiceCategoryRelatedWithServiceTypeQueryItem> st3LevelQuery = null;
                    if (level + 1 < 4)
                    {
                        st3LevelQuery = (from st in EntitySet.Get<ServiceTypeEntity>(true).ToLinqToDB()
                                         join st3 in EntitySet.Get<ServiceTypeEntity>(true).ToLinqToDB() on st.Level2Id equals st3.Level2Id
                                         join sc2 in EntitySet.Get<ServiceCategoryEntity>(true).ToLinqToDB() on st3.Level2Id equals sc2.Id
                                         join sc3 in EntitySet.Get<ServiceCategoryEntity>(true).ToLinqToDB() on st3.Level3Id equals sc3.Id

                                         let rowNumber = Sql.Ext.RowNumber()
                                                             .Over()
                                                             .PartitionBy(sc3.Id, st3.Level1Id, st3.Level2Id)
                                                             .OrderBy(st3.Deleted)
                                                             .ToValue()

                                         where st.Id == query.Value
                                         select new SubServiceCategoryRelatedWithServiceTypeQueryItem
                                         {
                                             Id = sc3.Id,
                                             ParentLevel1Id = st.Level1Id,
                                             ParentLevel2Id = st.Level2Id,
                                             ParentLevel3Id = (Guid?)null,
                                             Name = sc3.Name,
                                             ParentId = st.Level1Id.ToString() + "_" + st.Level2Id.ToString(),
                                             Path = sc2.Name + "/" + sc3.Name + "/" + sc3.Name,
                                             Level = (int)sc3.Type,
                                             Order = sc3.Order,
                                             Deleted = st3.Deleted,
                                             RowNumber = rowNumber
                                         });
                    }

                    IQueryable<SubServiceCategoryRelatedWithServiceTypeQueryItem> subLevelQuery = null;
                    if (st3LevelQuery != null && st2LevelQuery != null)
                    {
                        subLevelQuery = st2LevelQuery.Union(st3LevelQuery).Union(st4LevelQuery);
                    }
                    else if (st3LevelQuery != null)
                    {
                        subLevelQuery = st3LevelQuery.Union(st4LevelQuery);
                    }
                    else if (st2LevelQuery != null)
                    {
                        subLevelQuery = st2LevelQuery.Union(st4LevelQuery);
                    }
                    else
                    {
                        subLevelQuery = st4LevelQuery;
                    }

                    IQueryable<ServiceCategoryTreeQueryItem> subQuery = null;
                    if (level + 1 < 3)
                    {
                        subQuery = (from sca in subLevelQuery
                                    from st in EntitySet.Get<ServiceTypeEntity>(true).ToLinqToDB()
                                                 .Where(st =>
                                                     st.Level1Id == sca.Id ||
                                                     (st.Level2Id == sca.Id && st.Level1Id == sca.ParentLevel1Id) ||
                                                     (st.Level3Id == sca.Id && st.Level1Id == sca.ParentLevel1Id && st.Level2Id == sca.ParentLevel2Id) ||
                                                     (st.Level4Id == sca.Id && st.Level1Id == sca.ParentLevel1Id && st.Level2Id == sca.ParentLevel2Id && st.Level3Id == sca.ParentLevel3Id)
                                                 )
                                                 .DefaultIfEmpty()

                                    let rowNumber = Sql.Ext.RowNumber()
                                                        .Over()
                                                        .PartitionBy(sca.Id, sca.ParentId)
                                                        .OrderBy(sca.Deleted)
                                                        .ToValue()

                                    where sca.RowNumber == 1
                                    select new ServiceCategoryTreeQueryItem
                                    {
                                        Id = sca.Id,
                                        Name = sca.Name,
                                        ParentId = sca.ParentId,
                                        Level = sca.Level,
                                        Path = sca.Path,
                                        Order = sca.Order,
                                        Deleted = sca.Deleted,

                                        Value = (sca.Level == 2 && st.Level1Id != null && st.Level2Id != null && st.Level3Id == null && st.Level4Id == null) ? st.Id : (Guid?)null,

                                        Code = (sca.Level == 2 && st.Level1Id != null && st.Level2Id != null && st.Level3Id == null && st.Level4Id == null) ? st.Code : null,

                                        IsLeaf = (sca.Level == 2 && st.Level1Id != null && st.Level2Id != null && st.Level3Id == null && st.Level4Id == null) ? true : false,

                                        RowNumber = rowNumber
                                    });
                    }
                    else if (level + 1 < 4)
                    {
                        subQuery = (from sca in subLevelQuery
                                    from st in EntitySet.Get<ServiceTypeEntity>(true).ToLinqToDB()
                                                 .Where(st =>
                                                     st.Level1Id == sca.Id ||
                                                     (st.Level2Id == sca.Id && st.Level1Id == sca.ParentLevel1Id) ||
                                                     (st.Level3Id == sca.Id && st.Level1Id == sca.ParentLevel1Id && st.Level2Id == sca.ParentLevel2Id) ||
                                                     (st.Level4Id == sca.Id && st.Level1Id == sca.ParentLevel1Id && st.Level2Id == sca.ParentLevel2Id && st.Level3Id == sca.ParentLevel3Id)
                                                 )
                                                 .DefaultIfEmpty()

                                    let rowNumber = Sql.Ext.RowNumber()
                                                        .Over()
                                                        .PartitionBy(sca.Id, sca.ParentId)
                                                        .OrderBy(sca.Deleted)
                                                        .ToValue()

                                    where sca.RowNumber == 1
                                    select new ServiceCategoryTreeQueryItem
                                    {
                                        Id = sca.Id,
                                        Name = sca.Name,
                                        ParentId = sca.ParentId,
                                        Level = sca.Level,
                                        Path = sca.Path,
                                        Order = sca.Order,
                                        Deleted = sca.Deleted,

                                        Value = (sca.Level == 2 && st.Level1Id != null && st.Level2Id != null && st.Level3Id == null && st.Level4Id == null)
                                                || (sca.Level == 3 && st.Level1Id != null && st.Level2Id != null && st.Level3Id != null && st.Level4Id == null) ? st.Id : (Guid?)null,

                                        Code = (sca.Level == 2 && st.Level1Id != null && st.Level2Id != null && st.Level3Id == null && st.Level4Id == null)
                                                || (sca.Level == 3 && st.Level1Id != null && st.Level2Id != null && st.Level3Id != null && st.Level4Id == null) ? st.Code : null,

                                        IsLeaf = (sca.Level == 2 && st.Level1Id != null && st.Level2Id != null && st.Level3Id == null && st.Level4Id == null)
                                                || (sca.Level == 3 && st.Level1Id != null && st.Level2Id != null && st.Level3Id != null && st.Level4Id == null) ? true : false,

                                        RowNumber = rowNumber
                                    });
                    }
                    else if (level + 1 < 5)
                    {
                        subQuery = (from sca in subLevelQuery
                                    from st in EntitySet.Get<ServiceTypeEntity>(true).ToLinqToDB()
                                                 .Where(st =>
                                                     st.Level1Id == sca.Id ||
                                                     (st.Level2Id == sca.Id && st.Level1Id == sca.ParentLevel1Id) ||
                                                     (st.Level3Id == sca.Id && st.Level1Id == sca.ParentLevel1Id && st.Level2Id == sca.ParentLevel2Id) ||
                                                     (st.Level4Id == sca.Id && st.Level1Id == sca.ParentLevel1Id && st.Level2Id == sca.ParentLevel2Id && st.Level3Id == sca.ParentLevel3Id)
                                                 )
                                                 .DefaultIfEmpty()

                                    let rowNumber = Sql.Ext.RowNumber()
                                                        .Over()
                                                        .PartitionBy(sca.Id, sca.ParentId)
                                                        .OrderBy(sca.Deleted)
                                                        .ToValue()

                                    where sca.RowNumber == 1
                                    select new ServiceCategoryTreeQueryItem
                                    {
                                        Id = sca.Id,
                                        Name = sca.Name,
                                        ParentId = sca.ParentId,
                                        Level = sca.Level,
                                        Path = sca.Path,
                                        Order = sca.Order,
                                        Deleted = sca.Deleted,

                                        Value = (sca.Level == 2 && st.Level1Id != null && st.Level2Id != null && st.Level3Id == null && st.Level4Id == null)
                                                || (sca.Level == 3 && st.Level1Id != null && st.Level2Id != null && st.Level3Id != null && st.Level4Id == null)
                                                || (sca.Level == 4 && st.Level1Id != null && st.Level2Id != null && st.Level3Id != null && st.Level4Id != null) ? st.Id : (Guid?)null,

                                        Code = (sca.Level == 2 && st.Level1Id != null && st.Level2Id != null && st.Level3Id == null && st.Level4Id == null)
                                                || (sca.Level == 3 && st.Level1Id != null && st.Level2Id != null && st.Level3Id != null && st.Level4Id == null)
                                                || (sca.Level == 4 && st.Level1Id != null && st.Level2Id != null && st.Level3Id != null && st.Level4Id != null) ? st.Code : null,

                                        IsLeaf = (sca.Level == 2 && st.Level1Id != null && st.Level2Id != null && st.Level3Id == null && st.Level4Id == null)
                                                || (sca.Level == 3 && st.Level1Id != null && st.Level2Id != null && st.Level3Id != null && st.Level4Id == null)
                                                || (sca.Level == 4 && st.Level1Id != null && st.Level2Id != null && st.Level3Id != null && st.Level4Id != null) ? true : false,

                                        RowNumber = rowNumber
                                    });
                    }

                    var subQueryResult = subQuery.Where(sc => sc.RowNumber == 1).OrderBy(sc => sc.Level).ThenBy(sc => sc.Order).Select(sca => new ServiceCategoryTreeData
                    {
                        Id = sca.Id,
                        Name = sca.Name,
                        ParentId = sca.ParentId,
                        Level = sca.Level,
                        Path = sca.Path,
                        Deleted = sca.Deleted,
                        Value = sca.Value,
                        Code = sca.Code,
                        IsLeaf = sca.IsLeaf,
                    }).ToList();

                    #endregion

                    if (subQueryResult.Any())
                    {
                        var rootid = Guid.Empty;
                        var pid = subQuery.First().ParentId;
                        if (pid.IsNotNullOrEmpty())
                        {
                            if (pid.Contains("_"))
                            {
                                var p = pid.Split('_');
                                if (p.Length > 0)
                                {
                                    rootid = new Guid(p[p.Length - 1]);
                                }
                            }
                            else
                            {
                                rootid = new Guid(pid);
                            }
                        }
                        var sortedTreeItem = SortTreeItem(subQueryResult, level + 1);
                        var data = serviceCategoryTreeQuery.ToList();
                        for (int i = 0; i < data.Count; i++)
                        {
                            if (data[i].Id == rootid)
                            {
                                data.InsertRange(i + 1, sortedTreeItem);
                            }
                        }
                        return new QueryResult<ServiceCategoryTreeData>(data);
                    }

                    return new QueryResult<ServiceCategoryTreeData>(serviceCategoryTreeQuery);
                }
                else
                {
                    return new QueryResult<ServiceCategoryTreeData>(serviceCategoryTreeQuery);
                }
            }
            else
            {
                var subServiceTypeQuery = EntitySet.Get<ServiceTypeEntity>();
                if (parentLv1Id.IsNotNullOrEmpty())
                {
                    subServiceTypeQuery = subServiceTypeQuery.Where(x => x.Level1Id == parentLv1Id);
                }
                if (parentLv2Id.IsNotNullOrEmpty())
                {
                    subServiceTypeQuery = subServiceTypeQuery.Where(x => x.Level2Id == parentLv2Id);
                }
                if (parentLv3Id.IsNotNullOrEmpty())
                {
                    subServiceTypeQuery = subServiceTypeQuery.Where(x => x.Level3Id == parentLv3Id);
                }
                if (dataAuthorizedId != null)
                {
                    subServiceTypeQuery = subServiceTypeQuery.Where(x => x.DataAuthorizedId == dataAuthorizedId);
                }

                var serviceTypeQuery = from st in subServiceTypeQuery
                                       join sc1 in EntitySet.Get<ServiceCategoryEntity>() on st.Level1Id equals sc1.Id
                                       join sc2 in EntitySet.Get<ServiceCategoryEntity>() on st.Level2Id equals sc2.Id into sc2Group
                                       from sc2 in sc2Group.DefaultIfEmpty()
                                       join sc3 in EntitySet.Get<ServiceCategoryEntity>() on st.Level3Id equals sc3.Id into sc3Group
                                       from sc3 in sc3Group.DefaultIfEmpty()
                                       join sc4 in EntitySet.Get<ServiceCategoryEntity>() on st.Level4Id equals sc4.Id into sc4Group
                                       from sc4 in sc4Group.DefaultIfEmpty()
                                       where EF.Functions.Like(sc1.Name.ToLower(), $"%{query.Search.ToLower()}%")
                                          || (sc2 != null && EF.Functions.Like(sc2.Name.ToLower(), $"%{query.Search.ToLower()}%"))
                                          || (sc3 != null && EF.Functions.Like(sc3.Name.ToLower(), $"%{query.Search.ToLower()}%"))
                                          || (sc4 != null && EF.Functions.Like(sc4.Name.ToLower(), $"%{query.Search.ToLower()}%"))
                                       select new ServiceTypePathData
                                       {
                                           Level1Id = st.Level1Id.Value,
                                           Level1Name = sc1.Name,
                                           Level1Order = sc1.Order,
                                           Level1Value = (st.Level1Id != null && st.Level2Id == null && st.Level3Id == null && st.Level4Id == null) ? st.Id : (Guid?)null,
                                           Level1Code = (st.Level1Id != null && st.Level2Id == null && st.Level3Id == null && st.Level4Id == null) ? st.Code : null,
                                           Level1IsLeaf = (st.Level1Id != null && st.Level2Id == null && st.Level3Id == null && st.Level4Id == null),

                                           Level2Id = ((sc2 != null && (
                                                       EF.Functions.Like(sc2.Name.ToLower(), $"%{query.Search.ToLower()}%")
                                                       || (sc3 != null && EF.Functions.Like(sc3.Name.ToLower(), $"%{query.Search.ToLower()}%"))
                                                       || (sc4 != null && EF.Functions.Like(sc4.Name.ToLower(), $"%{query.Search.ToLower()}%"))
                                                   )) ? st.Level2Id : null),
                                           Level2Name = sc2 != null ? sc2.Name : null,
                                           Level2Order = sc2 != null ? sc2.Order : (int?)null,
                                           Level2Value = (st.Level1Id != null && st.Level2Id != null && st.Level3Id == null && st.Level4Id == null) ? st.Id : (Guid?)null,
                                           Level2Code = (st.Level1Id != null && st.Level2Id != null && st.Level3Id == null && st.Level4Id == null) ? st.Code : null,
                                           Level2IsLeaf = (st.Level1Id != null && st.Level2Id != null && st.Level3Id == null && st.Level4Id == null),

                                           Level3Id = ((sc3 != null && (
                                                       EF.Functions.Like(sc3.Name.ToLower(), $"%{query.Search.ToLower()}%")
                                                       || (sc4 != null && EF.Functions.Like(sc4.Name.ToLower(), $"%{query.Search.ToLower()}%"))
                                                   )) ? st.Level3Id : null),
                                           Level3Name = sc3 != null ? sc3.Name : null,
                                           Level3Order = sc3 != null ? sc3.Order : (int?)null,
                                           Level3Value = (st.Level1Id != null && st.Level2Id != null && st.Level3Id != null && st.Level4Id == null) ? st.Id : (Guid?)null,
                                           Level3Code = (st.Level1Id != null && st.Level2Id != null && st.Level3Id != null && st.Level4Id == null) ? st.Code : null,
                                           Level3IsLeaf = (st.Level1Id != null && st.Level2Id != null && st.Level3Id != null && st.Level4Id == null),

                                           Level4Id = (sc4 != null && EF.Functions.Like(sc4.Name.ToLower(), $"%{query.Search.ToLower()}%")) ? st.Level4Id : null,
                                           Level4Name = sc4 != null ? sc4.Name : null,
                                           Level4Order = sc4 != null ? sc4.Order : (int?)null,
                                           Level4Value = (st.Level1Id != null && st.Level2Id != null && st.Level3Id != null && st.Level4Id != null) ? st.Id : (Guid?)null,
                                           Level4Code = (st.Level1Id != null && st.Level2Id != null && st.Level3Id != null && st.Level4Id != null) ? st.Code : null,
                                           Level4IsLeaf = (st.Level1Id != null && st.Level2Id != null && st.Level3Id != null && st.Level4Id != null),

                                           Deleted = st.Deleted
                                       };


                // Sắp xếp theo Order
                var mainQuery = serviceTypeQuery
                                .OrderBy(x => x.Level1Order)
                                .ThenBy(x => x.Level2Order)
                                .ThenBy(x => x.Level3Order)
                                .ThenBy(x => x.Level4Order)
                                .ToList();

                var data = SortTreePath(mainQuery, 1);
                return new QueryResult<ServiceCategoryTreeData>(data);
            }
        }

        private List<ServiceCategoryTreeData> SortTreeItem(List<ServiceCategoryTreeData> inputList, int startLevel)
        {
            if (inputList.Count > 0)
            {
                var outputList = new List<ServiceCategoryTreeData>();
                outputList.AddRange(inputList.Where(x => x.Level == startLevel));
                var lastLevel = inputList.OrderByDescending(x => x.Level).First().Level;
                for (int i = startLevel + 1; i < lastLevel + 1; i++)
                {
                    var lvBranchId = Guid.Empty;
                    var lvItems = inputList.Where(x => x.Level == i).ToList();
                    var lvPid = lvItems[0].ParentId;
                    if (lvPid.IsNotNullOrEmpty())
                    {
                        if (lvPid.Contains("_"))
                        {
                            var lvP = lvPid.Split('_');
                            if (lvP.Length > 0)
                            {
                                lvBranchId = new Guid(lvP[lvP.Length - 1]);
                            }
                        }
                    }
                    else
                    {
                        lvBranchId = new Guid(lvPid);
                    }
                    for (int j = 0; j < outputList.Count; j++)
                    {
                        if (outputList[j].Id == lvBranchId)
                        {
                            outputList.InsertRange(j + 1, lvItems);
                        }
                    }
                }
                return outputList;

            }
            return new List<ServiceCategoryTreeData>();
        }

        private List<ServiceCategoryTreeData> SortTreePath(List<ServiceTypePathData> inputList, int startLevel, params Guid[] previousParentId)
        {
            if (startLevel < 1)
            {
                startLevel = 1;
            }

            var result = new List<ServiceCategoryTreeData>();
            result.AddRange(inputList
                .OrderBy(x => x.GetType().GetProperty(string.Format("Level{0}Order", startLevel)).GetValue(x, null))
                .GroupBy(x => new
                {
                    Id = (Guid?)x.GetType().GetProperty(string.Format("Level{0}Id", startLevel)).GetValue(x, null),
                    Name = (string)x.GetType().GetProperty(string.Format("Level{0}Name", startLevel)).GetValue(x, null),
                    Code = (string)x.GetType().GetProperty(string.Format("Level{0}Code", startLevel)).GetValue(x, null),
                    Order = (int)x.GetType().GetProperty(string.Format("Level{0}Order", startLevel)).GetValue(x, null),
                    Value = (Guid?)x.GetType().GetProperty(string.Format("Level{0}Value", startLevel)).GetValue(x, null),
                    IsLeaf = (bool)x.GetType().GetProperty(string.Format("Level{0}IsLeaf", startLevel)).GetValue(x, null),
                })
                .Select(x => new ServiceCategoryTreeData
                {
                    Id = x.Key.Id,
                    Name = x.Key.Name,
                    Code = x.Key.Code,
                    Value = x.Key.Value,
                    IsLeaf = x.Key.IsLeaf,
                    Level = startLevel,
                    ParentLevel1Id = startLevel > 1 ? previousParentId[0] : (Guid?)null,
                    ParentLevel2Id = startLevel > 2 ? previousParentId[1] : (Guid?)null,
                    ParentLevel3Id = startLevel > 3 ? previousParentId[2] : (Guid?)null,
                    ParentId = (startLevel == 1 ? null :
                                    (startLevel == 2 ? string.Format("{0}",
                                        previousParentId[0]) :
                                            (startLevel == 3 ? string.Format("{0}_{1}", previousParentId[0], previousParentId[1]) :
                                                string.Format("{0}_{1}_{2}", previousParentId[0], previousParentId[1], previousParentId[2])))),
                    Path = string.Format("{0}", x.Key.Name),
                    Deleted = x.All(y => y.Deleted)
                }).ToList());

            for (int i = startLevel + 1; i <= 4; i++)
            {
                for (int j = 0; j < result.Count; j++)
                {
                    if (result[j].Level == i - 1)
                    {
                        var newNodeList = inputList
                            .Where(x => x.GetType().GetProperty(string.Format("Level{0}Id", i)).GetValue(x, null) != null &&
                                (Guid?)x.GetType().GetProperty(string.Format("Level{0}Id", i - 1)).GetValue(x, null) == result[j].Id &&
                                ((i < 3 || x.Level1Id == result[j].ParentLevel1Id) &&
                                (i < 4 || x.Level2Id == result[j].ParentLevel2Id)))
                            .OrderBy(x => (int)x.GetType().GetProperty(string.Format("Level{0}Order", i)).GetValue(x, null))
                            .GroupBy(x => new
                            {
                                Id = (Guid?)x.GetType().GetProperty(string.Format("Level{0}Id", i)).GetValue(x, null),
                                Name = (string)x.GetType().GetProperty(string.Format("Level{0}Name", i)).GetValue(x, null),
                                Code = (string)x.GetType().GetProperty(string.Format("Level{0}Code", i)).GetValue(x, null),
                                Order = (int)x.GetType().GetProperty(string.Format("Level{0}Order", i)).GetValue(x, null),
                                Value = (Guid?)x.GetType().GetProperty(string.Format("Level{0}Value", i)).GetValue(x, null),
                                IsLeaf = (bool)x.GetType().GetProperty(string.Format("Level{0}IsLeaf", i)).GetValue(x, null)
                            })
                            .Select(x => new ServiceCategoryTreeData
                            {
                                Id = x.Key.Id,
                                Name = x.Key.Name,
                                Code = x.Key.Code,
                                IsLeaf = x.Key.IsLeaf,
                                Value = x.Key.Value,
                                Level = i,
                                ParentLevel1Id = i == 2 ? result[j].Id : (i > 2 ? result[j].ParentLevel1Id : (Guid?)null),
                                ParentLevel2Id = i == 3 ? result[j].Id : (i > 3 ? result[j].ParentLevel2Id : (Guid?)null),
                                ParentLevel3Id = i == 4 ? result[j].Id : (Guid?)null,
                                ParentId = i == 2 ? string.Format("{0}", result[j].Id) :
                                                (i == 3 ? string.Format("{0}_{1}", result[j].ParentLevel1Id, result[j].Id) :
                                                    i == 4 ? string.Format("{0}_{1}_{2}", result[j].ParentLevel1Id, result[j].ParentLevel2Id, result[j].Id) : string.Empty),
                                Path = string.Format("{0}/{1}", result[j].Name, x.Key.Name),
                                Deleted = x.All(y => y.Deleted)
                            }).ToList();
                        if (newNodeList.Count > 0)
                        {
                            result[j].HasChild = true;
                            result.InsertRange(j + 1, newNodeList);
                        }
                        else
                        {
                            result[j].HasChild = false;
                        }
                    }
                }
            }

            return result;
        }

        private class SubServiceCategoryQueryItem
        {
            public Guid Id { get; set; }
            public string Name { get; set; }
            public int Level { get; set; }
            public Guid? Level1Id { get; set; }
            public Guid? Level2Id { get; set; }
            public Guid? Level3Id { get; set; }
            public Guid? Level4Id { get; set; }
            public string Path { get; set; }
            public int Order { get; set; }
            public bool Deleted { get; set; }
            public long RowNumber { get; set; }
        }

        private class ServiceTypeLeafQueryItem
        {
            public Guid Id { get; set; }
            public string Code { get; set; }
            public int? PricePerUnit { get; set; }
            public string Unit { get; set; }
            public Guid? LeafId { get; set; }
        }

        private class ServiceCategoryQueryItem
        {
            public Guid Id { get; set; }
            public Guid? Level1Id { get; set; }
            public Guid? Level2Id { get; set; }
            public Guid? Level3Id { get; set; }
            public Guid? Level4Id { get; set; }
        }

        private class ServiceCategoryTreeQueryItem
        {
            public Guid? Id { get; set; }

            public string ParentId { get; set; }

            public Guid? ParentLevel1Id { get; set; }

            public Guid? ParentLevel2Id { get; set; }

            public Guid? ParentLevel3Id { get; set; }

            public string Name { get; set; }

            public int Level { get; set; }

            public bool IsLeaf { get; set; }

            public bool HasChild { get; set; }

            public string Code { get; set; }

            public Guid? Value { get; set; }

            public int? PricePerUnit { get; set; }

            public string Unit { get; set; }

            public string Path { get; set; }

            public bool Deleted { get; set; }

            public int Order { get; set; }

            public long RowNumber { get; set; }

            public int TotalCount { get; set; }
        }

        private class SubServiceCategoryRelatedWithServiceTypeQueryItem
        {
            public Guid? Id { get; set; }
            public Guid? ParentLevel1Id { get; set; }
            public Guid? ParentLevel2Id { get; set; }
            public Guid? ParentLevel3Id { get; set; }
            public string Name { get; set; }
            public string ParentId { get; set; }
            public string Path { get; set; }
            public int Level { get; set; }
            public int Order { get; set; }
            public bool Deleted { get; set; }
            public long RowNumber { get; set; }
        }
    }
}