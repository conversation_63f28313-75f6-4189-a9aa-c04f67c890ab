﻿using System;
using System.Data;
using System.Data.Common;
using System.Threading.Tasks;
using AutoMapper;
using TinyCRM.Enums;
using Webaby;
using Webaby.Data;

namespace TinyCRM.Order.Commands
{
    public class UpdateOrderStatusesCommand : CommandBase
    {
        public Guid OrderStatusFileId { get; set; }
    }

    internal class UpdateOrderStatusesCommandHandler : CommandHandlerBase<UpdateOrderStatusesCommand>
    {
        public UpdateOrderStatusesCommandHandler(IServiceProvider serviceProvider) : base(serviceProvider)
        {
        }

        public override async Task ExecuteAsync(UpdateOrderStatusesCommand command)
        {
            var cmd = EntitySet.CreateDbCommand(CommandType.StoredProcedure, "ec.UpdateOrderStatuses");
            cmd.Parameters.Add(DbParameterHelper.AddNullableGuid(cmd, "@OrderStatusFileId", command.OrderStatusFileId));

            await EntitySet.ExecuteNonQueryAsync(cmd);
            await Task.CompletedTask;
        }
    }
}