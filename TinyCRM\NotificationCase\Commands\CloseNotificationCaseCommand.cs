﻿using AutoMapper;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using Webaby;
using Webaby.Data;
using Webaby.Localization;
using Webaby.Notification;
using Webaby.Security;

namespace TinyCRM.NotificationCase.Commands
{
    public class CloseNotificationCaseCommand : CommandBase
    {
        public Guid RootEntityId { get; set; }

        public Guid NotificationChanelSettingId { get; set; }
    }

    internal class CloseNotificationCaseCommandHandler : CommandHandlerBase<CloseNotificationCaseCommand>
    {
        public IUserService _userService { get; set; }

        public CloseNotificationCaseCommandHandler(IServiceProvider serviceProvider) : base(serviceProvider)
        {
            _userService = ServiceProvider.GetRequiredService<IUserService>();
        }

        public override async Task ExecuteAsync(CloseNotificationCaseCommand command)
        {
            var cmd = EntitySet.CreateDbCommand();
            cmd.Parameters.AddRange(new[]
            {
                DbParameterHelper.AddNullableGuid(cmd, "@RootEntityId", command.RootEntityId),
                DbParameterHelper.AddNullableGuid(cmd, "@NotificationChanelSettingId", command.NotificationChanelSettingId),
                DbParameterHelper.AddNullableGuid(cmd, "@ClosedBy", _userService.GetCurrentUser().Id)
            });

            cmd.CommandText = "dbo.CloseNotificationCases";
            cmd.CommandType = CommandType.StoredProcedure;
            await EntitySet.ExecuteNonQueryAsync(cmd);
        }
    }
}
