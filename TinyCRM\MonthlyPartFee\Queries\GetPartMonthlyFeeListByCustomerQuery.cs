﻿using System;
using System.Data;
using System.Data.Common;
using System.Linq;
using System.Threading.Tasks;
using TinyCRM.PartCustomer;
using Webaby;
using Webaby.Data;

namespace TinyCRM.MonthlyPartFee.Queries
{
    public class GetPartMonthlyFeeListByCustomerQuery : QueryBase<MonthlyPartFeeInfo>
    {
        public Guid? PartCustomerId { get; set; }

        public Guid? PartId { get; set; }

        public string PartCode { get; set; }

        public Guid? CustomerId { get; set; }

        public int? Year { get; set; }

        public int? Month { get; set; }

        public MonthlyPartFeeStatus? Status { get; set; }

        public Guid? MonthlyPartFeeBatchItemId { get; set; }
    }

    public class GetPartMonthlyFeeListByCustomerQueryHandler : QueryHandlerBase<GetPartMonthlyFeeListByCustomerQuery, MonthlyPartFeeInfo>
    {
        public GetPartMonthlyFeeListByCustomerQueryHandler(IServiceProvider serviceProvider) : base(serviceProvider)
        {
        }

        public override async Task<QueryResult<MonthlyPartFeeInfo>> ExecuteAsync(GetPartMonthlyFeeListByCustomerQuery query)
        {
            int startRow = query.Pagination.Index * query.Pagination.Size + 1;
            int endRow = query.Pagination.Index * query.Pagination.Size + query.Pagination.Size;

            var cmd = EntitySet.CreateDbCommand(CommandType.StoredProcedure, "dbo.GetPartMonthlyFeeListByCustomer");
            cmd.Parameters.Add(DbParameterHelper.AddNullableGuid(cmd, "@PartCustomerId", query.PartCustomerId));
            cmd.Parameters.Add(DbParameterHelper.AddNullableGuid(cmd, "@PartId", query.PartId));
            cmd.Parameters.Add(DbParameterHelper.AddNullableString(cmd, "@PartCode", query.PartCode));
            cmd.Parameters.Add(DbParameterHelper.AddNullableGuid(cmd, "@CustomerId", query.CustomerId));
            cmd.Parameters.Add(DbParameterHelper.AddNullableInt(cmd, "@Year", query.Year));
            cmd.Parameters.Add(DbParameterHelper.AddNullableInt(cmd, "@Month", query.Month));
            cmd.Parameters.Add(DbParameterHelper.AddNullableEnum(cmd, "@Status", query.Status));
            cmd.Parameters.Add(DbParameterHelper.AddNullableGuid(cmd, "@MonthlyPartFeeBatchItemId", query.MonthlyPartFeeBatchItemId));
            cmd.Parameters.Add(DbParameterHelper.AddNullableInt(cmd, "@StartRow", startRow));
            cmd.Parameters.Add(DbParameterHelper.AddNullableInt(cmd, "@EndRow", endRow));

            var result = (await EntitySet.ExecuteReadCommandAsync<MonthlyPartFeeInfo>(cmd)).ToList();
            return QueryResult.Create(result);
        }
    }
}