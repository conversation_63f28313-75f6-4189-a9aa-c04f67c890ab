﻿using System;
using System.Data;
using System.Data.Common;
using System.Threading.Tasks;
using AutoMapper;
using TinyCRM.Enums;
using Webaby;
using Webaby.Data;

namespace TinyCRM.Order.Commands
{
    public class ProcessImportOrdersCommand : CommandBase
    {
        public Guid OrderFileId { get; set; }
    }

    internal class ProcessImportOrdersCommandHandler : CommandHandlerBase<ProcessImportOrdersCommand>
    {
        public ProcessImportOrdersCommandHandler(IServiceProvider serviceProvider) : base(serviceProvider)
        {
        }

        public override async Task ExecuteAsync(ProcessImportOrdersCommand command)
        {
            var cmd = EntitySet.CreateDbCommand(CommandType.StoredProcedure, "ec.ProcessImportOrders");
            cmd.Parameters.Add(DbParameterHelper.AddNullableGuid(cmd, "@OrderFileId", command.OrderFileId));

            await EntitySet.ExecuteNonQueryAsync(cmd);
        }
    }
}