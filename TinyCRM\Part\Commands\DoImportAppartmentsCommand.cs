﻿using System;
using System.Data;
using System.Threading.Tasks;
using Webaby;
using Webaby.Data;
using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Webaby.Localization;

namespace TinyCRM.Part.Commands
{
    public class DoImportAppartmentsCommand : CommandBase
    {
        public Guid ImportSessionId { get; set; }

        public Guid ImportUserId { get; set; }

        public Guid RootId { get; set; }
    }
    internal class DoImportAppartmentsCommandHandler : CommandHandlerBase<DoImportAppartmentsCommand>
    {
        private readonly ILogger<DoImportAppartmentsCommandHandler> _logger;

        public DoImportAppartmentsCommandHandler(IServiceProvider serviceProvider, ILogger<DoImportAppartmentsCommandHandler> logger) : base(serviceProvider)
        {
            _logger = logger;
        }

        /// <summary>Execute import apartments from staging data</summary>
        public override async Task ExecuteAsync(DoImportAppartmentsCommand command)
        {
            var cmd = EntitySet.CreateDbCommand();
            cmd.Parameters.Add(DbParameterHelper.AddNullableGuid(cmd, "@ImportSessionId", command.ImportSessionId));
            cmd.Parameters.Add(DbParameterHelper.AddNullableGuid(cmd, "@ImportUserId", command.ImportUserId));
            cmd.Parameters.Add(DbParameterHelper.AddNullableGuid(cmd, "@RootId", command.RootId));

            cmd.CommandText = "dbo.DoImportAppartments";
            cmd.CommandType = CommandType.StoredProcedure;
            await EntitySet.ExecuteNonQueryAsync(cmd);
        }
    }
}
