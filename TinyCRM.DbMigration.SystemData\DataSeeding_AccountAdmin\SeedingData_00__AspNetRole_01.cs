﻿using Microsoft.EntityFrameworkCore;
using System.Reflection;
using System.Security.Cryptography;
using System.Text;
using Webaby.Data.Seeding;
using Webaby.Security;

namespace TinyCRM.DbMigration.SystemData.DataSeeding_AccountAdmin
{
    //internal class SeedingData_01__AspNetRole_01(MigrationDbContext dbContext) : SeedingData(dbContext)
    //{
    //    public override async Task<string> LocalHashAsync()
    //    {
    //        await Task.CompletedTask;
    //        return Convert.ToHexString(MD5.HashData(Encoding.UTF8.GetBytes("SeedingData_01__AspNetRole_01-2025-06-19T11:00")));
    //    }

    //    protected override async Task SeedDataAsync()
    //    {
    //        var admin02 = await DbContext.Users.FirstOrDefaultAsync(u => u.UserName == "admin-02");
    //        if (admin02 != null)
    //        {
    //            admin02.IsApproved = false;
    //        }
    //    }
    //}
    internal class SeedingData_00__AspNetRole_01(MigrationDbContext dbContext)
        : SeedingJsonData<ApplicationRole>(dbContext)
    {
        protected override Assembly ResourceAssembly => Assembly.GetExecutingAssembly();
        protected override SeedingJsonDataAction SeedingJsonDataAction => SeedingJsonDataAction.AddOrUpdate;
    }
}
