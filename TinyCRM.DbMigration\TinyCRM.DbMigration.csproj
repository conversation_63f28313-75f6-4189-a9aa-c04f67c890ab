﻿<?xml version="1.0" encoding="utf-8"?>
<Project Sdk="Microsoft.NET.Sdk">
	<PropertyGroup Label="Globals">
		<SccProjectName>SAK</SccProjectName>
		<SccProvider>SAK</SccProvider>
		<SccAuxPath>SAK</SccAuxPath>
		<SccLocalPath>SAK</SccLocalPath>
	</PropertyGroup>
	<PropertyGroup>
		<TargetFramework>net9.0</TargetFramework>
		<ImplicitUsings>enable</ImplicitUsings>
		<Nullable>enable</Nullable>
	</PropertyGroup>
	<ItemGroup>
		<None Remove="Schema\Migration_000016_procedure__GetTaskBoardItemList.mssql.sql" />
		<None Remove="Schema\Migration_000016_procedure__GetTaskBoardItemList.postgres.sql" />
		<None Remove="TinyCRM.DbMigration.csproj.vspscc" />
	</ItemGroup>
	<ItemGroup>
		<EmbeddedResource Include="Schema\Migration_000003_func__dbo_Pop_GetDate.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000003_func__dbo_Pop_GetDate.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__SearchRequestTicket.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000012_procedure__SearchRequestTicket_SaveDynamicFormValue.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__SearchRequestTicket.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000012_procedure__SearchRequestTicket_SaveDynamicFormValue.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000013_procedure__GetDataSetFromTicketKey_GetDataSetFromTaskKey.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000013_procedure__GetDataSetFromTicketKey_GetDataSetFromTaskKey.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000014_procedure__ImportTicketTask.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000015_procedure__GetRequestTicketHistories.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000014_procedure__ImportTicketTask.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000016_procedure__GetTaskBoardItemList.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000016_procedure__GetTaskBoardItemList.postgres.sql" />
	</ItemGroup>
	<ItemGroup>
		<ProjectReference Include="..\Webaby\Webaby.csproj" />
	</ItemGroup>
	<ItemGroup>
		<EmbeddedResource Include="Schema\Migration_000001_type__dbo_AppointmentAudit.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000001_type__dbo_AppointmentAudit.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000001_type__dbo_AppointmentRawType.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000001_type__dbo_AppointmentRawType.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000001_type__dbo_BusinessSettingsTable.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000001_type__dbo_BusinessSettingsTable.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000001_type__dbo_CoupleIdNumber.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000001_type__dbo_CoupleIdNumber.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000001_type__dbo_CustomerAppartmentMultiFilter.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000001_type__dbo_CustomerAppartmentMultiFilter.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000001_type__dbo_CustomerAppartmentSingleFilter.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000001_type__dbo_CustomerAppartmentSingleFilter.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000001_type__dbo_DateTimeList.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000001_type__dbo_DateTimeList.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000001_type__dbo_DistributeData.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000001_type__dbo_DistributeData.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000001_type__dbo_DistributePlan.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000001_type__dbo_DistributePlan.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000001_type__dbo_DynamicDefinedTableCellValueTable.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000001_type__dbo_DynamicDefinedTableCellValueTable.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000001_type__dbo_DynamicDefinedTableRowJsonValueTable.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000001_type__dbo_DynamicDefinedTableRowJsonValueTable.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000001_type__dbo_DynamicFieldValueTable.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000001_type__dbo_DynamicFieldValueTable.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000001_type__dbo_GatewayTable.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000001_type__dbo_GatewayTable.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000001_type__dbo_GroupReceiver.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000001_type__dbo_GroupReceiver.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000001_type__dbo_GuidGuid.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000001_type__dbo_GuidGuid.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000001_type__dbo_GuidList.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000001_type__dbo_GuidList.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000001_type__dbo_GuidStringTable.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000001_type__dbo_GuidStringTable.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000001_type__dbo_IdList.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000001_type__dbo_IdList.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000001_type__dbo_ImportCustomerDuplicationList.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000001_type__dbo_ImportCustomerDuplicationList.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000001_type__dbo_ImportCustomerList.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000001_type__dbo_ImportCustomerList.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000001_type__dbo_IntList.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000001_type__dbo_IntList.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000001_type__dbo_json.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000001_type__dbo_json.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000001_type__dbo_KeyValue.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000001_type__dbo_KeyValue.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000001_type__dbo_ObjectNumberList.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000001_type__dbo_ObjectNumberList.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000001_type__dbo_PermissionTable.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000001_type__dbo_PermissionTable.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000001_type__dbo_POP_JSON.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000001_type__dbo_POP_JSON.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000001_type__dbo_RawTableParam.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000001_type__dbo_RawTableParam.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000001_type__dbo_RolePermissionTable.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000001_type__dbo_RolePermissionTable.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000001_type__dbo_SortIdList.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000001_type__dbo_SortIdList.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000001_type__dbo_SortIdListSectionId.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000001_type__dbo_SortIdListSectionId.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000001_type__dbo_stringList.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000001_type__dbo_stringList.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000001_type__dbo_StringString.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000001_type__dbo_StringString.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000001_type__dbo_StringStringString.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000001_type__dbo_StringStringString.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000001_type__dbo_StringStringStringString.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000001_type__dbo_StringStringStringString.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000001_type__import_ContactRawType.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000001_type__import_ContactRawType.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000001_type__import_ContactType.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000001_type__import_ContactType.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000001_type__telesale_CallInfoType.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000001_type__telesale_CallInfoType.postgres.sql" />
	</ItemGroup>
	<ItemGroup>
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_AddCustomerIntoCampaignByDynamicFieldDefinitionName.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_AddCustomerIntoCampaignByDynamicFieldDefinitionName.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_AddDigitalContactsToCampaign.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_AddDigitalContactsToCampaign.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_AddExpenseItemListToEndorsement.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_AddExpenseItemListToEndorsement.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_AddExpenseItemsToPaymentRequest.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_AddExpenseItemsToPaymentRequest.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_AgentSurveyRatingSummaryReport.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_AgentSurveyRatingSummaryReport.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_AppointmentAudit.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_AppointmentAudit.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_AskAndAddAppSingletonExecutedTask.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_AskAndAddAppSingletonExecutedTask.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_AskSingletonAppDeploymentExecute.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_AskSingletonAppDeploymentExecute.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_AskSingletonAppScheduledTaskExecute.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_AskSingletonAppScheduledTaskExecute.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_BatchCreateRequestTicketList.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_BatchCreateRequestTicketList.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_BatchInsertNewDynamicFieldToExistedTicketList.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_BatchInsertNewDynamicFieldToExistedTicketList.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_BatchInsertUpdateDynamicDefinedTableCellValueList.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_BatchInsertUpdateDynamicDefinedTableCellValueList.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_BatchInsertUpdateDynamicFieldValueList.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_BatchInsertUpdateDynamicFieldValueList.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_BatchUpdateDynamicDefinedTableCellValueList.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_BatchUpdateDynamicDefinedTableCellValueList.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_BatchUpdateDynamicDefinedTableCellValueListByOwnTable.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_BatchUpdateDynamicDefinedTableCellValueListByOwnTable.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_BatchUpdateDynamicDefinedTableRowJsonValueList.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_BatchUpdateDynamicDefinedTableRowJsonValueList.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_BatchUpdateDynamicFieldValueList.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_BatchUpdateDynamicFieldValueList.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_BatchUpdateSoonerProcessMinutes.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_BatchUpdateSoonerProcessMinutes.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_BehaviorsReport.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_BehaviorsReport.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_Campaign_AddWorks_AddTickets.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_Campaign_AddWorks_AddTickets.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_Campaign_AddWorks_SearchTickets.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_Campaign_AddWorks_SearchTickets.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_CB_AbortAllQueueingCallbacks.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_CB_AbortAllQueueingCallbacks.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_CB_AddCallback.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_CB_AddCallback.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_CB_AddCallbackDetail.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_CB_AddCallbackDetail.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_CB_GetCallback.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_CB_GetCallback.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_CB_GetCallbackbyPhone.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_CB_GetCallbackbyPhone.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_CB_GetCallbackByTelephonyPort.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_CB_GetCallbackByTelephonyPort.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_CB_GetCallbackDetail.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_CB_GetCallbackDetail.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_CB_GetCallbackSetting.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_CB_GetCallbackSetting.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_CB_GetCallbackSettingByName.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_CB_GetCallbackSettingByName.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_CB_GetLastCallbackDetail.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_CB_GetLastCallbackDetail.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_CB_GetQueueingCallback.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_CB_GetQueueingCallback.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_CB_ReturnToQueue.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_CB_ReturnToQueue.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_CB_UpdateCallback.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_CB_UpdateCallback.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_CB_UpdateCallbackbyPhone.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_CB_UpdateCallbackbyPhone.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_CB_UpdateCallbackDetail.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_CB_UpdateCallbackDetail.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_ChangeTaskOwnedByOrganizationIdBatch.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_ChangeTaskOwnedByOrganizationIdBatch.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_ChangeTicketOwnedByOrganizationIdBatch.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_ChangeTicketOwnedByOrganizationIdBatch.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_CheckDynamicTableValid.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_CheckDynamicTableValid.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_CheckEditRequestTicketPermission.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_CheckEditRequestTicketPermission.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_CheckLicenseBusinessPermission.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_CheckLicenseBusinessPermission.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_CheckLicenseBusinessPermissionWhenCreateEditRole.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_CheckLicenseBusinessPermissionWhenCreateEditRole.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_CheckSendingAndMarkSentEmail.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_CheckSendingAndMarkSentEmail.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_CloneDynamicForm.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_CloneDynamicForm.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_CloneWorkflow.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_CloneWorkflow.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_CloseAllNotificationCases.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_CloseAllNotificationCases.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_CloseAllNotificationList.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_CloseAllNotificationList.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_CloseAllTodoTaskStatus.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_CloseAllTodoTaskStatus.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_CloseNotificationCases.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_CloseNotificationCases.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_CloseTask.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_CloseTask.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_CommitSingletonAppDeploymentExecute.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_CommitSingletonAppDeploymentExecute.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_CommitSingletonAppScheduledTaskExecuting.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_CommitSingletonAppScheduledTaskExecuting.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_CorrectFieldOrdersInForm.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_CorrectFieldOrdersInForm.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_CreateAccessPermission.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_CreateAccessPermission.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_CreateGroupNotification.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_CreateGroupNotification.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_CreateLogLinkDigitalContact.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_CreateLogLinkDigitalContact.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_CreateMobileNotificationFromProspectAssignmentList.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_CreateMobileNotificationFromProspectAssignmentList.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_CreateMonthlyPartFeeBatch.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_CreateMonthlyPartFeeBatch.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_CreateOwnDbTableForDynamicDefinedTableSchema.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_CreateOwnDbTableForDynamicDefinedTableSchema.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_CreateRootPart.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_CreateRootPart.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_CreateSurveyFeedbackFromCampaignTicketAssignWork.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_CreateSurveyFeedbackFromCampaignTicketAssignWork.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_Customer_SearchCustomerToAddToCampaign.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_Customer_SearchCustomerToAddToCampaign.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_DedupBackendAndTempCustomers.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_DedupBackendAndTempCustomers.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_DedupSignleBackendAndTempCustomers.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_DedupSignleBackendAndTempCustomers.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_DeleteAppSingletonExecutedTask.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_DeleteAppSingletonExecutedTask.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_DeleteCustomerRequestTicketList.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_DeleteCustomerRequestTicketList.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_DeleteImportCustomerRaw.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_DeleteImportCustomerRaw.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_DeleteMonthlyPartFee.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_DeleteMonthlyPartFee.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_DeleteNonRecentPasswords.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_DeleteNonRecentPasswords.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_DigitalCampaign_AddCustomerDigitalContactToCampaign.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_DigitalCampaign_AddCustomerDigitalContactToCampaign.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_DigitalCampaign_SearchCustomerDigitalContactToAddToCampaign.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_DigitalCampaign_SearchCustomerDigitalContactToAddToCampaign.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_DigitalContact_AddAnonymousDigitalContactToCampaign.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_DigitalContact_AddAnonymousDigitalContactToCampaign.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_DigitalContact_SearchAnonymousDigitalContactToAddToCampaign.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_DigitalContact_SearchAnonymousDigitalContactToAddToCampaign.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_DistributeDataToAgent.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_DistributeDataToAgent.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_DistributeWork.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_DistributeWork.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_DoImportAppartments.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_DoImportAppartments.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_DoImportB2BCustomers.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_DoImportB2BCustomers.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_DoImportFeeCategoryParts.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_DoImportFeeCategoryParts.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_DoImportParts.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_DoImportParts.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_DoImportPartServiceUsedHistories.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_DoImportPartServiceUsedHistories.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_DoImportSO.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_DoImportSO.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_DoImportSystems.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_DoImportSystems.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_Drop_Foreign_Key.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_Drop_Foreign_Key.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_DropDynamicDefinedTableOwnDbTable.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_DropDynamicDefinedTableOwnDbTable.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_ExecuteEvaluateAutoNextTaskCondition.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_ExecuteEvaluateAutoNextTaskCondition.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_ExportChecklistByCategory.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_ExportChecklistByCategory.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_ExportDynamicForm.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_ExportDynamicForm.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_ExportImportB2BCustomers.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_ExportImportB2BCustomers.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_ExportMaintainListDetail.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_ExportMaintainListDetail.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_ExportMonthlyFeeList.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_ExportMonthlyFeeList.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_ExportRequestTicket.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_ExportRequestTicket.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_ExportRequestTicket_ElectricalCabinetCheckList.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_ExportRequestTicket_ElectricalCabinetCheckList.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_ExportRequestTicket_LiftCheckList.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_ExportRequestTicket_LiftCheckList.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_ExportRequestTicketList_ElectricalGeneratorCheckList.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_ExportRequestTicketList_ElectricalGeneratorCheckList.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_ExportRequestTicketRelatedProducts.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_ExportRequestTicketRelatedProducts.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_ExportRequestTickets.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_ExportRequestTickets.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_ExportRequestTicketTaskTypes.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_ExportRequestTicketTaskTypes.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_ExportSearchRequestTicketDynamicResult.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_ExportSearchRequestTicketDynamicResult.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_ExportSearchRequestTicketDynamicResultWithoutPivot.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_ExportSearchRequestTicketDynamicResultWithoutPivot.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_ExportSearchRequestTicketResult.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_ExportSearchRequestTicketResult.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_ExportSearchRequestTicketResult_MB_KhieuNai.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_ExportSearchRequestTicketResult_MB_KhieuNai.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_ExportSearchTaskDynamicResult.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_ExportSearchTaskDynamicResult.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_ExportServiceType.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_ExportServiceType.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_ExportTicketProductExchanges.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_ExportTicketProductExchanges.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_ExportWorkflow.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_ExportWorkflow.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_FeeCategoryPartListByMonthly.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_FeeCategoryPartListByMonthly.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_FirstAncestorOrg.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_FirstAncestorOrg.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_FirstAncestorOrgExistRole.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_FirstAncestorOrgExistRole.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_FirstSuccessorOrg.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_FirstSuccessorOrg.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GeolocationScanImportB2BCustomers.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GeolocationScanImportB2BCustomers.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetAgencyHierarchy.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetAgencyHierarchy.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetAgencyHierarchySummary.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetAgencyHierarchySummary.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetAgentCampaignList.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetAgentCampaignList.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetAgentDoSurveyByServiceType.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetAgentDoSurveyByServiceType.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetAllDynamicDefinedTableCellValueList.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetAllDynamicDefinedTableCellValueList.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetAllDynamicDefinedTableCellValueListByOwnTable.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetAllDynamicDefinedTableCellValueListByOwnTable.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetAllDynamicDefinedTableCellValueListByRowJsonValue.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetAllDynamicDefinedTableCellValueListByRowJsonValue.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetAllDynamicFieldValueInfoList.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetAllDynamicFieldValueInfoList.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetAllPartMonthlyFeeList.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetAllPartMonthlyFeeList.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetAllTaskOwnerInTicket.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetAllTaskOwnerInTicket.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetAlternativeContentTemplateNotificationChanelSetting.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetAlternativeContentTemplateNotificationChanelSetting.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetAlternativeNotiChannelContentTemplateByNotificationChannelSettingId.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetAlternativeNotiChannelContentTemplateByNotificationChannelSettingId.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetAnonymousDigitalContactListInCampaign.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetAnonymousDigitalContactListInCampaign.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetAppointmentSumaryForAgent_01.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetAppointmentSumaryForAgent_01.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetAppSingletonExecutedTask.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetAppSingletonExecutedTask.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetAutoDialCustomerListInCampaign.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetAutoDialCustomerListInCampaign.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetAvailableOrganization.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetAvailableOrganization.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetBaoCaoNangSuatLaoDong02Report.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetBaoCaoNangSuatLaoDong02Report.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetBucketDashboard.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetBucketDashboard.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetBusinessPermissionUserListInOrg.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetBusinessPermissionUserListInOrg.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetCallListByContact.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetCallListByContact.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetCallListByProspectAssignment.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetCallListByProspectAssignment.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetCampaignDataSummaryForCostEstimation.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetCampaignDataSummaryForCostEstimation.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetCampaignDropdownList.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetCampaignDropdownList.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetCampaignFileFromEntityLink.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetCampaignFileFromEntityLink.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetCampaignInfo.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetCampaignInfo.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetCampaignPushCodeInfoForCostEstimation.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetCampaignPushCodeInfoForCostEstimation.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetCampaignWorkCustomer.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetCampaignWorkCustomer.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetCampaignWorker.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetCampaignWorker.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetCampaignWorkTicket.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetCampaignWorkTicket.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetContactDuplicate.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetContactDuplicate.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetContextHistoriesQuery.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetContextHistoriesQuery.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetCreateAppointmentInfo.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetCreateAppointmentInfo.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetCustomerAlternativeAddress.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetCustomerAlternativeAddress.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetCustomerAlternativeAddressByCustomerId.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetCustomerAlternativeAddressByCustomerId.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetCustomerAlternativeAddressByListCustomerIdAndRpContact.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetCustomerAlternativeAddressByListCustomerIdAndRpContact.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetCustomerAppartmentInfoMulti.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetCustomerAppartmentInfoMulti.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetCustomerByRpContact.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetCustomerByRpContact.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetCustomerComplainedAnalysis.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetCustomerComplainedAnalysis.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetCustomerDigitalContactListInCampaign.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetCustomerDigitalContactListInCampaign.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetCustomerDuplicate.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetCustomerDuplicate.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetCustomerListByJobAndVersion.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetCustomerListByJobAndVersion.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetCustomerRequestTicketByServiceTypeId.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetCustomerRequestTicketByServiceTypeId.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetCustomerRequestTicketList.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetCustomerRequestTicketList.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetDataSetFromMonthlyPartFee.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetDataSetFromMonthlyPartFee.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetDataSetFromProspectAssignmentKey.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetDataSetFromProspectAssignmentKey.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetDataSetFromTaskKey.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetDataSetFromTaskKey.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetDataSetFromTbCallback.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetDataSetFromTbCallback.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetDataSetFromTicketKey.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetDataSetFromTicketKey.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetDetailCallTimesReport.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetDetailCallTimesReport.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetDetailContactInfo.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetDetailContactInfo.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetDetailPaymentDataByValueGroupId.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetDetailPaymentDataByValueGroupId.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetDetailTripBudgetDataByValueGroupId.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetDetailTripBudgetDataByValueGroupId.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetDigitalContactByUserIdList.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetDigitalContactByUserIdList.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetDigitalDeliverMessagesHistory.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetDigitalDeliverMessagesHistory.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetDigitalMessDetailByProspectAssignment.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetDigitalMessDetailByProspectAssignment.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetDisconnectedPartsReport.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetDisconnectedPartsReport.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetDoneRequestTicketImportSessionResultDetail.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetDoneRequestTicketImportSessionResultDetail.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetDoneRequestTicketListImportSession.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetDoneRequestTicketListImportSession.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetDynamicDefinedTableColumnOnFormList.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetDynamicDefinedTableColumnOnFormList.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetDynamicDefinedTableRowJsonValueList.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetDynamicDefinedTableRowJsonValueList.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetDynamicFieldListByServiceTypes.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetDynamicFieldListByServiceTypes.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetDynamicFieldSectionDropdownList.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetDynamicFieldSectionDropdownList.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetDynamicFieldValueInfo.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetDynamicFieldValueInfo.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetDynamicFieldValueInfoByDynamicFormValueIdList.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetDynamicFieldValueInfoByDynamicFormValueIdList.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetDynamicFieldValueInfoListByImportCustomer.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetDynamicFieldValueInfoListByImportCustomer.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetDynamicFormByEntityLinkTicketQuery.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetDynamicFormByEntityLinkTicketQuery.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetEndorsementItemListByValueGroupId.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetEndorsementItemListByValueGroupId.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetEndorsementItemsForPayment.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetEndorsementItemsForPayment.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetEndorsementSummaryGroupByContestForPayment.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetEndorsementSummaryGroupByContestForPayment.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetEntityLinkBusinessSpecificList.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetEntityLinkBusinessSpecificList.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetEquipmentHistories.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetEquipmentHistories.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetExchangeDataReportItems.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetExchangeDataReportItems.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetF3ReportData.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetF3ReportData.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetFactoryByName.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetFactoryByName.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetImportB2BCustomersResults.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetImportB2BCustomersResults.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetImportB2BDataStatistics.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetImportB2BDataStatistics.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetImportRequestTicketHistories.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetImportRequestTicketHistories.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetImportSODataStatistics.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetImportSODataStatistics.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetImportSOResult.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetImportSOResult.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetIndexMissingLog.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetIndexMissingLog.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetIndexStatistic.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetIndexStatistic.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetKnowledgeItemsByParentId.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetKnowledgeItemsByParentId.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetLandingServiceTypeList.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetLandingServiceTypeList.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetLatestTasksByTaskType.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetLatestTasksByTaskType.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetLeadTimeReportItems.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetLeadTimeReportItems.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetLicenseBusinessPermissionList.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetLicenseBusinessPermissionList.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetListOfTicketDynamicFormValueId.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetListOfTicketDynamicFormValueId.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetMaintenanceDataByValueGroupId.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetMaintenanceDataByValueGroupId.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetManagementUnitAndSubOrganizations.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetManagementUnitAndSubOrganizations.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetMessageReceive.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetMessageReceive.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetMessageSend.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetMessageSend.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetMonthFeeStatistics.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetMonthFeeStatistics.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetMonthlyFeePaidByTimeReport.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetMonthlyFeePaidByTimeReport.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetMonthlyFeePaymentReport.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetMonthlyFeePaymentReport.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetMonthlyPartFeeDebitNoteFileListByBatch.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetMonthlyPartFeeDebitNoteFileListByBatch.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetMonthlyPartFeeDebitReminderFileList.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetMonthlyPartFeeDebitReminderFileList.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetMonthlyPartFeeDebitReminderList.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetMonthlyPartFeeDebitReminderList.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetMonthlyPartFeeItemDetailList.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetMonthlyPartFeeItemDetailList.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetMonthlyPartFeeItemList.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetMonthlyPartFeeItemList.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetMonthlyPartFeeItemListByBatch.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetMonthlyPartFeeItemListByBatch.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetMostExpensiveStatementLogs.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetMostExpensiveStatementLogs.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetNextSequenceValue.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetNextSequenceValue.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetNotificationList.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetNotificationList.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetNotProcessedMonthlyPartFeeItemByBatch.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetNotProcessedMonthlyPartFeeItemByBatch.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetNumberOfCallByCallResultSummary.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetNumberOfCallByCallResultSummary.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetOrganizationByPrefixCode.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetOrganizationByPrefixCode.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetOrganizationPathByOrgId.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetOrganizationPathByOrgId.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetOrgFromUser.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetOrgFromUser.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetPartBookingList.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetPartBookingList.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetPartMaintenanceDetail.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetPartMaintenanceDetail.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetPartMonthlyFeeListByCustomer.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetPartMonthlyFeeListByCustomer.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetPartOperationSummaryByPCReport.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetPartOperationSummaryByPCReport.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetPartsAndRequestTicket.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetPartsAndRequestTicket.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetPartServiceUsedHistoryList.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetPartServiceUsedHistoryList.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetPartSurveyFeedbackList.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetPartSurveyFeedbackList.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetPasswordHistory.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetPasswordHistory.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetPathOfPart.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetPathOfPart.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetPaymentRequestItemListByValueGroupId.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetPaymentRequestItemListByValueGroupId.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetPaymentRequestSummaryByValueGroupId.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetPaymentRequestSummaryByValueGroupId.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetPlanJobList.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetPlanJobList.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetPredefinedListConcreteRecordByValueGroupId.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetPredefinedListConcreteRecordByValueGroupId.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetPredefinedListReportItems.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetPredefinedListReportItems.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetPreviousTaskOwnerInWorkflow.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetPreviousTaskOwnerInWorkflow.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetProductByCode.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetProductByCode.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetProductByName.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetProductByName.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetProductExchangeHistories.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetProductExchangeHistories.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetProductExchangeHistoryDetails.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetProductExchangeHistoryDetails.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetProductTicketHistories.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetProductTicketHistories.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetProductTicketHistoryDetails.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetProductTicketHistoryDetails.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetProspectListInCampaignByAgent.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetProspectListInCampaignByAgent.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetReportDataProcessStatus_001.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetReportDataProcessStatus_001.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetReportDataResult_02.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetReportDataResult_02.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetReportSummaryCallback.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetReportSummaryCallback.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetRequestTicketBoardItemList.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetRequestTicketBoardItemList.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetRequestTicketBusinessResultList.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetRequestTicketBusinessResultList.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetRequestTicketByListCustomerIdAndRpContact.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetRequestTicketByListCustomerIdAndRpContact.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetRequestTicketByProspectAssignmentId.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetRequestTicketByProspectAssignmentId.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetRequestTicketDataConversion.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetRequestTicketDataConversion.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetRequestTicketFirstTaskAutoNextTaskErrorList.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetRequestTicketFirstTaskAutoNextTaskErrorList.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetRequestTicketHistories.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetRequestTicketHistories.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetRequestTicketInteractionHistories.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetRequestTicketInteractionHistories.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetRequestTicketListByDynamicFieldConditions.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetRequestTicketListByDynamicFieldConditions.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetRequestTicketListByImportCustomer.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetRequestTicketListByImportCustomer.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetRequestTicketSumaryItems.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetRequestTicketSumaryItems.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetRequestTicketSummaryByTime.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetRequestTicketSummaryByTime.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetRetrievalReportItems.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetRetrievalReportItems.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetServiceTypeByDynamicFieldName.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetServiceTypeByDynamicFieldName.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetSLAChiTietReport.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetSLAChiTietReport.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetSLATongHopReport.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetSLATongHopReport.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetSLATraSoatReport.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetSLATraSoatReport.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetSumaryReport.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetSumaryReport.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetSummaryTicketByServiceTypeAndWeekInYear.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetSummaryTicketByServiceTypeAndWeekInYear.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetSurveyFeedbackListByCustomer.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetSurveyFeedbackListByCustomer.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetSurveyReport.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetSurveyReport.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetSurveyReportInbound.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetSurveyReportInbound.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetSurveyWithAnswerSuiteReport.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetSurveyWithAnswerSuiteReport.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetTaskBoardItemList.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetTaskBoardItemList.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetTaskDynamicFormHistories.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetTaskDynamicFormHistories.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetTaskListInTaskTypeGroup.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetTaskListInTaskTypeGroup.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetTaskSummary.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetTaskSummary.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetTaskSummaryCPC.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetTaskSummaryCPC.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetTaskTypeForTicket.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetTaskTypeForTicket.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetTerminalOperationReport.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetTerminalOperationReport.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetTicketOwnerSettingFromSurveyFeedback.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetTicketOwnerSettingFromSurveyFeedback.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetTicketReportByOwner.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetTicketReportByOwner.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetTicketReportByServiceType.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetTicketReportByServiceType.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetTicketStatusDetail.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetTicketStatusDetail.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetTmrPerformanceReport_01.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetTmrPerformanceReport_01.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetTopComplainedProductList.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetTopComplainedProductList.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetTopComplainedProductQuantity.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetTopComplainedProductQuantity.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetTopComplainedProductQuantitySamFactoryAndExpired.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetTopComplainedProductQuantitySamFactoryAndExpired.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetTopSMSByPhone.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetTopSMSByPhone.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetUncompletedContactCallResultServiceCallbackList.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetUncompletedContactCallResultServiceCallbackList.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetUserAssignmentByRouting.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetUserAssignmentByRouting.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetUserCurrentTicketListByTask.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetUserCurrentTicketListByTask.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetUserFilterPermission.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetUserFilterPermission.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetUserFuncBusinessPermissionConfigs.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetUserFuncBusinessPermissionConfigs.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetUserFuncBusinessPermissionConfigsInOrg.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetUserFuncBusinessPermissionConfigsInOrg.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetUserInHotListGroup.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetUserInHotListGroup.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetUserList.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetUserList.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetUserListByRole.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetUserListByRole.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetUserPermissions.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetUserPermissions.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetUsersByFilters.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetUsersByFilters.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetUsersByFiltersWithoutOrgs.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetUsersByFiltersWithoutOrgs.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetUsersByLikeFilters.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetUsersByLikeFilters.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetValdatePartBookingList.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetValdatePartBookingList.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetWebChatMessageList.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_GetWebChatMessageList.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_ImportCustomer_Execute.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_ImportCustomer_Execute.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_ImportCustomer_GetResult.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_ImportCustomer_GetResult.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_ImportCustomer_GetScanDataSummary.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_ImportCustomer_GetScanDataSummary.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_ImportCustomer_GetScanDataSummaryReport.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_ImportCustomer_GetScanDataSummaryReport.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_ImportCustomer_Log.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_ImportCustomer_Log.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_ImportCustomer_OverwriteInfo.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_ImportCustomer_OverwriteInfo.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_ImportCustomer_QuickScanData.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_ImportCustomer_QuickScanData.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_ImportCustomer_Retrieve.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_ImportCustomer_Retrieve.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_ImportCustomer_ScanData.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_ImportCustomer_ScanData.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_ImportCustomer_UpdateRawCompositeKey.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_ImportCustomer_UpdateRawCompositeKey.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_ImportCustomerToCampaign.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_ImportCustomerToCampaign.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_ImportOnlyDigitalContactCommand.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_ImportOnlyDigitalContactCommand.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_ImportPartDetail.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_ImportPartDetail.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_ImportPartError.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_ImportPartError.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_ImportPartServiceUsedHistories_GetScanDataSummaryReport.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_ImportPartServiceUsedHistories_GetScanDataSummaryReport.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_ImportPartV2.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_ImportPartV2.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_ImportProduct_ScanData.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_ImportProduct_ScanData.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_ImportRetrieval.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_ImportRetrieval.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_InsertGateway.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_InsertGateway.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_InsertObjectIntoJson.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_InsertObjectIntoJson.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_InsertPermissions.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_InsertPermissions.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_LinkTicketTaskPartByName.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_LinkTicketTaskPartByName.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_LoadAndLogIndexMissing.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_LoadAndLogIndexMissing.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_LoadAndLogIndexStatistic.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_LoadAndLogIndexStatistic.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_LoadAndLogMostExpensiveStatement.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_LoadAndLogMostExpensiveStatement.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_log_Ticketchange.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_log_Ticketchange.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_MBBank_OneOrgUpOneOrgDownManagerFind.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_MBBank_OneOrgUpOneOrgDownManagerFind.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_MergeCustomerAndUpdateLinkDigitalContact.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_MergeCustomerAndUpdateLinkDigitalContact.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_OverProcessedRequestTicketSLAReport.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_OverProcessedRequestTicketSLAReport.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_PartReportStatus.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_PartReportStatus.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_Proc_PrintLongText.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_Proc_PrintLongText.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_ProcessDoneRequestTicketImportSession.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_ProcessDoneRequestTicketImportSession.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_ProcessImportMassSessionCustomerVersioning.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_ProcessImportMassSessionCustomerVersioning.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_ProcessImportStagingCustomer.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_ProcessImportStagingCustomer.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_Query_Dashboard_KGS.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_Query_Dashboard_KGS.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_ReallocatedRequestTicketCustomer.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_ReallocatedRequestTicketCustomer.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_ReallocatePartCustomer.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_ReallocatePartCustomer.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_RegainCampaignAssignment.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_RegainCampaignAssignment.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_RemoveAdjustmentListByContestCommand.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_RemoveAdjustmentListByContestCommand.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_RemoveDuplicationNotification.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_RemoveDuplicationNotification.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_RemoveExpenseItemListFromEndorsement.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_RemoveExpenseItemListFromEndorsement.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_RemoveExpenseItemsFromPaymentRequest.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_RemoveExpenseItemsFromPaymentRequest.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_RequestTicketDetailWithDynamicFieldInfoReport.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_RequestTicketDetailWithDynamicFieldInfoReport.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_RequestTicketDetailWithDynamicFieldInfoReportDataTable.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_RequestTicketDetailWithDynamicFieldInfoReportDataTable.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_RequestTicketDetailWithoutDynamicFieldInfoReportDataTable.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_RequestTicketDetailWithoutDynamicFieldInfoReportDataTable.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_RequestTicketOwnershipSummaryReport.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_RequestTicketOwnershipSummaryReport.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_RequestTicketProcessStatusReport.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_RequestTicketProcessStatusReport.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_RequestTicketReport.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_RequestTicketReport.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_RequestTicketSLADetailReport.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_RequestTicketSLADetailReport.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_RequestTicketSummaryByOwnerReport.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_RequestTicketSummaryByOwnerReport.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_RequestTicketSummaryByServiceTypeReport.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_RequestTicketSummaryByServiceTypeReport.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_RequestTicketSummaryWithGroupbyReport.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_RequestTicketSummaryWithGroupbyReport.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_ResetRootFlowOrders.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_ResetRootFlowOrders.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_RollbackDistribute.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_RollbackDistribute.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_SaveAlternativeNotiChannelContentTemplateOrder.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_SaveAlternativeNotiChannelContentTemplateOrder.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_SaveBusinessPermissionDeletedInfoList.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_SaveBusinessPermissionDeletedInfoList.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_SaveDynamicFieldOrders.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_SaveDynamicFieldOrders.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_SaveDynamicFormValue.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_SaveDynamicFormValue.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_ScanDoneRequestTicketImportSession.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_ScanDoneRequestTicketImportSession.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_ScanDoneRequestTicketImportSessionResult.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_ScanDoneRequestTicketImportSessionResult.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_ScanImportAppartments.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_ScanImportAppartments.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_ScanImportB2BClassifications.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_ScanImportB2BClassifications.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_ScanImportB2BCustomers.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_ScanImportB2BCustomers.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_ScanImportFeeCategoryParts.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_ScanImportFeeCategoryParts.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_ScanImportParts.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_ScanImportParts.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_ScanImportPartServiceUsedHistory.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_ScanImportPartServiceUsedHistory.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_ScanImportSO.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_ScanImportSO.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_ScanImportSODistributor.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_ScanImportSODistributor.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_ScanImportSOProduct.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_ScanImportSOProduct.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_ScanImportSORequestTicket.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_ScanImportSORequestTicket.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_ScanImportSystems.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_ScanImportSystems.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_SearchAgencyWithoutTickets.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_SearchAgencyWithoutTickets.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_SearchAgentCampaignTicketAssginmentList.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_SearchAgentCampaignTicketAssginmentList.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_SearchAndAddRequestTicketToCampaign.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_SearchAndAddRequestTicketToCampaign.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_SearchAppartmentPartList.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_SearchAppartmentPartList.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_SearchAppSetting.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_SearchAppSetting.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_SearchAutoCompleteContentTemplate.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_SearchAutoCompleteContentTemplate.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_SearchCallbackList.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_SearchCallbackList.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_SearchCampaign.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_SearchCampaign.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_SearchChannels.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_SearchChannels.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_SearchClassifications.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_SearchClassifications.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_SearchContact.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_SearchContact.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_SearchCustomerAlternativeAddress.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_SearchCustomerAlternativeAddress.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_SearchCustomerAndRegainBySaleSupport.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_SearchCustomerAndRegainBySaleSupport.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_SearchCustomerForRegainBySaleSupport.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_SearchCustomerForRegainBySaleSupport.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_SearchCustomers.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_SearchCustomers.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_SearchEndorsementForPayment.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_SearchEndorsementForPayment.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_SearchGateways.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_SearchGateways.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_SearchInboundCustomerAlternativeAddresses.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_SearchInboundCustomerAlternativeAddresses.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_SearchInboundCustomers.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_SearchInboundCustomers.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_SearchInboundTickets.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_SearchInboundTickets.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_SearchKnowledgeItemQuery.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_SearchKnowledgeItemQuery.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_SearchLocalizationByLanguages.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_SearchLocalizationByLanguages.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_SearchMovedProspectAssignment.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_SearchMovedProspectAssignment.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_SearchOrganizationList.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_SearchOrganizationList.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_SearchOrganizations.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_SearchOrganizations.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_SearchPartMaintenance.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_SearchPartMaintenance.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_SearchPartMaintenanceWithRepair.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_SearchPartMaintenanceWithRepair.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_SearchParts.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_SearchParts.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_SearchProductList.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_SearchProductList.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_SearchRecursiveOrganizations.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_SearchRecursiveOrganizations.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_SearchRequestTicket.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_SearchRequestTicket.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_SearchRequestTicketNAB.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_SearchRequestTicketNAB.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_SearchRetrieval.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_SearchRetrieval.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_SearchScanErrorLog.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_SearchScanErrorLog.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_SearchServiceType_001.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_SearchServiceType_001.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_SearchServiceTypeTaskTypeByDueTimeId.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_SearchServiceTypeTaskTypeByDueTimeId.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_SearchTicketAssignmentList.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_SearchTicketAssignmentList.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_SearchToDistributeBySaleSupport_01.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_SearchToDistributeBySaleSupport_01.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_SearchToDistributeByTeamLead.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_SearchToDistributeByTeamLead.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_SearchUserList.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_SearchUserList.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_SearchUsersAddToCampaign.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_SearchUsersAddToCampaign.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_SearchUsersByRolesAndOrgs.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_SearchUsersByRolesAndOrgs.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_SendGroupMessage.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_SendGroupMessage.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_SendNotificationList.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_SendNotificationList.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_SetRolePermissions.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_SetRolePermissions.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_SetSequenceValue.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_SetSequenceValue.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_SourceChanelReport.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_SourceChanelReport.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_SubmitLocalizationList.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_SubmitLocalizationList.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_SyncAppRequestLicenseLog.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_SyncAppRequestLicenseLog.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_SyncMissedDynamicFormValueAndDynamicDefinedTableFieldValues.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_SyncMissedDynamicFormValueAndDynamicDefinedTableFieldValues.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_SyncOwnDbTableColumnForDynamicDefinedTableSchema.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_SyncOwnDbTableColumnForDynamicDefinedTableSchema.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_UpdateAppSingletonExecutedTask.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_UpdateAppSingletonExecutedTask.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_UpdateBusinessSettings.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_UpdateBusinessSettings.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_UpdateCurrentDueTimeReferenceBySysDueChange.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_UpdateCurrentDueTimeReferenceBySysDueChange.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_UpdateDialingProspectAssignment.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_UpdateDialingProspectAssignment.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_UpdateEntityLinkByLinkedTicketColumn.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_UpdateEntityLinkByLinkedTicketColumn.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_UpdateImportDigitalContact.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_UpdateImportDigitalContact.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_UpdateImportedVLCode.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_UpdateImportedVLCode.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_UpdateMonthlyPartFeeBatchStatus.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_UpdateMonthlyPartFeeBatchStatus.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_UpdatePartFullPathName.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_UpdatePartFullPathName.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_UpdateProspectAssignmentResult.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_UpdateProspectAssignmentResult.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_UpdateUserEmail.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_UpdateUserEmail.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_UpdateUserName.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_UpdateUserName.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_UpdateUserTaskAssignmentRoutingPriority.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_UpdateUserTaskAssignmentRoutingPriority.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_usp_CreateInserts.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__dbo_usp_CreateInserts.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__ec_ProcessImportOrders.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__ec_ProcessImportOrders.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__ec_ProcessStagingECommerceCustomers.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__ec_ProcessStagingECommerceCustomers.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__ec_ScanReferenceObjectImportOrders.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__ec_ScanReferenceObjectImportOrders.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__ec_ScanStagingECommerceCustomersGeolocation.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__ec_ScanStagingECommerceCustomersGeolocation.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__ec_UpdateImportECommerceCustomersCode.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__ec_UpdateImportECommerceCustomersCode.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__ec_UpdateOrderStatuses.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__ec_UpdateOrderStatuses.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__emccollab_SearchWebChatList.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__emccollab_SearchWebChatList.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__emccollab_SelfServiceFlow_GetProductivityUser.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__emccollab_SelfServiceFlow_GetProductivityUser.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__emccollab_SelfServiceFlow_UWCount.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__emccollab_SelfServiceFlow_UWCount.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__fwd_AddNewContractForCustomerWithTicket.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__fwd_AddNewContractForCustomerWithTicket.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__fwd_CommitContestExpense.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__fwd_CommitContestExpense.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__fwd_DeleteAllPolicies.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__fwd_DeleteAllPolicies.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__fwd_ExportCrossCheckData.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__fwd_ExportCrossCheckData.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__fwd_ExportCrossCheckData_PolicyList.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__fwd_ExportCrossCheckData_PolicyList.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__fwd_GetBudgetManagementReport.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__fwd_GetBudgetManagementReport.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__fwd_GetContestListAPI.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__fwd_GetContestListAPI.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__fwd_GetContestOverviewReport.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__fwd_GetContestOverviewReport.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__fwd_GetCustomerNotHaveRequestTicketByJobList.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__fwd_GetCustomerNotHaveRequestTicketByJobList.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__fwd_GetCustomerRequestTicketByJobList.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__fwd_GetCustomerRequestTicketByJobList.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__fwd_GetEndorseDefinitionReport.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__fwd_GetEndorseDefinitionReport.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__fwd_GetExpenseAdjustmentList.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__fwd_GetExpenseAdjustmentList.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__fwd_GetMemoLinkedFileListFromProposalTicket.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__fwd_GetMemoLinkedFileListFromProposalTicket.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__fwd_GetMonthlyPaymentsOrDisclosuresReport.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__fwd_GetMonthlyPaymentsOrDisclosuresReport.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__fwd_GetPaymentDefinitionReport.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__fwd_GetPaymentDefinitionReport.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__fwd_GetReportExpenseItemByContest.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__fwd_GetReportExpenseItemByContest.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__fwd_GetReportPayment.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__fwd_GetReportPayment.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__fwd_GetReportPaymentByContest.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__fwd_GetReportPaymentByContest.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__fwd_GetScanEndorsementImportSessionRawItemListResult.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__fwd_GetScanEndorsementImportSessionRawItemListResult.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__fwd_GetScanExpenseAdjustmentImportSessionRawItemListResult.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__fwd_GetScanExpenseAdjustmentImportSessionRawItemListResult.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__fwd_GetScanPaymentRequestImportResult.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__fwd_GetScanPaymentRequestImportResult.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__fwd_ImportContractRaw_Excute.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__fwd_ImportContractRaw_Excute.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__fwd_ImportFWDContract_Summary.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__fwd_ImportFWDContract_Summary.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__fwd_ProcessEndorsementImportSessionRawItemList.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__fwd_ProcessEndorsementImportSessionRawItemList.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__fwd_ProcessExpenseAdjustmentImportSessionRawItemList.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__fwd_ProcessExpenseAdjustmentImportSessionRawItemList.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__fwd_ProcessPaymentRequestImport.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__fwd_ProcessPaymentRequestImport.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__fwd_ScanEndorsementImportSessionRawItemList.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__fwd_ScanEndorsementImportSessionRawItemList.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__fwd_ScanExpenseAdjustmentImportSessionRawItemList.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__fwd_ScanExpenseAdjustmentImportSessionRawItemList.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__fwd_ScanImportContractRaw.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__fwd_ScanImportContractRaw.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__fwd_ScanPaymentRequestImport.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__fwd_ScanPaymentRequestImport.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__fwd_SearchListPolicy.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__fwd_SearchListPolicy.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__fwd_SyncAllPolicies.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__fwd_SyncAllPolicies.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__fwd_UpdateDynamicFieldFreezeValueByEndorsement.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__fwd_UpdateDynamicFieldFreezeValueByEndorsement.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__import_Import_ProspectAssigmentFromTMRData.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__import_Import_ProspectAssigmentFromTMRData.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__import_ImportContact_Campaign.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__import_ImportContact_Campaign.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__import_ImportContact_Execute.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__import_ImportContact_Execute.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__import_ImportContact_GetScanDataSummary.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__import_ImportContact_GetScanDataSummary.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__import_ImportContact_GetScanDataSummaryReport.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__import_ImportContact_GetScanDataSummaryReport.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__import_ImportContact_Log.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__import_ImportContact_Log.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__import_ImportContact_OverwriteInfo.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__import_ImportContact_OverwriteInfo.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__import_ImportContact_Retrieve.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__import_ImportContact_Retrieve.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__import_ImportContact_ScanData.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__import_ImportContact_ScanData.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__import_ImportContact_ScanData2.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__import_ImportContact_ScanData2.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__import_ImportContactAfterInsert.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__import_ImportContactAfterInsert.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__import_ImportContactFromExcel.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__import_ImportContactFromExcel.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__import_Report_DuplicationPerUser.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__import_Report_DuplicationPerUser.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__rpt_PreprocessData_BaoCaoNangSuatLaoDong02.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__rpt_PreprocessData_BaoCaoNangSuatLaoDong02.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__rpt_ReportPreprocessData.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__rpt_ReportPreprocessData.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_AssignProspectBackToLeadersDueTo15Days.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_AssignProspectBackToLeadersDueTo15Days.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_CallResultFunnelSumary.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_CallResultFunnelSumary.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_CallResultSumary.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_CallResultSumary.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_Contact_AddContactsToCampaign.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_Contact_AddContactsToCampaign.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_Contact_CreateProspectFromHotList.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_Contact_CreateProspectFromHotList.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_Contact_GetContactHistories.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_Contact_GetContactHistories.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_Contact_GetContactNotesHistories.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_Contact_GetContactNotesHistories.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_Contact_GetContractList.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_Contact_GetContractList.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_Contact_GetErrorsImportHotList.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_Contact_GetErrorsImportHotList.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_Contact_GetHotListMasterDuplication.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_Contact_GetHotListMasterDuplication.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_Contact_GetStatisticsImportHotList.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_Contact_GetStatisticsImportHotList.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_Contact_ScanHotListImportData.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_Contact_ScanHotListImportData.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_Contact_ScanHotListImportDataTemp.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_Contact_ScanHotListImportDataTemp.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_Contact_SearchContactList.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_Contact_SearchContactList.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_Contact_SearchContactsToAddToCampaign.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_Contact_SearchContactsToAddToCampaign.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_Contract_GetContractHistories.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_Contract_GetContractHistories.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_CreateAppointmentFromAppointmentRaw.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_CreateAppointmentFromAppointmentRaw.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_CreateContactCallFromAppointmentRaw.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_CreateContactCallFromAppointmentRaw.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_CreateContactFromAppointmentRaw.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_CreateContactFromAppointmentRaw.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_CreateContactRelationshipFromAppointmentRaw.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_CreateContactRelationshipFromAppointmentRaw.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_CreateLeadAssignmentFromAppointmentRaw.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_CreateLeadAssignmentFromAppointmentRaw.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_CreateLeadFromAppointmentRaw.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_CreateLeadFromAppointmentRaw.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_CreateProspectAssignmentFromAppointmentRaw.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_CreateProspectAssignmentFromAppointmentRaw.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_CreateProspectFromAppointmentRaw.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_CreateProspectFromAppointmentRaw.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_CreateSurveyFeedback.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_CreateSurveyFeedback.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_DataCallResultFunnelSumary.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_DataCallResultFunnelSumary.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_DataCallResultSumary.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_DataCallResultSumary.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_GetAgentCallResultReport.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_GetAgentCallResultReport.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_GetAgentCampaignDynamicFieldValueList.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_GetAgentCampaignDynamicFieldValueList.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_GetAppointmentDetailInfo.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_GetAppointmentDetailInfo.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_GetAppointmentListReport.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_GetAppointmentListReport.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_GetAppointmentListReport_ByAppId.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_GetAppointmentListReport_ByAppId.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_GetAppointmentListReport_XXX.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_GetAppointmentListReport_XXX.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_GetAppointmentSumary.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_GetAppointmentSumary.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_GetAppointmentTrackingFields.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_GetAppointmentTrackingFields.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_GetAppointmentTrackings.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_GetAppointmentTrackings.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_GetCallResultByTMRsReport.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_GetCallResultByTMRsReport.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_GetCallResultByTMRsReport2.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_GetCallResultByTMRsReport2.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_GetCallResultByTMRsReport2_BK20161011.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_GetCallResultByTMRsReport2_BK20161011.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_GetContactCallListReport.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_GetContactCallListReport.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_GetDistributedProspectSummary.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_GetDistributedProspectSummary.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_GetDynamicFormValueStatusReport.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_GetDynamicFormValueStatusReport.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_GetFieldSaleLeadList.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_GetFieldSaleLeadList.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_GetFieldSaleQuotaList.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_GetFieldSaleQuotaList.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_GetFieldSaleTeam.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_GetFieldSaleTeam.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_GetFieldSaleUsersList.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_GetFieldSaleUsersList.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_GetHotAgentListWithQuotaAndAssigned.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_GetHotAgentListWithQuotaAndAssigned.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_GetImportedHotListResults.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_GetImportedHotListResults.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_GetTeamAssignedSummryList.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_GetTeamAssignedSummryList.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_GetTeamLeadList.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_GetTeamLeadList.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_GetTempLockDynamicFormValueReport.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_GetTempLockDynamicFormValueReport.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_ImportAppointmentFromExcel.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_ImportAppointmentFromExcel.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_ImportAppointmentResult.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_ImportAppointmentResult.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_ImportAppointmentResult_GetResult.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_ImportAppointmentResult_GetResult.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_ImportAppointmentResult_GetSummaryErrorCodeReport.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_ImportAppointmentResult_GetSummaryErrorCodeReport.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_lead_GetAgentCampaignAppointments.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_lead_GetAgentCampaignAppointments.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_lead_GetAppointmentHistories.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_lead_GetAppointmentHistories.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_lead_GetAssignedAppointmentInfos.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_lead_GetAssignedAppointmentInfos.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_lead_GetContactAppointmentHistories.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_lead_GetContactAppointmentHistories.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_lead_GetCoupleFieldSales.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_lead_GetCoupleFieldSales.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_lead_GetLeadList.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_lead_GetLeadList.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_Lead_GetSummaryStatusByAgent.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_Lead_GetSummaryStatusByAgent.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_LoadTMRDataInfo.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_LoadTMRDataInfo.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_NewAppointmentRaw.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_NewAppointmentRaw.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_NotifyAppointmentList.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_NotifyAppointmentList.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_Prospect_AssignedHotProspects.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_Prospect_AssignedHotProspects.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_Prospect_AssignedHotProspectsTemp.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_Prospect_AssignedHotProspectsTemp.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_Prospect_ExecuteAgentDistribution.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_Prospect_ExecuteAgentDistribution.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_Prospect_ExecuteAgentReDistribution.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_Prospect_ExecuteAgentReDistribution.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_Prospect_ExecuteTeamDistribution.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_Prospect_ExecuteTeamDistribution.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_Prospect_GetAgentDistributionPlan.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_Prospect_GetAgentDistributionPlan.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_Prospect_GetTeamDistributionPlan.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_Prospect_GetTeamDistributionPlan.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_Prospect_MoveToOtherTeam.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_Prospect_MoveToOtherTeam.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_SalesSupportDistributeAppointments.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_SalesSupportDistributeAppointments.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_SaleSupport_SearchCampaignList.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_SaleSupport_SearchCampaignList.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_SearchAppointmentsToDistribute.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_SearchAppointmentsToDistribute.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_SearchAssignedCampaignList.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_SearchAssignedCampaignList.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_SearchCallResults.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_SearchCallResults.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_SearchContactBySaleSupportAndDistributeAgent_01.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_SearchContactBySaleSupportAndDistributeAgent_01.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_SearchContactBySaleSupportAndDistributeCustomer.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_SearchContactBySaleSupportAndDistributeCustomer.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_SearchContactForRegainBySaleSupport.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_SearchContactForRegainBySaleSupport.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_SearchDynamicFormValue.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_SearchDynamicFormValue.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_SearchFieldSaleAppointmentList.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_SearchFieldSaleAppointmentList.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_SearchTeamLeadCampaignList.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_SearchTeamLeadCampaignList.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_TMRMoveTeam.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_TMRMoveTeam.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_TMRSpeedAndRessultCareAppointment.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_TMRSpeedAndRessultCareAppointment.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_TMRSpeedAndRessultCareProtectedLead.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_TMRSpeedAndRessultCareProtectedLead.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_TMRSpeedAndRessultNewCalling.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_TMRSpeedAndRessultNewCalling.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_UpdateCallInfo.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_UpdateCallInfo.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_UpdateLeadUserDefinedFormatCode.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__telesale_UpdateLeadUserDefinedFormatCode.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__testUtil_CleanUp_CampaignCallReportData.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__testUtil_CleanUp_CampaignCallReportData.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__testUtil_CreateCampaignCallReport.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__testUtil_CreateCampaignCallReport.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__testUtil_CustomerImport.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__testUtil_CustomerImport.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__testUtil_InboundPage.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__testUtil_InboundPage.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__testUtil_InboundPage_TicketList.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000005_procedure__testUtil_InboundPage_TicketList.postgres.sql" />
	</ItemGroup>
	<ItemGroup>
		<EmbeddedResource Include="Schema\Migration_000004_table01view__dbo_vProspect_CurrentAssignmentStatus.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000004_table01view__dbo_vProspect_CurrentAssignmentStatus.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000004_table01view__dbo_vw_aspnet_Applications.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000004_table01view__dbo_vw_aspnet_Applications.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000004_table01view__dbo_vw_aspnet_MembershipUsers.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000004_table01view__dbo_vw_aspnet_MembershipUsers.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000004_table01view__dbo_vw_aspnet_Profiles.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000004_table01view__dbo_vw_aspnet_Profiles.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000004_table01view__dbo_vw_aspnet_Roles.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000004_table01view__dbo_vw_aspnet_Roles.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000004_table01view__dbo_vw_aspnet_Users.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000004_table01view__dbo_vw_aspnet_Users.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000004_table01view__dbo_vw_aspnet_UsersInRoles.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000004_table01view__dbo_vw_aspnet_UsersInRoles.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000004_table01view__dbo_vw_aspnet_WebPartState_Paths.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000004_table01view__dbo_vw_aspnet_WebPartState_Paths.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000004_table01view__dbo_vw_aspnet_WebPartState_Shared.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000004_table01view__dbo_vw_aspnet_WebPartState_Shared.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000004_table01view__dbo_vw_aspnet_WebPartState_User.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000004_table01view__dbo_vw_aspnet_WebPartState_User.postgres.sql" />
	</ItemGroup>
	<ItemGroup>
		<EmbeddedResource Include="Schema\Migration_000004_table02func__dbo_ConvertToGuid.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000004_table02func__dbo_ConvertToGuid.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000004_table02func__dbo_ConvertToInt.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000004_table02func__dbo_ConvertToInt.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000004_table02func__dbo_DiffMinutes.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000004_table02func__dbo_DiffMinutes.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000004_table02func__dbo_fnc_GetVnDateTimeString.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000004_table02func__dbo_fnc_GetVnDateTimeString.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000004_table02func__dbo_fncJSON_Escape.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000004_table02func__dbo_fncJSON_Escape.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000004_table02func__dbo_fncJSON_From_SQL.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000004_table02func__dbo_fncJSON_From_SQL.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000004_table02func__dbo_func_GetMaxValueOfDynamicListField.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000004_table02func__dbo_func_GetMaxValueOfDynamicListField.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000004_table02func__dbo_GetChannelClassification.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000004_table02func__dbo_GetChannelClassification.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000004_table02func__dbo_GetClosestManagementUnit.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000004_table02func__dbo_GetClosestManagementUnit.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000004_table02func__dbo_GetClosestSubManagementUnit1.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000004_table02func__dbo_GetClosestSubManagementUnit1.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000004_table02func__dbo_GetCustomerCode.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000004_table02func__dbo_GetCustomerCode.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000004_table02func__dbo_GetCustomerDedupByContactListString.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000004_table02func__dbo_GetCustomerDedupByContactListString.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000004_table02func__dbo_GetCustomerDedupJoinConditionString.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000004_table02func__dbo_GetCustomerDedupJoinConditionString.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000004_table02func__dbo_GetCustomerDedupUnionFromString.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000004_table02func__dbo_GetCustomerDedupUnionFromString.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000004_table02func__dbo_GetCustomerDedupUnionString.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000004_table02func__dbo_GetCustomerDedupUnionString.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000004_table02func__dbo_GetCustomerDedupWhereString.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000004_table02func__dbo_GetCustomerDedupWhereString.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000004_table02func__dbo_GetDynamicMultiFileNames.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000004_table02func__dbo_GetDynamicMultiFileNames.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000004_table02func__dbo_GetInternalDedupUnionString.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000004_table02func__dbo_GetInternalDedupUnionString.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000004_table02func__dbo_GetOrganizationShortFullPathName.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000004_table02func__dbo_GetOrganizationShortFullPathName.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000004_table02func__dbo_GetRequestTicketCode.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000004_table02func__dbo_GetRequestTicketCode.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000004_table02func__dbo_GetStatusDescription.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000004_table02func__dbo_GetStatusDescription.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000004_table02func__dbo_GetTimeStamp.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000004_table02func__dbo_GetTimeStamp.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000004_table02func__dbo_GuidEmpty.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000004_table02func__dbo_GuidEmpty.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000004_table02func__dbo_IsEqual.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000004_table02func__dbo_IsEqual.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000004_table02func__dbo_parseJSON.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000004_table02func__dbo_parseJSON.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000004_table02func__dbo_SplitFullPathName.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000004_table02func__dbo_SplitFullPathName.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000004_table02func__dbo_SplitString.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000004_table02func__dbo_SplitString.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000004_table02func__telesale_GetStatusDescription.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000004_table02func__telesale_GetStatusDescription.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000004_table02func__dbo_GetFullPathOfPart.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000004_table02func__dbo_GetFullPathOfPart.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000004_table02func__dbo_SearchPartDescendant.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000004_table02func__dbo_SearchPartDescendant.postgres.sql" />
	</ItemGroup>
	<ItemGroup>
		<EmbeddedResource Include="Schema\Migration_000004_table03fulltext___Customer.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000004_table03fulltext___Customer.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000004_table03fulltext___Customer_catalog.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000004_table03fulltext___Customer_catalog.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000004_table03fulltext___CustomerAlternativeAddress.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000004_table03fulltext___CustomerAlternativeAddress.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000004_table03fulltext___KnowledgeItem_catalog.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000004_table03fulltext___KnowledgeItem_catalog.postgres.sql" />
		<EmbeddedResource Include="Schema\Migration_000004_table03fulltext___Part.mssql.sql" />
		<EmbeddedResource Include="Schema\Migration_000004_table03fulltext___Part.postgres.sql" />
	</ItemGroup>
	<ItemGroup>
		<EmbeddedResource Include="Schema\Migration_000015_procedure__GetRequestTicketHistories.postgres.sql" />
	</ItemGroup>
</Project>