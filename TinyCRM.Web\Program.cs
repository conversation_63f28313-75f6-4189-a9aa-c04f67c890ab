using Autofac;
using Autofac.Extensions.DependencyInjection;
using Serilog;
using TinyCRM.AppServices.ImportRequestTicket.Tasks;
using TinyCRM.AppServices.ImportTask.Tasks;
using Webaby;
using Webaby.Web;
using Webaby.Web.Filters;
using Webaby.Web.ModelBinders;

Log.Logger = new LoggerConfiguration()
    .ReadFrom.Configuration(new ConfigurationBuilder()
        .AddJsonFile("appsettings.json")
        .AddJsonFile($"appsettings.{Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") ?? "Production"}.json", optional: true)
        .Build())
    .CreateLogger();

try
{
    Log.Information("Starting Poptech.Cep.NetCore Web Host");

    var builder = WebApplication.CreateBuilder(args);

    builder.Host.UseSerilog();

    // Add services to the container.
    builder.Services.AddControllersWithViews()
                    .AddMvcOptions(options =>
                    {
                        options.ModelMetadataDetailsProviders.Add(new WebabyAdditionalMetadataProvider());
                        options.ModelMetadataDetailsProviders.Add(new WebabyDisplayMetadataProvider(builder.Configuration));
                        options.Filters.Add<AuthorizationActionFilter>();
                        options.Filters.Add<WebabyExceptionFilter>();
                        options.AutoRegisterCustomModelBinders(); // Register custom model binders automatically
                    })
                    .AddJsonOptions(options =>
                    {
                        options.JsonSerializerOptions.PropertyNamingPolicy = null;
                    });

    var signalRBuilder = builder.Services.AddSignalR();
    signalRBuilder.AddJsonProtocol(options =>
    {
        options.PayloadSerializerOptions.PropertyNamingPolicy = null;
    });

    var rzPage = builder.Services.AddRazorPages();
#if DEBUG
    rzPage.AddRazorRuntimeCompilation();
#endif

    WebabyApplication.StartUp(builder);

    // Add Autofac as the service provider factory
    builder.Host.UseServiceProviderFactory(new AutofacServiceProviderFactory());

    // Register your services in the container
    builder.Host.ConfigureContainer<ContainerBuilder>(containerBuilder =>
    {
        WebabyApplication.DynamicRegisterServices(containerBuilder, builder.Services, builder.Configuration);
    });

    builder.Services.AddScoped<ImportRequestTicketListTask>();
    builder.Services.AddScoped<ImportTaskListTask>();

    var app = builder.Build();
    app.UseStaticFiles();

    app.UseSerilogRequestLogging();

    //WebabyApplication.AddDatabaseConfigurationSource(builder);

    using (var scope = app.Services.CreateScope())
    {
        var scopedServices = scope.ServiceProvider;
        WebabyApplication.ApplicationStarted(scopedServices, builder.Configuration);
    }

    WebabyApplication.AddDatabaseConfigurationSource(builder);

    // Configure the HTTP request pipeline.
    if (!app.Environment.IsDevelopment())
    {
        app.UseExceptionHandler("/Home/Error");
        // The default HSTS value is 30 days. You may want to change this for production scenarios, see https://aka.ms/aspnetcore-hsts.
        app.UseHsts();
    }

    //app.UseHttpsRedirection();
    app.UseRouting();

    app.MapControllerRoute(
            name: "default",
            pattern: "{controller=Home}/{action=Index}/{id?}");

    app.MapHub<EventHub>("/eventHub");

    app.Run();
}
catch (Exception ex)
{
    Log.Fatal(ex, "Host terminated unexpectedly");
}
finally
{
    Log.CloseAndFlush();
}