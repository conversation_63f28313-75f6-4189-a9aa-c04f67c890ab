﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Webaby;
using Webaby.Data;
using AutoMapper;
using Webaby.Localization;

namespace TinyCRM.Customer.Queries
{
    public class GetCustomerByRpContactQuery : QueryBase<CustomerListItem>
    {
        public string PhoneNumber { get; set; }
        public string Email { get; set; }
        public string FacebookId { get; set; }
    }

    internal class GetCustomerByRpContactQueryHandler : QueryHandlerBase<GetCustomerByRpContactQuery, CustomerListItem>
    {
        public GetCustomerByRpContactQueryHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task<QueryResult<CustomerListItem>> ExecuteAsync(GetCustomerByRpContactQuery query)
        {
            var cmd = EntitySet.CreateDbCommand();
            cmd.Parameters.Add(DbParameterHelper.AddNullableString(cmd, "@PhoneNumber", string.IsNullOrEmpty(query.PhoneNumber) ? string.Empty : query.PhoneNumber));
            cmd.Parameters.Add(DbParameterHelper.AddNullableString(cmd, "@Email", string.IsNullOrEmpty(query.Email) ? string.Empty : query.Email));
            cmd.Parameters.Add(DbParameterHelper.AddNullableString(cmd, "@FacebookId", string.IsNullOrEmpty(query.FacebookId) ? string.Empty : query.FacebookId));
            cmd.CommandText = "GetCustomerByRpContact";
            cmd.CommandType = CommandType.StoredProcedure;
            var mainQuery = await EntitySet.ExecuteReadCommandAsync<CustomerListItem>(cmd);
            return new QueryResult<CustomerListItem>(mainQuery);
        }
    }
}
