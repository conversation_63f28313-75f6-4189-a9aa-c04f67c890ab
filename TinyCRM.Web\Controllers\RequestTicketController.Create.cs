﻿using Microsoft.AspNetCore.Mvc;
using System.Text;
using TinyCRM.AppServices.RequestTicket.Dto;
using TinyCRM.Customer.Queries;
using TinyCRM.EntityLink;
using TinyCRM.EntityLink.Commands;
using TinyCRM.Enums;
using TinyCRM.Outbound.ProspectAssignment.Queries;
using TinyCRM.RequestTicket.Queries;
using TinyCRM.ServiceCategory.Queries;
using TinyCRM.ServiceType.Queries;
using TinyCRM.TicketHotButton.Commands;
using TinyCRM.TicketHotButton.Queries;
using TinyCRM.Web.Models.Customer;
using TinyCRM.Web.Models.RequestTicket;
using TinyCRM.Web.Util;
using TinyCRM.Workflow.Queries;
using Webaby;
using Webaby.BusinessSetting;
using Webaby.Core.DynamicForm;
using Webaby.Core.DueTime.Queries;
using Webaby.Core.DynamicForm.Queries;
using Webaby.Security;
using Webaby.Security.Authorize;
using Webaby.Web;

namespace TinyCRM.Web.Controllers
{
    public partial class RequestTicketController
    {
        [HttpPost]
        //[ValidateAntiForgeryToken]
        public async Task<IActionResult> Create(RequestTicketCreateEditModel model, IEnumerable<ProductRequestTicketModel> listModels)
        {
            RequestTicketCreateEditArguments ticketCreateEditArguments = Mapper.Map<RequestTicketCreateEditArguments>(model);
            List<InsertProductRequestTicketItem> insertProductRequestTickets = Mapper.Map<List<InsertProductRequestTicketItem>>(listModels);
            var createRequestTicketResult = await RequestTicketAppService.CreateRequestTicketAsync(ticketCreateEditArguments, insertProductRequestTickets, Request.Form.ToNameValueCollection(), Request.Form.Files);
            if (createRequestTicketResult.NeedToCreateTaskWarning)
            {
                TempData["NeedToCreateTaskWarning"] = true;
            }

            ExecutionResult result = Mapper.Map<ExecutionResult>(createRequestTicketResult);

            if (Request.Headers.ContainsKey("X-Response-Type") && Request.Headers["X-Response-Type"] == "Json")
            {
                return Json(result);
            }

            if (ModelState.IsValid)
            {
                if (result.ErrorMessage.IsNotNullOrEmpty())
                {
                    Error(result.ErrorMessage);
                }
                if (result.SuccessMessage.IsNotNullOrEmpty())
                {
                    Info(result.SuccessMessage);
                }
                if (result.WarningMessage != null && result.WarningMessage.Count > 0)
                {
                    foreach (var mess in result.WarningMessage)
                    {
                        Warn(mess);
                    }
                }

                // Link Entity
                if (result.IsSuccess)
                {
                    if (model.FromEntityLinkId.IsNotNullOrEmpty())
                    {
                        var toRequestTicketData = await QueryExecutor.ExecuteOneAsync(new GetRequestTicketByIdQuery(model.Id.Value));
                        RequestTicketData fromObjectData = null;
                        if (model.FromEntityType.IsEqualIgnoreCase("RequestTicket"))
                        {
                            fromObjectData = await QueryExecutor.ExecuteOneAsync(new GetRequestTicketByIdQuery(model.FromEntityLinkId.Value));
                        }
                        await CommandExecutor.ExecuteAsync(new CreateEditEntityLinkCommand
                        {
                            Id = Guid.NewGuid(),
                            FromEntityId = model.FromEntityLinkId.Value,
                            FromEntityType = model.FromEntityType,
                            FromObject = fromObjectData,
                            ToEntityId = model.Id.Value,
                            ToEntityType = "RequestTicket",
                            EntityLinkBusinessSpecificId = model.FromEntityLinkBusinessSpecificId,
                            LinkType = model.AddToLinkType,
                            ToObject = toRequestTicketData
                        });
                        if (model.FromEntityLinkBusinessSpecificId2.HasValue && fromObjectData != null) //model.FromEntityType.IsEqualIgnoreCase("RequestTicket")
                        {
                            var elbsId2 = EntitySet.Get<EntityLinkBusinessSpecificEntity>(model.FromEntityLinkBusinessSpecificId2.Value);
                            await CommandExecutor.ExecuteAsync(new CreateEditEntityLinkCommand
                            {
                                Id = Guid.NewGuid(),
                                FromEntityId = toRequestTicketData.Id,
                                FromEntityType = elbsId2.FromEntityType,
                                FromObject = toRequestTicketData,
                                ToEntityId = fromObjectData.Id,
                                ToEntityType = "RequestTicket",
                                EntityLinkBusinessSpecificId = elbsId2.Id,
                                LinkType = elbsId2.AddToLinkType,
                                ToObject = fromObjectData
                            });
                        }
                    }
                }

                if (result.ReturnUrl.IsNotNullOrEmpty())
                {
                    if (model.CreateAndFinish)
                    {
                        var executionResult = await RequestTicketAppService.FinishRequestTicketAsync(new FinishRequestTicketArguments
                        {
                            RequestTicketId = model.Id.Value,
                            BusinessResultId = null,
                            SourceChannel = null
                        });
                        if (executionResult.IsSuccess)
                        {
                            Info(executionResult.SuccessMessage);
                        }
                        else
                        {
                            Error(executionResult.ErrorMessage);
                        }
                    }
                    Redirect(result.ReturnUrl);
                }
            }
            return DefaultResult();
        }

        [HttpGet]
        public async Task<IActionResult> Create(Guid? customerId, Guid? prospectAssignmentId, int? channel, string ibPhoneNumber, string ibEmail, string ibOutgoing, string ibUCID, string ibEmcAiTopic, bool? cloneTicket, Guid? serviceTypeId)
        {
            if (ModelState.IsValid)
            {
                CustomerModel customerModel = new CustomerModel();

                RequestTicketCreateEditModel model = new RequestTicketCreateEditModel
                {
                    IsNew = true,
                    Id = Guid.NewGuid(),
                    ProcessDueTime = new DueTimeInfo(),
                    AcceptDueTime = new DueTimeInfo(),
                    OpenTicketDate = DateTime.Now,
                    ContextToken = Guid.NewGuid().ToBase46String(),
                    ibUCID = ibUCID,
                    IsNoneCustomerTicket = false
                };

                // Phiếu tạo từ Campaign
                if (prospectAssignmentId.IsNotNullOrEmpty())
                {
                    var prospectAssignment = await QueryExecutor.ExecuteOneAsync(new GetProspectAssignmentByIdQuery { ProspectAssignmentId = prospectAssignmentId.Value });
                    if (prospectAssignment != null)
                    {
                        model.ProspectAssignmentId = prospectAssignment.Id;
                        model.ProspectId = prospectAssignment.ProspectId;
                        model.CampaignId = prospectAssignment.CampaignId;

                        customerId = prospectAssignment.CustomerId;
                    }
                }

                if (customerId.HasValue)
                {
                    model.RpPhone = ibPhoneNumber;
                    model.RpEmail = ibEmail;
                    model.DelegatedTicket = ibPhoneNumber.IsNotNullOrEmpty() ? true : false;
                    var customerData = await QueryExecutor.ExecuteOneAsync(new GetCustomerByIdQuery { Id = customerId.Value });
                    customerModel = Mapper.Map<CustomerModel>(customerData);
                    if (!customerModel.IsBackendCustomer)
                    {
                        customerModel.CustomerConfigs = CustomerAppService.GetFieldConfiguration("CreateEditVL").ToList();
                    }
                    else
                    {
                        if (CustomerSearchConfiguration.CustomerSearchApi.IsNullOrEmpty())
                        {
                            customerModel.CustomerConfigs = CustomerAppService.GetFieldConfiguration("CreateEditVL").ToList();
                        }
                    }
                }
                else
                {
                    customerModel.CustomerConfigs = CustomerAppService.GetFieldConfiguration("CreateEditVL").ToList();
                    customerModel.Phone1 = ibOutgoing;
                }
                model.Customer = customerModel;
                model.OwnerId = UserIdentity.Id;
                if (channel.HasValue)
                {
                    model.SourceChannel = channel.Value;
                }
                else
                {
                    model.SourceChannel = 1;
                }

                if (customerId.IsNotNullOrEmpty())
                {
                    customerModel.AllowEdit = !customerModel.IsBackendCustomer;
                }
                else
                {
                    customerModel.AllowEdit = true;
                    customerModel.Type = CustomerType.B2C;
                }

                customerModel.PartialView = CustomerPartialPrefix;
                if (customerModel != null && !string.IsNullOrEmpty(ApiCoreCustomer) && customerModel.IsBackendCustomer && CustomerSearchConfiguration.CustomerSearchApi.IsNotNullOrEmpty())
                {
                    LoadApiCoreCustomer(model);
                }

                if (cloneTicket.HasValue && cloneTicket.Value)
                {
                    string copyRequestTicketCacheKey = CopyRequestTicketPrefix + UserIdentity.Id.ToString().ToUpper();
                    if (CacheProvider.IsSet(copyRequestTicketCacheKey))
                    {
                        RequestTicketCopyModel cloneModel = CacheProvider.Get(copyRequestTicketCacheKey) as RequestTicketCopyModel;
                        if (cloneModel != null)
                        {
                            Mapper.Map(cloneModel, model);
                        }
                    }
                }

                if (serviceTypeId.IsNullOrEmpty())
                {
                    if (ibEmcAiTopic.IsNotNullOrEmpty())
                    {
                        string topicDecoded = string.Empty;
                        try
                        {
                            byte[] base64EncodedBytes = Convert.FromBase64String(ibEmcAiTopic);
                            topicDecoded = Encoding.UTF8.GetString(base64EncodedBytes);
                            topicDecoded = topicDecoded.Trim().Trim('.');
                        }
                        catch { }

                        if (topicDecoded.IsNotNullOrEmpty())
                        {
                            var serviceTypeTopic = await QueryExecutor.ExecuteOneAsync(new SearchServiceTypeByTextQuery { SearchServiceName = topicDecoded });
                            if (serviceTypeTopic != null)
                            {
                                serviceTypeId = serviceTypeTopic.Id;
                            }
                        }
                    }
                }

                // Phiếu liên kết với chiến dịch
                if (serviceTypeId.IsNotNullOrEmpty())
                {
                    var serviceType = await QueryExecutor.ExecuteOneAsync(new GetServiceTypeByIdQuery(serviceTypeId.Value));
                    if (serviceType != null)
                    {
                        if (serviceType.WorkflowId.IsNotNullOrEmpty())
                        {
                            var workflow = await QueryExecutor.ExecuteOneAsync(new GetWorkflowByIdQuery { Id = serviceType.WorkflowId.Value });
                            if (workflow != null)
                            {
                                model.ObjectApproveType = workflow.ObjectApproveType;
                            }
                        }
                        model.ServiceTypeId = serviceTypeId.Value;
                        if (serviceType.Level1Id.IsNotNullOrEmpty())
                        {
                            var level1 = await QueryExecutor.ExecuteOneAsync(new GetServiceCategoryByIdQuery(serviceType.Level1Id.Value));
                            if (level1 != null)
                            {
                                model.Level1Id = level1.Id;
                                model.Level1Name = level1.Name;
                            }
                            if (serviceType.Level2Id.IsNotNullOrEmpty())
                            {
                                var level2 = await QueryExecutor.ExecuteOneAsync(new GetServiceCategoryByIdQuery(serviceType.Level2Id.Value));
                                if (level2 != null)
                                {
                                    model.Level2Id = level2.Id;
                                    model.Level2Name = level2.Name;
                                }
                            }
                            if (serviceType.Level3Id.IsNotNullOrEmpty())
                            {
                                var level3 = await QueryExecutor.ExecuteOneAsync(new GetServiceCategoryByIdQuery(serviceType.Level3Id.Value));
                                if (level3 != null)
                                {
                                    model.Level3Id = level3.Id;
                                    model.Level3Name = level3.Name;
                                }
                            }
                            if (serviceType.Level4Id.IsNotNullOrEmpty())
                            {
                                var level4 = await QueryExecutor.ExecuteOneAsync(new GetServiceCategoryByIdQuery(serviceType.Level4Id.Value));
                                if (level4 != null)
                                {
                                    model.Level4Id = level4.Id;
                                    model.Level4Name = level4.Name;
                                }
                            }
                        }
                    }
                }
                model.Customer = customerModel;

                return View("CreateEdit", model);
            }
            return DefaultResult();
        }

        [HttpGet]
        public async Task<IActionResult> CreateByHotServiceTypes()
        {
            var getOrg = new TicketHotButtonData();
            return View(getOrg);
        }

        [HttpGet]
        [AllowAuthenticated]
        public async Task<IActionResult> GetLayoutHotButtons(string TitleName, string ObjectName, Guid? Organization)
        {
            var hotButton = (await QueryExecutor.ExecuteManyAsync(new GetTicketHotButtonListQuery { Title = TitleName, ObjectName = ObjectName, Organization = Organization })).ToList();
            return View("Partials/GetLayoutHotButtons", hotButton);
        }

        [HttpPost]
        public async Task<IActionResult> SavedListHotButtons(TicketHotButtonListModel model)
        {
            await CommandExecutor.ExecuteAsync(new SavedListTicketHotButtonCommand
            {
                ListData = model.ListData
            });

            Info(T["Lưu thành công"]);
            return DefaultResult();
        }

        [HttpPost]
        [AllowAuthenticated]
        public async Task<IActionResult> CreateEditHotButtons(TicketHotButtonData model)
        {
            await CommandExecutor.ExecuteAsync(new CreateEditTicketHotButtonCommand
            {
                Id = model.Id,
                Title = model.Title,
                SubTitle = model.SubTitle,
                ShortDescription = model.ShortDescription,
                ServiceTypeId = model.ServiceTypeId,
                ButtonOrder = model.ButtonOrder,
                ClassName = model.ClassName,
                IconClass = model.IconClass,
                IsNoneCustomerTicket = model.IsNoneCustomerTicket,
                ObjectName = model.ObjectName,
                OrganizationId = model.OrganizationId
            });
            Info(T["Lưu thành công"]);
            return DefaultResult();
        }

        [HttpGet]
        [AllowAuthenticated]
        public async Task<IActionResult> CreateEditHotButtons(Guid? hotButtonId, string mode)
        {
            var isOpenCreateTicket = false;
            if (mode == "createticket")
            {
                isOpenCreateTicket = true;
            }

            var hotButton = new TicketHotButtonData();
            if (hotButtonId.HasValue)
            {
                hotButton = await QueryExecutor.ExecuteOneAsync(new GetTicketHotButtonByIdQuery { Id = hotButtonId.Value });
            }
            else
            {
                hotButton.Id = Guid.NewGuid();
                hotButton.ServiceTypeId = new Guid();
                hotButton.ButtonOrder = 1;
            }
            ViewBag.IsOpenCreateTicket = isOpenCreateTicket;
            return View("Partials/CreateEditHotButtons", hotButton);
        }

        [HttpPost]
        [AllowAuthenticated]
        public async Task<IActionResult> Copy(RequestTicketCopyModel model)
        {
            string copyRequestTicketCacheKey = CopyRequestTicketPrefix + UserIdentity.Id.ToString().ToUpper();
            if (CacheProvider.IsSet(copyRequestTicketCacheKey))
            {
                CacheProvider.Invalidate(copyRequestTicketCacheKey);
            }
            CacheProvider.Set(copyRequestTicketCacheKey, model, CopyRequestTicketExpireInMinutes);

            #region Copy Dynamic Form

            string copyRequestTicketDynamicFormCacheKey = CopyRequestTicketPrefix + "DynamicForm." + UserIdentity.Id.ToString().ToUpper();
            if (CacheProvider.IsSet(copyRequestTicketDynamicFormCacheKey))
            {
                CacheProvider.Invalidate(copyRequestTicketDynamicFormCacheKey);
            }

            if (model.ServiceTypeId.IsNotNullOrEmpty())
            {
                var serviceType = await QueryExecutor.ExecuteOneAsync(new GetServiceTypeByIdQuery(model.ServiceTypeId.Value));
                if (serviceType != null && serviceType.DynamicFormId.IsNotNullOrEmpty())
                {
                    List<DynamicFieldValueInfo> dynamicFieldValues = new List<DynamicFieldValueInfo>();
                    dynamicFieldValues = (await QueryExecutor
                        .ExecuteManyAsync(new GetDynamicFieldValueInfoByFormIdQuery
                        {
                            DynamicFormId = serviceType.DynamicFormId.Value,
                            Display = true
                        })).ToList();

                    if (dynamicFieldValues.Count > 0)
                    {
                        List<DynamicFieldValueInfo> newDynamicFieldValues = dynamicFieldValues.CopyDynamicFormValue(Request.Form.ToNameValueCollection(), Request.Form.Files, "", ServiceProvider).ToList();
                        CacheProvider.Set(copyRequestTicketDynamicFormCacheKey, newDynamicFieldValues, CopyRequestTicketExpireInMinutes);
                    }
                }
            }

            #endregion

            Info(T["Thông tin phiếu đã được sao chép."]);
            return DefaultResult();
        }

        [HttpPost]
        [AllowAuthenticated]
        public IActionResult Paste(RequestTicketCopyModel model)
        {
            bool hasCopyInfo = true;
            string copyRequestTicketCacheKey = CopyRequestTicketPrefix + UserIdentity.Id.ToString().ToUpper();
            if (CacheProvider.IsSet(copyRequestTicketCacheKey))
            {
                var copiedModel = CacheProvider.Get(copyRequestTicketCacheKey);
                if (copiedModel != null)
                {
                    if (model.IsNoneCustomerTicket)
                    {
                        Redirect(Url.Action("CreateNoneCustomerTicket", "RequestTicket", new { CloneTicket = true }));
                    }
                    else
                    {
                        if (model.Customer != null)
                        {
                            Redirect(Url.Action("Create", "RequestTicket", new { CustomerId = model.Customer.Id, CloneTicket = true }));
                        }
                    }
                }
                else
                {
                    hasCopyInfo = false;
                }
            }

            if (!hasCopyInfo)
            {
                Info("Không tìm thấy thông tin phiếu đã được sao chép.");
            }
            return DefaultResult();
        }

        [HttpGet]
        public async Task<IActionResult> CreateNoneCustomerTicket(Guid? serviceTypeId, Guid? partId, Guid? fromEntityId, Guid? elbsId, string fromEntityType, string nameclone, Guid? elbsId2 = null)
        {
            if (ModelState.IsValid)
            {
                if (nameclone.IsNotNullOrEmpty())
                {
                    var cloneConfigs = Configuration.GetValue<string>("requestticket.shellsearch.configs");
#if DEBUG
                    cloneConfigs = EntitySet.Get<BusinessSettingEntity>().FirstOrDefault(x => x.ImportKey == "requestticket.shellsearch.configs")?.Value;
#endif
                    if (cloneConfigs.IsNotNullOrEmpty())
                    {
                        var listConfigObject = Newtonsoft.Json.JsonConvert.DeserializeObject<List<dynamic>>(cloneConfigs);
                        var configObj = listConfigObject.Where(x => x.Name == nameclone && x.RouteConfig == "CreateTicket").FirstOrDefault();
                        if (configObj != null)
                        {
                            var referenceId = configObj.ReferenceId.Value;
                            serviceTypeId = Guid.Parse(referenceId);
                        }
                        else
                        {
                            return NotFound();
                        }
                    }
                }


                CustomerModel customerModel = new CustomerModel();
                customerModel.Type = CustomerType.B2C;
                customerModel.PartialView = CustomerPartialPrefix;

                RequestTicketCreateEditModel model = new RequestTicketCreateEditModel
                {
                    IsNew = true,
                    Id = Guid.NewGuid(),
                    Customer = customerModel,
                    OpenTicketDate = DateTime.Now,
                    ProcessDueTime = new DueTimeInfo(),
                    AcceptDueTime = new DueTimeInfo(),
                    ContextToken = Guid.NewGuid().ToBase46String(),
                    IsNoneCustomerTicket = true,
                    PartId = partId,
                    FromEntityLinkId = fromEntityId,
                    FromEntityType = fromEntityType,
                    FromEntityLinkBusinessSpecificId = elbsId,
                    FromEntityLinkBusinessSpecificId2 = elbsId2,
                };
                model.OwnerId = UserIdentity.Id;
                if (serviceTypeId.IsNotNullOrEmpty())
                {
                    var serviceType = await QueryExecutor.ExecuteOneAsync(new GetServiceTypeByIdQuery(serviceTypeId.Value));
                    if (serviceType != null)
                    {
                        if (serviceType.TicketMode == ServiceType.TicketCodeMode.InputCode)
                        {
                            model.HideTicketCode = false;
                        }
                        else
                        {
                            model.HideTicketCode = true;
                        }

                        if (serviceType.WorkflowId.IsNotNullOrEmpty())
                        {
                            var workflow = await QueryExecutor.ExecuteOneAsync(new GetWorkflowByIdQuery { Id = serviceType.WorkflowId.Value });
                            if (workflow != null)
                            {
                                model.ObjectApproveType = workflow.ObjectApproveType;
                            }
                        }
                        model.ServiceTypeId = serviceTypeId.Value;
                        if (serviceType.Level1Id.IsNotNullOrEmpty())
                        {
                            var level1 = await QueryExecutor.ExecuteOneAsync(new GetServiceCategoryByIdQuery(serviceType.Level1Id.Value));
                            if (level1 != null)
                            {
                                model.Level1Id = level1.Id;
                                model.Level1Name = level1.Name;
                            }
                            if (serviceType.Level2Id.IsNotNullOrEmpty())
                            {
                                var level2 = await QueryExecutor.ExecuteOneAsync(new GetServiceCategoryByIdQuery(serviceType.Level2Id.Value));
                                if (level2 != null)
                                {
                                    model.Level2Id = level2.Id;
                                    model.Level2Name = level2.Name;
                                }
                            }
                            if (serviceType.Level3Id.IsNotNullOrEmpty())
                            {
                                var level3 = await QueryExecutor.ExecuteOneAsync(new GetServiceCategoryByIdQuery(serviceType.Level3Id.Value));
                                if (level3 != null)
                                {
                                    model.Level3Id = level3.Id;
                                    model.Level3Name = level3.Name;
                                }
                            }
                            if (serviceType.Level4Id.IsNotNullOrEmpty())
                            {
                                var level4 = await QueryExecutor.ExecuteOneAsync(new GetServiceCategoryByIdQuery(serviceType.Level4Id.Value));
                                if (level4 != null)
                                {
                                    model.Level4Id = level4.Id;
                                    model.Level4Name = level4.Name;
                                }
                            }
                        }
                    }
                }
                return View("CreateEdit", model);
            }
            return DefaultResult();
        }
    }
}
