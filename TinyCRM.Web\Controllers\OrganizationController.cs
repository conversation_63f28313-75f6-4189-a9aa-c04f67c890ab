﻿using DocumentFormat.OpenXml.Office.Word;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.ModelBinding;
using Microsoft.EntityFrameworkCore;
using System.Data;
using TinyCRM.BusinessPermission;
using TinyCRM.Phase;
using TinyCRM.Phase.Queries;
using TinyCRM.TaskType;
using TinyCRM.Ticket.Queries;
using TinyCRM.UserAccount.Queries;
using TinyCRM.Web.Models.Organization;
using TinyCRM.Web.Models.Select;
using TinyCRM.Web.Util;
using Webaby;
using Webaby.Core.Organization;
using Webaby.Core.Organization.Commands;
using Webaby.Core.Organization.Queries;
using Webaby.Data;
using Webaby.Security;
using Webaby.Security.Authorize;
using Webaby.Web;
using System.Linq;

namespace TinyCRM.Web.Controllers
{
    public class OrganizationController : WebabyControllerBase
    {
        public IActionResult Index()
        {
            return View();
        }

        public OrganizationController(IServiceProvider serviceProvider) : base(serviceProvider)
        {
            EntitySet = serviceProvider.GetRequiredService<IEntitySet>();
            OrganizationUtility = serviceProvider.GetRequiredService<IOrganizationUtility>();
            Repository = serviceProvider.GetRequiredService<IRepository>();
        }

        public IEntitySet EntitySet;
        public IOrganizationUtility OrganizationUtility;
        public IRepository Repository;

        [HttpGet]
        [AllowAuthenticated]
        public async Task<IActionResult> GetOrganizationList()
        {
            var organizationList = await QueryExecutor.ExecuteManyAsync(new GetOrganizationListQuery { });
            return Json(organizationList);
        }

        [HttpGet]
        [AllowAuthenticated]
        public async Task<IActionResult> GetOrganizationJsTree()
        {
            var organizationList = await QueryExecutor.ExecuteManyAsync(new GetOrganizationListQuery { });
            var orgList = (from org in organizationList
                           select new
                           {
                               Id = org.Id,
                               Text = org.Name,
                               ParentId = org.ParentId,
                               Order = org.DisplaySortOrder
                           }).ToList();

            return Json(orgList);
        }

        [HttpGet]
        [NoCache]
        [AllowAuthenticated]
        public async Task<IActionResult> OpenCreateEdit(Guid? orgId, Guid? parentId)
        {
            var model = new CreateEditOrganizationModel();
            if (orgId.HasValue)
            {
                var organizationData = await QueryExecutor.ExecuteOneAsync(new GetOrganizationByIdQuery(orgId.Value));
                if (organizationData != null)
                {
                    model.Id = organizationData.Id;
                    model.Name = organizationData.Name;
                    model.Code = organizationData.Code;
                    model.ParentId = organizationData.ParentId;
                    model.OrganizationType = organizationData.OrganizationType;
                    model.HasOneStopDepartment = organizationData.HasOneStopDepartment;
                }
            }
            if (parentId.HasValue)
            {
                var parent = await QueryExecutor.ExecuteOneAsync(new GetOrganizationByIdQuery(parentId.Value));
                model.ParentName = parent.Name;
                model.Code = parent.Code + "-child";
            }
            return View("Partials/CreateEditV5", model);
        }

        [AllowAuthenticated]
        public IActionResult CreateEdit(Models.Organization.CreateEditOrganizationModel model)
        {
            if (ModelState.IsValid)
            {
                bool isNew = model.Id.IsNullOrEmpty();

                var result = CreateEditOrganization(model, ModelState);
                if (result.IsSuccess)
                {
                    return Json(new { Success = true, IsNew = isNew, Id = result.OrganizationId, Msg = (isNew ? T["Đã tạo mới đơn vị"] : T["Đã cập nhật đơn vị"]) });
                }
                else
                {
                    foreach (var error in result.Errors)
                    {
                        Error(error.ErrorMessage);
                    }
                }
            }

            return DefaultResult();
        }

        public CreateEditOrganizationResult CreateEditOrganization(CreateEditOrganizationModel model, ModelStateDictionary modelState)
        {
            CreateEditOrganizationResult result = new CreateEditOrganizationResult();
            result.Errors = new List<CreateEditOrganizationError>();

            Guid organizationId = Guid.NewGuid();
            if (model.Id.IsNotNullOrEmpty())
            {
                organizationId = model.Id.Value;
            }

            if (modelState.IsValid == false)
            {
                result.Errors.AddRange(modelState.GetErrors((k, m) => new CreateEditOrganizationError { ErrorCode = k, ErrorMessage = m }));
                result.IsSuccess = false;
                return result;
            }

            #region Check Duplicate CODE

            var sqlCommand = EntitySet.CreateDbCommand();
            sqlCommand.CommandType = System.Data.CommandType.Text;
            sqlCommand.Parameters.AddRange(new[]
            {
                DbParameterHelper.AddNullableGuid(sqlCommand, "@OrganizationId", model.Id),
                DbParameterHelper.AddNullableString(sqlCommand, "@Code", model.Code)
            });
            sqlCommand.CommandText = @"
                SELECT	Id
                FROM	dbo.Organization org WITH(NOLOCK)
                WHERE	org.Deleted = 0
		                AND org.Id <> @OrganizationId
		                AND org.Code = @Code";

            bool orgCodeExisted = EntitySet.ExecuteReadCommandAsync<Guid>(sqlCommand).Result.Any();
            if (orgCodeExisted)
            {
                result.Errors.Add(new CreateEditOrganizationError { ErrorCode = "CodeExisted", ErrorMessage = T["Mã đơn vị đã tồn tại"] });
                result.IsSuccess = false;
                return result;
            }

            #endregion

            #region Check Parent EXISTED

            if (model.ParentId.IsNotNullOrEmpty())
            {
                var parentOrg = QueryExecutor.ExecuteOneAsync(new GetOrganizationByIdQuery(model.ParentId.Value)).Result;
                if (parentOrg == null)
                {
                    result.Errors.Add(new CreateEditOrganizationError { ErrorCode = "ParentIdNotExisted", ErrorMessage = T["Id Đơn vị cha không tồn tại"] });
                    result.IsSuccess = false;
                    return result;
                }
            }

            #endregion

            try
            {
                CommandExecutor.ExecuteAsync(new CreateEditOrganizationCommand
                {
                    Id = organizationId,
                    Name = model.Name,
                    Code = model.Code,
                    OrganizationType = model.OrganizationType,
                    ParentId = model.ParentId,
                    HasOneStopDepartment = model.HasOneStopDepartment
                });

                result.IsSuccess = true;
                result.OrganizationId = organizationId;
                return result;
            }
            catch (Exception ex)
            {
                result.IsSuccess = false;
                result.Errors.Add(new CreateEditOrganizationError { ErrorCode = "Unknown", ErrorMessage = ex.Message });
                return result;
            }
        }

        [HttpPost]
        public async Task<IActionResult> UpdateOrganization(Guid organizationId, string organizationName, string organizationCode, List<Guid> parents)
        {
            if (parents != null)
            {
                parents.Reverse();
            }
            await OrganizationUtility.UpdateOrganizationAsync(organizationId, organizationName, organizationCode, parents);
            Info(T["Đã cập nhật thông tin phòng ban"]);
            return Json(new { UpdateId = organizationId });
        }

        [HttpPost]
        public async Task<IActionResult> CreateOrganization(Guid? parentId, string organizationName, string organizationCode, int type)
        {
            var newid = Guid.NewGuid();
            await OrganizationUtility.CreateOrganizationAsync(newid, parentId, organizationName, organizationCode, type);
            Info(T["Đã thêm phòng ban: {0}", organizationName]);
            return Json(new { CreatedId = newid });
        }

        [HttpPost]
        public IActionResult DeleteOrganization(Guid organizationId)
        {
            var delResult = DeleteOrganizationFunc(organizationId);
            if (delResult.IsSuccess)
            {
                Info("Đã xóa đơn vị phòng ban");
                return Json(new { Success = true });
            }
            else
            {
                foreach (var error in delResult.Errors)
                {
                    Error(error.ErrorMessage);
                }
            }

            return Json(new { Success = false });
        }

        public DeleteOrganizationResult DeleteOrganizationFunc(Guid organizationId)
        {
            DeleteOrganizationResult result = new DeleteOrganizationResult();

            if (OrganizationUtility.DeleteOrganizationAsync(organizationId).Result)
            {
                result.IsSuccess = true;
                return result;
            }

            result.IsSuccess = false;
            result.Errors = new List<DeleteOrganizationError>();
            result.Errors.Add(new DeleteOrganizationError { ErrorCode = "ExistActiveUser", ErrorMessage = T["Không thể xóa phòng ban đang có nhân viên hoạt động"] });

            return result;
        }

        [HttpGet]
        [NoCache]
        [AllowAuthenticated]
        public async Task<IActionResult> GetTaskTypeList(Guid organizationId)
        {
            var tasktype = EntitySet.Get<TaskTypeEntity>()
                .Join(EntitySet.Get<OrganizationTaskTypeEntity>(), o => o.Id, i => i.TaskTypeId, (o, i) => new { type = o, OrgId = i.OrganizationId })
                .Where(x => x.OrgId == organizationId)
                .Select(x => x.type);
            var model = await tasktype.ToListAsync();
            return View("Partials/TaskTypeList", model);
        }

        [HttpPost]
        public async Task<IActionResult> RemoveTaskRelate(Guid organizationId, Guid taskType)
        {
            await Repository.DeleteAsync(EntitySet.Get<OrganizationTaskTypeEntity>().Where(x => x.OrganizationId == organizationId && x.TaskTypeId == taskType));
            Info("Đã xóa công việc liên quan của phòng ban");
            return Json(new { Success = true });
        }

        [HttpGet]
        [NoCache]
        [AllowAuthenticated]
        public async Task<IActionResult> LoadAddTaskRelate(Guid organizationId)
        {
            var tasktype = EntitySet.Get<TaskTypeEntity>()
                .Join(EntitySet.Get<OrganizationTaskTypeEntity>(), o => o.Id, i => i.TaskTypeId, (o, i) => new { type = o, OrgId = i.OrganizationId })
                .Where(x => x.OrgId == organizationId)
                .Select(x => x.type.Id);
            var result = await EntitySet.Get<TaskTypeEntity>().Where(x => !tasktype.Contains(x.Id)).ToListAsync();
            return Json(result);
        }

        [HttpPost]
        public async Task<IActionResult> AddTaskRelate(Guid? organizationId, Guid taskType)
        {
            if (!organizationId.HasValue)
            {
                return Json(new { Success = false, Msg = T["Vui lòng chọn phòng ban"], Type = "error" });
            }
            if (EntitySet.Get<OrganizationTaskTypeEntity>().Any(x => x.OrganizationId == organizationId && x.TaskTypeId == taskType))
            {
                return Json(new { Success = false, Msg = T["Công việc và phòng ban đã được liên kết, vui lòng chọn phòng ban khác"], Type = "error" });
            }
            await Repository.SaveAsync(new OrganizationTaskTypeEntity
            {
                OrganizationId = organizationId.Value,
                TaskTypeId = taskType                
            });
            return Json(new { Success = true, Msg = T["Đã liên kết công việc vào phòng ban liên quan"], Type = "info" });
        }

        [HttpGet]
        [NoCache]
        [AllowAuthenticated]
        public async Task<IActionResult> GetOrganizationTreeForTicket(Guid? taskTypeFilter, Guid ticketId, bool productFilter, Guid? serviceTypeFilterId)
        {
            var org = await QueryExecutor.ExecuteAsync(new GetOrganizationForTicketQuery
            {
                TicketId = ticketId,
                TaskTypeFilter = taskTypeFilter,
                ProductFilter = productFilter,
                ServiceTypeFilterId = serviceTypeFilterId
            });
            return Json(org.Many);
        }

        [AllowAuthenticated]
        public async Task<IActionResult> GetAgentMemberList(Models.AgentSearchModel model)
        {
            if (model.OrganizationId != null)
            {
                var result = await QueryExecutor.ExecuteAsync(new GetAgentMemberQuery()
                {
                    FullName = model.FullName,
                    IncludeInactive = false,
                    OrganizationId = model.OrganizationId,
                    UserName = model.UserName,
                    RoleId = TinyCRM.Outbound.Role.StaticRole.AgentId
                });
                if (result.Total == 0) Info(T["Không tìm thấy điện thoại viên phù hợp"]);
                return View("Partials/AgentMemberList", new Models.AgentListModel()
                {
                    Total = result.Total == 0 ? 0 : result.Total,
                    Items = result.Many,
                    PageIndex = model.PageIndex,
                    PageSize = model.PageSize,
                    SelectionList = model.Selection ?? new List<Guid>(),
                });
            }

            Info(T["Không tìm thấy điện thoại viên phù hợp"]);
            return DefaultResult();
        }

        [AllowAuthenticated]
        public async Task<IActionResult> GetAgentMember(Guid? orgId, Guid? agentId, bool includeInactive = false)
        {
            var memberQuery = EntitySet.Get<ApplicationUser>();
            if (!includeInactive)
            {
                memberQuery = memberQuery.Where(c => c.IsApproved);
            }
            if (agentId.HasValue)
            {
                memberQuery = memberQuery.Where(c => c.Id == agentId);
            }

            var agentRoleId = TinyCRM.Outbound.Role.StaticRole.AgentId;            

            var result = await (from r in EntitySet.Get<ApplicationRole>()
                          join uir in EntitySet.Get<ApplicationUserRole>() on r.Id equals uir.RoleId
                          join mem in memberQuery on uir.Id equals mem.Id
                          join org in EntitySet.Get<OrganizationEntity>() on mem.OrganizationId equals org.Id                          
                          where r.Id == agentRoleId && (!orgId.HasValue || mem.OrganizationId == orgId)
                          orderby mem.FullName
                          select new
                          {
                              Id = mem.Id,
                              //AgentCode = mem.AgentCode,
                              //CompetenceLevel = mem.CompetenceLevel,
                              //Extension = mem.Extension,
                              FullName = mem.UserName,                              
                              Phone = mem.PhoneNumber,
                              OrganizationName = org.Name,
                              OrganizationId = org.Id,
                              Deleted = !mem.IsApproved
                          }).ToListAsync();
            return Json(result);
        }

        [AllowAuthenticated]
        public async Task<IActionResult> GetTeamList(Models.TeamSearchModel model)
        {
            var result = await QueryExecutor.ExecuteAsync(new GetOrganizationListQuery()
            {
                Name = model.TeamName,
                OrganizationType = "TMR Team"
            });
            if (result.Total == 0) Info(T["Không tìm thấy nhóm phù hợp"]);
            return View("Partials/TeamList", new Models.TeamListModel()
            {
                Total = result.Total == 0 ? 0 : result.Total,
                Items = result.Many,
                PageIndex = model.PageIndex,
                PageSize = model.PageSize,
                SelectionList = model.Selection ?? new List<Guid>()
            });
        }

        [HttpGet]
        [AllowAuthenticated]
        public async Task<IActionResult> GetUserFromOrganizationToAssignTask(Guid id, Guid taskId, bool? hasNoOneUser)
        {
            if (ModelState.IsValid)
            {
                hasNoOneUser = hasNoOneUser.HasValue ? hasNoOneUser.Value : false;

                var userProfileList = (await QueryExecutor.ExecuteManyAsync(new GetUserFromOrganizationToAssignTaskQuery { OrganizationId = id, TaskId = taskId, ExecuteUserId = UserIdentity.Id })).ToList();
                var users = userProfileList.Select(x => new { Value = x.Id, Text = x.FullName }).ToList();
                if (hasNoOneUser.HasValue && hasNoOneUser == true)
                {
                    users.Insert(0, new { Value = Guid.Empty, Text = T["Không có"] });
                }
                return Json(users);
            }
            return Json(new { });
        }

        [HttpGet]
        [AllowAuthenticated]
        public async Task<IActionResult> GetUserFromOrganizationWithPagination(string search, int page, int? pageSize, bool? hasNoOneUser, bool? isNotActive, bool? isNotBasedOnOrg, Guid? orgID, string id)
        {
            if (ModelState.IsValid)
            {
                if (!pageSize.HasValue)
                {
                    pageSize = 25;
                }
                var organizationId = Guid.Empty;
                isNotBasedOnOrg = isNotBasedOnOrg.HasValue ? isNotBasedOnOrg.Value : false;
                if (orgID.HasValue && isNotBasedOnOrg == false)
                {
                    organizationId = orgID.Value;
                }
                hasNoOneUser = hasNoOneUser.HasValue ? hasNoOneUser.Value : false;
                isNotActive = isNotActive.HasValue ? isNotActive.Value : false;
                var result = (await QueryExecutor.ExecuteManyAsync(new SearchUsersQuery
                {
                    OrganizationId = id.IsNotNullOrEmpty() ? new Guid?() : organizationId,
                    SearchInvalid = id.IsNotNullOrEmpty() ? true : isNotActive.Value,
                    SearchName = id.IsNotNullOrEmpty() ? null : search,
                    Pagination = id.IsNotNullOrEmpty() ? new Pagination(0, 1) : new Pagination(page - 1, pageSize.Value),
                    //UserId = id.IsNotNullOrEmpty() ? Guid.Parse(id) : new Guid?()
                })).ToList();
                if (result.Any())
                {
                    var getData = result.First().Items;
                    var users = getData.Select(x => new SelectItem
                    {
                        id = x.Id.ToString(),
                        text = x.FullName,
                    }).ToList();

                    if (hasNoOneUser.HasValue && hasNoOneUser == true && page == 1)
                    {
                        users.Insert(0, new SelectItem { id = Guid.Empty.ToString(), text = T["Không có"] });
                    }

                    var totalCount = result.First().TotalCount;

                    var returnObj = new SelectJsonPagingModel
                    {
                        results = users,
                        pagination = new SelectPagination
                        {
                            more = totalCount > users.Count
                        }
                    };                    
                    return Json(returnObj);
                }
            }
            return Json(new { });
        }

        [HttpGet]
        [AllowAuthenticated]
        public async Task<IActionResult> GetFactory()
        {
            if (ModelState.IsValid)
            {
                var result = await QueryExecutor.ExecuteAsync(new GetFactoryQuery());
                return Json(result.Many.Select(x => new { Value = x.Id, Text = string.IsNullOrEmpty(x.Code) ? x.Name : string.Format("{0} - {1}", x.Code, x.Name) }));
            }
            return Json(new { });
        }

        [HttpGet]
        [AllowAuthenticated]
        public async Task<IActionResult> GetFactoryByCode(string q)
        {
            var result = await QueryExecutor.ExecuteAsync(new GetFactoryByCodeQuery(q));
            if (result != null && result.Total > 0)
            {
                var factory = result.One;
                return Json(new { Value = factory.Id, Text = factory.Name });
            }
            return DefaultResult();
        }

        [HttpGet]
        [AllowAuthenticated]
        public async Task<IActionResult> GetCustomerCare()
        {
            var result = await QueryExecutor.ExecuteManyAsync(new GetOrganizationByParentIdQuery(new Guid("E38563A8-2C95-42DF-9081-05268E9699D4")));
            return Json(result.Select(x => new { Value = x.Id, Text = string.IsNullOrEmpty(x.Code) ? x.Name : string.Format("{0} - {1}", x.Code, x.Name) }));
        }

        [HttpGet]
        [AllowAuthenticated]
        public async Task<IActionResult> GetWareHouse()
        {
            var result = await QueryExecutor.ExecuteManyAsync(new GetOrganizationByParentIdQuery(new Guid("74BD9BE2-C25F-4E69-8834-DF90E73093FA")));
            return Json(result.Select(x => new { Value = x.Id, Text = string.IsNullOrEmpty(x.Code) ? x.Name : string.Format("{0} - {1}", x.Code, x.Name) }));
        }

        [AllowAuthenticated]
        public async Task<IActionResult> OrganizationFuncConfigs(Guid organizationId)
        {
            OrganizationFuncConfigsModel model = new OrganizationFuncConfigsModel();

            model.UserFuncBusinessPermissionConfigsInOrgItems = (await QueryExecutor.ExecuteManyAsync(new GetUserFuncBusinessPermissionConfigsInOrgQuery
            {
                OrganizationId = organizationId,
                BusinessPermissionIds = new List<Guid>
                {
                    BusinessPermissionContants.UserTaskDelegatedInOrg,
                    BusinessPermissionContants.UserTicketDelegatedInOrg,
                    BusinessPermissionContants.TicketCreatedNotifiedFromApi
                }
            })).ToList();

            model.TaskServiceTypeDefaultOrganizations = (await QueryExecutor.ExecuteManyAsync(new GetTaskServiceTypeDefaultOrganizationQuery { OrganizationId = organizationId })).ToList();
            model.TicketServiceTypeDefaultOrganizations = (await QueryExecutor.ExecuteManyAsync(new GetTicketServiceTypeDefaultOrganizationQuery { OrganizationId = organizationId })).ToList();

            return View("Partials/OrganizationFuncConfigs", model);
        }

        #region old

        [AllowAuthenticated]
        public async Task<IActionResult> GetOrganizationTreeItems(Guid? parentId)
        {
            var result = await QueryExecutor.ExecuteAsync(new GetOrganizationQuery() { });
            return Json(result.Many);
        }

        [HttpPost]
        [AllowAuthenticated]
        public async Task<IActionResult> GetOrganizationNamePath(Guid OrganizationId, bool? AuthorizeFilter)
        {
            string organizationNamePath = string.Empty;

            Guid? organizationId = OrganizationId;
            while (organizationId.HasValue && organizationId.Value != Guid.Empty)
            {
                var organization = await QueryExecutor.ExecuteOneAsync(new GetOrganizationByIdQuery(organizationId.Value));
                if (organization == null)
                {
                    break;
                }

                if (string.IsNullOrEmpty(organizationNamePath))
                {
                    organizationNamePath = organization.Name;
                }
                else
                {
                    organizationNamePath = organizationNamePath + "/" + organization.Name;
                }

                if (AuthorizeFilter.HasValue && AuthorizeFilter.Value)
                {
                    var usrProfile = await UserService.GetCurrentUserAsync();
                    if (usrProfile.OrganizationId == organizationId)
                    {
                        break;
                    }
                }

                organizationId = organization.ParentId;
            }

            return Json(new { OrganizationNamePath = organizationNamePath });
        }

        [HttpPost]
        [AllowAuthenticated]
        public IActionResult GetSelectOrganizationPopupHtml()
        {
            return View("Partials/SelectOrganizationPopup", null);
        }
        #endregion
    }
}
