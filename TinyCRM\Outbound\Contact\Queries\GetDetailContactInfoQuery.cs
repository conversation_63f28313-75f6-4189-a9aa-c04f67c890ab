﻿using System;
using System.Data;
using System.Threading.Tasks;
using Webaby;
using Webaby.Data;

namespace TinyCRM.Outbound.Contact.Queries
{
    public class GetDetailContactInfoQuery : QueryBase<GetDetailContactInfoQuery.Result>
    {
        public class Result
        {
            public Guid CurrentProspectAssignmentId { get; set; }
            public string CampaignName { get; set; }
            public Guid? AssignedTeamId { get; set; }
            public string Team { get; set; }
            public Guid? AssignedAgentId { get; set; }
            public string TMR { get; set; }
            public DateTime? AssignedAgentDate { get; set; }
            public DateTime? LastCallDate { get; set; }
            public string ViCallResult { get; set; }
            public string EnCallResult { get; set; }
        }
        public Guid ContactId { get; set; }
    }
    internal class SearchDetailContactInfoQueryHandler : QueryHandlerBase<GetDetailContactInfoQuery, GetDetailContactInfoQuery.Result>
    {
        public SearchDetailContactInfoQueryHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task<QueryResult<GetDetailContactInfoQuery.Result>> ExecuteAsync(GetDetailContactInfoQuery query)
        {
            var cmd = EntitySet.CreateDbCommand();
            cmd.CommandText = "GetDetailContactInfo";
            cmd.CommandType = CommandType.StoredProcedure;

            cmd.Parameters.Add(DbParameterHelper.AddNullableGuid(cmd, "@ContactId", query.ContactId));

            var main = await EntitySet.ExecuteReadCommandAsync<GetDetailContactInfoQuery.Result>(cmd);

            return new QueryResult<GetDetailContactInfoQuery.Result>(main);
        }
    }
}
