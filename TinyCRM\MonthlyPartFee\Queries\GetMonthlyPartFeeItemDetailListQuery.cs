﻿using System;
using System.Data;
using System.Data.Common;
using System.Linq;
using System.Threading.Tasks;
using Webaby;
using Webaby.Data;

namespace TinyCRM.MonthlyPartFee.Queries
{
    public class GetMonthlyPartFeeItemDetailListQuery : QueryBase<MonthlyPartFeeExportItemInfoDetail>
    {
        public Guid MonthlyPartFeeId { get; set; }
    }

    public class GetMonthlyPartFeeItemDetailListQueryHandler : QueryHandlerBase<GetMonthlyPartFeeItemDetailListQuery, MonthlyPartFeeExportItemInfoDetail>
    {
        public GetMonthlyPartFeeItemDetailListQueryHandler(IServiceProvider serviceProvider) : base(serviceProvider)
        {
        }

        public override async Task<QueryResult<MonthlyPartFeeExportItemInfoDetail>> ExecuteAsync(GetMonthlyPartFeeItemDetailListQuery query)
        {
            var cmd = EntitySet.CreateDbCommand(CommandType.StoredProcedure, "dbo.GetMonthlyPartFeeItemDetailList");
            cmd.Parameters.Add(DbParameterHelper.AddNullableGuid(cmd, "@MonthlyPartFeeId", query.MonthlyPartFeeId));

            var result = (await EntitySet.ExecuteReadCommandAsync<MonthlyPartFeeExportItemInfoDetail>(cmd)).ToList();
            return QueryResult.Create(result);
        }
    }
}