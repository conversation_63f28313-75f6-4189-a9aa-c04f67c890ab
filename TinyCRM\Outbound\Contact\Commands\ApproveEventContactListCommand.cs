﻿using AutoMapper;
using Microsoft.EntityFrameworkCore;
using System;
using System.Data;
using System.Threading.Tasks;
using Webaby;
using Webaby.Data;
using Webaby.Localization;

namespace TinyCRM.Outbound.Contact.Commands
{
    public class ApproveEventContactListCommand : CommandBase
    {
        public DateTime? FromDate
        {
            get;
            set;
        }

        public DateTime? ToDate
        {
            get;
            set;
        }

        public bool? Inactive
        {
            get;
            set;
        }
    }

    internal class ApproveEventContactListCommandHandler : CommandHandlerBase<ApproveEventContactListCommand>
    {
        public ApproveEventContactListCommandHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task ExecuteAsync(ApproveEventContactListCommand command)
        {
            object inactive = DBNull.Value;
            if (command.Inactive.HasValue)
            {
                inactive = command.Inactive.Value;
            }

            var cmd = EntitySet.CreateDbCommand();
            cmd.CommandTimeout = 3600;
            cmd.CommandType = CommandType.StoredProcedure;
            cmd.CommandText = "cia.ApproveEventContactList";
            cmd.Parameters.Add(DbParameterHelper.NewNullableDateTimeParameter(cmd, "@FromDate", command.FromDate));
            cmd.Parameters.Add(DbParameterHelper.NewNullableDateTimeParameter(cmd, "@ToDate", command.ToDate));
            var inactiveParam = cmd.CreateParameter();
            inactiveParam.ParameterName = "@Inactive";
            inactiveParam.Value = inactive;
            cmd.Parameters.Add(inactiveParam);

            await EntitySet.ExecuteNonQueryAsync(cmd);
        }
    }
}