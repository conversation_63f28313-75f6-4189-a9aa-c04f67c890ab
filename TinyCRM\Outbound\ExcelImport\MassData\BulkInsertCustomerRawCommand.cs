﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Threading.Tasks;
using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Webaby;
using Webaby.Data;
using Webaby.Localization;

namespace TinyCRM.Outbound.ExcelImport.MassData
{
    public class BulkInsertCustomerRawCommand : CommandBase
    {
        public Guid ImportSessionId { get; set; }

        public DataTable RawData { get; set; }

        public List<string[]> Mappings { get; set; }
    }
    internal class BulkInsertCustomerRawCommandHandler : CommandHandlerBase<BulkInsertCustomerRawCommand>
    {
        public BulkInsertCustomerRawCommandHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task ExecuteAsync(BulkInsertCustomerRawCommand command)
        {
            command.RawData.Columns.Add(new DataColumn("ImportSessionId", typeof(Guid)));
            command.RawData.Columns.Add(new DataColumn("Id", typeof(Guid)));

            foreach (DataRow row in command.RawData.Rows)
            {
                row["ImportSessionId"] = command.ImportSessionId;
                row["Id"] = Guid.NewGuid();
            }

            #region Phần dành cho AdditionalJsonData

            var dbImportCustomerRawColumnList = await GetImportCustomerRawColumnNamesAsync();
            var importTableColumnList = command.RawData.Columns.Select(c => c.ColumnName).ToList();

            List<string> additionalColumnList = new List<string>();
            foreach (var imCol in importTableColumnList)
            {
                if (!imCol.ToLower().StartsWith("digitalcontact_") && !dbImportCustomerRawColumnList.Exists(col => col.IsEqualIgnoreCase(imCol)))
                {
                    additionalColumnList.Add(imCol);
                }
            }

            if (additionalColumnList.Count > 0)
            {
                command.RawData.Columns.Add("AdditionalJsonData", typeof(string));
                command.Mappings.Add(new[] { "AdditionalJsonData", "AdditionalJsonData" });

                foreach (DataRow rowItem in command.RawData.Rows)
                {
                    List<Dictionary<string, string>> rowDicList = new List<Dictionary<string, string>>();

                    Dictionary<string, string> rowDic = new Dictionary<string, string>();
                    foreach (var col in additionalColumnList)
                    {
                        rowDic.Add(col, rowItem[col].ToString());
                    }

                    rowDicList.Add(rowDic);

                    rowItem["AdditionalJsonData"] = JsonConvert.SerializeObject(rowDicList);
                }
            }

            #endregion
            if (!command.Mappings.Any(x => x[0] == "IsDupInternal"))
            {
                command.Mappings.Add(new[] { "IsDupInternal", "IsDupInternal" });
                command.RawData.Columns.Add("IsDupInternal", typeof(bool));
                foreach (DataRow r in command.RawData.Rows)
                {
                    r["IsDupInternal"] = false;
                }
            }
            if (!command.Mappings.Any(x => x[0] == "WarningCode"))
            {
                command.Mappings.Add(new[] { "WarningCode", "WarningCode" });
                command.RawData.Columns.Add("WarningCode", typeof(int));
                foreach (DataRow r in command.RawData.Rows)
                {
                    r["WarningCode"] = 0;
                }
            }
            Repository.BulkInsertAll(command.RawData, "dbo.ImportCustomerRaw", command.Mappings);
        }

        private async Task<List<string>> GetImportCustomerRawColumnNamesAsync()
        {
            var cmd = EntitySet.CreateDbCommand();
            cmd.CommandText = "SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'ImportCustomerRaw'";
            cmd.CommandType = CommandType.Text;

            var columnNameList = (await EntitySet.ExecuteReadCommandAsync<string>(cmd)).ToList();
            return columnNameList;
        }
    }
}