﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;
using Webaby;
using Webaby.Data;
using AutoMapper;
using Webaby.Localization;
using Microsoft.Data.SqlClient;

namespace TinyCRM.EntityLink.Queries
{
    public class GetEntityLinkBusinessSpecificListQuery : QueryBase<EntityLinkBusinessSpecificData>
    {
        public string EntityType { get; set; }

        public object ObjectData { get; set; }
    }

    internal class GetEntityLinkBusinessSpecificListQueryHandler : QueryHandlerBase<GetEntityLinkBusinessSpecificListQuery, EntityLinkBusinessSpecificData>
    {
        public GetEntityLinkBusinessSpecificListQueryHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task<QueryResult<EntityLinkBusinessSpecificData>> ExecuteAsync(GetEntityLinkBusinessSpecificListQuery query)
        {
            DataTable stringstringParams = new DataTable("StringString");
            stringstringParams.Columns.Add("Value1", typeof(string));
            stringstringParams.Columns.Add("Value2", typeof(string));
            if (query.ObjectData != null)
            {
                Type objectDataType = query.ObjectData.GetType();
                PropertyInfo[] objectDataProperties = objectDataType.GetProperties();
                foreach (var objectDataProperty in objectDataProperties)
                {
                    string value = string.Empty;
                    object propertyValue = objectDataProperty.GetValue(query.ObjectData);
                    if (propertyValue != null)
                    {
                        value = propertyValue.ToString();
                    }
                    stringstringParams.Rows.Add(objectDataProperty.Name, value);
                }
            }

            var sqlCommand = EntitySet.CreateDbCommand();
            sqlCommand.CommandText = "dbo.GetEntityLinkBusinessSpecificList";
            sqlCommand.CommandType = CommandType.StoredProcedure;
            sqlCommand.Parameters.Add(DbParameterHelper.AddNullableString(sqlCommand, "@EntityType", query.EntityType));
            var param = new SqlParameter();
            param.ParameterName = "@PropertyNameValue";
            param.Value = stringstringParams;
            param.SqlDbType = SqlDbType.Structured;
            sqlCommand.Parameters.Add(param);

            var mainQuery = await EntitySet.ExecuteReadCommandAsync<EntityLinkBusinessSpecificData>(sqlCommand);
            return new QueryResult<EntityLinkBusinessSpecificData>(mainQuery);
        }
    }
}