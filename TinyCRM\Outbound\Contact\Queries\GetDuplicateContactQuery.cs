﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TinyCRM.Enums;
using TinyCRM.Outbound.ProspectAssignment;
using Webaby;
using Webaby.Data;

namespace TinyCRM.Outbound.Contact.Queries
{
    public class GetDuplicateContactQuery : QueryBase<GetDuplicateContactQuery.Result>
    {
        public Guid ContactId { get; set; }
        public string PhoneNumber { get; set; }
        public class Result
        {
            public Guid Id { get; set; }
        }
    }
    internal class GetDuplicateContactQueryHandler : QueryHandlerBase<GetDuplicateContactQuery, GetDuplicateContactQuery.Result>
    {
        public GetDuplicateContactQueryHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task<QueryResult<GetDuplicateContactQuery.Result>> ExecuteAsync(GetDuplicateContactQuery query)
        {
            var cmd = EntitySet.CreateDbCommand();
            cmd.CommandText = @"SELECT
                                    c.Id
                                    FROM dbo.Contact c 
                                    WHERE (c.Phone = @PhoneNumber OR c.Phone2 = @PhoneNumber OR c.Phone3 = @PhoneNumber)";
            cmd.CommandType = CommandType.Text;

            cmd.Parameters.Add(DbParameterHelper.AddNullableString(cmd, "@PhoneNumber", query.PhoneNumber));

            var mainQuery = await EntitySet.ExecuteReadCommandAsync<GetDuplicateContactQuery.Result>(cmd);
            mainQuery = mainQuery.Where(x => x.Id != query.ContactId);
            return new QueryResult<GetDuplicateContactQuery.Result>(mainQuery);
        }
    }

    public class GetProspectAssignmenInfoQuery : QueryBase<GetProspectAssignmenInfoQuery.Result>
    {
        public Guid ProspectAssignmentId { get; set; }
        public class Result
        {
            public Guid? AssignedAgentId { get; set; }
            public ProspectAssignmentStatus Status { get; set; }
            public DateTime? UnassignedDate { get; set; }
            public ProspectAssignmentClosedReason? ClosedReason { get; set; }
        }
    }
    internal class GetProspectAssignmenInfoHandler : QueryHandlerBase<GetProspectAssignmenInfoQuery, GetProspectAssignmenInfoQuery.Result>
    {
        public GetProspectAssignmenInfoHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task<QueryResult<GetProspectAssignmenInfoQuery.Result>> ExecuteAsync(GetProspectAssignmenInfoQuery query)
        {
            var cmd = EntitySet.CreateDbCommand();
            cmd.CommandText = @"SELECT
                                   pa.AssignedAgentId, pa.Status, pa.UnassignedDate, pa.ClosedReason
                                   FROM dbo.Prospect p
                                   JOIN dbo.ProspectAssignment pa WITH (FORCESEEK) on p.CurrentAssignmentId=pa.Id
                                   WHERE pa.Id = @ProspectAssignmentId";
            cmd.CommandType = CommandType.Text;

            cmd.Parameters.Add(DbParameterHelper.AddNullableGuid(cmd, "@ProspectAssignmentId", query.ProspectAssignmentId));

            var mainQuery = await EntitySet.ExecuteReadCommandAsync<GetProspectAssignmenInfoQuery.Result>(cmd);
            return new QueryResult<GetProspectAssignmenInfoQuery.Result>(mainQuery);
        }
    }
}