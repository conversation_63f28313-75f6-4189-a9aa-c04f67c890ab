﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Threading.Tasks;
using Webaby;
using Webaby.Data;

namespace TinyCRM.Outbound.TeamLeadCampaign
{
    public class UnassignCommand : CommandBase
    {
        public List<Guid> ContactList { get; set; }
        public DateTime? AssignedDate { get; set; }
        public List<Guid> AgentList { get; set; }
        public int? CallCountFrom { get; set; }
        public int? CallCountTo { get; set; }
        public Guid CampaignId { get; set; }
        public Guid TeamId { get; set; }
        public Guid UnassignedBy { get; set; }
        public string PhoneNumber { get; set; }
    }
    internal class UnassignCommandHandler : CommandHandlerBase<UnassignCommand>
    {
        public UnassignCommandHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task ExecuteAsync(UnassignCommand command)
        { 

            var cmd = EntitySet.CreateDbCommand(); 
            cmd.Parameters.Add(DbParameterHelper.AddNullableGuid(cmd, "@CampaignId", command.CampaignId));
            cmd.Parameters.Add(DbParameterHelper.AddNullableGuid(cmd, "@TeamId", command.TeamId));
            cmd.Parameters.Add(DbParameterHelper.NewNullableDateTimeParameter(cmd, "@AssignedDateBefore", command.AssignedDate));
            cmd.Parameters.Add(DbParameterHelper.AddNullableInt(cmd, "@CallCountTo", command.CallCountTo));
            cmd.Parameters.Add(DbParameterHelper.AddNullableInt(cmd, "@CallCountFrom", command.CallCountFrom));
            cmd.Parameters.Add(DbParameterHelper.AddNullableGuid(cmd, "@UnassignedBy", command.UnassignedBy));
            cmd.Parameters.Add(DbParameterHelper.AddNullableString(cmd, "@PhoneNumber", command.PhoneNumber));
            cmd.Parameters.Add(DbParameterHelper.NewIdListParameter("@SelectedProspectToReturn", command.ContactList));
            cmd.Parameters.Add(DbParameterHelper.NewIdListParameter("@AgentList", command.AgentList));

            cmd.CommandText = "telesale.Prospect_ReturnToTeamBucket";
            cmd.CommandType = CommandType.StoredProcedure;

            await EntitySet.ExecuteNonQueryAsync(cmd);

        }
    }
}
