﻿using System;
using System.Configuration;
using System.Data;
using System.Linq;
using System.Threading.Tasks;
using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using TinyCRM.Outbound.AgentFieldSaleCouple.Queries;
using TinyCRM.Outbound.LeadAssignment;
using TinyCRM.Outbound.SlotTime;
using Webaby;
using Webaby.Data;
using Webaby.Localization;
using Webaby.Security;

namespace TinyCRM.Outbound.Appointment.Commands
{
    public class DistributeAppointmentCoupleFieldSalesCommand : CommandBase
    {
        public Guid AppointmentId
        {
            get;
            set;
        }

        public Guid AppointmentProvinceId
        {
            get;
            set;
        }
    }
    public class DistributeAppointmentCoupleFieldSalesCommandHandler : CommandHandlerBase<DistributeAppointmentCoupleFieldSalesCommand>
    {
        IConfiguration _configuration;
        IUserService _userService;
        public DistributeAppointmentCoupleFieldSalesCommandHandler(IServiceProvider serviceProvider, IConfiguration configuration, IUserService userService) : base(serviceProvider) 
        { 
            _configuration = configuration; 
            _userService = userService; 
        }

        public override async Task ExecuteAsync(DistributeAppointmentCoupleFieldSalesCommand command)
        {
            var appointmentEntity = await EntitySet.GetAsync<AppointmentEntity>(command.AppointmentId);
            var leadAssignmentEntity = EntitySet.Get<LeadAssignmentEntity>().SingleOrDefault(la => la.WaitingAppointmentId == command.AppointmentId);

            #region Get Slot Time Range

            DateTime fromTime = appointmentEntity.MeetDate.Date;
            DateTime toTime = appointmentEntity.MeetDate.Date.AddDays(1).AddSeconds(-1);

            var slotTimeQuery = EntitySet.Get<SlotTimeEntity>();
            var slotTime = (from st in slotTimeQuery
                            where st.FromTime <= DateTime.Now.TimeOfDay && DateTime.Now.TimeOfDay < st.ToTime
                            select st).SingleOrDefault();

            if (slotTime != null)
            {
                fromTime = DateTime.Now.Date.Add(slotTime.FromTime);
                toTime = DateTime.Now.Date.Add(slotTime.ToTime);
            }

            #endregion

            // Find Field Sale couples
            var cmd = EntitySet.CreateDbCommand();
            cmd.Parameters.Add(DbParameterHelper.AddNullableGuid(cmd, "@AgentId", _userService.GetCurrentUser().Id));
            cmd.Parameters.Add(DbParameterHelper.AddNullableGuid(cmd, "@AppointmentProvinceId", command.AppointmentProvinceId));
            cmd.Parameters.Add(DbParameterHelper.NewNullableDateTimeParameter(cmd, "@MeetDate", appointmentEntity.MeetDate));
            cmd.Parameters.Add(DbParameterHelper.NewNullableDateTimeParameter(cmd, "@SlotFromTime", fromTime));
            cmd.Parameters.Add(DbParameterHelper.NewNullableDateTimeParameter(cmd, "@SlotToTime", toTime));
            cmd.CommandText = "telesale.lead_GetCoupleFieldSales";
            cmd.CommandType = CommandType.StoredProcedure;
            var coupleFieldSales = await EntitySet.ExecuteReadCommandAsync<FieldSaleAppointmentInfo>(cmd);

            int appointmentsPerDay = _configuration.GetValue<int>("AppointmentsPerDay");

            coupleFieldSales = (from cfs in coupleFieldSales
                                where cfs.DayAppointmentCount < appointmentsPerDay
                                select cfs);

            // If found coupled Field Sale teams
            if (coupleFieldSales.Any())
            {
                // Round robin, get Field Sale that appointments can be assigned largest
                var coupleFieldSale = (from cfst in coupleFieldSales
                                       orderby cfst.DayAppointmentCount
                                       select cfst).FirstOrDefault();

                // Assign Lead to found DMO.
                //leadAssignmentEntity.AssignedFieldSaleTeamId = coupleFieldSale.OrganizationId;
                //leadAssignmentEntity.AssignedFieldSaleTeamDate = DateTime.Now;
                //leadAssignmentEntity.AssignedFieldSaleId = coupleFieldSale.FieldSaleId;
                //leadAssignmentEntity.AssignedFieldSaleDate = DateTime.Now;
                leadAssignmentEntity.SuggestedFieldSaleTeamId = coupleFieldSale.OrganizationId;
                leadAssignmentEntity.SuggestedFieldSaleId = coupleFieldSale.FieldSaleId;
                leadAssignmentEntity.SuggestedFieldSaleReason = SuggestedFieldSaleReason.Couple;
                await Repository.SaveAsync(leadAssignmentEntity);
            }
        }
    }
}