﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TinyCRM.AutomaticTask;
using TinyCRM.TaskType;
using TinyCRM.Workflow;
using Webaby;
using Webaby.Data;
using AutoMapper;
using Webaby.Localization;
using Microsoft.EntityFrameworkCore;

namespace TinyCRM.WorkflowTaskTypeStage.Commands
{
    public class DeleteWorkflowTaskTypeStageCommand : CommandBase
    {
        public Guid WorkflowTaskTypeId { get; set; }

        public bool IsGroup { get; set; }
    }

    internal class DeleteWorkflowTaskTypeStageCommandHandler : CommandHandlerBase<DeleteWorkflowTaskTypeStageCommand>
    {
        public DeleteWorkflowTaskTypeStageCommandHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task ExecuteAsync(DeleteWorkflowTaskTypeStageCommand command)
        {
            if (command.IsGroup)
            {
                var listDelete = new List<IEntity>();
                var taskGroup = await EntitySet.GetAsync<WorkflowTaskTypeStageEntity>(command.WorkflowTaskTypeId);
                listDelete.Add(taskGroup);
                var autoNextAction = await EntitySet.Get<AutoNextTaskEntity>().Where(x => x.ReferenceObjectId == command.WorkflowTaskTypeId).FirstOrDefaultAsync();
                listDelete.AddRange(autoNextAction);
                await Repository.DeleteAsync(listDelete);
                var editEntity = await EntitySet.Get<WorkflowTaskTypeEntity>().Where(x => x.WorkflowTaskTypeGroupId == command.WorkflowTaskTypeId).ToListAsync();
                editEntity.ForEach(x => { x.WorkflowTaskTypeGroupId = null; });
                await Repository.SaveAsync(editEntity);
            } else
            {
                var editEntity = await EntitySet.GetAsync<WorkflowTaskTypeEntity>(command.WorkflowTaskTypeId);
                editEntity.WorkflowTaskTypeGroupId = null;
                await Repository.SaveAsync(editEntity);
            }
        }
    }
}