﻿using System;
using System.Data;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Webaby;
using Webaby.Data;

namespace TinyCRM.Outbound.Report.DynamicFormValue
{
    /// <summary>
    /// Truy vấn báo cáo trạng thái Dynamic Form Value
    /// </summary>
    public class GetDynamicFormValueStatusReportQuery : QueryBase<DynamicFormValueStatusReportItem>
    {
        public Guid CampaignId { get; set; }
        public Guid AgentId { get; set; }
        public int TempLockDays { get; set; }
    }

    internal class GetDynamicFormValueStatusReportQueryHandler :
        QueryHandlerBase<GetDynamicFormValueStatusReportQuery, DynamicFormValueStatusReportItem>
    {
        private readonly ILogger<GetDynamicFormValueStatusReportQueryHandler> _logger;

        public GetDynamicFormValueStatusReportQueryHandler(
            IServiceProvider serviceProvider,
            ILogger<GetDynamicFormValueStatusReportQueryHandler> logger)
            : base(serviceProvider)
        {
            _logger = logger;
        }

        public override async Task<QueryResult<DynamicFormValueStatusReportItem>> ExecuteAsync(
            GetDynamicFormValueStatusReportQuery query)
        {
            using (_logger.BeginScope("GetDynamicFormValueStatusReport {@CampaignId} {@AgentId}",
                       query.CampaignId, query.AgentId))
            {
                var cmd = EntitySet.CreateDbCommand();
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandText = "telesale.GetDynamicFormValueStatusReport";

                // DbParameterHelper đã được dùng đúng kiểu, thêm trở lại vào Parameters
                cmd.Parameters.Add(
                    cmd.Parameters.Add(DbParameterHelper.AddNullableGuid(cmd, "@CampaignId", query.CampaignId)));
                cmd.Parameters.Add(
                    cmd.Parameters.Add(DbParameterHelper.AddNullableGuid(cmd, "@AgentId", query.AgentId)));
                cmd.Parameters.Add(
                    cmd.Parameters.Add(DbParameterHelper.AddNullableInt(cmd, "@TempLockDays", query.TempLockDays)));

                var items = await EntitySet
                    .ExecuteReadCommandAsync<DynamicFormValueStatusReportItem>(cmd);

                return QueryResult.Create(items);           
            }
        }
    }
}
